/*
 Navicat Premium Dump SQL

 Source Server         : vida
 Source Server Type    : MySQL
 Source Server Version : 50742 (5.7.42-log)
 Source Host           : localhost:3302
 Source Schema         : xplife_pro_test

 Target Server Type    : MySQL
 Target Server Version : 50742 (5.7.42-log)
 File Encoding         : 65001

 Date: 27/06/2025 10:49:55
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for templet_group
-- ----------------------------
DROP TABLE IF EXISTS `templet_group`;
CREATE TABLE `templet_group`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分组名称',
  `sort` int(11) NULL DEFAULT 0 COMMENT '排序',
  `source_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '分组类型（0-用户  1-系统）',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '标签分组表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of templet_group
-- ----------------------------
INSERT INTO `templet_group` VALUES (2, '分组1', 1, 1, '2025-02-27 08:39:10', '2025-02-27 08:39:10');
INSERT INTO `templet_group` VALUES (3, '分组2', 3, 1, '2025-02-27 08:39:25', '2025-02-27 08:39:25');
INSERT INTO `templet_group` VALUES (4, '分组3', 2, 1, '2025-02-27 08:39:33', '2025-02-27 08:39:33');
INSERT INTO `templet_group` VALUES (5, '测试多语言', 10, 1, '2025-04-22 17:07:37', '2025-04-22 17:08:55');

SET FOREIGN_KEY_CHECKS = 1;
