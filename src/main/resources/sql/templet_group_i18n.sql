/*
 Navicat Premium Dump SQL

 Source Server         : vida
 Source Server Type    : MySQL
 Source Server Version : 50742 (5.7.42-log)
 Source Host           : localhost:3302
 Source Schema         : xplife_pro_test

 Target Server Type    : MySQL
 Target Server Version : 50742 (5.7.42-log)
 File Encoding         : 65001

 Date: 27/06/2025 10:50:08
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for templet_group_i18n
-- ----------------------------
DROP TABLE IF EXISTS `templet_group_i18n`;
CREATE TABLE `templet_group_i18n`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '唯一标识符',
  `group_id` bigint(20) NULL DEFAULT NULL COMMENT '标签宝箱类别唯一标识符',
  `locale` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '语言代码，例如：en, cn, sp',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类别名称多语言',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_templet_group_i18n_group_id_locale`(`group_id`, `locale`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '标签宝箱类别名称多语言' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of templet_group_i18n
-- ----------------------------
INSERT INTO `templet_group_i18n` VALUES (1, 2, 'en', 'test1');
INSERT INTO `templet_group_i18n` VALUES (2, 5, 'en', 'test i18n');

SET FOREIGN_KEY_CHECKS = 1;
