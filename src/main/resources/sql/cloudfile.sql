/*
 Navicat Premium Dump SQL

 Source Server         : vida
 Source Server Type    : MySQL
 Source Server Version : 50742 (5.7.42-log)
 Source Host           : localhost:3302
 Source Schema         : xplife_pro_test

 Target Server Type    : MySQL
 Target Server Version : 50742 (5.7.42-log)
 File Encoding         : 65001

 Date: 17/07/2025 09:48:47
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for cloudfile
-- ----------------------------
DROP TABLE IF EXISTS `cloudfile`;
CREATE TABLE `cloudfile`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '唯一标识符ID',
  `user_id` bigint(20) NULL DEFAULT NULL COMMENT '用户ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文件名称',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文件url路径',
  `type` tinyint(4) NULL DEFAULT 0 COMMENT '文件类型： 0 excel',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '收藏时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间，自动更新',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 86 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '云文件' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of cloudfile
-- ----------------------------
INSERT INTO `cloudfile` VALUES (25, 5, '烟草商品库_x (1).xlsx', 'http://img.ycjqb.com/xinye/x1/test/5/1741069417864.xlsx', 0, '2025-03-04 14:23:39', '2025-03-04 14:23:39');
INSERT INTO `cloudfile` VALUES (26, 3, '副本烟草商品库_num_小数.xlsx', 'http://img.ycjqb.com/xinye/x1/test/3/1741084445138.xlsx', 0, '2025-03-04 18:34:05', '2025-03-04 18:34:05');
INSERT INTO `cloudfile` VALUES (56, 1, '副本烟草商品库_多列数据12255554456669878722020202020.xls', 'http://img.ycjqb.com/xinye/x1/test/1/1744595449235.xls', 0, '2025-04-14 09:50:50', '2025-04-14 09:50:50');
INSERT INTO `cloudfile` VALUES (57, 1, '五金手册-含常用体积-面积的计算公式- (1).xls', 'http://img.ycjqb.com/xinye/x1/test/1/1744855534632.xls', 0, '2025-04-17 10:05:35', '2025-04-17 10:05:35');
INSERT INTO `cloudfile` VALUES (58, 1, '带宏公式的.xls', 'http://img.ycjqb.com/xinye/x1/test/1/1744856075688.xls', 0, '2025-04-17 10:14:36', '2025-04-17 10:14:36');
INSERT INTO `cloudfile` VALUES (62, 6, '报表用例(1).xlsx', 'http://img.ycjqb.com/xinye/x1/test/6/1745370264998.xlsx', 0, '2025-04-23 09:04:25', '2025-04-23 09:04:25');
INSERT INTO `cloudfile` VALUES (63, 6, 'A4测试用例.xlsx', 'http://img.ycjqb.com/xinye/x1/test/6/1745372722253.xlsx', 0, '2025-04-23 09:45:22', '2025-04-23 09:45:22');
INSERT INTO `cloudfile` VALUES (64, 6, '测试用例.xlsx', 'http://img.ycjqb.com/xinye/x1/test/6/1745372752012.xlsx', 0, '2025-04-23 09:45:52', '2025-04-23 09:45:52');
INSERT INTO `cloudfile` VALUES (65, 1, 'test.xlsx', 'http://img.ycjqb.com/xinye/x1/test/1/1745822795742.xlsx', 0, '2025-04-28 14:46:36', '2025-04-28 14:46:36');
INSERT INTO `cloudfile` VALUES (66, 8, '新建 XLSX 工作表(1).xlsx', 'http://img.ycjqb.com/xinye/x1/test/8/1747030503874.xlsx', 0, '2025-04-29 19:53:58', '2025-04-29 19:53:58');
INSERT INTO `cloudfile` VALUES (70, 3, '5000条数据(1).xlsx', 'http://img.ycjqb.com/xinye/x1/test/3/1746694650647.xlsx', 0, '2025-05-08 16:57:31', '2025-05-08 16:57:31');
INSERT INTO `cloudfile` VALUES (73, 5, '5000条数据(1).xlsx', 'http://img.ycjqb.com/xinye/x1/test/5/1746930290310.xlsx', 0, '2025-05-11 10:24:51', '2025-05-11 10:24:51');
INSERT INTO `cloudfile` VALUES (74, 5, '库存4.19(1).xlsx', 'http://img.ycjqb.com/xinye/x1/test/5/1746930293888.xlsx', 0, '2025-05-11 10:24:54', '2025-05-11 10:24:54');
INSERT INTO `cloudfile` VALUES (75, 5, '库存4.19(1)(1).xlsx', 'http://img.ycjqb.com/xinye/x1/test/5/1746930296965.xlsx', 0, '2025-05-11 10:24:59', '2025-05-11 10:24:59');
INSERT INTO `cloudfile` VALUES (76, 3, '测试用例.xlsx', 'http://img.ycjqb.com/xinye/x1/test/3/1746947819152.xlsx', 0, '2025-05-11 15:16:59', '2025-05-11 15:16:59');
INSERT INTO `cloudfile` VALUES (78, 8, '冯希鹏芯烨工作周报.xlsx', 'http://img.ycjqb.com/xinye/x1/test/8/1747030479276.xlsx', 0, '2025-05-12 14:14:39', '2025-05-12 14:14:39');
INSERT INTO `cloudfile` VALUES (79, 8, '新人入职培训计划表-冯希鹏.xlsx', 'http://img.ycjqb.com/xinye/x1/test/8/1747030516889.xlsx', 0, '2025-05-12 14:15:16', '2025-05-12 14:15:16');
INSERT INTO `cloudfile` VALUES (80, 6, '带宏公式的(1).xls', 'http://img.ycjqb.com/xinye/x1/test/6/1747989590342.xls', 0, '2025-05-23 16:39:51', '2025-05-23 16:39:51');
INSERT INTO `cloudfile` VALUES (81, 5, '新建 XLSX 工作表(1).xlsx', 'http://img.ycjqb.com/xinye/x1/test/5/1752661225221.xlsx', 0, '2025-07-16 18:20:25', '2025-07-16 18:20:25');
INSERT INTO `cloudfile` VALUES (84, 5, '1_Xprinter导入Excel数据模板(1).xlsx', 'http://img.ycjqb.com/xinye/x1/test/5/1752661526028.xlsx', 0, '2025-07-16 18:25:26', '2025-07-16 18:25:26');
INSERT INTO `cloudfile` VALUES (85, 4, '商品列表格-韩文.xlsx', 'http://img.ycjqb.com/xinye/x1/test/4/1752662479459.xlsx', 0, '2025-07-16 18:41:21', '2025-07-16 18:41:21');

SET FOREIGN_KEY_CHECKS = 1;
