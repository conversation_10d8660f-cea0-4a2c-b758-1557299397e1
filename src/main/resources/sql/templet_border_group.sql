/*
 Navicat Premium Dump SQL

 Source Server         : vida
 Source Server Type    : MySQL
 Source Server Version : 50742 (5.7.42-log)
 Source Host           : localhost:3302
 Source Schema         : xplife_pro_test

 Target Server Type    : MySQL
 Target Server Version : 50742 (5.7.42-log)
 File Encoding         : 65001

 Date: 27/06/2025 10:49:18
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for templet_border_group
-- ----------------------------
DROP TABLE IF EXISTS `templet_border_group`;
CREATE TABLE `templet_border_group`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '边框分组名称',
  `sort` int(11) NULL DEFAULT 0 COMMENT '排序',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '统预设标签边框分组表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of templet_border_group
-- ----------------------------
INSERT INTO `templet_border_group` VALUES (6, '分组2', 1, '2025-03-25 16:47:19', '2025-04-29 17:00:06');
INSERT INTO `templet_border_group` VALUES (7, '简约', 1, '2025-03-25 16:47:19', '2025-03-25 16:47:19');
INSERT INTO `templet_border_group` VALUES (8, '可爱', 2, '2025-03-25 16:47:19', '2025-03-25 16:47:19');
INSERT INTO `templet_border_group` VALUES (9, '花纹', 3, '2025-03-25 16:47:19', '2025-03-25 16:47:19');
INSERT INTO `templet_border_group` VALUES (10, '中式', 4, '2025-03-25 16:47:19', '2025-03-25 16:47:19');
INSERT INTO `templet_border_group` VALUES (11, '典雅', 5, '2025-03-25 16:47:19', '2025-03-25 16:47:19');

SET FOREIGN_KEY_CHECKS = 1;
