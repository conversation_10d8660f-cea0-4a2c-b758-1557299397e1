/*
 Navicat Premium Dump SQL

 Source Server         : vida
 Source Server Type    : MySQL
 Source Server Version : 50742 (5.7.42-log)
 Source Host           : localhost:3302
 Source Schema         : xplife_pro_test

 Target Server Type    : MySQL
 Target Server Version : 50742 (5.7.42-log)
 File Encoding         : 65001

 Date: 27/06/2025 10:50:37
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for templet_market_group
-- ----------------------------
DROP TABLE IF EXISTS `templet_market_group`;
CREATE TABLE `templet_market_group`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分组唯一标识，用于区分不同的模板分组',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分组名称，用于展示该分组的用途或类型',
  `type` int(11) NOT NULL COMMENT '分组类型，可根据业务需求定义不同的类型值  1. 待办清单、2. 课程表、3. 便利贴、4. 装饰框、5. 贺卡',
  `sort` int(11) NOT NULL COMMENT '分组排序，用于确定分组在列表中的显示顺序',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '存储模板分组的基本信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of templet_market_group
-- ----------------------------
INSERT INTO `templet_market_group` VALUES (7, '便签', 1, 2);
INSERT INTO `templet_market_group` VALUES (8, '清单', 2, 1);
INSERT INTO `templet_market_group` VALUES (9, '课程表', 3, 3);
INSERT INTO `templet_market_group` VALUES (10, '便利贴', 4, 4);
INSERT INTO `templet_market_group` VALUES (12, '名片', 5, 5);

SET FOREIGN_KEY_CHECKS = 1;
