<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vida.xinye.x2.dao.TempletMarketGroupDao">
    <insert id="insert">
        INSERT IGNORE INTO templet_market_group_i18n (group_id, locale, name)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.groupId}, #{item.locale}, #{item.name})
            ON DUPLICATE KEY UPDATE name = #{item.name}
        </foreach>
    </insert>

    <select id="list" resultType="com.vida.xinye.x2.dto.TempletMarketGroupDto">
        SELECT
        tmg.id,
        tmg.sort,
        tmg.type,
        CASE
        WHEN #{locale} IS NULL THEN tmg.name
        ELSE COALESCE(tmgi.name, tmg.name)
        END AS name
        FROM templet_market_group tmg
        LEFT JOIN templet_market_group_i18n tmgi
        ON tmg.id = tmgi.group_id
        <if test="locale != null">
            AND tmgi.locale = #{locale}
        </if>
        ORDER BY tmg.sort ASC
    </select>

</mapper> 