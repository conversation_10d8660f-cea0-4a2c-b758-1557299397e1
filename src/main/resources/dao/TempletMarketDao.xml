<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vida.xinye.x2.dao.TempletMarketDao">

    <resultMap id="TempletMarketResultMap" type="com.vida.xinye.x2.dto.TempletMarketDto">
        <id property="id" column="id"/>
        <result property="data" column="data"/>
        <result property="groupId" column="group_id"/>
        <result property="sort" column="sort"/>
        <result property="createTime" column="create_time"/>

        <association property="resUrl" javaType="com.vida.xinye.x2.domain.TempletMarketImageInfo">
            <result property="width" column="resUrl_width"/>
            <result property="pic" column="resUrl_pic"/>
            <result property="height" column="resUrl_height"/>
        </association>
        <association property="downUrl" javaType="com.vida.xinye.x2.domain.TempletMarketImageInfo">
            <result property="width" column="downUrl_width"/>
            <result property="pic" column="downUrl_pic"/>
            <result property="height" column="downUrl_height"/>
        </association>
        <association property="rightUrl" javaType="com.vida.xinye.x2.domain.TempletMarketImageInfo">
            <result property="width" column="rightUrl_width"/>
            <result property="pic" column="rightUrl_pic"/>
            <result property="height" column="rightUrl_height"/>
        </association>
        <association property="leftUrl" javaType="com.vida.xinye.x2.domain.TempletMarketImageInfo">
            <result property="width" column="leftUrl_width"/>
            <result property="pic" column="leftUrl_pic"/>
            <result property="height" column="leftUrl_height"/>
        </association>
        <association property="topUrl" javaType="com.vida.xinye.x2.domain.TempletMarketImageInfo">
            <result property="width" column="topUrl_width"/>
            <result property="pic" column="topUrl_pic"/>
            <result property="height" column="topUrl_height"/>
        </association>
        <association property="listUrl" javaType="com.vida.xinye.x2.domain.TempletMarketImageInfo">
            <result property="width" column="listUrl_width"/>
            <result property="pic" column="listUrl_pic"/>
            <result property="height" column="listUrl_height"/>
        </association>
        <association property="addLeftUrl" javaType="com.vida.xinye.x2.domain.TempletMarketImageInfo">
            <result property="width" column="addLeftUrl_width"/>
            <result property="pic" column="addLeftUrl_pic"/>
            <result property="height" column="addLeftUrl_height"/>
        </association>
    </resultMap>


    <select id="list" resultMap="TempletMarketResultMap">
        SELECT
        id,
        resUrl_width,
        resUrl_pic,
        resUrl_height,
        downUrl_width,
        downUrl_pic,
        downUrl_height,
        rightUrl_width,
        rightUrl_pic,
        rightUrl_height,
        leftUrl_width,
        leftUrl_pic,
        leftUrl_height,
        topUrl_width,
        topUrl_pic,
        topUrl_height,
        listUrl_width,
        listUrl_pic,
        listUrl_height,
        addLeftUrl_width,
        addLeftUrl_pic,
        addLeftUrl_height,
        group_id,
        sort,
        data,
        create_time
        FROM templet_market tm
        <where>
            <if test="groupId != null">
                tm.group_id = #{groupId}
            </if>
        </where>
        ORDER BY tm.sort ASC
    </select>

    <insert id="insert" parameterType="com.vida.xinye.x2.dto.TempletMarketDto">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
            SELECT LAST_INSERT_ID()
        </selectKey>
        INSERT INTO templet_market (
        <if test="resUrl != null">
            resUrl_width,
            resUrl_pic,
            resUrl_height,
        </if>
        <if test="downUrl != null">
            downUrl_width,
            downUrl_pic,
            downUrl_height,
        </if>
        <if test="rightUrl != null">
            rightUrl_width,
            rightUrl_pic,
            rightUrl_height,
        </if>
        <if test="leftUrl != null">
            leftUrl_width,
            leftUrl_pic,
            leftUrl_height,
        </if>
        <if test="topUrl != null">
            topUrl_width,
            topUrl_pic,
            topUrl_height,
        </if>
        <if test="listUrl != null">
            listUrl_width,
            listUrl_pic,
            listUrl_height,
        </if>
        <if test="addLeftUrl != null">
            addLeftUrl_width,
            addLeftUrl_pic,
            addLeftUrl_height,
        </if>
        <if test="data != null">
            data,
        </if>
        group_id,
        sort
        ) VALUES (
        <if test="resUrl != null">
            #{resUrl.width},
            #{resUrl.pic},
            #{resUrl.height},
        </if>
        <if test="downUrl != null">
            #{downUrl.width},
            #{downUrl.pic},
            #{downUrl.height},
        </if>
        <if test="rightUrl != null">
            #{rightUrl.width},
            #{rightUrl.pic},
            #{rightUrl.height},
        </if>
        <if test="leftUrl != null">
            #{leftUrl.width},
            #{leftUrl.pic},
            #{leftUrl.height},
        </if>
        <if test="topUrl != null">
            #{topUrl.width},
            #{topUrl.pic},
            #{topUrl.height},
        </if>
        <if test="listUrl != null">
            #{listUrl.width},
            #{listUrl.pic},
            #{listUrl.height},
        </if>
        <if test="addLeftUrl != null">
            #{addLeftUrl.width},
            #{addLeftUrl.pic},
            #{addLeftUrl.height},
        </if>
        <if test="data != null">
            #{data},
        </if>
        #{groupId},
        #{sort}
        )
    </insert>

    <update id="update" parameterType="com.vida.xinye.x2.dto.TempletMarketDto">
        UPDATE templet_market
        SET
        <if test="resUrl != null">
            resUrl_width = #{resUrl.width},
            resUrl_pic = #{resUrl.pic},
            resUrl_height = #{resUrl.height},
        </if>
        <if test="downUrl != null">
            downUrl_width = #{downUrl.width},
            downUrl_pic = #{downUrl.pic},
            downUrl_height = #{downUrl.height},
        </if>
        <if test="rightUrl != null">
            rightUrl_width = #{rightUrl.width},
            rightUrl_pic = #{rightUrl.pic},
            rightUrl_height = #{rightUrl.height},
        </if>
        <if test="leftUrl != null">
            leftUrl_width = #{leftUrl.width},
            leftUrl_pic = #{leftUrl.pic},
            leftUrl_height = #{leftUrl.height},
        </if>
        <if test="topUrl != null">
            topUrl_width = #{topUrl.width},
            topUrl_pic = #{topUrl.pic},
            topUrl_height = #{topUrl.height},
        </if>
        <if test="listUrl != null">
            listUrl_width = #{listUrl.width},
            listUrl_pic = #{listUrl.pic},
            listUrl_height = #{listUrl.height},
        </if>
        <if test="addLeftUrl != null">
            addLeftUrl_width = #{addLeftUrl.width},
            addLeftUrl_pic = #{addLeftUrl.pic},
            addLeftUrl_height = #{addLeftUrl.height},
        </if>
        <if test="data != null">data = #{data}, </if>
        <if test="groupId != null">group_id = #{groupId}, </if>
        <if test="sort != null">sort = #{sort}</if>
        WHERE id = #{id}
    </update>


</mapper>
