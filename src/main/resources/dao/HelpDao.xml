<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vida.xinye.x2.dao.HelpDao">

    <select id="tutorials" resultType="com.vida.xinye.x2.dto.HelpTutorialDto">
        SELECT COALESCE(i18n.title, t.title) AS title,
        COALESCE(i18n.cover, t.cover) AS cover,
        COALESCE(i18n.url, t.url) AS url
        FROM help_tutorial t
        LEFT JOIN help_tutorial_i18n i18n ON t.id = i18n.tutorial_id AND i18n.locale = #{locale}
        order by t.create_time asc
    </select>

    <select id="productManuals" resultType="com.vida.xinye.x2.dto.HelpProductManualDto">
        SELECT COALESCE(i18n.title, t.title) AS title,
        COALESCE(i18n.url, t.url) AS url
        FROM help_product_manual t
        LEFT JOIN help_product_manual_i18n i18n ON t.id = i18n.product_manual_id AND i18n.locale = #{locale}
        order by t.create_time asc
    </select>

    <select id="faqs" resultType="com.vida.xinye.x2.dto.HelpFaqDto">
        SELECT COALESCE(i18n.question, t.question) AS question,
        COALESCE(i18n.answer, t.answer) AS answer
        FROM help_faq t
        LEFT JOIN help_faq_i18n i18n ON t.id = i18n.faq_id AND i18n.locale = #{locale}
        order by t.create_time asc
    </select>

</mapper>
