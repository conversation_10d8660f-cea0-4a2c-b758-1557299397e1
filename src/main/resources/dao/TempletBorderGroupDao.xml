<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vida.xinye.x2.dao.TempletBorderGroupDao">
    <insert id="insert">
        INSERT IGNORE INTO templet_border_group_i18n(group_id, locale, name)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.groupId}, #{item.locale}, #{item.name})
            ON DUPLICATE KEY UPDATE name = #{item.name}
        </foreach>
    </insert>

    <select id="list" resultType="com.vida.xinye.x2.dto.TempletBorderGroupDto">
        SELECT
        tbg.id,
        tbg.sort,
        CASE
        WHEN #{locale} IS NULL THEN tbg.name
        ELSE COALESCE(tbgi.name, tbg.name)
        END AS name
        FROM templet_border_group tbg
        LEFT JOIN templet_border_group_i18n tbgi
        ON tbg.id = tbgi.group_id
        <if test="locale != null">
            AND tbgi.locale = #{locale}
        </if>
        ORDER BY tbg.sort ASC
    </select>

</mapper> 