<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vida.xinye.x2.dao.WrongQuestionTagRelationDao">

    <insert id="insertList">
        INSERT IGNORE INTO wrong_question_tag_relation
        (question_id,tag_id,tag_type_id) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.questionId,jdbcType=BIGINT},
            #{item.tagId,jdbcType=BIGINT},
            #{item.tagTypeId,jdbcType=BIGINT})
        </foreach>
    </insert>


</mapper>
