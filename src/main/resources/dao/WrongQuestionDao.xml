<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vida.xinye.x2.dao.WrongQuestionDao">

    <resultMap id="listMap" type="com.vida.xinye.x2.dto.WrongQuestionDto" extends="com.vida.xinye.x2.mbg.mapper.WrongQuestionMapper.ResultMapWithBLOBs">

        <collection property="tags" column="question_id" ofType="com.vida.xinye.x2.mbg.model.WrongQuestionTag" select="selectTagsByQuestionId"/>
    </resultMap>

    <select id="getList" resultMap="listMap">
        SELECT wq.id question_id, wq.*
        FROM wrong_question wq
        WHERE wq.id IN (
        SELECT question_id
        FROM wrong_question_tag_relation
        WHERE tag_id IN
        <foreach item="tagId" collection="tagIds" open="(" separator="," close=")">
            #{tagId}
        </foreach>
        GROUP BY question_id
        HAVING COUNT(DISTINCT tag_id) = ${tagIds.size}
        )
        AND wq.user_id = #{userId}
        <if test="sourceType != null and sourceType != ''">
            AND wq.source_type = #{sourceType}
        </if>
        <if test="saveTimeRange != null and saveTimeRange != ''">
            <choose>
                <when test="saveTimeRange == 'THREE_DAYS'">
                    AND wq.create_time &gt;= DATE_SUB(NOW(), INTERVAL 3 DAY)
                </when>
                <when test="saveTimeRange == 'ONE_WEEK'">
                    AND wq.create_time &gt;= DATE_SUB(NOW(), INTERVAL 7 DAY)
                </when>
                <when test="saveTimeRange == 'ONE_MONTH'">
                    AND wq.create_time &gt;= DATE_SUB(NOW(), INTERVAL 1 MONTH)
                </when>
            </choose>
        </if>
        ORDER BY wq.id DESC
    </select>
    <!--子查询-->
    <select id="selectTagsByQuestionId" resultMap="com.vida.xinye.x2.mbg.mapper.WrongQuestionTagMapper.BaseResultMap">
         SELECT *
         FROM wrong_question_tag
         WHERE id IN (
         SELECT tag_id
         FROM wrong_question_tag_relation
         WHERE question_id = #{questionId}
         )
    </select>

    <select id="querySubjects" resultType="com.vida.xinye.x2.dto.WrongQuestionSubject">
        SELECT
        wqt.id,
        wqt.tag_name AS tagName,
        wqt.tag_type_id AS tagTypeId,
        wqt.is_default AS isDefault,
        COUNT(DISTINCT wq.id) AS wrongQuestionCount
        FROM
        wrong_question_tag_type wqtt
        JOIN
        wrong_question_tag wqt ON wqtt.id = wqt.tag_type_id AND (wqt.user_id = 0 OR wqt.user_id = #{userId})
        LEFT JOIN
        wrong_question_tag_relation wqtr ON wqt.id = wqtr.tag_id
        LEFT JOIN wrong_question wq ON wqtr.question_id = wq.id AND wq.user_id = #{userId}
        WHERE
        wqtt.type_name = '选择科目'
        AND (wqt.user_id = 0 OR wqt.user_id = #{userId})
        GROUP BY
        wqt.id, wqt.tag_name, wqt.tag_type_id, wqt.is_default;
    </select>

</mapper>
