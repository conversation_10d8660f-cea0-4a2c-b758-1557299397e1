<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vida.xinye.x2.dao.TempletBorderDao">

    <resultMap id="listMap" type="com.vida.xinye.x2.dto.TempletBorderDto">
        <id property="id" column="id"/>
        <result property="groupId" column="group_id"/>
        <result property="createTime" column="create_time"/>

        <association property="leftUrl" javaType="com.vida.xinye.x2.domain.TempletMarketImageInfo">
            <result property="width" column="leftUrl_width"/>
            <result property="pic" column="leftUrl_pic"/>
            <result property="height" column="leftUrl_height"/>
        </association>
        <association property="rightUrl" javaType="com.vida.xinye.x2.domain.TempletMarketImageInfo">
            <result property="width" column="rightUrl_width"/>
            <result property="pic" column="rightUrl_pic"/>
            <result property="height" column="rightUrl_height"/>
        </association>
        <association property="topUrl" javaType="com.vida.xinye.x2.domain.TempletMarketImageInfo">
            <result property="width" column="topUrl_width"/>
            <result property="pic" column="topUrl_pic"/>
            <result property="height" column="topUrl_height"/>
        </association>
        <association property="downUrl" javaType="com.vida.xinye.x2.domain.TempletMarketImageInfo">
            <result property="width" column="downUrl_width"/>
            <result property="pic" column="downUrl_pic"/>
            <result property="height" column="downUrl_height"/>
        </association>
        <association property="leftTopUrl" javaType="com.vida.xinye.x2.domain.TempletMarketImageInfo">
            <result property="width" column="leftTopUrl_width"/>
            <result property="pic" column="leftTopUrl_pic"/>
            <result property="height" column="leftTopUrl_height"/>
        </association>
        <association property="leftDownUrl" javaType="com.vida.xinye.x2.domain.TempletMarketImageInfo">
            <result property="width" column="leftDownUrl_width"/>
            <result property="pic" column="leftDownUrl_pic"/>
            <result property="height" column="leftDownUrl_height"/>
        </association>
        <association property="rightTopUrl" javaType="com.vida.xinye.x2.domain.TempletMarketImageInfo">
            <result property="width" column="rightTopUrl_width"/>
            <result property="pic" column="rightTopUrl_pic"/>
            <result property="height" column="rightTopUrl_height"/>
        </association>
        <association property="rightDownUrl" javaType="com.vida.xinye.x2.domain.TempletMarketImageInfo">
            <result property="width" column="rightDownUrl_width"/>
            <result property="pic" column="rightDownUrl_pic"/>
            <result property="height" column="rightDownUrl_height"/>
        </association>
        <association property="resUrl" javaType="com.vida.xinye.x2.domain.TempletMarketImageInfo">
            <result property="width" column="resUrl_width"/>
            <result property="pic" column="resUrl_pic"/>
            <result property="height" column="resUrl_height"/>
        </association>
        <association property="listUrl" javaType="com.vida.xinye.x2.domain.TempletMarketImageInfo">
            <result property="width" column="listUrl_width"/>
            <result property="pic" column="listUrl_pic"/>
            <result property="height" column="listUrl_height"/>
        </association>
    </resultMap>

    <select id="list" resultMap="listMap">
        SELECT
        id,
        group_id,
        leftUrl_width,
        leftUrl_pic,
        leftUrl_height,
        rightUrl_width,
        rightUrl_pic,
        rightUrl_height,
        topUrl_width,
        topUrl_pic,
        topUrl_height,
        downUrl_width,
        downUrl_pic,
        downUrl_height,
        leftTopUrl_width,
        leftTopUrl_pic,
        leftTopUrl_height,
        leftDownUrl_width,
        leftDownUrl_pic,
        leftDownUrl_height,
        rightTopUrl_width,
        rightTopUrl_pic,
        rightTopUrl_height,
        rightDownUrl_width,
        rightDownUrl_pic,
        rightDownUrl_height,
        resUrl_width,
        resUrl_pic,
        resUrl_height,
        listUrl_width,
        listUrl_pic,
        listUrl_height,
        create_time
        FROM templet_border tm
        <where>
            <if test="groupId != null">
                tm.group_id = #{groupId}
            </if>
        </where>
    </select>

    <insert id="insert" parameterType="com.vida.xinye.x2.dto.TempletBorderDto">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
            SELECT LAST_INSERT_ID()
        </selectKey>
        INSERT INTO templet_border (
        <if test="leftUrl != null">
            leftUrl_width,
            leftUrl_pic,
            leftUrl_height,
        </if>
        <if test="rightUrl != null">
            rightUrl_width,
            rightUrl_pic,
            rightUrl_height,
        </if>
        <if test="topUrl != null">
            topUrl_width,
            topUrl_pic,
            topUrl_height,
        </if>
        <if test="downUrl != null">
            downUrl_width,
            downUrl_pic,
            downUrl_height,
        </if>
        <if test="leftTopUrl != null">
            leftTopUrl_width,
            leftTopUrl_pic,
            leftTopUrl_height,
        </if>
        <if test="leftDownUrl != null">
            leftDownUrl_width,
            leftDownUrl_pic,
            leftDownUrl_height,
        </if>
        <if test="rightTopUrl != null">
            rightTopUrl_width,
            rightTopUrl_pic,
            rightTopUrl_height,
        </if>
        <if test="rightDownUrl != null">
            rightDownUrl_width,
            rightDownUrl_pic,
            rightDownUrl_height,
        </if>
        <if test="resUrl != null">
            resUrl_width,
            resUrl_pic,
            resUrl_height,
        </if>
        <if test="listUrl != null">
            listUrl_width,
            listUrl_pic,
            listUrl_height,
        </if>
        <if test="groupId != null">group_id </if>
        ) VALUES (
        <if test="leftUrl != null">
            #{leftUrl.width},
            #{leftUrl.pic},
            #{leftUrl.height},
        </if>
        <if test="rightUrl != null">
            #{rightUrl.width},
            #{rightUrl.pic},
            #{rightUrl.height},
        </if>
        <if test="topUrl != null">
            #{topUrl.width},
            #{topUrl.pic},
            #{topUrl.height},
        </if>
        <if test="downUrl != null">
            #{downUrl.width},
            #{downUrl.pic},
            #{downUrl.height},
        </if>
        <if test="leftTopUrl != null">
            #{leftTopUrl.width},
            #{leftTopUrl.pic},
            #{leftTopUrl.height},
        </if>
        <if test="leftDownUrl != null">
            #{leftDownUrl.width},
            #{leftDownUrl.pic},
            #{leftDownUrl.height},
        </if>
        <if test="rightTopUrl != null">
            #{rightTopUrl.width},
            #{rightTopUrl.pic},
            #{rightTopUrl.height},
        </if>
        <if test="rightDownUrl != null">
            #{rightDownUrl.width},
            #{rightDownUrl.pic},
            #{rightDownUrl.height},
        </if>
        <if test="resUrl != null">
            #{resUrl.width},
            #{resUrl.pic},
            #{resUrl.height},
        </if>
        <if test="listUrl != null">
            #{listUrl.width},
            #{listUrl.pic},
            #{listUrl.height},
        </if>
        <if test="groupId != null">#{groupId} </if>
        )
    </insert>

    <update id="update" parameterType="com.vida.xinye.x2.dto.TempletBorderDto">
        UPDATE templet_border
        SET
        <if test="leftUrl != null">
            leftUrl_width = #{leftUrl.width},
            leftUrl_pic = #{leftUrl.pic},
            leftUrl_height = #{leftUrl.height},
        </if>
        <if test="rightUrl != null">
            rightUrl_width = #{rightUrl.width},
            rightUrl_pic = #{rightUrl.pic},
            rightUrl_height = #{rightUrl.height},
        </if>
        <if test="topUrl != null">
            topUrl_width = #{topUrl.width},
            topUrl_pic = #{topUrl.pic},
            topUrl_height = #{topUrl.height},
        </if>
        <if test="downUrl != null">
            downUrl_width = #{downUrl.width},
            downUrl_pic = #{downUrl.pic},
            downUrl_height = #{downUrl.height},
        </if>
        <if test="leftTopUrl != null">
            leftTopUrl_width = #{leftTopUrl.width},
            leftTopUrl_pic = #{leftTopUrl.pic},
            leftTopUrl_height = #{leftTopUrl.height},
        </if>
        <if test="leftDownUrl != null">
            leftDownUrl_width = #{leftDownUrl.width},
            leftDownUrl_pic = #{leftDownUrl.pic},
            leftDownUrl_height = #{leftDownUrl.height},
        </if>
        <if test="rightTopUrl != null">
            rightTopUrl_width = #{rightTopUrl.width},
            rightTopUrl_pic = #{rightTopUrl.pic},
            rightTopUrl_height = #{rightTopUrl.height},
        </if>
        <if test="rightDownUrl != null">
            rightDownUrl_width = #{rightDownUrl.width},
            rightDownUrl_pic = #{rightDownUrl.pic},
            rightDownUrl_height = #{rightDownUrl.height},
        </if>
        <if test="resUrl != null">
            resUrl_width = #{resUrl.width},
            resUrl_pic = #{resUrl.pic},
            resUrl_height = #{resUrl.height},
        </if>
        <if test="listUrl != null">
            listUrl_width = #{listUrl.width},
            listUrl_pic = #{listUrl.pic},
            listUrl_height = #{listUrl.height},
        </if>
        <if test="groupId != null">group_id = #{groupId} </if>
        WHERE id = #{id}
    </update>


</mapper>
