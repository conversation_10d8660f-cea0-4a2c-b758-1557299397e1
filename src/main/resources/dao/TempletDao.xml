<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vida.xinye.x2.dao.TempletDao">

    <resultMap id="listMap" type="com.vida.xinye.x2.dto.TempletDto"
               extends="com.vida.xinye.x2.mbg.mapper.TempletMapper.BaseResultMap">
    </resultMap>

    <select id="list" resultMap="listMap">
        SELECT *
        FROM templet t
        where t.user_id = #{userId}
          and t.source_type = #{sourceType}
        order by t.id desc
    </select>

    <!--    获取当前用户的标签打印历史-->
    <select id="getTempletHistory" resultMap="listMap">
        SELECT p.id
             , p.user_id
             , p.templet_name  name
             , p.templet_cover cover
             , p.templet_data  data
             , p.print_time    create_time
             , p.print_time    update_time
        FROM print_history p
        where p.user_id = #{userId}
          and p.source_type = #{sourceType}
        order by p.id desc
    </select>

    <!-- 获取 user_id = 1 的记录数 -->
    <select id="countPrintHistoryByUserId" resultType="int">
        SELECT COUNT(*)
        FROM print_history
        WHERE user_id = 1
    </select>

    <select id="getPrintHistoryCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM print_history
        WHERE user_id = #{userId}
          and source_type = #{sourceType}
    </select>
    <!-- 检查记录数，如果已经有10条数据，删除最旧的记录 -->
    <delete id="deleteOldestPrintHistory">
        DELETE
        FROM print_history
        WHERE id IN (SELECT id
                     FROM (SELECT id
                           FROM print_history
                           WHERE user_id = #{userId}
                           ORDER BY print_time ASC LIMIT 1) AS subquery)
          and source_type = #{sourceType}
    </delete>

</mapper>
