<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vida.xinye.x2.dao.IdCardVerificationDao">

    <resultMap id="BaseResultMap" type="com.vida.xinye.x2.mbg.model.IdCardVerification">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="id_card_hash" jdbcType="VARCHAR" property="idCardHash"/>
        <result column="verification_result" jdbcType="TINYINT" property="verificationResult"/>
        <result column="birthday" jdbcType="VARCHAR" property="birthday"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="sex" jdbcType="VARCHAR" property="sex"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="order_no" jdbcType="VARCHAR" property="orderNo"/>
        <result column="verification_time" jdbcType="TIMESTAMP" property="verificationTime"/>
        <result column="expire_time" jdbcType="TIMESTAMP" property="expireTime"/>
        <result column="source" jdbcType="VARCHAR" property="source"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <!-- 根据姓名和身份证哈希查询有效的认证记录 -->
    <select id="findValidRecord" resultMap="BaseResultMap">
        SELECT *
        FROM id_card_verification
        WHERE name = #{name}
          AND id_card_hash = #{idCardHash}
        ORDER BY verification_time DESC
        LIMIT 1
    </select>

    <!-- 插入认证记录 -->
    <insert id="insertRecord" parameterType="com.vida.xinye.x2.mbg.model.IdCardVerification"
            useGeneratedKeys="true" keyProperty="id">
        INSERT IGNORE INTO id_card_verification (
            name,
            id_card_hash,
            verification_result,
            birthday,
            address,
            sex,
            description,
            order_no,
            verification_time,
            expire_time,
            source,
            create_time,
            update_time
        ) VALUES (
            #{name},
            #{idCardHash},
            #{verificationResult},
            #{birthday},
            #{address},
            #{sex},
            #{description},
            #{orderNo},
            #{verificationTime},
            #{expireTime},
            #{source},
            #{createTime},
            #{updateTime}
        )
        ON DUPLICATE KEY UPDATE
            verification_result = VALUES(verification_result),
            birthday = VALUES(birthday),
            address = VALUES(address),
            sex = VALUES(sex),
            description = VALUES(description),
            order_no = VALUES(order_no),
            verification_time = VALUES(verification_time),
            expire_time = VALUES(expire_time),
            update_time = VALUES(update_time)
    </insert>

    <!-- 清理过期记录 -->
    <delete id="deleteExpiredRecords">
        DELETE FROM id_card_verification
        WHERE expire_time &lt; NOW()
    </delete>

</mapper>