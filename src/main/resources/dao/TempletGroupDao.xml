<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vida.xinye.x2.dao.TempletGroupDao">

    <resultMap id="listMap" type="com.vida.xinye.x2.mbg.model.TempletGroup"
               extends="com.vida.xinye.x2.mbg.mapper.TempletMapper.BaseResultMap">
    </resultMap>
    <resultMap id="sysListMap" type="com.vida.xinye.x2.mbg.model.SysTempletGroup"
               extends="com.vida.xinye.x2.mbg.mapper.SysTempletMapper.BaseResultMap">
    </resultMap>
    <insert id="insert">
        INSERT INTO templet_group_i18n (group_id, locale, name)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.groupId}, #{item.locale}, #{item.name})
            ON DUPLICATE KEY UPDATE name = #{item.name}
        </foreach>
    </insert>

    <!--    标签宝箱分组查询-->
    <select id="selectSystemGroups" resultMap="sysListMap">
        select id, name, sort, create_time
        FROM sys_templet_group tg
        where tg.source_type = 0
        <if test="locale != null">
            AND tg.locale = #{locale}
        </if>
        order by tg.sort asc
    </select>

</mapper>
