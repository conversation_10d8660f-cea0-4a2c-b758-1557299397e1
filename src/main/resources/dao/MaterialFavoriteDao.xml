<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vida.xinye.x2.dao.MaterialFavoriteDao">
    <insert id="insert">
        INSERT
        IGNORE INTO material_favorite (user_id, material_id) VALUES
        (
        #{item.userId,jdbcType=BIGINT},
        #{item.materialId,jdbcType=BIGINT}
        )
    </insert>

    <select id="favorites" resultMap="com.vida.xinye.x2.mbg.mapper.MaterialMapper.BaseResultMap">
        select m.*
        from material m
        inner join material_favorite mf on m.id = mf.material_id
        where mf.user_id = #{userId}
    </select>

</mapper>
