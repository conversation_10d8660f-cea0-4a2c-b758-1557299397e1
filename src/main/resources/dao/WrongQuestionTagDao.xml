<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vida.xinye.x2.dao.WrongQuestionTagDao">

    <resultMap id="listMap" type="com.vida.xinye.x2.mbg.model.WrongQuestionTag">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="tag_type_id" jdbcType="VARCHAR" property="tagTypeId" />
        <result column="tag_name" jdbcType="VARCHAR" property="tagName" />
        <result column="is_default" jdbcType="BOOLEAN" property="isDefault" />
    </resultMap>

    <select id="queryTagsByTypeId" resultMap="listMap">
        SELECT
        wqt.id,
        wqt.tag_type_id,
        wqt.tag_name,
        wqt.is_default
        FROM
        wrong_question_tag wqt
        JOIN
        wrong_question_tag_type wqtt ON wqt.tag_type_id = wqtt.id
        WHERE
        wqtt.id = #{typeId}
        AND (wqt.user_id = #{userId} OR wqt.user_id = 0)
    </select>



</mapper>
