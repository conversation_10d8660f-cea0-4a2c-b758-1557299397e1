<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vida.xinye.x2.dao.MaterialDao">

    <resultMap id="listMap" type="com.vida.xinye.x2.dto.MaterialDto"
               extends="com.vida.xinye.x2.mbg.mapper.MaterialMapper.BaseResultMap">
        <result property="isFavorite" column="isFavorite"/>
    </resultMap>
    <resultMap id="hotMap" type="com.vida.xinye.x2.dto.MaterialDto"
               extends="com.vida.xinye.x2.mbg.mapper.MaterialMapper.BaseResultMap">
        <result property="favoriteCount" column="favoriteCount"/>
    </resultMap>

    <select id="list" resultMap="listMap">
        SELECT DISTINCT
        CASE
        WHEN #{userId} IS NULL THEN NULL
        WHEN mf.id IS NULL THEN 0
        ELSE 1
        END AS isFavorite,
        m.*
        from material m
        left join material_favorite mf on m.id = mf.material_id AND mf.user_id = COALESCE(#{userId}, mf.user_id)
        <if test="categoryId != null">
            where m.category_id = #{categoryId}
        </if>
        order by m.id asc
    </select>

    <!--    查询热门素材，根据收藏表material_favorite收藏的数量倒序-->
    <select id="listHotMaterials" resultMap="hotMap">
        SELECT m.*,
        COUNT(f.material_id) AS favoriteCount,
        MAX(CASE
        WHEN #{userId} IS NULL THEN NULL
        WHEN f.user_id = #{userId} THEN 1
        ELSE 0
        END
        ) AS isFavorite
        FROM material m
        LEFT JOIN material_favorite f ON m.id = f.material_id
        GROUP BY m.id
        ORDER BY favoriteCount DESC
    </select>
</mapper>
