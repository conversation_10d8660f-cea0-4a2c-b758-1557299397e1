<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vida.xinye.x2.dao.SysTempletDao">

    <resultMap id="systemListMap" type="com.vida.xinye.x2.dto.SystemTempletDto"
               extends="com.vida.xinye.x2.mbg.mapper.TempletMapper.BaseResultMap">
    </resultMap>

    <!--    标签宝箱分组查询-->
    <select id="selectSystemGroups" resultType="com.vida.xinye.x2.mbg.model.TempletGroup">
        select id, name, sort, create_time
        from sys_templet_group
        order by sort asc
    </select>
    <!--    标签宝箱查询-->
    <select id="selectSystemTemples" resultMap="systemListMap">
        select * from sys_templet
        <if test="groupId != null">
            where group_id = #{groupId}
        </if>
        order by create_time desc
    </select>


</mapper>
