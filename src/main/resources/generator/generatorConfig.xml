<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>
    <properties resource="generator/generator.properties"/>
    <context id="MySqlContext" targetRuntime="MyBatis3" defaultModelType="flat">
        <property name="beginningDelimiter" value="`"/>
        <property name="endingDelimiter" value="`"/>
        <property name="javaFileEncoding" value="UTF-8"/>
        <!-- 为模型生成序列化方法-->
        <plugin type="org.mybatis.generator.plugins.SerializablePlugin"/>
        <!-- 为生成的Java模型创建一个toString方法 -->
        <plugin type="org.mybatis.generator.plugins.ToStringPlugin"/>
        <!--生成mapper.xml时覆盖原文件-->
        <plugin type="org.mybatis.generator.plugins.UnmergeableXmlMappersPlugin" />
        <commentGenerator type="com.vida.xinye.x2.mbg.CommentGenerator">
            <!-- 是否去除自动生成的注释 true：是 ： false:否 -->
            <property name="suppressAllComments" value="true"/>
            <property name="suppressDate" value="true"/>
            <property name="addRemarkComments" value="true"/>
        </commentGenerator>

        <jdbcConnection driverClass="${jdbc.driverClass}"
                        connectionURL="${jdbc.connectionURL}"
                        userId="${jdbc.userId}"
                        password="${jdbc.password}">
            <!--解决mysql驱动升级到8.0后不生成指定数据库代码的问题-->
            <property name="nullCatalogMeansCurrent" value="true" />
        </jdbcConnection>

        <javaModelGenerator targetPackage="com.vida.xinye.x2.mbg.model" targetProject="src\main\java"/>

        <sqlMapGenerator targetPackage="com.vida.xinye.x2.mbg.mapper" targetProject="src\main\resources"/>

        <javaClientGenerator type="XMLMAPPER" targetPackage="com.vida.xinye.x2.mbg.mapper"
                             targetProject="src\main\java"/>

<!--        &lt;!&ndash;content为text类型，会自动转成Blob，需要覆盖处理&ndash;&gt;-->
<!--        <table domainObjectName="UmsBanner" tableName="ums_banner">-->
<!--            <columnOverride column="content" javaType="java.lang.String" jdbcType="VARCHAR" />-->
<!--        </table>-->
<!--        <table domainObjectName="UmsArticle" tableName="ums_article">-->
<!--            <columnOverride column="content" javaType="java.lang.String" jdbcType="VARCHAR" />-->
<!--        </table>-->
        <!--生成全部表tableName设为%-->
        <table tableName="%">
            <generatedKey column="id" sqlStatement="MySql" identity="true"/>
        </table>
    </context>
</generatorConfiguration>
