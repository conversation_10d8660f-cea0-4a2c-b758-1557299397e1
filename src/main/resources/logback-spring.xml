<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE configuration>
<configuration>
    <!--引用默认日志配置-->
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <!--使用默认的控制台日志输出实现-->
    <include resource="org/springframework/boot/logging/logback/console-appender.xml"/>
    <!--应用名称-->
    <springProperty scope="context" name="APP_NAME" source="spring.application.name" defaultValue="springBoot"/>
    <!--日志文件保存路径-->
    <property name="LOG_FILE_PATH" value="${LOG_FILE:-${LOG_PATH:-${LOG_TEMP:-${java.io.tmpdir:-/tmp}}}/logs}"/>

    <!--读取spring配置文件参数-->
    <springProperty scope="context" name="LOG_HOME" source="log.path"/>
    <springProperty name="ALIYUN_LOG_ENDPOINT" scope="context" source="aliyun.log.endpoint" defaultValue="localhost"/>
    <springProperty name="ALIYUN_LOG_ACCESS_KEY_ID" scope="context" source="aliyun.log.accessKeyId" defaultValue="localhost"/>
    <springProperty name="ALIYUN_LOG_ACCESS_KEY_SECRET" scope="context" source="aliyun.log.accessKeySecret" defaultValue="localhost"/>
    <springProperty name="ALIYUN_LOG_SYSLOG_PROJECT" scope="context" source="aliyun.log.syslog.project" defaultValue="localhost"/>
    <springProperty name="ALIYUN_LOG_SYSLOG_LOGSTORE" scope="context" source="aliyun.log.syslog.logstore" defaultValue="localhost"/>
    <springProperty name="SPRING_PROFILES_ACTIVE" scope="context" source="spring.profiles.active" defaultValue="dev"/>

    <!-- 自定义控制台输出模板 -->
    <property name="CONSOLE_LOG_PATTERN" value="%clr(%d{yyyy-MM-dd HH:mm:ss.SSS,GMT+8}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} [%clr(%X{X-Request-ID})]  %clr(%-40.40logger{39}){cyan} - %msg%n"/>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <!--DEBUG日志输出到文件-->
    <appender name="FILE_DEBUG"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!--输出DEBUG以上级别日志-->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>DEBUG</level>
        </filter>
        <encoder>
<!--            &lt;!&ndash;设置为默认的文件日志格式&ndash;&gt;-->
<!--            <pattern>${FILE_LOG_PATTERN}</pattern>-->
            <pattern>[%d{yyyy-MM-dd HH:mm:ss.SSS,GMT+8}] [%level] [%t] [%c] - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!--设置文件命名格式-->
            <fileNamePattern>${LOG_FILE_PATH}/debug/${APP_NAME}-%d{yyyy-MM-dd}-%i.log</fileNamePattern>
            <!--设置日志文件大小，超过就重新生成文件，默认10M-->
            <maxFileSize>${LOG_FILE_MAX_SIZE:-10MB}</maxFileSize>
            <!--日志文件保留天数，默认30天-->
            <maxHistory>${LOG_FILE_MAX_HISTORY:-30}</maxHistory>
        </rollingPolicy>
    </appender>

    <!--ERROR日志输出到文件-->
    <appender name="FILE_ERROR"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!--只输出ERROR级别的日志-->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <encoder>
            <!--设置为默认的文件日志格式-->
<!--            <pattern>${FILE_LOG_PATTERN}</pattern>-->
            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符-->
            <pattern>[%d{yyyy-MM-dd HH:mm:ss.SSS,GMT+8}] [%level] [%t] [%c] - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!--设置文件命名格式-->
            <fileNamePattern>${LOG_FILE_PATH}/error/${APP_NAME}-%d{yyyy-MM-dd}-%i.log</fileNamePattern>
            <!--设置日志文件大小，超过就重新生成文件，默认10M-->
            <maxFileSize>${LOG_FILE_MAX_SIZE:-10MB}</maxFileSize>
            <!--日志文件保留天数，默认30天-->
            <maxHistory>${LOG_FILE_MAX_HISTORY:-30}</maxHistory>
        </rollingPolicy>
    </appender>

    <!--为了防止进程退出时，内存中的数据丢失，请加上此选项-->
    <shutdownHook class="ch.qos.logback.core.hook.DelayingShutdownHook"/>
    <appender name="ALIYUN_SYSLOG" class="com.aliyun.openservices.log.logback.LoghubAppender">
        <!--必选项-->
        <!-- 账号及网络配置 -->
        <endpoint>${ALIYUN_LOG_ENDPOINT}</endpoint>
        <accessKeyId>${ALIYUN_LOG_ACCESS_KEY_ID}</accessKeyId>
        <accessKeySecret>${ALIYUN_LOG_ACCESS_KEY_SECRET}</accessKeySecret>
        <!-- sls 项目配置 -->
        <project>${ALIYUN_LOG_SYSLOG_PROJECT}</project>
        <logStore>${ALIYUN_LOG_SYSLOG_LOGSTORE}</logStore>
        <!--必选项 (end)-->

        <!-- 可选项 -->
        <topic>x2</topic>
        <source>${SPRING_PROFILES_ACTIVE}</source>

        <!-- 可选项 设置 time 字段呈现的格式 -->
        <timeFormat>yyyy-MM-dd HH:mm:ss</timeFormat>
        <!-- 可选项 设置 time 字段呈现的时区 -->
        <timeZone>Asia/Shanghai</timeZone>
    </appender>

    <!--控制框架输出日志-->
    <logger name="org.slf4j" level="INFO"/>
    <logger name="org.springframework" level="INFO"/>
    <logger name="org.hibernate.validator" level="INFO"/>
    <logger name="com.alibaba.nacos.client.naming" level="INFO"/>

    <root level="debug">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="FILE_DEBUG"/>
        <appender-ref ref="FILE_ERROR"/>
    </root>

    <logger name="com.vida.xinye" level="DEBUG">
        <appender-ref ref="ALIYUN_SYSLOG"/>
    </logger>
</configuration>
