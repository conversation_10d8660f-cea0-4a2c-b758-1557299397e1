<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vida.xinye.x2.mbg.mapper.TempletMapper">
  <resultMap id="BaseResultMap" type="com.vida.xinye.x2.mbg.model.Templet">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="group_id" jdbcType="BIGINT" property="groupId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="cover" jdbcType="VARCHAR" property="cover" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="preview_image" jdbcType="VARCHAR" property="previewImage" />
    <result column="source_type" jdbcType="TINYINT" property="sourceType" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.vida.xinye.x2.mbg.model.Templet">
    <result column="data" jdbcType="LONGVARCHAR" property="data" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, user_id, group_id, name, cover, create_time, update_time, preview_image, source_type
  </sql>
  <sql id="Blob_Column_List">
    data
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.vida.xinye.x2.mbg.model.TempletExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from templet
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.vida.xinye.x2.mbg.model.TempletExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from templet
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from templet
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from templet
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.vida.xinye.x2.mbg.model.TempletExample">
    delete from templet
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.vida.xinye.x2.mbg.model.Templet">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into templet (user_id, group_id, name,
    cover, create_time, update_time,
    preview_image, source_type, data)
    values (#{userId,jdbcType=BIGINT}, #{groupId,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR},
    #{cover,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
    #{previewImage,jdbcType=VARCHAR}, #{sourceType,jdbcType=TINYINT}, #{data,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.vida.xinye.x2.mbg.model.Templet">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into templet
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        user_id,
      </if>
      <if test="groupId != null">
        group_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="cover != null">
        cover,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="previewImage != null">
        preview_image,
      </if>
      <if test="sourceType != null">
        source_type,
      </if>
      <if test="data != null">
        data,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="groupId != null">
        #{groupId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="cover != null">
        #{cover,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="previewImage != null">
        #{previewImage,jdbcType=VARCHAR},
      </if>
      <if test="sourceType != null">
        #{sourceType,jdbcType=TINYINT},
      </if>
      <if test="data != null">
        #{data,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vida.xinye.x2.mbg.model.TempletExample" resultType="java.lang.Long">
    select count(*) from templet
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update templet
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.userId != null">
        user_id = #{row.userId,jdbcType=BIGINT},
      </if>
      <if test="row.groupId != null">
        group_id = #{row.groupId,jdbcType=BIGINT},
      </if>
      <if test="row.name != null">
        name = #{row.name,jdbcType=VARCHAR},
      </if>
      <if test="row.cover != null">
        cover = #{row.cover,jdbcType=VARCHAR},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.previewImage != null">
        preview_image = #{row.previewImage,jdbcType=VARCHAR},
      </if>
      <if test="row.sourceType != null">
        source_type = #{row.sourceType,jdbcType=TINYINT},
      </if>
      <if test="row.data != null">
        data = #{row.data,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause"/>
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update templet
    set id = #{row.id,jdbcType=BIGINT},
    user_id = #{row.userId,jdbcType=BIGINT},
    group_id = #{row.groupId,jdbcType=BIGINT},
    name = #{row.name,jdbcType=VARCHAR},
    cover = #{row.cover,jdbcType=VARCHAR},
    create_time = #{row.createTime,jdbcType=TIMESTAMP},
    update_time = #{row.updateTime,jdbcType=TIMESTAMP},
    preview_image = #{row.previewImage,jdbcType=VARCHAR},
    source_type = #{row.sourceType,jdbcType=TINYINT},
    data = #{row.data,jdbcType=LONGVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause"/>
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update templet
    set id = #{row.id,jdbcType=BIGINT},
    user_id = #{row.userId,jdbcType=BIGINT},
    group_id = #{row.groupId,jdbcType=BIGINT},
    name = #{row.name,jdbcType=VARCHAR},
    cover = #{row.cover,jdbcType=VARCHAR},
    create_time = #{row.createTime,jdbcType=TIMESTAMP},
    update_time = #{row.updateTime,jdbcType=TIMESTAMP},
    preview_image = #{row.previewImage,jdbcType=VARCHAR},
    source_type = #{row.sourceType,jdbcType=TINYINT}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause"/>
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vida.xinye.x2.mbg.model.Templet">
    update templet
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="groupId != null">
        group_id = #{groupId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="cover != null">
        cover = #{cover,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="previewImage != null">
        preview_image = #{previewImage,jdbcType=VARCHAR},
      </if>
      <if test="sourceType != null">
        source_type = #{sourceType,jdbcType=TINYINT},
      </if>
      <if test="data != null">
        data = #{data,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.vida.xinye.x2.mbg.model.Templet">
    update templet
    set user_id       = #{userId,jdbcType=BIGINT},
        group_id      = #{groupId,jdbcType=BIGINT},
        name          = #{name,jdbcType=VARCHAR},
        cover         = #{cover,jdbcType=VARCHAR},
        create_time   = #{createTime,jdbcType=TIMESTAMP},
        update_time   = #{updateTime,jdbcType=TIMESTAMP},
        preview_image = #{previewImage,jdbcType=VARCHAR},
        source_type   = #{sourceType,jdbcType=TINYINT},
        data          = #{data,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vida.xinye.x2.mbg.model.Templet">
    update templet
    set user_id       = #{userId,jdbcType=BIGINT},
        group_id      = #{groupId,jdbcType=BIGINT},
        name          = #{name,jdbcType=VARCHAR},
        cover         = #{cover,jdbcType=VARCHAR},
        create_time   = #{createTime,jdbcType=TIMESTAMP},
        update_time   = #{updateTime,jdbcType=TIMESTAMP},
        preview_image = #{previewImage,jdbcType=VARCHAR},
        source_type   = #{sourceType,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>