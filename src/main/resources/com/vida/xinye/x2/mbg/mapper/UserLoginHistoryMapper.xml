<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vida.xinye.x2.mbg.mapper.UserLoginHistoryMapper">
  <resultMap id="BaseResultMap" type="com.vida.xinye.x2.mbg.model.UserLoginHistory">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="login_way" jdbcType="VARCHAR" property="loginWay" />
    <result column="login_ip" jdbcType="VARCHAR" property="loginIp" />
    <result column="login_device" jdbcType="VARCHAR" property="loginDevice" />
    <result column="login_location" jdbcType="VARCHAR" property="loginLocation" />
    <result column="user_agent" jdbcType="LONGVARCHAR" property="userAgent" />
    <result column="login_status" jdbcType="VARCHAR" property="loginStatus" />
    <result column="login_time" jdbcType="TIMESTAMP" property="loginTime" />
  </resultMap>
  
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  
  <sql id="Base_Column_List">
    id, user_id, login_way, login_ip, login_device, login_location, user_agent, login_status, login_time
  </sql>
  
  <select id="selectByExample" parameterType="com.vida.xinye.x2.mbg.model.UserLoginHistoryExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from user_login_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from user_login_history
    where id = #{id,jdbcType=BIGINT}
  </select>
  
  <select id="selectByUserId" parameterType="map" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from user_login_history
    where user_id = #{userId,jdbcType=BIGINT}
    order by login_time desc
    <if test="limit != null">
      limit #{limit}
    </if>
  </select>
  
  <select id="selectLatestByUserId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from user_login_history
    where user_id = #{userId,jdbcType=BIGINT}
    order by login_time desc
    limit 1
  </select>
  
  <select id="countByUserIdAndLoginWay" parameterType="map" resultType="java.lang.Long">
    select count(*)
    from user_login_history
    where user_id = #{userId,jdbcType=BIGINT}
    and login_way = #{loginWay,jdbcType=VARCHAR}
  </select>
  
  <select id="selectByTimeRange" parameterType="map" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from user_login_history
    where user_id = #{userId,jdbcType=BIGINT}
    <if test="startTime != null">
      and login_time &gt;= #{startTime,jdbcType=TIMESTAMP}
    </if>
    <if test="endTime != null">
      and login_time &lt;= #{endTime,jdbcType=TIMESTAMP}
    </if>
    order by login_time desc
  </select>
  
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from user_login_history
    where id = #{id,jdbcType=BIGINT}
  </delete>
  
  <delete id="deleteByExample" parameterType="com.vida.xinye.x2.mbg.model.UserLoginHistoryExample">
    delete from user_login_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  
  <insert id="insert" parameterType="com.vida.xinye.x2.mbg.model.UserLoginHistory">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into user_login_history (user_id, login_way, login_ip, 
      login_device, login_location, user_agent, 
      login_status, login_time)
    values (#{userId,jdbcType=BIGINT}, #{loginWay,jdbcType=VARCHAR}, #{loginIp,jdbcType=VARCHAR}, 
      #{loginDevice,jdbcType=VARCHAR}, #{loginLocation,jdbcType=VARCHAR}, #{userAgent,jdbcType=LONGVARCHAR}, 
      #{loginStatus,jdbcType=VARCHAR}, #{loginTime,jdbcType=TIMESTAMP})
  </insert>
  
  <insert id="insertSelective" parameterType="com.vida.xinye.x2.mbg.model.UserLoginHistory">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into user_login_history
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        user_id,
      </if>
      <if test="loginWay != null">
        login_way,
      </if>
      <if test="loginIp != null">
        login_ip,
      </if>
      <if test="loginDevice != null">
        login_device,
      </if>
      <if test="loginLocation != null">
        login_location,
      </if>
      <if test="userAgent != null">
        user_agent,
      </if>
      <if test="loginStatus != null">
        login_status,
      </if>
      <if test="loginTime != null">
        login_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="loginWay != null">
        #{loginWay,jdbcType=VARCHAR},
      </if>
      <if test="loginIp != null">
        #{loginIp,jdbcType=VARCHAR},
      </if>
      <if test="loginDevice != null">
        #{loginDevice,jdbcType=VARCHAR},
      </if>
      <if test="loginLocation != null">
        #{loginLocation,jdbcType=VARCHAR},
      </if>
      <if test="userAgent != null">
        #{userAgent,jdbcType=LONGVARCHAR},
      </if>
      <if test="loginStatus != null">
        #{loginStatus,jdbcType=VARCHAR},
      </if>
      <if test="loginTime != null">
        #{loginTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  
  <select id="countByExample" parameterType="com.vida.xinye.x2.mbg.model.UserLoginHistoryExample" resultType="java.lang.Long">
    select count(*) from user_login_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  
  <update id="updateByExampleSelective" parameterType="map">
    update user_login_history
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=BIGINT},
      </if>
      <if test="record.loginWay != null">
        login_way = #{record.loginWay,jdbcType=VARCHAR},
      </if>
      <if test="record.loginIp != null">
        login_ip = #{record.loginIp,jdbcType=VARCHAR},
      </if>
      <if test="record.loginDevice != null">
        login_device = #{record.loginDevice,jdbcType=VARCHAR},
      </if>
      <if test="record.loginLocation != null">
        login_location = #{record.loginLocation,jdbcType=VARCHAR},
      </if>
      <if test="record.userAgent != null">
        user_agent = #{record.userAgent,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.loginStatus != null">
        login_status = #{record.loginStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.loginTime != null">
        login_time = #{record.loginTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </update>
  
  <update id="updateByPrimaryKeySelective" parameterType="com.vida.xinye.x2.mbg.model.UserLoginHistory">
    update user_login_history
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="loginWay != null">
        login_way = #{loginWay,jdbcType=VARCHAR},
      </if>
      <if test="loginIp != null">
        login_ip = #{loginIp,jdbcType=VARCHAR},
      </if>
      <if test="loginDevice != null">
        login_device = #{loginDevice,jdbcType=VARCHAR},
      </if>
      <if test="loginLocation != null">
        login_location = #{loginLocation,jdbcType=VARCHAR},
      </if>
      <if test="userAgent != null">
        user_agent = #{userAgent,jdbcType=LONGVARCHAR},
      </if>
      <if test="loginStatus != null">
        login_status = #{loginStatus,jdbcType=VARCHAR},
      </if>
      <if test="loginTime != null">
        login_time = #{loginTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  
  <update id="updateByPrimaryKey" parameterType="com.vida.xinye.x2.mbg.model.UserLoginHistory">
    update user_login_history
    set user_id = #{userId,jdbcType=BIGINT},
      login_way = #{loginWay,jdbcType=VARCHAR},
      login_ip = #{loginIp,jdbcType=VARCHAR},
      login_device = #{loginDevice,jdbcType=VARCHAR},
      login_location = #{loginLocation,jdbcType=VARCHAR},
      user_agent = #{userAgent,jdbcType=LONGVARCHAR},
      login_status = #{loginStatus,jdbcType=VARCHAR},
      login_time = #{loginTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
