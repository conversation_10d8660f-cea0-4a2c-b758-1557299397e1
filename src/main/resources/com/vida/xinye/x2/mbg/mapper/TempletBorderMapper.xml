<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vida.xinye.x2.mbg.mapper.TempletBorderMapper">
  <resultMap id="BaseResultMap" type="com.vida.xinye.x2.mbg.model.TempletBorder">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="group_id" jdbcType="BIGINT" property="groupId" />
    <result column="leftUrl_width" jdbcType="INTEGER" property="lefturlWidth" />
    <result column="leftUrl_pic" jdbcType="VARCHAR" property="lefturlPic" />
    <result column="leftUrl_height" jdbcType="INTEGER" property="lefturlHeight" />
    <result column="rightUrl_width" jdbcType="INTEGER" property="righturlWidth" />
    <result column="rightUrl_pic" jdbcType="VARCHAR" property="righturlPic" />
    <result column="rightUrl_height" jdbcType="INTEGER" property="righturlHeight" />
    <result column="topUrl_width" jdbcType="INTEGER" property="topurlWidth" />
    <result column="topUrl_pic" jdbcType="VARCHAR" property="topurlPic" />
    <result column="topUrl_height" jdbcType="INTEGER" property="topurlHeight" />
    <result column="downUrl_width" jdbcType="INTEGER" property="downurlWidth" />
    <result column="downUrl_pic" jdbcType="VARCHAR" property="downurlPic" />
    <result column="downUrl_height" jdbcType="INTEGER" property="downurlHeight" />
    <result column="leftTopUrl_width" jdbcType="INTEGER" property="lefttopurlWidth" />
    <result column="leftTopUrl_pic" jdbcType="VARCHAR" property="lefttopurlPic" />
    <result column="leftTopUrl_height" jdbcType="INTEGER" property="lefttopurlHeight" />
    <result column="leftDownUrl_width" jdbcType="INTEGER" property="leftdownurlWidth" />
    <result column="leftDownUrl_pic" jdbcType="VARCHAR" property="leftdownurlPic" />
    <result column="leftDownUrl_height" jdbcType="INTEGER" property="leftdownurlHeight" />
    <result column="rightTopUrl_width" jdbcType="INTEGER" property="righttopurlWidth" />
    <result column="rightTopUrl_pic" jdbcType="VARCHAR" property="righttopurlPic" />
    <result column="rightTopUrl_height" jdbcType="INTEGER" property="righttopurlHeight" />
    <result column="rightDownUrl_width" jdbcType="INTEGER" property="rightdownurlWidth" />
    <result column="rightDownUrl_pic" jdbcType="VARCHAR" property="rightdownurlPic" />
    <result column="rightDownUrl_height" jdbcType="INTEGER" property="rightdownurlHeight" />
    <result column="resUrl_width" jdbcType="INTEGER" property="resurlWidth" />
    <result column="resUrl_pic" jdbcType="VARCHAR" property="resurlPic" />
    <result column="resUrl_height" jdbcType="INTEGER" property="resurlHeight" />
    <result column="listUrl_width" jdbcType="INTEGER" property="listurlWidth" />
    <result column="listUrl_pic" jdbcType="VARCHAR" property="listurlPic" />
    <result column="listUrl_height" jdbcType="INTEGER" property="listurlHeight" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, group_id, leftUrl_width, leftUrl_pic, leftUrl_height, rightUrl_width, rightUrl_pic, 
    rightUrl_height, topUrl_width, topUrl_pic, topUrl_height, downUrl_width, downUrl_pic, 
    downUrl_height, leftTopUrl_width, leftTopUrl_pic, leftTopUrl_height, leftDownUrl_width, 
    leftDownUrl_pic, leftDownUrl_height, rightTopUrl_width, rightTopUrl_pic, rightTopUrl_height, 
    rightDownUrl_width, rightDownUrl_pic, rightDownUrl_height, resUrl_width, resUrl_pic, 
    resUrl_height, listUrl_width, listUrl_pic, listUrl_height, create_time
  </sql>
  <select id="selectByExample" parameterType="com.vida.xinye.x2.mbg.model.TempletBorderExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from templet_border
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from templet_border
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from templet_border
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.vida.xinye.x2.mbg.model.TempletBorderExample">
    delete from templet_border
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.vida.xinye.x2.mbg.model.TempletBorder">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into templet_border (group_id, leftUrl_width, leftUrl_pic, 
      leftUrl_height, rightUrl_width, rightUrl_pic, 
      rightUrl_height, topUrl_width, topUrl_pic, 
      topUrl_height, downUrl_width, downUrl_pic, 
      downUrl_height, leftTopUrl_width, leftTopUrl_pic, 
      leftTopUrl_height, leftDownUrl_width, leftDownUrl_pic, 
      leftDownUrl_height, rightTopUrl_width, rightTopUrl_pic, 
      rightTopUrl_height, rightDownUrl_width, rightDownUrl_pic, 
      rightDownUrl_height, resUrl_width, resUrl_pic, 
      resUrl_height, listUrl_width, listUrl_pic, 
      listUrl_height, create_time)
    values (#{groupId,jdbcType=BIGINT}, #{lefturlWidth,jdbcType=INTEGER}, #{lefturlPic,jdbcType=VARCHAR}, 
      #{lefturlHeight,jdbcType=INTEGER}, #{righturlWidth,jdbcType=INTEGER}, #{righturlPic,jdbcType=VARCHAR}, 
      #{righturlHeight,jdbcType=INTEGER}, #{topurlWidth,jdbcType=INTEGER}, #{topurlPic,jdbcType=VARCHAR}, 
      #{topurlHeight,jdbcType=INTEGER}, #{downurlWidth,jdbcType=INTEGER}, #{downurlPic,jdbcType=VARCHAR}, 
      #{downurlHeight,jdbcType=INTEGER}, #{lefttopurlWidth,jdbcType=INTEGER}, #{lefttopurlPic,jdbcType=VARCHAR}, 
      #{lefttopurlHeight,jdbcType=INTEGER}, #{leftdownurlWidth,jdbcType=INTEGER}, #{leftdownurlPic,jdbcType=VARCHAR}, 
      #{leftdownurlHeight,jdbcType=INTEGER}, #{righttopurlWidth,jdbcType=INTEGER}, #{righttopurlPic,jdbcType=VARCHAR}, 
      #{righttopurlHeight,jdbcType=INTEGER}, #{rightdownurlWidth,jdbcType=INTEGER}, #{rightdownurlPic,jdbcType=VARCHAR}, 
      #{rightdownurlHeight,jdbcType=INTEGER}, #{resurlWidth,jdbcType=INTEGER}, #{resurlPic,jdbcType=VARCHAR}, 
      #{resurlHeight,jdbcType=INTEGER}, #{listurlWidth,jdbcType=INTEGER}, #{listurlPic,jdbcType=VARCHAR}, 
      #{listurlHeight,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.vida.xinye.x2.mbg.model.TempletBorder">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into templet_border
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="groupId != null">
        group_id,
      </if>
      <if test="lefturlWidth != null">
        leftUrl_width,
      </if>
      <if test="lefturlPic != null">
        leftUrl_pic,
      </if>
      <if test="lefturlHeight != null">
        leftUrl_height,
      </if>
      <if test="righturlWidth != null">
        rightUrl_width,
      </if>
      <if test="righturlPic != null">
        rightUrl_pic,
      </if>
      <if test="righturlHeight != null">
        rightUrl_height,
      </if>
      <if test="topurlWidth != null">
        topUrl_width,
      </if>
      <if test="topurlPic != null">
        topUrl_pic,
      </if>
      <if test="topurlHeight != null">
        topUrl_height,
      </if>
      <if test="downurlWidth != null">
        downUrl_width,
      </if>
      <if test="downurlPic != null">
        downUrl_pic,
      </if>
      <if test="downurlHeight != null">
        downUrl_height,
      </if>
      <if test="lefttopurlWidth != null">
        leftTopUrl_width,
      </if>
      <if test="lefttopurlPic != null">
        leftTopUrl_pic,
      </if>
      <if test="lefttopurlHeight != null">
        leftTopUrl_height,
      </if>
      <if test="leftdownurlWidth != null">
        leftDownUrl_width,
      </if>
      <if test="leftdownurlPic != null">
        leftDownUrl_pic,
      </if>
      <if test="leftdownurlHeight != null">
        leftDownUrl_height,
      </if>
      <if test="righttopurlWidth != null">
        rightTopUrl_width,
      </if>
      <if test="righttopurlPic != null">
        rightTopUrl_pic,
      </if>
      <if test="righttopurlHeight != null">
        rightTopUrl_height,
      </if>
      <if test="rightdownurlWidth != null">
        rightDownUrl_width,
      </if>
      <if test="rightdownurlPic != null">
        rightDownUrl_pic,
      </if>
      <if test="rightdownurlHeight != null">
        rightDownUrl_height,
      </if>
      <if test="resurlWidth != null">
        resUrl_width,
      </if>
      <if test="resurlPic != null">
        resUrl_pic,
      </if>
      <if test="resurlHeight != null">
        resUrl_height,
      </if>
      <if test="listurlWidth != null">
        listUrl_width,
      </if>
      <if test="listurlPic != null">
        listUrl_pic,
      </if>
      <if test="listurlHeight != null">
        listUrl_height,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="groupId != null">
        #{groupId,jdbcType=BIGINT},
      </if>
      <if test="lefturlWidth != null">
        #{lefturlWidth,jdbcType=INTEGER},
      </if>
      <if test="lefturlPic != null">
        #{lefturlPic,jdbcType=VARCHAR},
      </if>
      <if test="lefturlHeight != null">
        #{lefturlHeight,jdbcType=INTEGER},
      </if>
      <if test="righturlWidth != null">
        #{righturlWidth,jdbcType=INTEGER},
      </if>
      <if test="righturlPic != null">
        #{righturlPic,jdbcType=VARCHAR},
      </if>
      <if test="righturlHeight != null">
        #{righturlHeight,jdbcType=INTEGER},
      </if>
      <if test="topurlWidth != null">
        #{topurlWidth,jdbcType=INTEGER},
      </if>
      <if test="topurlPic != null">
        #{topurlPic,jdbcType=VARCHAR},
      </if>
      <if test="topurlHeight != null">
        #{topurlHeight,jdbcType=INTEGER},
      </if>
      <if test="downurlWidth != null">
        #{downurlWidth,jdbcType=INTEGER},
      </if>
      <if test="downurlPic != null">
        #{downurlPic,jdbcType=VARCHAR},
      </if>
      <if test="downurlHeight != null">
        #{downurlHeight,jdbcType=INTEGER},
      </if>
      <if test="lefttopurlWidth != null">
        #{lefttopurlWidth,jdbcType=INTEGER},
      </if>
      <if test="lefttopurlPic != null">
        #{lefttopurlPic,jdbcType=VARCHAR},
      </if>
      <if test="lefttopurlHeight != null">
        #{lefttopurlHeight,jdbcType=INTEGER},
      </if>
      <if test="leftdownurlWidth != null">
        #{leftdownurlWidth,jdbcType=INTEGER},
      </if>
      <if test="leftdownurlPic != null">
        #{leftdownurlPic,jdbcType=VARCHAR},
      </if>
      <if test="leftdownurlHeight != null">
        #{leftdownurlHeight,jdbcType=INTEGER},
      </if>
      <if test="righttopurlWidth != null">
        #{righttopurlWidth,jdbcType=INTEGER},
      </if>
      <if test="righttopurlPic != null">
        #{righttopurlPic,jdbcType=VARCHAR},
      </if>
      <if test="righttopurlHeight != null">
        #{righttopurlHeight,jdbcType=INTEGER},
      </if>
      <if test="rightdownurlWidth != null">
        #{rightdownurlWidth,jdbcType=INTEGER},
      </if>
      <if test="rightdownurlPic != null">
        #{rightdownurlPic,jdbcType=VARCHAR},
      </if>
      <if test="rightdownurlHeight != null">
        #{rightdownurlHeight,jdbcType=INTEGER},
      </if>
      <if test="resurlWidth != null">
        #{resurlWidth,jdbcType=INTEGER},
      </if>
      <if test="resurlPic != null">
        #{resurlPic,jdbcType=VARCHAR},
      </if>
      <if test="resurlHeight != null">
        #{resurlHeight,jdbcType=INTEGER},
      </if>
      <if test="listurlWidth != null">
        #{listurlWidth,jdbcType=INTEGER},
      </if>
      <if test="listurlPic != null">
        #{listurlPic,jdbcType=VARCHAR},
      </if>
      <if test="listurlHeight != null">
        #{listurlHeight,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vida.xinye.x2.mbg.model.TempletBorderExample" resultType="java.lang.Long">
    select count(*) from templet_border
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update templet_border
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.groupId != null">
        group_id = #{row.groupId,jdbcType=BIGINT},
      </if>
      <if test="row.lefturlWidth != null">
        leftUrl_width = #{row.lefturlWidth,jdbcType=INTEGER},
      </if>
      <if test="row.lefturlPic != null">
        leftUrl_pic = #{row.lefturlPic,jdbcType=VARCHAR},
      </if>
      <if test="row.lefturlHeight != null">
        leftUrl_height = #{row.lefturlHeight,jdbcType=INTEGER},
      </if>
      <if test="row.righturlWidth != null">
        rightUrl_width = #{row.righturlWidth,jdbcType=INTEGER},
      </if>
      <if test="row.righturlPic != null">
        rightUrl_pic = #{row.righturlPic,jdbcType=VARCHAR},
      </if>
      <if test="row.righturlHeight != null">
        rightUrl_height = #{row.righturlHeight,jdbcType=INTEGER},
      </if>
      <if test="row.topurlWidth != null">
        topUrl_width = #{row.topurlWidth,jdbcType=INTEGER},
      </if>
      <if test="row.topurlPic != null">
        topUrl_pic = #{row.topurlPic,jdbcType=VARCHAR},
      </if>
      <if test="row.topurlHeight != null">
        topUrl_height = #{row.topurlHeight,jdbcType=INTEGER},
      </if>
      <if test="row.downurlWidth != null">
        downUrl_width = #{row.downurlWidth,jdbcType=INTEGER},
      </if>
      <if test="row.downurlPic != null">
        downUrl_pic = #{row.downurlPic,jdbcType=VARCHAR},
      </if>
      <if test="row.downurlHeight != null">
        downUrl_height = #{row.downurlHeight,jdbcType=INTEGER},
      </if>
      <if test="row.lefttopurlWidth != null">
        leftTopUrl_width = #{row.lefttopurlWidth,jdbcType=INTEGER},
      </if>
      <if test="row.lefttopurlPic != null">
        leftTopUrl_pic = #{row.lefttopurlPic,jdbcType=VARCHAR},
      </if>
      <if test="row.lefttopurlHeight != null">
        leftTopUrl_height = #{row.lefttopurlHeight,jdbcType=INTEGER},
      </if>
      <if test="row.leftdownurlWidth != null">
        leftDownUrl_width = #{row.leftdownurlWidth,jdbcType=INTEGER},
      </if>
      <if test="row.leftdownurlPic != null">
        leftDownUrl_pic = #{row.leftdownurlPic,jdbcType=VARCHAR},
      </if>
      <if test="row.leftdownurlHeight != null">
        leftDownUrl_height = #{row.leftdownurlHeight,jdbcType=INTEGER},
      </if>
      <if test="row.righttopurlWidth != null">
        rightTopUrl_width = #{row.righttopurlWidth,jdbcType=INTEGER},
      </if>
      <if test="row.righttopurlPic != null">
        rightTopUrl_pic = #{row.righttopurlPic,jdbcType=VARCHAR},
      </if>
      <if test="row.righttopurlHeight != null">
        rightTopUrl_height = #{row.righttopurlHeight,jdbcType=INTEGER},
      </if>
      <if test="row.rightdownurlWidth != null">
        rightDownUrl_width = #{row.rightdownurlWidth,jdbcType=INTEGER},
      </if>
      <if test="row.rightdownurlPic != null">
        rightDownUrl_pic = #{row.rightdownurlPic,jdbcType=VARCHAR},
      </if>
      <if test="row.rightdownurlHeight != null">
        rightDownUrl_height = #{row.rightdownurlHeight,jdbcType=INTEGER},
      </if>
      <if test="row.resurlWidth != null">
        resUrl_width = #{row.resurlWidth,jdbcType=INTEGER},
      </if>
      <if test="row.resurlPic != null">
        resUrl_pic = #{row.resurlPic,jdbcType=VARCHAR},
      </if>
      <if test="row.resurlHeight != null">
        resUrl_height = #{row.resurlHeight,jdbcType=INTEGER},
      </if>
      <if test="row.listurlWidth != null">
        listUrl_width = #{row.listurlWidth,jdbcType=INTEGER},
      </if>
      <if test="row.listurlPic != null">
        listUrl_pic = #{row.listurlPic,jdbcType=VARCHAR},
      </if>
      <if test="row.listurlHeight != null">
        listUrl_height = #{row.listurlHeight,jdbcType=INTEGER},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update templet_border
    set id = #{row.id,jdbcType=BIGINT},
      group_id = #{row.groupId,jdbcType=BIGINT},
      leftUrl_width = #{row.lefturlWidth,jdbcType=INTEGER},
      leftUrl_pic = #{row.lefturlPic,jdbcType=VARCHAR},
      leftUrl_height = #{row.lefturlHeight,jdbcType=INTEGER},
      rightUrl_width = #{row.righturlWidth,jdbcType=INTEGER},
      rightUrl_pic = #{row.righturlPic,jdbcType=VARCHAR},
      rightUrl_height = #{row.righturlHeight,jdbcType=INTEGER},
      topUrl_width = #{row.topurlWidth,jdbcType=INTEGER},
      topUrl_pic = #{row.topurlPic,jdbcType=VARCHAR},
      topUrl_height = #{row.topurlHeight,jdbcType=INTEGER},
      downUrl_width = #{row.downurlWidth,jdbcType=INTEGER},
      downUrl_pic = #{row.downurlPic,jdbcType=VARCHAR},
      downUrl_height = #{row.downurlHeight,jdbcType=INTEGER},
      leftTopUrl_width = #{row.lefttopurlWidth,jdbcType=INTEGER},
      leftTopUrl_pic = #{row.lefttopurlPic,jdbcType=VARCHAR},
      leftTopUrl_height = #{row.lefttopurlHeight,jdbcType=INTEGER},
      leftDownUrl_width = #{row.leftdownurlWidth,jdbcType=INTEGER},
      leftDownUrl_pic = #{row.leftdownurlPic,jdbcType=VARCHAR},
      leftDownUrl_height = #{row.leftdownurlHeight,jdbcType=INTEGER},
      rightTopUrl_width = #{row.righttopurlWidth,jdbcType=INTEGER},
      rightTopUrl_pic = #{row.righttopurlPic,jdbcType=VARCHAR},
      rightTopUrl_height = #{row.righttopurlHeight,jdbcType=INTEGER},
      rightDownUrl_width = #{row.rightdownurlWidth,jdbcType=INTEGER},
      rightDownUrl_pic = #{row.rightdownurlPic,jdbcType=VARCHAR},
      rightDownUrl_height = #{row.rightdownurlHeight,jdbcType=INTEGER},
      resUrl_width = #{row.resurlWidth,jdbcType=INTEGER},
      resUrl_pic = #{row.resurlPic,jdbcType=VARCHAR},
      resUrl_height = #{row.resurlHeight,jdbcType=INTEGER},
      listUrl_width = #{row.listurlWidth,jdbcType=INTEGER},
      listUrl_pic = #{row.listurlPic,jdbcType=VARCHAR},
      listUrl_height = #{row.listurlHeight,jdbcType=INTEGER},
      create_time = #{row.createTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vida.xinye.x2.mbg.model.TempletBorder">
    update templet_border
    <set>
      <if test="groupId != null">
        group_id = #{groupId,jdbcType=BIGINT},
      </if>
      <if test="lefturlWidth != null">
        leftUrl_width = #{lefturlWidth,jdbcType=INTEGER},
      </if>
      <if test="lefturlPic != null">
        leftUrl_pic = #{lefturlPic,jdbcType=VARCHAR},
      </if>
      <if test="lefturlHeight != null">
        leftUrl_height = #{lefturlHeight,jdbcType=INTEGER},
      </if>
      <if test="righturlWidth != null">
        rightUrl_width = #{righturlWidth,jdbcType=INTEGER},
      </if>
      <if test="righturlPic != null">
        rightUrl_pic = #{righturlPic,jdbcType=VARCHAR},
      </if>
      <if test="righturlHeight != null">
        rightUrl_height = #{righturlHeight,jdbcType=INTEGER},
      </if>
      <if test="topurlWidth != null">
        topUrl_width = #{topurlWidth,jdbcType=INTEGER},
      </if>
      <if test="topurlPic != null">
        topUrl_pic = #{topurlPic,jdbcType=VARCHAR},
      </if>
      <if test="topurlHeight != null">
        topUrl_height = #{topurlHeight,jdbcType=INTEGER},
      </if>
      <if test="downurlWidth != null">
        downUrl_width = #{downurlWidth,jdbcType=INTEGER},
      </if>
      <if test="downurlPic != null">
        downUrl_pic = #{downurlPic,jdbcType=VARCHAR},
      </if>
      <if test="downurlHeight != null">
        downUrl_height = #{downurlHeight,jdbcType=INTEGER},
      </if>
      <if test="lefttopurlWidth != null">
        leftTopUrl_width = #{lefttopurlWidth,jdbcType=INTEGER},
      </if>
      <if test="lefttopurlPic != null">
        leftTopUrl_pic = #{lefttopurlPic,jdbcType=VARCHAR},
      </if>
      <if test="lefttopurlHeight != null">
        leftTopUrl_height = #{lefttopurlHeight,jdbcType=INTEGER},
      </if>
      <if test="leftdownurlWidth != null">
        leftDownUrl_width = #{leftdownurlWidth,jdbcType=INTEGER},
      </if>
      <if test="leftdownurlPic != null">
        leftDownUrl_pic = #{leftdownurlPic,jdbcType=VARCHAR},
      </if>
      <if test="leftdownurlHeight != null">
        leftDownUrl_height = #{leftdownurlHeight,jdbcType=INTEGER},
      </if>
      <if test="righttopurlWidth != null">
        rightTopUrl_width = #{righttopurlWidth,jdbcType=INTEGER},
      </if>
      <if test="righttopurlPic != null">
        rightTopUrl_pic = #{righttopurlPic,jdbcType=VARCHAR},
      </if>
      <if test="righttopurlHeight != null">
        rightTopUrl_height = #{righttopurlHeight,jdbcType=INTEGER},
      </if>
      <if test="rightdownurlWidth != null">
        rightDownUrl_width = #{rightdownurlWidth,jdbcType=INTEGER},
      </if>
      <if test="rightdownurlPic != null">
        rightDownUrl_pic = #{rightdownurlPic,jdbcType=VARCHAR},
      </if>
      <if test="rightdownurlHeight != null">
        rightDownUrl_height = #{rightdownurlHeight,jdbcType=INTEGER},
      </if>
      <if test="resurlWidth != null">
        resUrl_width = #{resurlWidth,jdbcType=INTEGER},
      </if>
      <if test="resurlPic != null">
        resUrl_pic = #{resurlPic,jdbcType=VARCHAR},
      </if>
      <if test="resurlHeight != null">
        resUrl_height = #{resurlHeight,jdbcType=INTEGER},
      </if>
      <if test="listurlWidth != null">
        listUrl_width = #{listurlWidth,jdbcType=INTEGER},
      </if>
      <if test="listurlPic != null">
        listUrl_pic = #{listurlPic,jdbcType=VARCHAR},
      </if>
      <if test="listurlHeight != null">
        listUrl_height = #{listurlHeight,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vida.xinye.x2.mbg.model.TempletBorder">
    update templet_border
    set group_id = #{groupId,jdbcType=BIGINT},
      leftUrl_width = #{lefturlWidth,jdbcType=INTEGER},
      leftUrl_pic = #{lefturlPic,jdbcType=VARCHAR},
      leftUrl_height = #{lefturlHeight,jdbcType=INTEGER},
      rightUrl_width = #{righturlWidth,jdbcType=INTEGER},
      rightUrl_pic = #{righturlPic,jdbcType=VARCHAR},
      rightUrl_height = #{righturlHeight,jdbcType=INTEGER},
      topUrl_width = #{topurlWidth,jdbcType=INTEGER},
      topUrl_pic = #{topurlPic,jdbcType=VARCHAR},
      topUrl_height = #{topurlHeight,jdbcType=INTEGER},
      downUrl_width = #{downurlWidth,jdbcType=INTEGER},
      downUrl_pic = #{downurlPic,jdbcType=VARCHAR},
      downUrl_height = #{downurlHeight,jdbcType=INTEGER},
      leftTopUrl_width = #{lefttopurlWidth,jdbcType=INTEGER},
      leftTopUrl_pic = #{lefttopurlPic,jdbcType=VARCHAR},
      leftTopUrl_height = #{lefttopurlHeight,jdbcType=INTEGER},
      leftDownUrl_width = #{leftdownurlWidth,jdbcType=INTEGER},
      leftDownUrl_pic = #{leftdownurlPic,jdbcType=VARCHAR},
      leftDownUrl_height = #{leftdownurlHeight,jdbcType=INTEGER},
      rightTopUrl_width = #{righttopurlWidth,jdbcType=INTEGER},
      rightTopUrl_pic = #{righttopurlPic,jdbcType=VARCHAR},
      rightTopUrl_height = #{righttopurlHeight,jdbcType=INTEGER},
      rightDownUrl_width = #{rightdownurlWidth,jdbcType=INTEGER},
      rightDownUrl_pic = #{rightdownurlPic,jdbcType=VARCHAR},
      rightDownUrl_height = #{rightdownurlHeight,jdbcType=INTEGER},
      resUrl_width = #{resurlWidth,jdbcType=INTEGER},
      resUrl_pic = #{resurlPic,jdbcType=VARCHAR},
      resUrl_height = #{resurlHeight,jdbcType=INTEGER},
      listUrl_width = #{listurlWidth,jdbcType=INTEGER},
      listUrl_pic = #{listurlPic,jdbcType=VARCHAR},
      listUrl_height = #{listurlHeight,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>