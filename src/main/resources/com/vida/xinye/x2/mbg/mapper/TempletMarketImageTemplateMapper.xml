<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vida.xinye.x2.mbg.mapper.TempletMarketImageTemplateMapper">
  <resultMap id="BaseResultMap" type="com.vida.xinye.x2.mbg.model.TempletMarketImageTemplate">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="group_id" jdbcType="BIGINT" property="groupId" />
    <result column="thumb_pic" jdbcType="VARCHAR" property="thumbPic" />
    <result column="cell_width" jdbcType="INTEGER" property="cellWidth" />
    <result column="cell_height" jdbcType="INTEGER" property="cellHeight" />
    <result column="edit_pic" jdbcType="VARCHAR" property="editPic" />
    <result column="full_line" jdbcType="BIT" property="fullLine" />
    <result column="bgview_height" jdbcType="INTEGER" property="bgviewHeight" />
    <result column="rows" jdbcType="INTEGER" property="rows" />
    <result column="columns" jdbcType="INTEGER" property="columns" />
    <result column="line_width" jdbcType="INTEGER" property="lineWidth" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, group_id, thumb_pic, cell_width, cell_height, edit_pic, full_line, bgview_height, 
    rows, columns, line_width, sort, create_time
  </sql>
  <select id="selectByExample" parameterType="com.vida.xinye.x2.mbg.model.TempletMarketImageTemplateExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from templet_market_image_template
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from templet_market_image_template
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from templet_market_image_template
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.vida.xinye.x2.mbg.model.TempletMarketImageTemplateExample">
    delete from templet_market_image_template
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.vida.xinye.x2.mbg.model.TempletMarketImageTemplate">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into templet_market_image_template (group_id, thumb_pic, cell_width, 
      cell_height, edit_pic, full_line, 
      bgview_height, rows, columns, 
      line_width, sort, create_time
      )
    values (#{groupId,jdbcType=BIGINT}, #{thumbPic,jdbcType=VARCHAR}, #{cellWidth,jdbcType=INTEGER}, 
      #{cellHeight,jdbcType=INTEGER}, #{editPic,jdbcType=VARCHAR}, #{fullLine,jdbcType=BIT}, 
      #{bgviewHeight,jdbcType=INTEGER}, #{rows,jdbcType=INTEGER}, #{columns,jdbcType=INTEGER}, 
      #{lineWidth,jdbcType=INTEGER}, #{sort,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.vida.xinye.x2.mbg.model.TempletMarketImageTemplate">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into templet_market_image_template
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="groupId != null">
        group_id,
      </if>
      <if test="thumbPic != null">
        thumb_pic,
      </if>
      <if test="cellWidth != null">
        cell_width,
      </if>
      <if test="cellHeight != null">
        cell_height,
      </if>
      <if test="editPic != null">
        edit_pic,
      </if>
      <if test="fullLine != null">
        full_line,
      </if>
      <if test="bgviewHeight != null">
        bgview_height,
      </if>
      <if test="rows != null">
        rows,
      </if>
      <if test="columns != null">
        columns,
      </if>
      <if test="lineWidth != null">
        line_width,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="groupId != null">
        #{groupId,jdbcType=BIGINT},
      </if>
      <if test="thumbPic != null">
        #{thumbPic,jdbcType=VARCHAR},
      </if>
      <if test="cellWidth != null">
        #{cellWidth,jdbcType=INTEGER},
      </if>
      <if test="cellHeight != null">
        #{cellHeight,jdbcType=INTEGER},
      </if>
      <if test="editPic != null">
        #{editPic,jdbcType=VARCHAR},
      </if>
      <if test="fullLine != null">
        #{fullLine,jdbcType=BIT},
      </if>
      <if test="bgviewHeight != null">
        #{bgviewHeight,jdbcType=INTEGER},
      </if>
      <if test="rows != null">
        #{rows,jdbcType=INTEGER},
      </if>
      <if test="columns != null">
        #{columns,jdbcType=INTEGER},
      </if>
      <if test="lineWidth != null">
        #{lineWidth,jdbcType=INTEGER},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vida.xinye.x2.mbg.model.TempletMarketImageTemplateExample" resultType="java.lang.Long">
    select count(*) from templet_market_image_template
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update templet_market_image_template
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.groupId != null">
        group_id = #{row.groupId,jdbcType=BIGINT},
      </if>
      <if test="row.thumbPic != null">
        thumb_pic = #{row.thumbPic,jdbcType=VARCHAR},
      </if>
      <if test="row.cellWidth != null">
        cell_width = #{row.cellWidth,jdbcType=INTEGER},
      </if>
      <if test="row.cellHeight != null">
        cell_height = #{row.cellHeight,jdbcType=INTEGER},
      </if>
      <if test="row.editPic != null">
        edit_pic = #{row.editPic,jdbcType=VARCHAR},
      </if>
      <if test="row.fullLine != null">
        full_line = #{row.fullLine,jdbcType=BIT},
      </if>
      <if test="row.bgviewHeight != null">
        bgview_height = #{row.bgviewHeight,jdbcType=INTEGER},
      </if>
      <if test="row.rows != null">
        rows = #{row.rows,jdbcType=INTEGER},
      </if>
      <if test="row.columns != null">
        columns = #{row.columns,jdbcType=INTEGER},
      </if>
      <if test="row.lineWidth != null">
        line_width = #{row.lineWidth,jdbcType=INTEGER},
      </if>
      <if test="row.sort != null">
        sort = #{row.sort,jdbcType=INTEGER},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update templet_market_image_template
    set id = #{row.id,jdbcType=BIGINT},
      group_id = #{row.groupId,jdbcType=BIGINT},
      thumb_pic = #{row.thumbPic,jdbcType=VARCHAR},
      cell_width = #{row.cellWidth,jdbcType=INTEGER},
      cell_height = #{row.cellHeight,jdbcType=INTEGER},
      edit_pic = #{row.editPic,jdbcType=VARCHAR},
      full_line = #{row.fullLine,jdbcType=BIT},
      bgview_height = #{row.bgviewHeight,jdbcType=INTEGER},
      rows = #{row.rows,jdbcType=INTEGER},
      columns = #{row.columns,jdbcType=INTEGER},
      line_width = #{row.lineWidth,jdbcType=INTEGER},
      sort = #{row.sort,jdbcType=INTEGER},
      create_time = #{row.createTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vida.xinye.x2.mbg.model.TempletMarketImageTemplate">
    update templet_market_image_template
    <set>
      <if test="groupId != null">
        group_id = #{groupId,jdbcType=BIGINT},
      </if>
      <if test="thumbPic != null">
        thumb_pic = #{thumbPic,jdbcType=VARCHAR},
      </if>
      <if test="cellWidth != null">
        cell_width = #{cellWidth,jdbcType=INTEGER},
      </if>
      <if test="cellHeight != null">
        cell_height = #{cellHeight,jdbcType=INTEGER},
      </if>
      <if test="editPic != null">
        edit_pic = #{editPic,jdbcType=VARCHAR},
      </if>
      <if test="fullLine != null">
        full_line = #{fullLine,jdbcType=BIT},
      </if>
      <if test="bgviewHeight != null">
        bgview_height = #{bgviewHeight,jdbcType=INTEGER},
      </if>
      <if test="rows != null">
        rows = #{rows,jdbcType=INTEGER},
      </if>
      <if test="columns != null">
        columns = #{columns,jdbcType=INTEGER},
      </if>
      <if test="lineWidth != null">
        line_width = #{lineWidth,jdbcType=INTEGER},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vida.xinye.x2.mbg.model.TempletMarketImageTemplate">
    update templet_market_image_template
    set group_id = #{groupId,jdbcType=BIGINT},
      thumb_pic = #{thumbPic,jdbcType=VARCHAR},
      cell_width = #{cellWidth,jdbcType=INTEGER},
      cell_height = #{cellHeight,jdbcType=INTEGER},
      edit_pic = #{editPic,jdbcType=VARCHAR},
      full_line = #{fullLine,jdbcType=BIT},
      bgview_height = #{bgviewHeight,jdbcType=INTEGER},
      rows = #{rows,jdbcType=INTEGER},
      columns = #{columns,jdbcType=INTEGER},
      line_width = #{lineWidth,jdbcType=INTEGER},
      sort = #{sort,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>