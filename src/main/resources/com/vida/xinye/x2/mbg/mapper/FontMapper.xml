<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vida.xinye.x2.mbg.mapper.FontMapper">
  <resultMap id="BaseResultMap" type="com.vida.xinye.x2.mbg.model.Font">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="kind" jdbcType="VARCHAR" property="kind" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="value" jdbcType="VARCHAR" property="value" />
    <result column="cover" jdbcType="VARCHAR" property="cover" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="createTime" jdbcType="TIMESTAMP" property="createtime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, name, kind, url, value, cover, sort, createTime
  </sql>
  <select id="selectByExample" parameterType="com.vida.xinye.x2.mbg.model.FontExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from font
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from font
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from font
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.vida.xinye.x2.mbg.model.FontExample">
    delete from font
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.vida.xinye.x2.mbg.model.Font">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into font (name, kind, url, 
      value, cover, sort, 
      createTime)
    values (#{name,jdbcType=VARCHAR}, #{kind,jdbcType=VARCHAR}, #{url,jdbcType=VARCHAR}, 
      #{value,jdbcType=VARCHAR}, #{cover,jdbcType=VARCHAR}, #{sort,jdbcType=INTEGER}, 
      #{createtime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.vida.xinye.x2.mbg.model.Font">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into font
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        name,
      </if>
      <if test="kind != null">
        kind,
      </if>
      <if test="url != null">
        url,
      </if>
      <if test="value != null">
        value,
      </if>
      <if test="cover != null">
        cover,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="createtime != null">
        createTime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="kind != null">
        #{kind,jdbcType=VARCHAR},
      </if>
      <if test="url != null">
        #{url,jdbcType=VARCHAR},
      </if>
      <if test="value != null">
        #{value,jdbcType=VARCHAR},
      </if>
      <if test="cover != null">
        #{cover,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="createtime != null">
        #{createtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vida.xinye.x2.mbg.model.FontExample" resultType="java.lang.Long">
    select count(*) from font
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update font
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.name != null">
        name = #{row.name,jdbcType=VARCHAR},
      </if>
      <if test="row.kind != null">
        kind = #{row.kind,jdbcType=VARCHAR},
      </if>
      <if test="row.url != null">
        url = #{row.url,jdbcType=VARCHAR},
      </if>
      <if test="row.value != null">
        value = #{row.value,jdbcType=VARCHAR},
      </if>
      <if test="row.cover != null">
        cover = #{row.cover,jdbcType=VARCHAR},
      </if>
      <if test="row.sort != null">
        sort = #{row.sort,jdbcType=INTEGER},
      </if>
      <if test="row.createtime != null">
        createTime = #{row.createtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update font
    set id = #{row.id,jdbcType=BIGINT},
      name = #{row.name,jdbcType=VARCHAR},
      kind = #{row.kind,jdbcType=VARCHAR},
      url = #{row.url,jdbcType=VARCHAR},
      value = #{row.value,jdbcType=VARCHAR},
      cover = #{row.cover,jdbcType=VARCHAR},
      sort = #{row.sort,jdbcType=INTEGER},
      createTime = #{row.createtime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vida.xinye.x2.mbg.model.Font">
    update font
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="kind != null">
        kind = #{kind,jdbcType=VARCHAR},
      </if>
      <if test="url != null">
        url = #{url,jdbcType=VARCHAR},
      </if>
      <if test="value != null">
        value = #{value,jdbcType=VARCHAR},
      </if>
      <if test="cover != null">
        cover = #{cover,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="createtime != null">
        createTime = #{createtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vida.xinye.x2.mbg.model.Font">
    update font
    set name = #{name,jdbcType=VARCHAR},
      kind = #{kind,jdbcType=VARCHAR},
      url = #{url,jdbcType=VARCHAR},
      value = #{value,jdbcType=VARCHAR},
      cover = #{cover,jdbcType=VARCHAR},
      sort = #{sort,jdbcType=INTEGER},
      createTime = #{createtime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>