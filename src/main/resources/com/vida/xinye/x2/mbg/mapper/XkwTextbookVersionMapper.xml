<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vida.xinye.x2.mbg.mapper.XkwTextbookVersionMapper">
  <resultMap id="BaseResultMap" type="com.vida.xinye.x2.mbg.model.XkwTextbookVersion">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="course_id" jdbcType="INTEGER" property="courseId" />
    <result column="year" jdbcType="INTEGER" property="year" />
    <result column="ordinal" jdbcType="INTEGER" property="ordinal" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, name, course_id, year, ordinal
  </sql>
  <select id="selectByExample" parameterType="com.vida.xinye.x2.mbg.model.XkwTextbookVersionExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from xkw_textbook_version
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from xkw_textbook_version
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from xkw_textbook_version
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.vida.xinye.x2.mbg.model.XkwTextbookVersionExample">
    delete from xkw_textbook_version
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.vida.xinye.x2.mbg.model.XkwTextbookVersion">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into xkw_textbook_version (name, course_id, year, 
      ordinal)
    values (#{name,jdbcType=VARCHAR}, #{courseId,jdbcType=INTEGER}, #{year,jdbcType=INTEGER}, 
      #{ordinal,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.vida.xinye.x2.mbg.model.XkwTextbookVersion">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into xkw_textbook_version
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        name,
      </if>
      <if test="courseId != null">
        course_id,
      </if>
      <if test="year != null">
        year,
      </if>
      <if test="ordinal != null">
        ordinal,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="courseId != null">
        #{courseId,jdbcType=INTEGER},
      </if>
      <if test="year != null">
        #{year,jdbcType=INTEGER},
      </if>
      <if test="ordinal != null">
        #{ordinal,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vida.xinye.x2.mbg.model.XkwTextbookVersionExample" resultType="java.lang.Long">
    select count(*) from xkw_textbook_version
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update xkw_textbook_version
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=INTEGER},
      </if>
      <if test="row.name != null">
        name = #{row.name,jdbcType=VARCHAR},
      </if>
      <if test="row.courseId != null">
        course_id = #{row.courseId,jdbcType=INTEGER},
      </if>
      <if test="row.year != null">
        year = #{row.year,jdbcType=INTEGER},
      </if>
      <if test="row.ordinal != null">
        ordinal = #{row.ordinal,jdbcType=INTEGER},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update xkw_textbook_version
    set id = #{row.id,jdbcType=INTEGER},
      name = #{row.name,jdbcType=VARCHAR},
      course_id = #{row.courseId,jdbcType=INTEGER},
      year = #{row.year,jdbcType=INTEGER},
      ordinal = #{row.ordinal,jdbcType=INTEGER}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vida.xinye.x2.mbg.model.XkwTextbookVersion">
    update xkw_textbook_version
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="courseId != null">
        course_id = #{courseId,jdbcType=INTEGER},
      </if>
      <if test="year != null">
        year = #{year,jdbcType=INTEGER},
      </if>
      <if test="ordinal != null">
        ordinal = #{ordinal,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vida.xinye.x2.mbg.model.XkwTextbookVersion">
    update xkw_textbook_version
    set name = #{name,jdbcType=VARCHAR},
      course_id = #{courseId,jdbcType=INTEGER},
      year = #{year,jdbcType=INTEGER},
      ordinal = #{ordinal,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>