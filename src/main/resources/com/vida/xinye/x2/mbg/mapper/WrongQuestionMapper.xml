<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vida.xinye.x2.mbg.mapper.WrongQuestionMapper">
  <resultMap id="BaseResultMap" type="com.vida.xinye.x2.mbg.model.WrongQuestion">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="source_type" jdbcType="VARCHAR" property="sourceType" />
    <result column="note" jdbcType="VARCHAR" property="note" />
    <result column="thumbnail" jdbcType="VARCHAR" property="thumbnail" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="xkw_question_id" jdbcType="VARCHAR" property="xkwQuestionId" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.vida.xinye.x2.mbg.model.WrongQuestion">
    <result column="data" jdbcType="LONGVARCHAR" property="data" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id
    , user_id, source_type, note, thumbnail, create_time, update_time, xkw_question_id
  </sql>
  <sql id="Blob_Column_List">
    data
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.vida.xinye.x2.mbg.model.WrongQuestionExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from wrong_question
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.vida.xinye.x2.mbg.model.WrongQuestionExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from wrong_question
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from wrong_question
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wrong_question
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.vida.xinye.x2.mbg.model.WrongQuestionExample">
    delete from wrong_question
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.vida.xinye.x2.mbg.model.WrongQuestion">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into wrong_question (user_id, source_type, note,
    thumbnail, create_time, update_time, xkw_question_id,
    data)
    values (#{userId,jdbcType=BIGINT}, #{sourceType,jdbcType=VARCHAR}, #{note,jdbcType=VARCHAR},
    #{thumbnail,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
    #{xkwQuestionId,jdbcType=VARCHAR},#{data,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.vida.xinye.x2.mbg.model.WrongQuestion">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into wrong_question
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        user_id,
      </if>
      <if test="sourceType != null">
        source_type,
      </if>
      <if test="note != null">
        note,
      </if>
      <if test="thumbnail != null">
        thumbnail,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="xkwQuestionId != null">
        xkw_question_id,
      </if>
      <if test="data != null">
        data,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="sourceType != null">
        #{sourceType,jdbcType=VARCHAR},
      </if>
      <if test="note != null">
        #{note,jdbcType=VARCHAR},
      </if>
      <if test="thumbnail != null">
        #{thumbnail,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="xkwQuestionId != null">
        #{xkwQuestionId,jdbcType=VARCHAR},
      </if>
      <if test="data != null">
        #{data,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vida.xinye.x2.mbg.model.WrongQuestionExample" resultType="java.lang.Long">
    select count(*) from wrong_question
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update wrong_question
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.userId != null">
        user_id = #{row.userId,jdbcType=BIGINT},
      </if>
      <if test="row.sourceType != null">
        source_type = #{row.sourceType,jdbcType=VARCHAR},
      </if>
      <if test="row.note != null">
        note = #{row.note,jdbcType=VARCHAR},
      </if>
      <if test="row.thumbnail != null">
        thumbnail = #{row.thumbnail,jdbcType=VARCHAR},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.xkwQuestionId != null">
        xkw_question_id = #{row.xkwQuestionId,jdbcType=VARCHAR},
      </if>
      <if test="row.data != null">
        data = #{row.data,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause"/>
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update wrong_question
    set id = #{row.id,jdbcType=BIGINT},
    user_id = #{row.userId,jdbcType=BIGINT},
    source_type = #{row.sourceType,jdbcType=VARCHAR},
    note = #{row.note,jdbcType=VARCHAR},
    thumbnail = #{row.thumbnail,jdbcType=VARCHAR},
    create_time = #{row.createTime,jdbcType=TIMESTAMP},
    update_time = #{row.updateTime,jdbcType=TIMESTAMP},
    xkw_question_id = #{row.xkwQuestionId,jdbcType=VARCHAR},
    data = #{row.data,jdbcType=LONGVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause"/>
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update wrong_question
    set id = #{row.id,jdbcType=BIGINT},
    user_id = #{row.userId,jdbcType=BIGINT},
    source_type = #{row.sourceType,jdbcType=VARCHAR},
    note = #{row.note,jdbcType=VARCHAR},
    thumbnail = #{row.thumbnail,jdbcType=VARCHAR},
    create_time = #{row.createTime,jdbcType=TIMESTAMP},
    update_time = #{row.updateTime,jdbcType=TIMESTAMP},
    xkw_question_id = #{row.xkwQuestionId,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause"/>
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vida.xinye.x2.mbg.model.WrongQuestion">
    update wrong_question
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="sourceType != null">
        source_type = #{sourceType,jdbcType=VARCHAR},
      </if>
      <if test="note != null">
        note = #{note,jdbcType=VARCHAR},
      </if>
      <if test="thumbnail != null">
        thumbnail = #{thumbnail,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="xkwQuestionId != null">
        xkw_question_id = #{xkwQuestionId,jdbcType=VARCHAR},
      </if>
      <if test="data != null">
        data = #{data,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.vida.xinye.x2.mbg.model.WrongQuestion">
    update wrong_question
    set user_id         = #{userId,jdbcType=BIGINT},
        source_type     = #{sourceType,jdbcType=VARCHAR},
        note            = #{note,jdbcType=VARCHAR},
        thumbnail       = #{thumbnail,jdbcType=VARCHAR},
        create_time     = #{createTime,jdbcType=TIMESTAMP},
        update_time     = #{updateTime,jdbcType=TIMESTAMP},
        xkw_question_id = #{xkwQuestionId,jdbcType=VARCHAR},
        data            = #{data,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vida.xinye.x2.mbg.model.WrongQuestion">
    update wrong_question
    set user_id         = #{userId,jdbcType=BIGINT},
        source_type     = #{sourceType,jdbcType=VARCHAR},
        note            = #{note,jdbcType=VARCHAR},
        thumbnail       = #{thumbnail,jdbcType=VARCHAR},
        create_time     = #{createTime,jdbcType=TIMESTAMP},
        update_time     = #{updateTime,jdbcType=TIMESTAMP},
        xkw_question_id = #{xkwQuestionId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>