<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vida.xinye.x2.mbg.mapper.PrintHistoryMapper">
    <resultMap id="BaseResultMap" type="com.vida.xinye.x2.mbg.model.PrintHistory">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="templet_name" jdbcType="VARCHAR" property="templetName"/>
        <result column="templet_cover" jdbcType="VARCHAR" property="templetCover"/>
        <result column="print_time" jdbcType="TIMESTAMP" property="printTime"/>
        <result column="source_type" jdbcType="TINYINT" property="sourceType"/>
    </resultMap>
    <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.vida.xinye.x2.mbg.model.PrintHistory">
        <result column="templet_data" jdbcType="LONGVARCHAR" property="templetData"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        id
        , user_id, templet_name, templet_cover, print_time
    </sql>
    <sql id="Blob_Column_List">
        templet_data
    </sql>
    <select id="selectByExampleWithBLOBs" parameterType="com.vida.xinye.x2.mbg.model.PrintHistoryExample"
            resultMap="ResultMapWithBLOBs">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from print_history
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <select id="selectByExample" parameterType="com.vida.xinye.x2.mbg.model.PrintHistoryExample"
            resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from print_history
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from print_history
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from print_history
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <delete id="deleteByExample" parameterType="com.vida.xinye.x2.mbg.model.PrintHistoryExample">
        delete from print_history
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" parameterType="com.vida.xinye.x2.mbg.model.PrintHistory">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into print_history (user_id, templet_name, templet_cover,
        print_time, templet_data)
        values (#{userId,jdbcType=BIGINT}, #{templetName,jdbcType=VARCHAR}, #{templetCover,jdbcType=VARCHAR},
        #{printTime,jdbcType=TIMESTAMP}, #{sourceType,jdbcType=TINYINT}, #{templetData,jdbcType=LONGVARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.vida.xinye.x2.mbg.model.PrintHistory">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into print_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                user_id,
            </if>
            <if test="templetName != null">
                templet_name,
            </if>
            <if test="templetCover != null">
                templet_cover,
            </if>
            <if test="printTime != null">
                print_time,
            </if>
            <if test="sourceType != null">
                source_type,
            </if>
            <if test="templetData != null">
                templet_data,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="templetName != null">
                #{templetName,jdbcType=VARCHAR},
            </if>
            <if test="templetCover != null">
                #{templetCover,jdbcType=VARCHAR},
            </if>
            <if test="printTime != null">
                #{printTime,jdbcType=TIMESTAMP},
            </if>
            <if test="sourceType != null">
                #{sourceType,jdbcType=TINYINT},
            </if>
            <if test="templetData != null">
                #{templetData,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="com.vida.xinye.x2.mbg.model.PrintHistoryExample"
            resultType="java.lang.Long">
        select count(*) from print_history
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        update print_history
        <set>
            <if test="row.id != null">
                id = #{row.id,jdbcType=BIGINT},
            </if>
            <if test="row.userId != null">
                user_id = #{row.userId,jdbcType=BIGINT},
            </if>
            <if test="row.templetName != null">
                templet_name = #{row.templetName,jdbcType=VARCHAR},
            </if>
            <if test="row.templetCover != null">
                templet_cover = #{row.templetCover,jdbcType=VARCHAR},
            </if>
            <if test="row.printTime != null">
                print_time = #{row.printTime,jdbcType=TIMESTAMP},
            </if>
            <if test="row.sourceType != null">
                source_type = #{row.sourceType,jdbcType=TINYINT},
            </if>
            <if test="row.templetData != null">
                templet_data = #{row.templetData,jdbcType=LONGVARCHAR},
            </if>
        </set>
        <if test="example != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExampleWithBLOBs" parameterType="map">
        update print_history
        set id = #{row.id,jdbcType=BIGINT},
        user_id = #{row.userId,jdbcType=BIGINT},
        templet_name = #{row.templetName,jdbcType=VARCHAR},
        templet_cover = #{row.templetCover,jdbcType=VARCHAR},
        print_time = #{row.printTime,jdbcType=TIMESTAMP},
        source_type = #{row.sourceType,jdbcType=TINYINT},
        templet_data = #{row.templetData,jdbcType=LONGVARCHAR}
        <if test="example != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        update print_history
        set id = #{row.id,jdbcType=BIGINT},
        user_id = #{row.userId,jdbcType=BIGINT},
        templet_name = #{row.templetName,jdbcType=VARCHAR},
        templet_cover = #{row.templetCover,jdbcType=VARCHAR},
        print_time = #{row.printTime,jdbcType=TIMESTAMP},
        source_type = #{row.sourceType,jdbcType=TINYINT}
        <if test="example != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective" parameterType="com.vida.xinye.x2.mbg.model.PrintHistory">
        update print_history
        <set>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="templetName != null">
                templet_name = #{templetName,jdbcType=VARCHAR},
            </if>
            <if test="templetCover != null">
                templet_cover = #{templetCover,jdbcType=VARCHAR},
            </if>
            <if test="printTime != null">
                print_time = #{printTime,jdbcType=TIMESTAMP},
            </if>
            <if test="sourceType != null">
                source_type = #{sourceType,jdbcType=TINYINT},
            </if>
            <if test="templetData != null">
                templet_data = #{templetData,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.vida.xinye.x2.mbg.model.PrintHistory">
        update print_history
        set user_id       = #{userId,jdbcType=BIGINT},
            templet_name  = #{templetName,jdbcType=VARCHAR},
            templet_cover = #{templetCover,jdbcType=VARCHAR},
            print_time    = #{printTime,jdbcType=TIMESTAMP},
            source_type   = #{sourceType,jdbcType=TINYINT},
            templet_data  = #{templetData,jdbcType=LONGVARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.vida.xinye.x2.mbg.model.PrintHistory">
        update print_history
        set user_id       = #{userId,jdbcType=BIGINT},
            templet_name  = #{templetName,jdbcType=VARCHAR},
            templet_cover = #{templetCover,jdbcType=VARCHAR},
            print_time    = #{printTime,jdbcType=TIMESTAMP},
            source_type   = #{sourceType,jdbcType=TINYINT}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>