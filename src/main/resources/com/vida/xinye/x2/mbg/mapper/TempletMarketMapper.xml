<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vida.xinye.x2.mbg.mapper.TempletMarketMapper">
  <resultMap id="BaseResultMap" type="com.vida.xinye.x2.mbg.model.TempletMarket">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="group_id" jdbcType="BIGINT" property="groupId" />
    <result column="resUrl_width" jdbcType="INTEGER" property="resurlWidth" />
    <result column="resUrl_pic" jdbcType="VARCHAR" property="resurlPic" />
    <result column="resUrl_height" jdbcType="INTEGER" property="resurlHeight" />
    <result column="downUrl_width" jdbcType="INTEGER" property="downurlWidth" />
    <result column="downUrl_pic" jdbcType="VARCHAR" property="downurlPic" />
    <result column="downUrl_height" jdbcType="INTEGER" property="downurlHeight" />
    <result column="rightUrl_width" jdbcType="INTEGER" property="righturlWidth" />
    <result column="rightUrl_pic" jdbcType="VARCHAR" property="righturlPic" />
    <result column="rightUrl_height" jdbcType="INTEGER" property="righturlHeight" />
    <result column="leftUrl_width" jdbcType="INTEGER" property="lefturlWidth" />
    <result column="leftUrl_pic" jdbcType="VARCHAR" property="lefturlPic" />
    <result column="leftUrl_height" jdbcType="INTEGER" property="lefturlHeight" />
    <result column="topUrl_width" jdbcType="INTEGER" property="topurlWidth" />
    <result column="topUrl_pic" jdbcType="VARCHAR" property="topurlPic" />
    <result column="topUrl_height" jdbcType="INTEGER" property="topurlHeight" />
    <result column="listUrl_width" jdbcType="INTEGER" property="listurlWidth" />
    <result column="listUrl_pic" jdbcType="VARCHAR" property="listurlPic" />
    <result column="listUrl_height" jdbcType="INTEGER" property="listurlHeight" />
    <result column="addLeftUrl_width" jdbcType="INTEGER" property="addlefturlWidth" />
    <result column="addLeftUrl_pic" jdbcType="VARCHAR" property="addlefturlPic" />
    <result column="addLeftUrl_height" jdbcType="INTEGER" property="addlefturlHeight" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.vida.xinye.x2.mbg.model.TempletMarket">
    <result column="data" jdbcType="LONGVARCHAR" property="data" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, group_id, resUrl_width, resUrl_pic, resUrl_height, downUrl_width, downUrl_pic, 
    downUrl_height, rightUrl_width, rightUrl_pic, rightUrl_height, leftUrl_width, leftUrl_pic, 
    leftUrl_height, topUrl_width, topUrl_pic, topUrl_height, listUrl_width, listUrl_pic, 
    listUrl_height, addLeftUrl_width, addLeftUrl_pic, addLeftUrl_height, sort, create_time
  </sql>
  <sql id="Blob_Column_List">
    data
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.vida.xinye.x2.mbg.model.TempletMarketExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from templet_market
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.vida.xinye.x2.mbg.model.TempletMarketExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from templet_market
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from templet_market
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from templet_market
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.vida.xinye.x2.mbg.model.TempletMarketExample">
    delete from templet_market
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.vida.xinye.x2.mbg.model.TempletMarket">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into templet_market (group_id, resUrl_width, resUrl_pic, 
      resUrl_height, downUrl_width, downUrl_pic, 
      downUrl_height, rightUrl_width, rightUrl_pic, 
      rightUrl_height, leftUrl_width, leftUrl_pic, 
      leftUrl_height, topUrl_width, topUrl_pic, 
      topUrl_height, listUrl_width, listUrl_pic, 
      listUrl_height, addLeftUrl_width, addLeftUrl_pic, 
      addLeftUrl_height, sort, create_time, 
      data)
    values (#{groupId,jdbcType=BIGINT}, #{resurlWidth,jdbcType=INTEGER}, #{resurlPic,jdbcType=VARCHAR}, 
      #{resurlHeight,jdbcType=INTEGER}, #{downurlWidth,jdbcType=INTEGER}, #{downurlPic,jdbcType=VARCHAR}, 
      #{downurlHeight,jdbcType=INTEGER}, #{righturlWidth,jdbcType=INTEGER}, #{righturlPic,jdbcType=VARCHAR}, 
      #{righturlHeight,jdbcType=INTEGER}, #{lefturlWidth,jdbcType=INTEGER}, #{lefturlPic,jdbcType=VARCHAR}, 
      #{lefturlHeight,jdbcType=INTEGER}, #{topurlWidth,jdbcType=INTEGER}, #{topurlPic,jdbcType=VARCHAR}, 
      #{topurlHeight,jdbcType=INTEGER}, #{listurlWidth,jdbcType=INTEGER}, #{listurlPic,jdbcType=VARCHAR}, 
      #{listurlHeight,jdbcType=INTEGER}, #{addlefturlWidth,jdbcType=INTEGER}, #{addlefturlPic,jdbcType=VARCHAR}, 
      #{addlefturlHeight,jdbcType=INTEGER}, #{sort,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{data,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.vida.xinye.x2.mbg.model.TempletMarket">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into templet_market
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="groupId != null">
        group_id,
      </if>
      <if test="resurlWidth != null">
        resUrl_width,
      </if>
      <if test="resurlPic != null">
        resUrl_pic,
      </if>
      <if test="resurlHeight != null">
        resUrl_height,
      </if>
      <if test="downurlWidth != null">
        downUrl_width,
      </if>
      <if test="downurlPic != null">
        downUrl_pic,
      </if>
      <if test="downurlHeight != null">
        downUrl_height,
      </if>
      <if test="righturlWidth != null">
        rightUrl_width,
      </if>
      <if test="righturlPic != null">
        rightUrl_pic,
      </if>
      <if test="righturlHeight != null">
        rightUrl_height,
      </if>
      <if test="lefturlWidth != null">
        leftUrl_width,
      </if>
      <if test="lefturlPic != null">
        leftUrl_pic,
      </if>
      <if test="lefturlHeight != null">
        leftUrl_height,
      </if>
      <if test="topurlWidth != null">
        topUrl_width,
      </if>
      <if test="topurlPic != null">
        topUrl_pic,
      </if>
      <if test="topurlHeight != null">
        topUrl_height,
      </if>
      <if test="listurlWidth != null">
        listUrl_width,
      </if>
      <if test="listurlPic != null">
        listUrl_pic,
      </if>
      <if test="listurlHeight != null">
        listUrl_height,
      </if>
      <if test="addlefturlWidth != null">
        addLeftUrl_width,
      </if>
      <if test="addlefturlPic != null">
        addLeftUrl_pic,
      </if>
      <if test="addlefturlHeight != null">
        addLeftUrl_height,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="data != null">
        data,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="groupId != null">
        #{groupId,jdbcType=BIGINT},
      </if>
      <if test="resurlWidth != null">
        #{resurlWidth,jdbcType=INTEGER},
      </if>
      <if test="resurlPic != null">
        #{resurlPic,jdbcType=VARCHAR},
      </if>
      <if test="resurlHeight != null">
        #{resurlHeight,jdbcType=INTEGER},
      </if>
      <if test="downurlWidth != null">
        #{downurlWidth,jdbcType=INTEGER},
      </if>
      <if test="downurlPic != null">
        #{downurlPic,jdbcType=VARCHAR},
      </if>
      <if test="downurlHeight != null">
        #{downurlHeight,jdbcType=INTEGER},
      </if>
      <if test="righturlWidth != null">
        #{righturlWidth,jdbcType=INTEGER},
      </if>
      <if test="righturlPic != null">
        #{righturlPic,jdbcType=VARCHAR},
      </if>
      <if test="righturlHeight != null">
        #{righturlHeight,jdbcType=INTEGER},
      </if>
      <if test="lefturlWidth != null">
        #{lefturlWidth,jdbcType=INTEGER},
      </if>
      <if test="lefturlPic != null">
        #{lefturlPic,jdbcType=VARCHAR},
      </if>
      <if test="lefturlHeight != null">
        #{lefturlHeight,jdbcType=INTEGER},
      </if>
      <if test="topurlWidth != null">
        #{topurlWidth,jdbcType=INTEGER},
      </if>
      <if test="topurlPic != null">
        #{topurlPic,jdbcType=VARCHAR},
      </if>
      <if test="topurlHeight != null">
        #{topurlHeight,jdbcType=INTEGER},
      </if>
      <if test="listurlWidth != null">
        #{listurlWidth,jdbcType=INTEGER},
      </if>
      <if test="listurlPic != null">
        #{listurlPic,jdbcType=VARCHAR},
      </if>
      <if test="listurlHeight != null">
        #{listurlHeight,jdbcType=INTEGER},
      </if>
      <if test="addlefturlWidth != null">
        #{addlefturlWidth,jdbcType=INTEGER},
      </if>
      <if test="addlefturlPic != null">
        #{addlefturlPic,jdbcType=VARCHAR},
      </if>
      <if test="addlefturlHeight != null">
        #{addlefturlHeight,jdbcType=INTEGER},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="data != null">
        #{data,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vida.xinye.x2.mbg.model.TempletMarketExample" resultType="java.lang.Long">
    select count(*) from templet_market
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update templet_market
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.groupId != null">
        group_id = #{row.groupId,jdbcType=BIGINT},
      </if>
      <if test="row.resurlWidth != null">
        resUrl_width = #{row.resurlWidth,jdbcType=INTEGER},
      </if>
      <if test="row.resurlPic != null">
        resUrl_pic = #{row.resurlPic,jdbcType=VARCHAR},
      </if>
      <if test="row.resurlHeight != null">
        resUrl_height = #{row.resurlHeight,jdbcType=INTEGER},
      </if>
      <if test="row.downurlWidth != null">
        downUrl_width = #{row.downurlWidth,jdbcType=INTEGER},
      </if>
      <if test="row.downurlPic != null">
        downUrl_pic = #{row.downurlPic,jdbcType=VARCHAR},
      </if>
      <if test="row.downurlHeight != null">
        downUrl_height = #{row.downurlHeight,jdbcType=INTEGER},
      </if>
      <if test="row.righturlWidth != null">
        rightUrl_width = #{row.righturlWidth,jdbcType=INTEGER},
      </if>
      <if test="row.righturlPic != null">
        rightUrl_pic = #{row.righturlPic,jdbcType=VARCHAR},
      </if>
      <if test="row.righturlHeight != null">
        rightUrl_height = #{row.righturlHeight,jdbcType=INTEGER},
      </if>
      <if test="row.lefturlWidth != null">
        leftUrl_width = #{row.lefturlWidth,jdbcType=INTEGER},
      </if>
      <if test="row.lefturlPic != null">
        leftUrl_pic = #{row.lefturlPic,jdbcType=VARCHAR},
      </if>
      <if test="row.lefturlHeight != null">
        leftUrl_height = #{row.lefturlHeight,jdbcType=INTEGER},
      </if>
      <if test="row.topurlWidth != null">
        topUrl_width = #{row.topurlWidth,jdbcType=INTEGER},
      </if>
      <if test="row.topurlPic != null">
        topUrl_pic = #{row.topurlPic,jdbcType=VARCHAR},
      </if>
      <if test="row.topurlHeight != null">
        topUrl_height = #{row.topurlHeight,jdbcType=INTEGER},
      </if>
      <if test="row.listurlWidth != null">
        listUrl_width = #{row.listurlWidth,jdbcType=INTEGER},
      </if>
      <if test="row.listurlPic != null">
        listUrl_pic = #{row.listurlPic,jdbcType=VARCHAR},
      </if>
      <if test="row.listurlHeight != null">
        listUrl_height = #{row.listurlHeight,jdbcType=INTEGER},
      </if>
      <if test="row.addlefturlWidth != null">
        addLeftUrl_width = #{row.addlefturlWidth,jdbcType=INTEGER},
      </if>
      <if test="row.addlefturlPic != null">
        addLeftUrl_pic = #{row.addlefturlPic,jdbcType=VARCHAR},
      </if>
      <if test="row.addlefturlHeight != null">
        addLeftUrl_height = #{row.addlefturlHeight,jdbcType=INTEGER},
      </if>
      <if test="row.sort != null">
        sort = #{row.sort,jdbcType=INTEGER},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.data != null">
        data = #{row.data,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update templet_market
    set id = #{row.id,jdbcType=BIGINT},
      group_id = #{row.groupId,jdbcType=BIGINT},
      resUrl_width = #{row.resurlWidth,jdbcType=INTEGER},
      resUrl_pic = #{row.resurlPic,jdbcType=VARCHAR},
      resUrl_height = #{row.resurlHeight,jdbcType=INTEGER},
      downUrl_width = #{row.downurlWidth,jdbcType=INTEGER},
      downUrl_pic = #{row.downurlPic,jdbcType=VARCHAR},
      downUrl_height = #{row.downurlHeight,jdbcType=INTEGER},
      rightUrl_width = #{row.righturlWidth,jdbcType=INTEGER},
      rightUrl_pic = #{row.righturlPic,jdbcType=VARCHAR},
      rightUrl_height = #{row.righturlHeight,jdbcType=INTEGER},
      leftUrl_width = #{row.lefturlWidth,jdbcType=INTEGER},
      leftUrl_pic = #{row.lefturlPic,jdbcType=VARCHAR},
      leftUrl_height = #{row.lefturlHeight,jdbcType=INTEGER},
      topUrl_width = #{row.topurlWidth,jdbcType=INTEGER},
      topUrl_pic = #{row.topurlPic,jdbcType=VARCHAR},
      topUrl_height = #{row.topurlHeight,jdbcType=INTEGER},
      listUrl_width = #{row.listurlWidth,jdbcType=INTEGER},
      listUrl_pic = #{row.listurlPic,jdbcType=VARCHAR},
      listUrl_height = #{row.listurlHeight,jdbcType=INTEGER},
      addLeftUrl_width = #{row.addlefturlWidth,jdbcType=INTEGER},
      addLeftUrl_pic = #{row.addlefturlPic,jdbcType=VARCHAR},
      addLeftUrl_height = #{row.addlefturlHeight,jdbcType=INTEGER},
      sort = #{row.sort,jdbcType=INTEGER},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      data = #{row.data,jdbcType=LONGVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update templet_market
    set id = #{row.id,jdbcType=BIGINT},
      group_id = #{row.groupId,jdbcType=BIGINT},
      resUrl_width = #{row.resurlWidth,jdbcType=INTEGER},
      resUrl_pic = #{row.resurlPic,jdbcType=VARCHAR},
      resUrl_height = #{row.resurlHeight,jdbcType=INTEGER},
      downUrl_width = #{row.downurlWidth,jdbcType=INTEGER},
      downUrl_pic = #{row.downurlPic,jdbcType=VARCHAR},
      downUrl_height = #{row.downurlHeight,jdbcType=INTEGER},
      rightUrl_width = #{row.righturlWidth,jdbcType=INTEGER},
      rightUrl_pic = #{row.righturlPic,jdbcType=VARCHAR},
      rightUrl_height = #{row.righturlHeight,jdbcType=INTEGER},
      leftUrl_width = #{row.lefturlWidth,jdbcType=INTEGER},
      leftUrl_pic = #{row.lefturlPic,jdbcType=VARCHAR},
      leftUrl_height = #{row.lefturlHeight,jdbcType=INTEGER},
      topUrl_width = #{row.topurlWidth,jdbcType=INTEGER},
      topUrl_pic = #{row.topurlPic,jdbcType=VARCHAR},
      topUrl_height = #{row.topurlHeight,jdbcType=INTEGER},
      listUrl_width = #{row.listurlWidth,jdbcType=INTEGER},
      listUrl_pic = #{row.listurlPic,jdbcType=VARCHAR},
      listUrl_height = #{row.listurlHeight,jdbcType=INTEGER},
      addLeftUrl_width = #{row.addlefturlWidth,jdbcType=INTEGER},
      addLeftUrl_pic = #{row.addlefturlPic,jdbcType=VARCHAR},
      addLeftUrl_height = #{row.addlefturlHeight,jdbcType=INTEGER},
      sort = #{row.sort,jdbcType=INTEGER},
      create_time = #{row.createTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vida.xinye.x2.mbg.model.TempletMarket">
    update templet_market
    <set>
      <if test="groupId != null">
        group_id = #{groupId,jdbcType=BIGINT},
      </if>
      <if test="resurlWidth != null">
        resUrl_width = #{resurlWidth,jdbcType=INTEGER},
      </if>
      <if test="resurlPic != null">
        resUrl_pic = #{resurlPic,jdbcType=VARCHAR},
      </if>
      <if test="resurlHeight != null">
        resUrl_height = #{resurlHeight,jdbcType=INTEGER},
      </if>
      <if test="downurlWidth != null">
        downUrl_width = #{downurlWidth,jdbcType=INTEGER},
      </if>
      <if test="downurlPic != null">
        downUrl_pic = #{downurlPic,jdbcType=VARCHAR},
      </if>
      <if test="downurlHeight != null">
        downUrl_height = #{downurlHeight,jdbcType=INTEGER},
      </if>
      <if test="righturlWidth != null">
        rightUrl_width = #{righturlWidth,jdbcType=INTEGER},
      </if>
      <if test="righturlPic != null">
        rightUrl_pic = #{righturlPic,jdbcType=VARCHAR},
      </if>
      <if test="righturlHeight != null">
        rightUrl_height = #{righturlHeight,jdbcType=INTEGER},
      </if>
      <if test="lefturlWidth != null">
        leftUrl_width = #{lefturlWidth,jdbcType=INTEGER},
      </if>
      <if test="lefturlPic != null">
        leftUrl_pic = #{lefturlPic,jdbcType=VARCHAR},
      </if>
      <if test="lefturlHeight != null">
        leftUrl_height = #{lefturlHeight,jdbcType=INTEGER},
      </if>
      <if test="topurlWidth != null">
        topUrl_width = #{topurlWidth,jdbcType=INTEGER},
      </if>
      <if test="topurlPic != null">
        topUrl_pic = #{topurlPic,jdbcType=VARCHAR},
      </if>
      <if test="topurlHeight != null">
        topUrl_height = #{topurlHeight,jdbcType=INTEGER},
      </if>
      <if test="listurlWidth != null">
        listUrl_width = #{listurlWidth,jdbcType=INTEGER},
      </if>
      <if test="listurlPic != null">
        listUrl_pic = #{listurlPic,jdbcType=VARCHAR},
      </if>
      <if test="listurlHeight != null">
        listUrl_height = #{listurlHeight,jdbcType=INTEGER},
      </if>
      <if test="addlefturlWidth != null">
        addLeftUrl_width = #{addlefturlWidth,jdbcType=INTEGER},
      </if>
      <if test="addlefturlPic != null">
        addLeftUrl_pic = #{addlefturlPic,jdbcType=VARCHAR},
      </if>
      <if test="addlefturlHeight != null">
        addLeftUrl_height = #{addlefturlHeight,jdbcType=INTEGER},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="data != null">
        data = #{data,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.vida.xinye.x2.mbg.model.TempletMarket">
    update templet_market
    set group_id = #{groupId,jdbcType=BIGINT},
      resUrl_width = #{resurlWidth,jdbcType=INTEGER},
      resUrl_pic = #{resurlPic,jdbcType=VARCHAR},
      resUrl_height = #{resurlHeight,jdbcType=INTEGER},
      downUrl_width = #{downurlWidth,jdbcType=INTEGER},
      downUrl_pic = #{downurlPic,jdbcType=VARCHAR},
      downUrl_height = #{downurlHeight,jdbcType=INTEGER},
      rightUrl_width = #{righturlWidth,jdbcType=INTEGER},
      rightUrl_pic = #{righturlPic,jdbcType=VARCHAR},
      rightUrl_height = #{righturlHeight,jdbcType=INTEGER},
      leftUrl_width = #{lefturlWidth,jdbcType=INTEGER},
      leftUrl_pic = #{lefturlPic,jdbcType=VARCHAR},
      leftUrl_height = #{lefturlHeight,jdbcType=INTEGER},
      topUrl_width = #{topurlWidth,jdbcType=INTEGER},
      topUrl_pic = #{topurlPic,jdbcType=VARCHAR},
      topUrl_height = #{topurlHeight,jdbcType=INTEGER},
      listUrl_width = #{listurlWidth,jdbcType=INTEGER},
      listUrl_pic = #{listurlPic,jdbcType=VARCHAR},
      listUrl_height = #{listurlHeight,jdbcType=INTEGER},
      addLeftUrl_width = #{addlefturlWidth,jdbcType=INTEGER},
      addLeftUrl_pic = #{addlefturlPic,jdbcType=VARCHAR},
      addLeftUrl_height = #{addlefturlHeight,jdbcType=INTEGER},
      sort = #{sort,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      data = #{data,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vida.xinye.x2.mbg.model.TempletMarket">
    update templet_market
    set group_id = #{groupId,jdbcType=BIGINT},
      resUrl_width = #{resurlWidth,jdbcType=INTEGER},
      resUrl_pic = #{resurlPic,jdbcType=VARCHAR},
      resUrl_height = #{resurlHeight,jdbcType=INTEGER},
      downUrl_width = #{downurlWidth,jdbcType=INTEGER},
      downUrl_pic = #{downurlPic,jdbcType=VARCHAR},
      downUrl_height = #{downurlHeight,jdbcType=INTEGER},
      rightUrl_width = #{righturlWidth,jdbcType=INTEGER},
      rightUrl_pic = #{righturlPic,jdbcType=VARCHAR},
      rightUrl_height = #{righturlHeight,jdbcType=INTEGER},
      leftUrl_width = #{lefturlWidth,jdbcType=INTEGER},
      leftUrl_pic = #{lefturlPic,jdbcType=VARCHAR},
      leftUrl_height = #{lefturlHeight,jdbcType=INTEGER},
      topUrl_width = #{topurlWidth,jdbcType=INTEGER},
      topUrl_pic = #{topurlPic,jdbcType=VARCHAR},
      topUrl_height = #{topurlHeight,jdbcType=INTEGER},
      listUrl_width = #{listurlWidth,jdbcType=INTEGER},
      listUrl_pic = #{listurlPic,jdbcType=VARCHAR},
      listUrl_height = #{listurlHeight,jdbcType=INTEGER},
      addLeftUrl_width = #{addlefturlWidth,jdbcType=INTEGER},
      addLeftUrl_pic = #{addlefturlPic,jdbcType=VARCHAR},
      addLeftUrl_height = #{addlefturlHeight,jdbcType=INTEGER},
      sort = #{sort,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>