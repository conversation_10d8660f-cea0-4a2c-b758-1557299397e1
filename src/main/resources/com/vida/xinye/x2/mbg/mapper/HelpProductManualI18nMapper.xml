<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vida.xinye.x2.mbg.mapper.HelpProductManualI18nMapper">
  <resultMap id="BaseResultMap" type="com.vida.xinye.x2.mbg.model.HelpProductManualI18n">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="product_manual_id" jdbcType="BIGINT" property="productManualId" />
    <result column="locale" jdbcType="VARCHAR" property="locale" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="url" jdbcType="VARCHAR" property="url" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, product_manual_id, locale, title, url
  </sql>
  <select id="selectByExample" parameterType="com.vida.xinye.x2.mbg.model.HelpProductManualI18nExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from help_product_manual_i18n
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from help_product_manual_i18n
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from help_product_manual_i18n
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.vida.xinye.x2.mbg.model.HelpProductManualI18nExample">
    delete from help_product_manual_i18n
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.vida.xinye.x2.mbg.model.HelpProductManualI18n">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into help_product_manual_i18n (product_manual_id, locale, title, 
      url)
    values (#{productManualId,jdbcType=BIGINT}, #{locale,jdbcType=VARCHAR}, #{title,jdbcType=VARCHAR}, 
      #{url,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.vida.xinye.x2.mbg.model.HelpProductManualI18n">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into help_product_manual_i18n
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="productManualId != null">
        product_manual_id,
      </if>
      <if test="locale != null">
        locale,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="url != null">
        url,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="productManualId != null">
        #{productManualId,jdbcType=BIGINT},
      </if>
      <if test="locale != null">
        #{locale,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="url != null">
        #{url,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vida.xinye.x2.mbg.model.HelpProductManualI18nExample" resultType="java.lang.Long">
    select count(*) from help_product_manual_i18n
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update help_product_manual_i18n
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.productManualId != null">
        product_manual_id = #{row.productManualId,jdbcType=BIGINT},
      </if>
      <if test="row.locale != null">
        locale = #{row.locale,jdbcType=VARCHAR},
      </if>
      <if test="row.title != null">
        title = #{row.title,jdbcType=VARCHAR},
      </if>
      <if test="row.url != null">
        url = #{row.url,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update help_product_manual_i18n
    set id = #{row.id,jdbcType=BIGINT},
      product_manual_id = #{row.productManualId,jdbcType=BIGINT},
      locale = #{row.locale,jdbcType=VARCHAR},
      title = #{row.title,jdbcType=VARCHAR},
      url = #{row.url,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vida.xinye.x2.mbg.model.HelpProductManualI18n">
    update help_product_manual_i18n
    <set>
      <if test="productManualId != null">
        product_manual_id = #{productManualId,jdbcType=BIGINT},
      </if>
      <if test="locale != null">
        locale = #{locale,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="url != null">
        url = #{url,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vida.xinye.x2.mbg.model.HelpProductManualI18n">
    update help_product_manual_i18n
    set product_manual_id = #{productManualId,jdbcType=BIGINT},
      locale = #{locale,jdbcType=VARCHAR},
      title = #{title,jdbcType=VARCHAR},
      url = #{url,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>