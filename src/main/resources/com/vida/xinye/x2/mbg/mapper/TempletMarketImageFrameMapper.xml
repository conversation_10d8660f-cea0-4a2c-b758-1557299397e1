<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vida.xinye.x2.mbg.mapper.TempletMarketImageFrameMapper">
  <resultMap id="BaseResultMap" type="com.vida.xinye.x2.mbg.model.TempletMarketImageFrame">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="template_id" jdbcType="BIGINT" property="templateId" />
    <result column="frame_x" jdbcType="INTEGER" property="frameX" />
    <result column="frame_y" jdbcType="INTEGER" property="frameY" />
    <result column="frame_width" jdbcType="INTEGER" property="frameWidth" />
    <result column="frame_height" jdbcType="INTEGER" property="frameHeight" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, template_id, frame_x, frame_y, frame_width, frame_height
  </sql>
  <select id="selectByExample" parameterType="com.vida.xinye.x2.mbg.model.TempletMarketImageFrameExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from templet_market_image_frame
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from templet_market_image_frame
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from templet_market_image_frame
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.vida.xinye.x2.mbg.model.TempletMarketImageFrameExample">
    delete from templet_market_image_frame
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.vida.xinye.x2.mbg.model.TempletMarketImageFrame">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into templet_market_image_frame (template_id, frame_x, frame_y, 
      frame_width, frame_height)
    values (#{templateId,jdbcType=BIGINT}, #{frameX,jdbcType=INTEGER}, #{frameY,jdbcType=INTEGER}, 
      #{frameWidth,jdbcType=INTEGER}, #{frameHeight,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.vida.xinye.x2.mbg.model.TempletMarketImageFrame">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into templet_market_image_frame
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="templateId != null">
        template_id,
      </if>
      <if test="frameX != null">
        frame_x,
      </if>
      <if test="frameY != null">
        frame_y,
      </if>
      <if test="frameWidth != null">
        frame_width,
      </if>
      <if test="frameHeight != null">
        frame_height,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="templateId != null">
        #{templateId,jdbcType=BIGINT},
      </if>
      <if test="frameX != null">
        #{frameX,jdbcType=INTEGER},
      </if>
      <if test="frameY != null">
        #{frameY,jdbcType=INTEGER},
      </if>
      <if test="frameWidth != null">
        #{frameWidth,jdbcType=INTEGER},
      </if>
      <if test="frameHeight != null">
        #{frameHeight,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vida.xinye.x2.mbg.model.TempletMarketImageFrameExample" resultType="java.lang.Long">
    select count(*) from templet_market_image_frame
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update templet_market_image_frame
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.templateId != null">
        template_id = #{row.templateId,jdbcType=BIGINT},
      </if>
      <if test="row.frameX != null">
        frame_x = #{row.frameX,jdbcType=INTEGER},
      </if>
      <if test="row.frameY != null">
        frame_y = #{row.frameY,jdbcType=INTEGER},
      </if>
      <if test="row.frameWidth != null">
        frame_width = #{row.frameWidth,jdbcType=INTEGER},
      </if>
      <if test="row.frameHeight != null">
        frame_height = #{row.frameHeight,jdbcType=INTEGER},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update templet_market_image_frame
    set id = #{row.id,jdbcType=BIGINT},
      template_id = #{row.templateId,jdbcType=BIGINT},
      frame_x = #{row.frameX,jdbcType=INTEGER},
      frame_y = #{row.frameY,jdbcType=INTEGER},
      frame_width = #{row.frameWidth,jdbcType=INTEGER},
      frame_height = #{row.frameHeight,jdbcType=INTEGER}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vida.xinye.x2.mbg.model.TempletMarketImageFrame">
    update templet_market_image_frame
    <set>
      <if test="templateId != null">
        template_id = #{templateId,jdbcType=BIGINT},
      </if>
      <if test="frameX != null">
        frame_x = #{frameX,jdbcType=INTEGER},
      </if>
      <if test="frameY != null">
        frame_y = #{frameY,jdbcType=INTEGER},
      </if>
      <if test="frameWidth != null">
        frame_width = #{frameWidth,jdbcType=INTEGER},
      </if>
      <if test="frameHeight != null">
        frame_height = #{frameHeight,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vida.xinye.x2.mbg.model.TempletMarketImageFrame">
    update templet_market_image_frame
    set template_id = #{templateId,jdbcType=BIGINT},
      frame_x = #{frameX,jdbcType=INTEGER},
      frame_y = #{frameY,jdbcType=INTEGER},
      frame_width = #{frameWidth,jdbcType=INTEGER},
      frame_height = #{frameHeight,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>