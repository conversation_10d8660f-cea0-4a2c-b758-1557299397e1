<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vida.xinye.x2.mbg.mapper.IdCardVerificationMapper">
  <resultMap id="BaseResultMap" type="com.vida.xinye.x2.mbg.model.IdCardVerification">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="id_card_hash" jdbcType="VARCHAR" property="idCardHash" />
    <result column="verification_result" jdbcType="INTEGER" property="verificationResult" />
    <result column="birthday" jdbcType="VARCHAR" property="birthday" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="sex" jdbcType="VARCHAR" property="sex" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="verification_time" jdbcType="TIMESTAMP" property="verificationTime" />
    <result column="expire_time" jdbcType="TIMESTAMP" property="expireTime" />
    <result column="source" jdbcType="VARCHAR" property="source" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, name, id_card_hash, verification_result, birthday, address, sex, description, 
    order_no, verification_time, expire_time, source, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.vida.xinye.x2.mbg.model.IdCardVerificationExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from id_card_verification
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from id_card_verification
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from id_card_verification
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.vida.xinye.x2.mbg.model.IdCardVerificationExample">
    delete from id_card_verification
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.vida.xinye.x2.mbg.model.IdCardVerification">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into id_card_verification (name, id_card_hash, verification_result, 
      birthday, address, sex, 
      description, order_no, verification_time, 
      expire_time, source, create_time, 
      update_time)
    values (#{name,jdbcType=VARCHAR}, #{idCardHash,jdbcType=VARCHAR}, #{verificationResult,jdbcType=INTEGER}, 
      #{birthday,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR}, #{sex,jdbcType=VARCHAR}, 
      #{description,jdbcType=VARCHAR}, #{orderNo,jdbcType=VARCHAR}, #{verificationTime,jdbcType=TIMESTAMP}, 
      #{expireTime,jdbcType=TIMESTAMP}, #{source,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.vida.xinye.x2.mbg.model.IdCardVerification">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into id_card_verification
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        name,
      </if>
      <if test="idCardHash != null">
        id_card_hash,
      </if>
      <if test="verificationResult != null">
        verification_result,
      </if>
      <if test="birthday != null">
        birthday,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="sex != null">
        sex,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="verificationTime != null">
        verification_time,
      </if>
      <if test="expireTime != null">
        expire_time,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="idCardHash != null">
        #{idCardHash,jdbcType=VARCHAR},
      </if>
      <if test="verificationResult != null">
        #{verificationResult,jdbcType=INTEGER},
      </if>
      <if test="birthday != null">
        #{birthday,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="sex != null">
        #{sex,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="verificationTime != null">
        #{verificationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="expireTime != null">
        #{expireTime,jdbcType=TIMESTAMP},
      </if>
      <if test="source != null">
        #{source,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vida.xinye.x2.mbg.model.IdCardVerificationExample" resultType="java.lang.Long">
    select count(*) from id_card_verification
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update id_card_verification
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.name != null">
        name = #{row.name,jdbcType=VARCHAR},
      </if>
      <if test="row.idCardHash != null">
        id_card_hash = #{row.idCardHash,jdbcType=VARCHAR},
      </if>
      <if test="row.verificationResult != null">
        verification_result = #{row.verificationResult,jdbcType=INTEGER},
      </if>
      <if test="row.birthday != null">
        birthday = #{row.birthday,jdbcType=VARCHAR},
      </if>
      <if test="row.address != null">
        address = #{row.address,jdbcType=VARCHAR},
      </if>
      <if test="row.sex != null">
        sex = #{row.sex,jdbcType=VARCHAR},
      </if>
      <if test="row.description != null">
        description = #{row.description,jdbcType=VARCHAR},
      </if>
      <if test="row.orderNo != null">
        order_no = #{row.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="row.verificationTime != null">
        verification_time = #{row.verificationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.expireTime != null">
        expire_time = #{row.expireTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.source != null">
        source = #{row.source,jdbcType=VARCHAR},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update id_card_verification
    set id = #{row.id,jdbcType=BIGINT},
      name = #{row.name,jdbcType=VARCHAR},
      id_card_hash = #{row.idCardHash,jdbcType=VARCHAR},
      verification_result = #{row.verificationResult,jdbcType=INTEGER},
      birthday = #{row.birthday,jdbcType=VARCHAR},
      address = #{row.address,jdbcType=VARCHAR},
      sex = #{row.sex,jdbcType=VARCHAR},
      description = #{row.description,jdbcType=VARCHAR},
      order_no = #{row.orderNo,jdbcType=VARCHAR},
      verification_time = #{row.verificationTime,jdbcType=TIMESTAMP},
      expire_time = #{row.expireTime,jdbcType=TIMESTAMP},
      source = #{row.source,jdbcType=VARCHAR},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vida.xinye.x2.mbg.model.IdCardVerification">
    update id_card_verification
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="idCardHash != null">
        id_card_hash = #{idCardHash,jdbcType=VARCHAR},
      </if>
      <if test="verificationResult != null">
        verification_result = #{verificationResult,jdbcType=INTEGER},
      </if>
      <if test="birthday != null">
        birthday = #{birthday,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="sex != null">
        sex = #{sex,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="verificationTime != null">
        verification_time = #{verificationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="expireTime != null">
        expire_time = #{expireTime,jdbcType=TIMESTAMP},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vida.xinye.x2.mbg.model.IdCardVerification">
    update id_card_verification
    set name = #{name,jdbcType=VARCHAR},
      id_card_hash = #{idCardHash,jdbcType=VARCHAR},
      verification_result = #{verificationResult,jdbcType=INTEGER},
      birthday = #{birthday,jdbcType=VARCHAR},
      address = #{address,jdbcType=VARCHAR},
      sex = #{sex,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      order_no = #{orderNo,jdbcType=VARCHAR},
      verification_time = #{verificationTime,jdbcType=TIMESTAMP},
      expire_time = #{expireTime,jdbcType=TIMESTAMP},
      source = #{source,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>