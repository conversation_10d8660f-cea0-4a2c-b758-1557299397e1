<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vida.xinye.x2.mbg.mapper.TrainingQuestionMapper">
  <resultMap id="BaseResultMap" type="com.vida.xinye.x2.mbg.model.TrainingQuestion">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="course_id" jdbcType="BIGINT" property="courseId" />
    <result column="thumbnail" jdbcType="VARCHAR" property="thumbnail" />
    <result column="difficulty_level" jdbcType="INTEGER" property="difficultyLevel" />
    <result column="type_id" jdbcType="VARCHAR" property="typeId" />
    <result column="year" jdbcType="INTEGER" property="year" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="xkw_question_id" jdbcType="VARCHAR" property="xkwQuestionId" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.vida.xinye.x2.mbg.model.TrainingQuestion">
    <result column="data" jdbcType="LONGVARCHAR" property="data" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, user_id, course_id, thumbnail, difficulty_level, type_id, year, create_time, 
    update_time, xkw_question_id
  </sql>
  <sql id="Blob_Column_List">
    data
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.vida.xinye.x2.mbg.model.TrainingQuestionExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from training_question
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.vida.xinye.x2.mbg.model.TrainingQuestionExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from training_question
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from training_question
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from training_question
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.vida.xinye.x2.mbg.model.TrainingQuestionExample">
    delete from training_question
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.vida.xinye.x2.mbg.model.TrainingQuestion">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into training_question (user_id, course_id, thumbnail, 
      difficulty_level, type_id, year, 
      create_time, update_time, xkw_question_id, 
      data)
    values (#{userId,jdbcType=BIGINT}, #{courseId,jdbcType=BIGINT}, #{thumbnail,jdbcType=VARCHAR}, 
      #{difficultyLevel,jdbcType=INTEGER}, #{typeId,jdbcType=VARCHAR}, #{year,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{xkwQuestionId,jdbcType=VARCHAR}, 
      #{data,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.vida.xinye.x2.mbg.model.TrainingQuestion">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into training_question
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        user_id,
      </if>
      <if test="courseId != null">
        course_id,
      </if>
      <if test="thumbnail != null">
        thumbnail,
      </if>
      <if test="difficultyLevel != null">
        difficulty_level,
      </if>
      <if test="typeId != null">
        type_id,
      </if>
      <if test="year != null">
        year,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="xkwQuestionId != null">
        xkw_question_id,
      </if>
      <if test="data != null">
        data,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="courseId != null">
        #{courseId,jdbcType=BIGINT},
      </if>
      <if test="thumbnail != null">
        #{thumbnail,jdbcType=VARCHAR},
      </if>
      <if test="difficultyLevel != null">
        #{difficultyLevel,jdbcType=INTEGER},
      </if>
      <if test="typeId != null">
        #{typeId,jdbcType=VARCHAR},
      </if>
      <if test="year != null">
        #{year,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="xkwQuestionId != null">
        #{xkwQuestionId,jdbcType=VARCHAR},
      </if>
      <if test="data != null">
        #{data,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vida.xinye.x2.mbg.model.TrainingQuestionExample" resultType="java.lang.Long">
    select count(*) from training_question
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update training_question
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.userId != null">
        user_id = #{row.userId,jdbcType=BIGINT},
      </if>
      <if test="row.courseId != null">
        course_id = #{row.courseId,jdbcType=BIGINT},
      </if>
      <if test="row.thumbnail != null">
        thumbnail = #{row.thumbnail,jdbcType=VARCHAR},
      </if>
      <if test="row.difficultyLevel != null">
        difficulty_level = #{row.difficultyLevel,jdbcType=INTEGER},
      </if>
      <if test="row.typeId != null">
        type_id = #{row.typeId,jdbcType=VARCHAR},
      </if>
      <if test="row.year != null">
        year = #{row.year,jdbcType=INTEGER},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.xkwQuestionId != null">
        xkw_question_id = #{row.xkwQuestionId,jdbcType=VARCHAR},
      </if>
      <if test="row.data != null">
        data = #{row.data,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update training_question
    set id = #{row.id,jdbcType=BIGINT},
      user_id = #{row.userId,jdbcType=BIGINT},
      course_id = #{row.courseId,jdbcType=BIGINT},
      thumbnail = #{row.thumbnail,jdbcType=VARCHAR},
      difficulty_level = #{row.difficultyLevel,jdbcType=INTEGER},
      type_id = #{row.typeId,jdbcType=VARCHAR},
      year = #{row.year,jdbcType=INTEGER},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      xkw_question_id = #{row.xkwQuestionId,jdbcType=VARCHAR},
      data = #{row.data,jdbcType=LONGVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update training_question
    set id = #{row.id,jdbcType=BIGINT},
      user_id = #{row.userId,jdbcType=BIGINT},
      course_id = #{row.courseId,jdbcType=BIGINT},
      thumbnail = #{row.thumbnail,jdbcType=VARCHAR},
      difficulty_level = #{row.difficultyLevel,jdbcType=INTEGER},
      type_id = #{row.typeId,jdbcType=VARCHAR},
      year = #{row.year,jdbcType=INTEGER},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      xkw_question_id = #{row.xkwQuestionId,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vida.xinye.x2.mbg.model.TrainingQuestion">
    update training_question
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="courseId != null">
        course_id = #{courseId,jdbcType=BIGINT},
      </if>
      <if test="thumbnail != null">
        thumbnail = #{thumbnail,jdbcType=VARCHAR},
      </if>
      <if test="difficultyLevel != null">
        difficulty_level = #{difficultyLevel,jdbcType=INTEGER},
      </if>
      <if test="typeId != null">
        type_id = #{typeId,jdbcType=VARCHAR},
      </if>
      <if test="year != null">
        year = #{year,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="xkwQuestionId != null">
        xkw_question_id = #{xkwQuestionId,jdbcType=VARCHAR},
      </if>
      <if test="data != null">
        data = #{data,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.vida.xinye.x2.mbg.model.TrainingQuestion">
    update training_question
    set user_id = #{userId,jdbcType=BIGINT},
      course_id = #{courseId,jdbcType=BIGINT},
      thumbnail = #{thumbnail,jdbcType=VARCHAR},
      difficulty_level = #{difficultyLevel,jdbcType=INTEGER},
      type_id = #{typeId,jdbcType=VARCHAR},
      year = #{year,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      xkw_question_id = #{xkwQuestionId,jdbcType=VARCHAR},
      data = #{data,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vida.xinye.x2.mbg.model.TrainingQuestion">
    update training_question
    set user_id = #{userId,jdbcType=BIGINT},
      course_id = #{courseId,jdbcType=BIGINT},
      thumbnail = #{thumbnail,jdbcType=VARCHAR},
      difficulty_level = #{difficultyLevel,jdbcType=INTEGER},
      type_id = #{typeId,jdbcType=VARCHAR},
      year = #{year,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      xkw_question_id = #{xkwQuestionId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>