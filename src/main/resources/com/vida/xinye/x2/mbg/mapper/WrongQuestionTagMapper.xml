<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vida.xinye.x2.mbg.mapper.WrongQuestionTagMapper">
  <resultMap id="BaseResultMap" type="com.vida.xinye.x2.mbg.model.WrongQuestionTag">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="tag_type_id" jdbcType="BIGINT" property="tagTypeId" />
    <result column="tag_name" jdbcType="VARCHAR" property="tagName" />
    <result column="is_default" jdbcType="BIT" property="isDefault" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, user_id, tag_type_id, tag_name, is_default
  </sql>
  <select id="selectByExample" parameterType="com.vida.xinye.x2.mbg.model.WrongQuestionTagExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from wrong_question_tag
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wrong_question_tag
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wrong_question_tag
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.vida.xinye.x2.mbg.model.WrongQuestionTagExample">
    delete from wrong_question_tag
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.vida.xinye.x2.mbg.model.WrongQuestionTag">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into wrong_question_tag (user_id, tag_type_id, tag_name, 
      is_default)
    values (#{userId,jdbcType=BIGINT}, #{tagTypeId,jdbcType=BIGINT}, #{tagName,jdbcType=VARCHAR}, 
      #{isDefault,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.vida.xinye.x2.mbg.model.WrongQuestionTag">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into wrong_question_tag
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        user_id,
      </if>
      <if test="tagTypeId != null">
        tag_type_id,
      </if>
      <if test="tagName != null">
        tag_name,
      </if>
      <if test="isDefault != null">
        is_default,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="tagTypeId != null">
        #{tagTypeId,jdbcType=BIGINT},
      </if>
      <if test="tagName != null">
        #{tagName,jdbcType=VARCHAR},
      </if>
      <if test="isDefault != null">
        #{isDefault,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vida.xinye.x2.mbg.model.WrongQuestionTagExample" resultType="java.lang.Long">
    select count(*) from wrong_question_tag
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update wrong_question_tag
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.userId != null">
        user_id = #{row.userId,jdbcType=BIGINT},
      </if>
      <if test="row.tagTypeId != null">
        tag_type_id = #{row.tagTypeId,jdbcType=BIGINT},
      </if>
      <if test="row.tagName != null">
        tag_name = #{row.tagName,jdbcType=VARCHAR},
      </if>
      <if test="row.isDefault != null">
        is_default = #{row.isDefault,jdbcType=BIT},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update wrong_question_tag
    set id = #{row.id,jdbcType=BIGINT},
      user_id = #{row.userId,jdbcType=BIGINT},
      tag_type_id = #{row.tagTypeId,jdbcType=BIGINT},
      tag_name = #{row.tagName,jdbcType=VARCHAR},
      is_default = #{row.isDefault,jdbcType=BIT}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vida.xinye.x2.mbg.model.WrongQuestionTag">
    update wrong_question_tag
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="tagTypeId != null">
        tag_type_id = #{tagTypeId,jdbcType=BIGINT},
      </if>
      <if test="tagName != null">
        tag_name = #{tagName,jdbcType=VARCHAR},
      </if>
      <if test="isDefault != null">
        is_default = #{isDefault,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vida.xinye.x2.mbg.model.WrongQuestionTag">
    update wrong_question_tag
    set user_id = #{userId,jdbcType=BIGINT},
      tag_type_id = #{tagTypeId,jdbcType=BIGINT},
      tag_name = #{tagName,jdbcType=VARCHAR},
      is_default = #{isDefault,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>