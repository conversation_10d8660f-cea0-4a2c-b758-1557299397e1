<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vida.xinye.x2.mbg.mapper.UserMapperExtend">
  <update id="updateProfile">
    UPDATE user
    SET role_type = #{roleType}, grade_type = #{gradeType}
    WHERE id = #{userId}
  </update>
  <update id="updateParentMode">
    UPDATE user
    <set>
      parent_mode = #{parentMode}
      <if test="parentName != null">
        , parent_name = #{parentName}
      </if>
      <if test="parentIdcard != null">
        , parent_idcard = #{parentIdcard}
      </if>
      <if test="teenagerPassword != null">
        , teenager_password = #{teenagerPassword}
      </if>
    </set>
    WHERE id = #{userId}
  </update>
</mapper>