<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vida.xinye.x2.mbg.mapper.UserMapper">
  <resultMap id="BaseResultMap" type="com.vida.xinye.x2.mbg.model.User">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="username" jdbcType="VARCHAR" property="username" />
    <result column="password" jdbcType="VARCHAR" property="password" />
    <result column="nickname" jdbcType="VARCHAR" property="nickname" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="avatar" jdbcType="VARCHAR" property="avatar" />
    <result column="register_type" jdbcType="INTEGER" property="registerType" />
    <result column="last_login_time" jdbcType="TIMESTAMP" property="lastLoginTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="role_type" jdbcType="VARCHAR" property="roleType" />
    <result column="grade_type" jdbcType="VARCHAR" property="gradeType" />
    <result column="parent_mode" jdbcType="INTEGER" property="parentMode" />
    <result column="parent_name" jdbcType="VARCHAR" property="parentName" />
    <result column="parent_idcard" jdbcType="VARCHAR" property="parentIdcard" />
    <result column="teenager_password" jdbcType="VARCHAR" property="teenagerPassword" />
    <result column="phone_verified" jdbcType="TINYINT" property="phoneVerified" />
    <result column="email_verified" jdbcType="TINYINT" property="emailVerified" />
    <result column="require_phone_binding" jdbcType="TINYINT" property="requirePhoneBinding" />
    <result column="login_count" jdbcType="INTEGER" property="loginCount" />
    <result column="account_status" jdbcType="TINYINT" property="accountStatus" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, username, password, nickname, phone, email, avatar, register_type, last_login_time,
    create_time, update_time, role_type, grade_type, parent_mode, parent_name, parent_idcard,
    teenager_password, phone_verified, email_verified, require_phone_binding, login_count,
    account_status
  </sql>
  <select id="selectByExample" parameterType="com.vida.xinye.x2.mbg.model.UserExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from user
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from user
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.vida.xinye.x2.mbg.model.UserExample">
    delete from user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.vida.xinye.x2.mbg.model.User">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into user (username, password, nickname, 
      phone, email, avatar, 
      register_type, last_login_time, create_time, 
      update_time, role_type, grade_type, 
      parent_mode, parent_name, parent_idcard, 
      teenager_password)
    values (#{username,jdbcType=VARCHAR}, #{password,jdbcType=VARCHAR}, #{nickname,jdbcType=VARCHAR}, 
      #{phone,jdbcType=VARCHAR}, #{email,jdbcType=VARCHAR}, #{avatar,jdbcType=VARCHAR}, 
      #{registerType,jdbcType=INTEGER}, #{lastLoginTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{roleType,jdbcType=VARCHAR}, #{gradeType,jdbcType=VARCHAR}, 
      #{parentMode,jdbcType=INTEGER}, #{parentName,jdbcType=VARCHAR}, #{parentIdcard,jdbcType=VARCHAR}, 
      #{teenagerPassword,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.vida.xinye.x2.mbg.model.User">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into user
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="username != null">
        username,
      </if>
      <if test="password != null">
        password,
      </if>
      <if test="nickname != null">
        nickname,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="email != null">
        email,
      </if>
      <if test="avatar != null">
        avatar,
      </if>
      <if test="registerType != null">
        register_type,
      </if>
      <if test="lastLoginTime != null">
        last_login_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="roleType != null">
        role_type,
      </if>
      <if test="gradeType != null">
        grade_type,
      </if>
      <if test="parentMode != null">
        parent_mode,
      </if>
      <if test="parentName != null">
        parent_name,
      </if>
      <if test="parentIdcard != null">
        parent_idcard,
      </if>
      <if test="teenagerPassword != null">
        teenager_password,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="username != null">
        #{username,jdbcType=VARCHAR},
      </if>
      <if test="password != null">
        #{password,jdbcType=VARCHAR},
      </if>
      <if test="nickname != null">
        #{nickname,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="avatar != null">
        #{avatar,jdbcType=VARCHAR},
      </if>
      <if test="registerType != null">
        #{registerType,jdbcType=INTEGER},
      </if>
      <if test="lastLoginTime != null">
        #{lastLoginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="roleType != null">
        #{roleType,jdbcType=VARCHAR},
      </if>
      <if test="gradeType != null">
        #{gradeType,jdbcType=VARCHAR},
      </if>
      <if test="parentMode != null">
        #{parentMode,jdbcType=INTEGER},
      </if>
      <if test="parentName != null">
        #{parentName,jdbcType=VARCHAR},
      </if>
      <if test="parentIdcard != null">
        #{parentIdcard,jdbcType=VARCHAR},
      </if>
      <if test="teenagerPassword != null">
        #{teenagerPassword,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vida.xinye.x2.mbg.model.UserExample" resultType="java.lang.Long">
    select count(*) from user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update user
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.username != null">
        username = #{row.username,jdbcType=VARCHAR},
      </if>
      <if test="row.password != null">
        password = #{row.password,jdbcType=VARCHAR},
      </if>
      <if test="row.nickname != null">
        nickname = #{row.nickname,jdbcType=VARCHAR},
      </if>
      <if test="row.phone != null">
        phone = #{row.phone,jdbcType=VARCHAR},
      </if>
      <if test="row.email != null">
        email = #{row.email,jdbcType=VARCHAR},
      </if>
      <if test="row.avatar != null">
        avatar = #{row.avatar,jdbcType=VARCHAR},
      </if>
      <if test="row.registerType != null">
        register_type = #{row.registerType,jdbcType=INTEGER},
      </if>
      <if test="row.lastLoginTime != null">
        last_login_time = #{row.lastLoginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.roleType != null">
        role_type = #{row.roleType,jdbcType=VARCHAR},
      </if>
      <if test="row.gradeType != null">
        grade_type = #{row.gradeType,jdbcType=VARCHAR},
      </if>
      <if test="row.parentMode != null">
        parent_mode = #{row.parentMode,jdbcType=INTEGER},
      </if>
      <if test="row.parentName != null">
        parent_name = #{row.parentName,jdbcType=VARCHAR},
      </if>
      <if test="row.parentIdcard != null">
        parent_idcard = #{row.parentIdcard,jdbcType=VARCHAR},
      </if>
      <if test="row.teenagerPassword != null">
        teenager_password = #{row.teenagerPassword,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update user
    set id = #{row.id,jdbcType=BIGINT},
      username = #{row.username,jdbcType=VARCHAR},
      password = #{row.password,jdbcType=VARCHAR},
      nickname = #{row.nickname,jdbcType=VARCHAR},
      phone = #{row.phone,jdbcType=VARCHAR},
      email = #{row.email,jdbcType=VARCHAR},
      avatar = #{row.avatar,jdbcType=VARCHAR},
      register_type = #{row.registerType,jdbcType=INTEGER},
      last_login_time = #{row.lastLoginTime,jdbcType=TIMESTAMP},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      role_type = #{row.roleType,jdbcType=VARCHAR},
      grade_type = #{row.gradeType,jdbcType=VARCHAR},
      parent_mode = #{row.parentMode,jdbcType=INTEGER},
      parent_name = #{row.parentName,jdbcType=VARCHAR},
      parent_idcard = #{row.parentIdcard,jdbcType=VARCHAR},
      teenager_password = #{row.teenagerPassword,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vida.xinye.x2.mbg.model.User">
    update user
    <set>
      <if test="username != null">
        username = #{username,jdbcType=VARCHAR},
      </if>
      <if test="password != null">
        password = #{password,jdbcType=VARCHAR},
      </if>
      <if test="nickname != null">
        nickname = #{nickname,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="avatar != null">
        avatar = #{avatar,jdbcType=VARCHAR},
      </if>
      <if test="registerType != null">
        register_type = #{registerType,jdbcType=INTEGER},
      </if>
      <if test="lastLoginTime != null">
        last_login_time = #{lastLoginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="roleType != null">
        role_type = #{roleType,jdbcType=VARCHAR},
      </if>
      <if test="gradeType != null">
        grade_type = #{gradeType,jdbcType=VARCHAR},
      </if>
      <if test="parentMode != null">
        parent_mode = #{parentMode,jdbcType=INTEGER},
      </if>
      <if test="parentName != null">
        parent_name = #{parentName,jdbcType=VARCHAR},
      </if>
      <if test="parentIdcard != null">
        parent_idcard = #{parentIdcard,jdbcType=VARCHAR},
      </if>
      <if test="teenagerPassword != null">
        teenager_password = #{teenagerPassword,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vida.xinye.x2.mbg.model.User">
    update user
    set username = #{username,jdbcType=VARCHAR},
      password = #{password,jdbcType=VARCHAR},
      nickname = #{nickname,jdbcType=VARCHAR},
      phone = #{phone,jdbcType=VARCHAR},
      email = #{email,jdbcType=VARCHAR},
      avatar = #{avatar,jdbcType=VARCHAR},
      register_type = #{registerType,jdbcType=INTEGER},
      last_login_time = #{lastLoginTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      role_type = #{roleType,jdbcType=VARCHAR},
      grade_type = #{gradeType,jdbcType=VARCHAR},
      parent_mode = #{parentMode,jdbcType=INTEGER},
      parent_name = #{parentName,jdbcType=VARCHAR},
      parent_idcard = #{parentIdcard,jdbcType=VARCHAR},
      teenager_password = #{teenagerPassword,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>