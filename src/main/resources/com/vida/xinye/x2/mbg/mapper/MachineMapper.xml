<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vida.xinye.x2.mbg.mapper.MachineMapper">
  <resultMap id="BaseResultMap" type="com.vida.xinye.x2.mbg.model.Machine">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="machine_name" jdbcType="VARCHAR" property="machineName" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="bluetooth_name" jdbcType="VARCHAR" property="bluetoothName" />
    <result column="hardware_name" jdbcType="VARCHAR" property="hardwareName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, machine_name, product_name, bluetooth_name, hardware_name, create_time
  </sql>
  <select id="selectByExample" parameterType="com.vida.xinye.x2.mbg.model.MachineExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from machine
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from machine
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from machine
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.vida.xinye.x2.mbg.model.MachineExample">
    delete from machine
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.vida.xinye.x2.mbg.model.Machine">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into machine (machine_name, product_name, bluetooth_name, 
      hardware_name, create_time)
    values (#{machineName,jdbcType=VARCHAR}, #{productName,jdbcType=VARCHAR}, #{bluetoothName,jdbcType=VARCHAR}, 
      #{hardwareName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.vida.xinye.x2.mbg.model.Machine">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into machine
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="machineName != null">
        machine_name,
      </if>
      <if test="productName != null">
        product_name,
      </if>
      <if test="bluetoothName != null">
        bluetooth_name,
      </if>
      <if test="hardwareName != null">
        hardware_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="machineName != null">
        #{machineName,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="bluetoothName != null">
        #{bluetoothName,jdbcType=VARCHAR},
      </if>
      <if test="hardwareName != null">
        #{hardwareName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vida.xinye.x2.mbg.model.MachineExample" resultType="java.lang.Long">
    select count(*) from machine
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update machine
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.machineName != null">
        machine_name = #{row.machineName,jdbcType=VARCHAR},
      </if>
      <if test="row.productName != null">
        product_name = #{row.productName,jdbcType=VARCHAR},
      </if>
      <if test="row.bluetoothName != null">
        bluetooth_name = #{row.bluetoothName,jdbcType=VARCHAR},
      </if>
      <if test="row.hardwareName != null">
        hardware_name = #{row.hardwareName,jdbcType=VARCHAR},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update machine
    set id = #{row.id,jdbcType=BIGINT},
      machine_name = #{row.machineName,jdbcType=VARCHAR},
      product_name = #{row.productName,jdbcType=VARCHAR},
      bluetooth_name = #{row.bluetoothName,jdbcType=VARCHAR},
      hardware_name = #{row.hardwareName,jdbcType=VARCHAR},
      create_time = #{row.createTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vida.xinye.x2.mbg.model.Machine">
    update machine
    <set>
      <if test="machineName != null">
        machine_name = #{machineName,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        product_name = #{productName,jdbcType=VARCHAR},
      </if>
      <if test="bluetoothName != null">
        bluetooth_name = #{bluetoothName,jdbcType=VARCHAR},
      </if>
      <if test="hardwareName != null">
        hardware_name = #{hardwareName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vida.xinye.x2.mbg.model.Machine">
    update machine
    set machine_name = #{machineName,jdbcType=VARCHAR},
      product_name = #{productName,jdbcType=VARCHAR},
      bluetooth_name = #{bluetoothName,jdbcType=VARCHAR},
      hardware_name = #{hardwareName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>