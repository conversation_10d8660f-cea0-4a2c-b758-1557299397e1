<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vida.xinye.x2.mbg.mapper.UserThirdPartyAuthMapper">
  
  <resultMap id="BaseResultMap" type="com.vida.xinye.x2.mbg.model.UserThirdPartyAuth">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="provider" jdbcType="VARCHAR" property="provider" />
    <result column="open_id" jdbcType="VARCHAR" property="openId" />
    <result column="union_id" jdbcType="VARCHAR" property="unionId" />
    <result column="bind_time" jdbcType="TIMESTAMP" property="bindTime" />
    <result column="last_login_time" jdbcType="TIMESTAMP" property="lastLoginTime" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, user_id, provider, open_id, union_id, bind_time, last_login_time,
    status, create_time, update_time
  </sql>

  <!-- 基础CRUD操作 -->
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from user_third_party_auth
    where id = #{id,jdbcType=BIGINT}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from user_third_party_auth
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insert" parameterType="com.vida.xinye.x2.mbg.model.UserThirdPartyAuth">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into user_third_party_auth (user_id, provider, open_id,
      union_id, bind_time, last_login_time,
      status, create_time, update_time)
    values (#{userId,jdbcType=BIGINT}, #{provider,jdbcType=VARCHAR}, #{openId,jdbcType=VARCHAR},
      #{unionId,jdbcType=VARCHAR}, #{bindTime,jdbcType=TIMESTAMP}, #{lastLoginTime,jdbcType=TIMESTAMP},
      #{status,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>

  <insert id="insertSelective" parameterType="com.vida.xinye.x2.mbg.model.UserThirdPartyAuth">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into user_third_party_auth
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        user_id,
      </if>
      <if test="provider != null">
        provider,
      </if>
      <if test="openId != null">
        open_id,
      </if>
      <if test="unionId != null">
        union_id,
      </if>
      <if test="bindTime != null">
        bind_time,
      </if>
      <if test="lastLoginTime != null">
        last_login_time,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="provider != null">
        #{provider,jdbcType=VARCHAR},
      </if>
      <if test="openId != null">
        #{openId,jdbcType=VARCHAR},
      </if>
      <if test="unionId != null">
        #{unionId,jdbcType=VARCHAR},
      </if>
      <if test="bindTime != null">
        #{bindTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastLoginTime != null">
        #{lastLoginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.vida.xinye.x2.mbg.model.UserThirdPartyAuth">
    update user_third_party_auth
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="provider != null">
        provider = #{provider,jdbcType=VARCHAR},
      </if>
      <if test="openId != null">
        open_id = #{openId,jdbcType=VARCHAR},
      </if>
      <if test="unionId != null">
        union_id = #{unionId,jdbcType=VARCHAR},
      </if>
      <if test="bindTime != null">
        bind_time = #{bindTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastLoginTime != null">
        last_login_time = #{lastLoginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateByPrimaryKey" parameterType="com.vida.xinye.x2.mbg.model.UserThirdPartyAuth">
    update user_third_party_auth
    set user_id = #{userId,jdbcType=BIGINT},
      provider = #{provider,jdbcType=VARCHAR},
      open_id = #{openId,jdbcType=VARCHAR},
      union_id = #{unionId,jdbcType=VARCHAR},
      bind_time = #{bindTime,jdbcType=TIMESTAMP},
      last_login_time = #{lastLoginTime,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <!-- 业务查询方法 -->
  <select id="selectByUserId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from user_third_party_auth
    where user_id = #{userId,jdbcType=BIGINT}
    and status = 1
    order by bind_time desc
  </select>

  <select id="selectByUserIdAndProvider" parameterType="map" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from user_third_party_auth
    where user_id = #{userId,jdbcType=BIGINT} 
    and provider = #{provider,jdbcType=VARCHAR}
    and status = 1
  </select>

  <select id="selectByProviderAndOpenId" parameterType="map" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from user_third_party_auth
    where provider = #{provider,jdbcType=VARCHAR} 
    and open_id = #{openId,jdbcType=VARCHAR}
    and status = 1
  </select>

  <select id="selectByProviderAndUnionId" parameterType="map" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from user_third_party_auth
    where provider = #{provider,jdbcType=VARCHAR} 
    and union_id = #{unionId,jdbcType=VARCHAR}
    and status = 1
  </select>

  <select id="selectByUnionId" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from user_third_party_auth
    where union_id = #{unionId,jdbcType=VARCHAR}
    and status = 1
  </select>

  <update id="updateLastLoginTime">
    update user_third_party_auth
    set last_login_time = #{lastLoginTime,jdbcType=TIMESTAMP},
        update_time = NOW()
    where id = #{id,jdbcType=BIGINT}
  </update>

  <delete id="deleteByUserIdAndProvider">
    delete from user_third_party_auth
    where user_id = #{userId,jdbcType=BIGINT}
    and provider = #{provider,jdbcType=VARCHAR}
  </delete>

  <select id="countByUserId" parameterType="java.lang.Long" resultType="java.lang.Integer">
    select count(*)
    from user_third_party_auth
    where user_id = #{userId,jdbcType=BIGINT}
    and status = 1
  </select>

  <select id="selectProvidersByUserId" parameterType="java.lang.Long" resultType="java.lang.String">
    select provider
    from user_third_party_auth
    where user_id = #{userId,jdbcType=BIGINT}
    and status = 1
    order by bind_time desc
  </select>

</mapper>
