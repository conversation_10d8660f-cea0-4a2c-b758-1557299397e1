<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vida.xinye.x2.mbg.mapper.WrongQuestionTagRelationMapper">
  <resultMap id="BaseResultMap" type="com.vida.xinye.x2.mbg.model.WrongQuestionTagRelation">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="question_id" jdbcType="BIGINT" property="questionId" />
    <result column="tag_id" jdbcType="BIGINT" property="tagId" />
    <result column="tag_type_id" jdbcType="BIGINT" property="tagTypeId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, question_id, tag_id, tag_type_id
  </sql>
  <select id="selectByExample" parameterType="com.vida.xinye.x2.mbg.model.WrongQuestionTagRelationExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from wrong_question_tag_relation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wrong_question_tag_relation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wrong_question_tag_relation
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.vida.xinye.x2.mbg.model.WrongQuestionTagRelationExample">
    delete from wrong_question_tag_relation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.vida.xinye.x2.mbg.model.WrongQuestionTagRelation">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into wrong_question_tag_relation (question_id, tag_id, tag_type_id
      )
    values (#{questionId,jdbcType=BIGINT}, #{tagId,jdbcType=BIGINT}, #{tagTypeId,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.vida.xinye.x2.mbg.model.WrongQuestionTagRelation">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into wrong_question_tag_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="questionId != null">
        question_id,
      </if>
      <if test="tagId != null">
        tag_id,
      </if>
      <if test="tagTypeId != null">
        tag_type_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="questionId != null">
        #{questionId,jdbcType=BIGINT},
      </if>
      <if test="tagId != null">
        #{tagId,jdbcType=BIGINT},
      </if>
      <if test="tagTypeId != null">
        #{tagTypeId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vida.xinye.x2.mbg.model.WrongQuestionTagRelationExample" resultType="java.lang.Long">
    select count(*) from wrong_question_tag_relation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update wrong_question_tag_relation
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.questionId != null">
        question_id = #{row.questionId,jdbcType=BIGINT},
      </if>
      <if test="row.tagId != null">
        tag_id = #{row.tagId,jdbcType=BIGINT},
      </if>
      <if test="row.tagTypeId != null">
        tag_type_id = #{row.tagTypeId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update wrong_question_tag_relation
    set id = #{row.id,jdbcType=BIGINT},
      question_id = #{row.questionId,jdbcType=BIGINT},
      tag_id = #{row.tagId,jdbcType=BIGINT},
      tag_type_id = #{row.tagTypeId,jdbcType=BIGINT}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vida.xinye.x2.mbg.model.WrongQuestionTagRelation">
    update wrong_question_tag_relation
    <set>
      <if test="questionId != null">
        question_id = #{questionId,jdbcType=BIGINT},
      </if>
      <if test="tagId != null">
        tag_id = #{tagId,jdbcType=BIGINT},
      </if>
      <if test="tagTypeId != null">
        tag_type_id = #{tagTypeId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vida.xinye.x2.mbg.model.WrongQuestionTagRelation">
    update wrong_question_tag_relation
    set question_id = #{questionId,jdbcType=BIGINT},
      tag_id = #{tagId,jdbcType=BIGINT},
      tag_type_id = #{tagTypeId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>