<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vida.xinye.x2.mbg.mapper.HelpFaqI18nMapper">
  <resultMap id="BaseResultMap" type="com.vida.xinye.x2.mbg.model.HelpFaqI18n">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="faq_id" jdbcType="BIGINT" property="faqId" />
    <result column="locale" jdbcType="VARCHAR" property="locale" />
    <result column="question" jdbcType="VARCHAR" property="question" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.vida.xinye.x2.mbg.model.HelpFaqI18n">
    <result column="answer" jdbcType="LONGVARCHAR" property="answer" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, faq_id, locale, question
  </sql>
  <sql id="Blob_Column_List">
    answer
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.vida.xinye.x2.mbg.model.HelpFaqI18nExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from help_faq_i18n
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.vida.xinye.x2.mbg.model.HelpFaqI18nExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from help_faq_i18n
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from help_faq_i18n
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from help_faq_i18n
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.vida.xinye.x2.mbg.model.HelpFaqI18nExample">
    delete from help_faq_i18n
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.vida.xinye.x2.mbg.model.HelpFaqI18n">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into help_faq_i18n (faq_id, locale, question, 
      answer)
    values (#{faqId,jdbcType=BIGINT}, #{locale,jdbcType=VARCHAR}, #{question,jdbcType=VARCHAR}, 
      #{answer,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.vida.xinye.x2.mbg.model.HelpFaqI18n">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into help_faq_i18n
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="faqId != null">
        faq_id,
      </if>
      <if test="locale != null">
        locale,
      </if>
      <if test="question != null">
        question,
      </if>
      <if test="answer != null">
        answer,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="faqId != null">
        #{faqId,jdbcType=BIGINT},
      </if>
      <if test="locale != null">
        #{locale,jdbcType=VARCHAR},
      </if>
      <if test="question != null">
        #{question,jdbcType=VARCHAR},
      </if>
      <if test="answer != null">
        #{answer,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vida.xinye.x2.mbg.model.HelpFaqI18nExample" resultType="java.lang.Long">
    select count(*) from help_faq_i18n
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update help_faq_i18n
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.faqId != null">
        faq_id = #{row.faqId,jdbcType=BIGINT},
      </if>
      <if test="row.locale != null">
        locale = #{row.locale,jdbcType=VARCHAR},
      </if>
      <if test="row.question != null">
        question = #{row.question,jdbcType=VARCHAR},
      </if>
      <if test="row.answer != null">
        answer = #{row.answer,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update help_faq_i18n
    set id = #{row.id,jdbcType=BIGINT},
      faq_id = #{row.faqId,jdbcType=BIGINT},
      locale = #{row.locale,jdbcType=VARCHAR},
      question = #{row.question,jdbcType=VARCHAR},
      answer = #{row.answer,jdbcType=LONGVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update help_faq_i18n
    set id = #{row.id,jdbcType=BIGINT},
      faq_id = #{row.faqId,jdbcType=BIGINT},
      locale = #{row.locale,jdbcType=VARCHAR},
      question = #{row.question,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vida.xinye.x2.mbg.model.HelpFaqI18n">
    update help_faq_i18n
    <set>
      <if test="faqId != null">
        faq_id = #{faqId,jdbcType=BIGINT},
      </if>
      <if test="locale != null">
        locale = #{locale,jdbcType=VARCHAR},
      </if>
      <if test="question != null">
        question = #{question,jdbcType=VARCHAR},
      </if>
      <if test="answer != null">
        answer = #{answer,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.vida.xinye.x2.mbg.model.HelpFaqI18n">
    update help_faq_i18n
    set faq_id = #{faqId,jdbcType=BIGINT},
      locale = #{locale,jdbcType=VARCHAR},
      question = #{question,jdbcType=VARCHAR},
      answer = #{answer,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vida.xinye.x2.mbg.model.HelpFaqI18n">
    update help_faq_i18n
    set faq_id = #{faqId,jdbcType=BIGINT},
      locale = #{locale,jdbcType=VARCHAR},
      question = #{question,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>