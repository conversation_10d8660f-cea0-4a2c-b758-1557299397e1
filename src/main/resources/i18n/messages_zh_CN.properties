idcard.name.notnull=姓名不能为空
idcard.number.notnull=身份证号不能为空

# 限流相关消息
rate.limit.frequent=访问过于频繁，请稍后再试
rate.limit.idcard.verify=身份证验证接口访问频繁，请稍后再试
rate.limit.idcard.verify.frequent=身份证验证接口访问频繁，请稍后再试
rate.limit.parent.mode.auth.frequent=家长模式认证操作过于频繁，请稍后再试

user.role.notnull=请选择身份
user.grade.notnull=请选择年级

parent.name.notnull=请输入家长姓名
parent.idcard.notnull=请输入家长身份证号
teenager.password.notnull=请输入青少年密码

idcard.verify.failed=身份证验证失败

user.phone.notblank=请输入手机号
user.smscode.notblank=请输入验证码
login.success=登录成功
login.failed=登录失败！
register.success=注册成功
register.failed=注册失败！
token.refresh.failed=刷新token失败！
token.refresh.success=刷新token成功 