server:
  port: 8480
spring:
  servlet:
    multipart:
      enabled: true
      max-request-size: 20480MB
      max-file-size: 10240MB
  main:
    #允许存在多个Feign调用相同Service的接口
    allow-bean-definition-overriding: true
  web:
    locale: zh_CN
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  datasource:
    url: *************************************************************************************************************************
    username: xprinter_test
    password: Vida@2022
    druid:
      initial-size: 5 #连接池初始化大小
      min-idle: 10 #最小空闲连接数
      max-active: 20 #最大连接数
      web-stat-filter:
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*" #不统计这些请求数据
      stat-view-servlet: #访问监控网页的登录用户名和密码
        login-username: druid
        login-password: druid
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  redis:
    host: localhost
    port: 6377
    database: 0
    # 如果本地Redis有密码，请取消注释并修改
    username: r-wz9zemom88r2yiqd8l
    password: vIdaPrinter@2022
  mail:
    protocol: smtps
    host: smtp.exmail.qq.com
    port: 465
    username: <EMAIL>
    password: m8XpVEjzMNDBAv4q
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
          ssl:
            enable: true
          socketFactory:
            class: javax.net.ssl.SSLSocketFactory
            fallback: false
            port: 465
    default-encoding: UTF-8
  messages:
    basename: i18n/messages
    encoding: UTF-8
mybatis:
  mapper-locations:
    - classpath:dao/*.xml
    - classpath*:com/**/mapper/*.xml

# 日志配置
logging:
  level:
    root: info
    com.vida.xinye: debug
    com.vida.xinye.x2.mbg.mapper.*: debug
    com.vida.xinye.x2.dao.*: debug
    com.aliyun.*: error
    com.vida.xinye.x2.mbg.mapper.UserMapper: OFF
    # 登录相关日志
    com.vida.xinye.x2.service.impl.ExtendedAuthServiceImpl: DEBUG
    com.vida.xinye.x2.controller.api.AuthenController: DEBUG
aliyun:
  oss:
    cdn: https://img.ycjqb.com/ # CDN访问域名
    endpoint: oss-cn-shenzhen.aliyuncs.com # oss对外服务的访问域名
    accessKeyId: LTAI5tLF6WShrgd5NPJ3vV6t # 访问身份验证中用到用户标识
    accessKeySecret: ****************************** # 用户用于加密签名字符串和oss用来验证签名字符串的密钥
    bucketName: xprinter-private # oss的存储空间
    stsTokenServer: http://localhost:7084
  idcard:
    app-key: *********
    app-secret: xVECIhu4OIK8doSfepN1vro6ml4pAvyg
  sms:
    access-key-id: LTAI5tJZZh61K68hkPtRaXZj
    access-key-secret: ******************************
    sign-name: 微嗒科技
    template-code: SMS_217875529
    endpoint: dysmsapi.aliyuncs.com
    # 验证码模板配置
    templates:
      login: SMS_217875529
      register: SMS_217875529
      reset-password: SMS_217875529
      bind-account: SMS_217875529
  log:
    level: debug
    endpoint: cn-shenzhen.log.aliyuncs.com
    accessKeyId: LTAI5tHoxVK6fykQWRFEhhm2
    accessKeySecret: ******************************
    producer:
      project: xinye-jiayong
      logstore: logstore-business
    syslog:
      project: xinye-jiayong
      logstore: logstore-syslog

# 智能切题配置
question:
  cutting:
    # 天壤接口配置
    tianrang:
      url: https://questiontr.market.alicloudapi.com/sjtmjc
      api-key: ${TIANRANG_API_KEY:your_tianrang_appcode}
      timeout: 30000
      enabled: true
      retry-count: 2

    # 有道接口配置
    youdao:
      url: https://openapi.youdao.com/cut_question
      app-key: ${YOUDAO_APP_KEY:6735066aa7dd797d}
      app-secret: ${YOUDAO_APP_SECRET:wUZ8mxlcfJLiYF9GDKAaml7EPtUstGxa}
      timeout: 30000
      enabled: true
      retry-count: 2

    # 阿里云接口配置
    aliyun:
      endpoint: ocr-api.cn-hangzhou.aliyuncs.com
      access-key-id: ${ALIYUN_ACCESS_KEY_ID:your_aliyun_access_key_id}
      access-key-secret: ${ALIYUN_ACCESS_KEY_SECRET:your_aliyun_access_key_secret}
      timeout: 30000
      enabled: true
      retry-count: 2

    # 通用配置
    common:
      default-strategy: FASTEST
      default-provider: youdao
      enable-aggregation: true
      max-concurrent-requests: 10
      max-image-size: 10485760 # 10MB
      supported-formats: [jpg, jpeg, png, bmp, gif]
      enable-cache: true
      cache-expire-seconds: 3600


# 安全配置
security:
  blacklist:
    # 静态黑名单（配置文件）
    static-ips:
      - "*************"
      - "10.0.0.*"
      - "**********/16"
    
    # 动态黑名单配置
    dynamic:
      # Redis key前缀
      redis-prefix: "blacklist:"
      # 默认封禁时间（分钟）
      default-ban-duration: 30
      # 自动解封检查间隔（分钟）
      cleanup-interval: 10
      
    # 自动封禁规则
    auto-ban:
      # 发送验证码：5分钟内超过20次请求封禁30分钟
      captcha-send:
        requests: 20
        window-minutes: 5
        ban-minutes: 30
      # 验证验证码：10分钟内超过50次请求封禁60分钟
      captcha-verify:
        requests: 50
        window-minutes: 10
        ban-minutes: 60

# API签名配置
api:
  signature:
    # 签名密钥（生产环境请修改为复杂密钥）
    secret-key: "xinye_captcha_secret_key_2024_v1.0"

#redis缓存配置
redis:
  database: x2-dev
  captcha:
    prefix: captcha_
    expiration: 300

# jwt
jwt:
  secret: 123456
  expiration: 86400

# 系统业务常量
constants:
  locale: en
  print:
    history:
      max: 10

# 文件上传本地缓存地址
file:
  download:
    path: D:/flow

# 登录功能配置
xinye:
  login:
    # 第三方登录配置
    third-party:
      # 微信登录配置
      wechat:
        # APP登录（原生应用）
        app:
          app-id: wx9be6a3590a740028
          app-secret: fb0040615c23c615102b8a62e79c1bc6
        # 小程序登录（使用同一个APP配置）
        miniprogram:
          app-id: wx9be6a3590a740028
          app-secret: fb0040615c23c615102b8a62e79c1bc6
        # 网页登录（H5）
        web:
          app-id: wx92d53b2b360320e1
          app-secret: 79489ab0a4089543fd4c4d64c4e3bb5a
          redirect-uri: ${WECHAT_REDIRECT_URI:http://localhost:8080/callback/wechat}

      # QQ登录配置
      qq:
        android:
          app-id: "1107873591"
          app-key: yUKfENLm8ta5ihjL
        ios:
          app-id: "1107873591"
          app-key: yUKfENLm8ta5ihjL
        # H5网页登录
        web:
          app-id: "101526977"
          app-secret: 2e9a7382ddda7027cdfaf9e142467166
        redirect-uri: ${QQ_REDIRECT_URI:http://localhost:8080/callback/qq}

      # 新浪微博登录配置（暂时禁用，后续配置）
      weibo:
        app-key: ${WEIBO_APP_KEY:}
        app-secret: ${WEIBO_APP_SECRET:}
        redirect-uri: ${WEIBO_REDIRECT_URI:http://localhost:8080/callback/weibo}
        enabled: false

      # Apple ID登录配置（暂时禁用，后续配置）
      apple:
        client-id: ${APPLE_CLIENT_ID:}
        team-id: ${APPLE_TEAM_ID:}
        key-id: ${APPLE_KEY_ID:}
        private-key-path: ${APPLE_PRIVATE_KEY_PATH:}
        enabled: false

    # 安全配置
    security:
      # 登录失败限制
      max-login-attempts: 5
      # 账号锁定时间（分钟）
      account-lock-minutes: 30
      # 登录失败记录过期时间（小时）
      failure-record-expire-hours: 24

      # 验证码配置
      captcha:
        # 验证码长度
        length: 6
        # 验证码有效期（分钟）
        expire-minutes: 5
        # 单日短信验证码上限
        sms-daily-limit: 50
        # 单日邮箱验证码上限
        email-daily-limit: 20
        # 同一IP单日验证码上限
        ip-daily-limit: 100

      # 密码强度配置
      password:
        min-length: 6
        max-length: 20
        require-digit: true
        require-letter: true
        require-special-char: false

    # 缓存配置
    cache:
      # 第三方认证信息缓存时间（小时）
      third-party-auth-expire-hours: 24
      # 用户信息缓存时间（分钟）
      user-info-expire-minutes: 30
      # 登录失败记录缓存时间（小时）
      login-failure-expire-hours: 24

# 验证码Redis配置
captcha:
  redis:
    prefix: "captcha:"
    expire-seconds: 300  # 5分钟过期
  email:
    expire-seconds: 300  # 5分钟过期
    daily-limit: 20      # 每日限额
    frequency-limit: 60  # 发送频率限制（秒）
  sms:
    expire-seconds: 300  # 5分钟过期
    daily-limit: 50      # 每日限额
    frequency-limit: 60  # 发送频率限制（秒）