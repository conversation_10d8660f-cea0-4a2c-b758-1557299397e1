package com.vida.xinye.x2.annotation;

import java.lang.annotation.*;

/**
 * @Description: IP黑名单检查注解
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/11/05 16:30
 */
@Target({ ElementType.METHOD, ElementType.TYPE })
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface BlacklistCheck {

  /**
   * 黑名单检查类型
   */
  CheckType checkType() default CheckType.IP;

  /**
   * 被拦截时的提示消息
   */
  String message() default "Access denied";

  /**
   * 是否启用自动封禁（当达到一定条件时自动加入黑名单）
   */
  boolean autoBlock() default false;

  /**
   * 自动封禁的触发条件（分钟内的请求次数）
   */
  int autoBlockThreshold() default 100;

  /**
   * 自动封禁的时间窗口（分钟）
   */
  int autoBlockWindow() default 10;

  /**
   * 自动封禁的持续时间（分钟）
   */
  int autoBlockDuration() default 60;

  /**
   * 检查类型枚举
   */
  enum CheckType {
    /**
     * IP地址检查
     */
    IP,
    /**
     * 用户ID检查
     */
    USER,
    /**
     * 设备ID检查
     */
    DEVICE
  }
}