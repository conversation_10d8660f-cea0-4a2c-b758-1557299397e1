package com.vida.xinye.x2.annotation;

import com.vida.xinye.x2.constant.OperationLogTypeEnum;

import java.lang.annotation.*;

/**
 * 操作日志
 *
 * <AUTHOR>
 * @date 2024/1/3
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface OperationLog {
    String category();
    String subcategory() default "";
    String desc();
    OperationLogTypeEnum type() default OperationLogTypeEnum.CREATE;
}
