package com.vida.xinye.x2.annotation;

import org.springframework.core.annotation.AliasFor;
import org.springframework.web.bind.annotation.RestController;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@RestController
public @interface ApiRestController {
    @AliasFor(
            annotation = RestController.class
    )
    String value() default "";
}
