package com.vida.xinye.x2.annotation;

import java.lang.annotation.*;

/**
 * @Description: API签名验证注解
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/11/05 17:00
 */
@Target({ ElementType.METHOD, ElementType.TYPE })
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface SignatureCheck {

  /**
   * 签名有效期（秒），默认300秒（5分钟）
   */
  int expireTime() default 300;

  /**
   * 是否必须包含IP参数
   */
  boolean requireIp() default true;

  /**
   * 签名验证失败时的提示消息
   */
  String message() default "Invalid signature";

  /**
   * 是否验证IP一致性（header中的ip与实际请求IP是否一致）
   */
  boolean verifyIpConsistency() default false;
}