package com.vida.xinye.x2.annotation;

import com.vida.xinye.x2.validator.ImageBase64Validator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = ImageBase64Validator.class)
public @interface ValidImageBase64 {
    String message() default "图片不符合要求";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
    long maxSize() default 2 * 1024 * 1024; // 2MB
}
