package com.vida.xinye.x2.annotation;

import java.lang.annotation.*;
import java.util.concurrent.TimeUnit;

/**
 * @Description: 限流注解
 * @Author: z<PERSON><PERSON>bin
 * @Date: 2025/06/03 16:15
 */
@Target({ ElementType.METHOD, ElementType.TYPE })
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RateLimit {

  /**
   * 限流唯一标识
   */
  String key() default "";

  /**
   * 限流时间窗口，默认60秒
   */
  int time() default 60;

  /**
   * 时间单位，默认秒
   */
  TimeUnit timeUnit() default TimeUnit.SECONDS;

  /**
   * 限流次数，默认10次
   */
  int count() default 10;

  /**
   * 限流类型
   */
  LimitType limitType() default LimitType.DEFAULT;

  /**
   * 限流提示消息
   */
  String message() default "访问过于频繁，请稍后再试";

  /**
   * 限流类型枚举
   */
  enum LimitType {
    /**
     * 默认策略全局限流
     */
    DEFAULT,
    /**
     * 根据请求者IP进行限流
     */
    IP,
    /**
     * 根据用户进行限流
     */
    USER
  }
}