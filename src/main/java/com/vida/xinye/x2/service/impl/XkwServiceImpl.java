package com.vida.xinye.x2.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.vida.xinye.x2.component.XopClientSingleton;
import com.vida.xinye.x2.constant.XkwConstant;
import com.vida.xinye.x2.domain.param.xkw.KeywordSearchParam;
import com.vida.xinye.x2.domain.param.xkw.ScoreImprovementRecommendParam;
import com.vida.xinye.x2.domain.param.xkw.SimilarRecommendParam;
import com.vida.xinye.x2.domain.param.xkw.XkwSearchParam;
import com.vida.xinye.x2.dto.edu.*;
import com.vida.xinye.x2.exception.Asserts;
import com.vida.xinye.x2.mbg.mapper.TrainingQuestionMapper;
import com.vida.xinye.x2.mbg.mapper.WrongQuestionMapper;
import com.vida.xinye.x2.mbg.model.TrainingQuestion;
import com.vida.xinye.x2.mbg.model.TrainingQuestionExample;
import com.vida.xinye.x2.mbg.model.WrongQuestion;
import com.vida.xinye.x2.mbg.model.WrongQuestionExample;
import com.vida.xinye.x2.service.XkwService;
import com.vida.xinye.x2.util.XkwQbmParser;
import com.xkw.xop.client.XopHttpClient;
import com.xkw.xop.qbmsdk.QuestionParser;
import com.xkw.xop.qbmsdk.Setting;
import kong.unirest.HttpResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 学科网服务接口
 *
 * <AUTHOR>
 * @date 2025/1/14
 */
@Service
@Slf4j
public class XkwServiceImpl implements XkwService {
    private XopHttpClient xopClient;
    @Autowired
    private TrainingQuestionMapper trainingQuestionMapper;

    @Autowired
    private WrongQuestionMapper wrongQuestionMapper;

    @Override
    public XopTextSearchRespVO searchJP(XkwSearchParam param) {
        return performSearch(param, XkwConstant.TEXT_SEARCH_URL);
    }

    @Override
    public XopTextSearchRespVO searchDeep(XkwSearchParam param) {
        return performSearch(param, XkwConstant.TIKU_SEARCH_URL);
    }

    @Override
    public XopSimilarRecommendRespVO similarRecommend(Long userId, SimilarRecommendParam param) {
        return similarRecommend(userId, param, null);
    }

    @Override
    public XopSimilarRecommendRespVO similarRecommend(Long userId, SimilarRecommendParam param, Integer typeId) {
        log.debug("Request param: {}", JSONUtil.toJsonStr(param));
        String url = XkwConstant.SIMILAR_RECOMMEND_URL;
        XopSimilarRecommendRespDto respDto = performRequest(url, param, XopSimilarRecommendRespDto.class);
        XopSimilarRecommendRespVO respVO = new XopSimilarRecommendRespVO();
        List<XopQuestionPushVO> questionList = buildRecommendList(respDto.getData());
        Long wrongBookId = null;
        if (CollUtil.isNotEmpty(questionList)) {
            for (XopQuestionPushVO question : questionList) {
                switch (typeId) {
                    case 1:
                        wrongBookId = this.getWrongQuestionId(userId, question.getId());
                        break;
                    case 2:
                        wrongBookId = this.getTraniningId(userId, question.getId());
                        break;
                }
                if (wrongBookId != null) {
                    question.setWrongBookId(wrongBookId);
                    question.setIsInWrongBook(true);
                } else {
                    question.setIsInWrongBook(false);
                }
            }
        }
        respVO.setList(questionList);
        return respVO;
    }

    private Long getTraniningId(Long userId, String questionId) {
        // 检查用户是否已经添加过该题，设置wrongBookId字段
        TrainingQuestionExample example = new TrainingQuestionExample();
        example.createCriteria()
                .andUserIdEqualTo(userId)
                .andXkwQuestionIdEqualTo(questionId);
        TrainingQuestion trainingQuestion = trainingQuestionMapper.selectByExample(example)
                .stream()
                .findFirst()
                .orElse(null);
        if (trainingQuestion != null) {
            return trainingQuestion.getId();
        }
        return null;
    }

    private Long getWrongQuestionId(Long userId, String questionId) {
        WrongQuestionExample example = new WrongQuestionExample();
        example.createCriteria()
                .andUserIdEqualTo(userId)
                .andXkwQuestionIdEqualTo(questionId);
        WrongQuestion wrongQuestion = wrongQuestionMapper.selectByExample(example)
                .stream()
                .findFirst()
                .orElse(null);
        if (wrongQuestion != null) {
            return wrongQuestion.getId();
        }
        return null;
    }

    // 提分训练推题方法
    @Override
    public List<XopQuestionPushVO> scoreImprovementRecommend(ScoreImprovementRecommendParam param) {
        Integer courseId = param.getCourse_id();
        Integer year = param.getYear();
        List<String> kpointIds = param.getKpoint_ids();
        List<String> typeIds = param.getType_ids();
        Integer count = param.getCount();
        List<String> paperTypeIds = param.getPaper_type_ids();
        Integer versionId = param.getVersion_id();
        String sessionId = param.getSession_id();
        Integer kpointMatchType = param.getKpoint_match_type();
        List<String> catalogIds = param.getCatalog_ids();
        List<String> difficultyLevels = param.getDifficulty_levels();
        List<String> areaIds = param.getArea_ids();
        String formulaPicFormat = param.getFormula_pic_format();
        List<String> enWordsIds = param.getEn_words_ids();
        Integer textbookId = param.getTextbook_id();

        log.debug(
                "提分训练推题请求参数: course_id={}, year={}, kpoint_ids={}, type_ids={}, count={}, paper_type_ids={}, version_id={}, session_id={}, kpoint_match_type={}, catalog_ids={}, difficulty_levels={}, area_ids={}, formula_pic_format={}, en_words_ids={}, textbook_id={}",
                courseId, year, kpointIds, typeIds, count, paperTypeIds, versionId, sessionId, kpointMatchType,
                catalogIds, difficultyLevels, areaIds, formulaPicFormat, enWordsIds, textbookId);

        // 处理参数，截取超出数量限制的部分
        if (kpointIds != null && kpointIds.size() > 10) {
            kpointIds = kpointIds.subList(0, 10);
            param.setKpoint_ids(kpointIds);
        }
        if (typeIds != null && typeIds.size() > 10) {
            typeIds = typeIds.subList(0, 10);
            param.setType_ids(typeIds);
        }
        if (count == null || count < 1) {
            count = 1;
        } else if (count > 10) {
            count = 10;
        }
        param.setCount(count);

        if (paperTypeIds != null && paperTypeIds.size() > 10) {
            paperTypeIds = paperTypeIds.subList(0, 10);
            param.setPaper_type_ids(paperTypeIds);
        }
        if (catalogIds != null && catalogIds.size() > 10) {
            catalogIds = catalogIds.subList(0, 10);
            param.setCatalog_ids(catalogIds);
        }
        if (difficultyLevels != null && difficultyLevels.size() > 5) {
            difficultyLevels = difficultyLevels.subList(0, 5);
            param.setDifficulty_levels(difficultyLevels);
        }
        if (areaIds != null && areaIds.size() > 10) {
            areaIds = areaIds.subList(0, 10);
            param.setArea_ids(areaIds);
        }
        if (enWordsIds != null && enWordsIds.size() > 10) {
            enWordsIds = enWordsIds.subList(0, 10);
            param.setEn_words_ids(enWordsIds);
        }

        // 调用第三方接口
        String url = XkwConstant.QUESTION_RECOMMENDATION_URL;
        XopQuestionRecommendRespDto respDto = performRequest(url, param, XopQuestionRecommendRespDto.class);

        // 如果返回结果为空，返回空列表
        if (respDto == null || respDto.getData() == null) {
            return new ArrayList<>();
        }

        XopQuestionRecommendRespVO respVO = new XopQuestionRecommendRespVO();
        List<XopQuestionPushVO> questionList = buildRecommendList(respDto.getData());
        respVO.setList(questionList);

        return respVO.getList();
    }

    /**
     * 关键词搜题
     *
     * @param param
     * @return
     */
    @Override
    public XopQuestionRecommendRespVO keywordSearch(KeywordSearchParam param) {
        Integer course_id = param.getCourse_id();
        Boolean highlight = param.getHighlight();
        List<Integer> area_ids = param.getArea_ids();
        String formula_pic_format = param.getFormula_pic_format();
        String keywords = param.getKeywords();
        Integer year = param.getYear();
        List<Integer> type_ids = param.getType_ids();
        Integer page_index = param.getPage_index();
        List<Integer> difficulty_levels = param.getDifficulty_levels();
        Integer page_size = param.getPage_size();

        log.debug(
                "关键词搜题请求参数: course_id={}, highlight={}, area_ids={}, formula_pic_format={}, keywords={}, year={}, type_ids={}, page_index={}, difficulty_levels={}, page_size={}",
                course_id, highlight, area_ids, formula_pic_format, keywords, year, type_ids, page_index,
                difficulty_levels, page_size);

        // 调用第三方接口
        String url = XkwConstant.QUESTION_KEYWORD_SEARCH_URL; // 假设 XkwConstant 中添加了这个常量
        XopKeywordSearchRespDto respDto = performRequest(url, param, XopKeywordSearchRespDto.class);

        XopQuestionRecommendRespVO respVO = new XopQuestionRecommendRespVO();
        List<XopQuestionPushVO> questionList = buildRecommendList(respDto.getData().getItems());
        respVO.setList(questionList);

        return respVO;
    }

    @Override
    public List<XopKnowledgePoint> getKnowledgePoints(Integer courseId) {
        String url = XkwConstant.KNOWLEDGE_POINTS_URL;
        Map<String, Object> params = new HashMap<>();
        if (courseId != null) {
            params.put("course_id", courseId);
        }
        XopKnowledgePointListRespDto respDto = performRequest(url, params, XopKnowledgePointListRespDto.class);
        return respDto.getData();
    }

    @Override
    public List<XopTextbookVersion> getTextbookVersions(Integer courseId) {
        String url = XkwConstant.TEXTBOOK_VERSIONS_URL;
        Map<String, Object> params = new HashMap<>();
        if (courseId != null) {
            params.put("course_id", courseId);
        }
        XopTextbookVersionListRespDto respDto = performRequest(url, params, XopTextbookVersionListRespDto.class);
        return respDto.getData();
    }

    @Override
    public XopPageData<XopTextbook> getTextbooks(Integer courseId, Integer gradeId, Integer versionId,
                                                 Integer pageIndex, Integer pageSize) {
        String url = XkwConstant.TEXTBOOKS_URL;
        Map<String, Object> params = new HashMap<>();
        if (courseId != null) {
            params.put("course_id", courseId);
        }
        if (gradeId != null) {
            params.put("grade_id", gradeId);
        }
        if (versionId != null) {
            params.put("version_id", versionId);
        }
        if (pageIndex != null) {
            params.put("page_index", pageIndex);
        }
        if (pageSize != null) {
            params.put("page_size", pageSize);
        }
        XopTextbookListRespDto respDto = performRequest(url, params, XopTextbookListRespDto.class);
        return respDto.getData();
    }

    @Override
    public List<XopTextbookCatalog> getTextbookCatalog(Integer textbookId) {
        String url = XkwConstant.TEXTBOOK_CATALOG_URL;
        Map<String, Object> params = new HashMap<>();
        if (textbookId != null) {
            params.put("textbook_id", textbookId);
        }
        XopTextbookCatalogListRespDto respDto = performRequest(url, params, XopTextbookCatalogListRespDto.class);
        List<XopTextbookCatalog> catalogList = respDto.getData();
        return catalogList;
    }

    @Override
    public List<XopQuestionType> getQuestionTypes(Integer courseId) {
        String url = XkwConstant.QUESTION_TYPES_URL;
        Map<String, Object> params = new HashMap<>();
        if (courseId != null) {
            params.put("course_id", courseId);
        }
        XopQuestionTypeListRespDto respDto = performRequest(url, params, XopQuestionTypeListRespDto.class);

        // 筛选出 parent_id = "0" 的二级分类
        if (respDto != null && respDto.getData() != null) {
            return respDto.getData().stream()
                    .filter(type -> "0".equals(type.getParent_id()))
                    .sorted(Comparator.comparing(XopQuestionType::getId))
                    .collect(Collectors.toList());
        }

        return new ArrayList<>();
    }

    @Override
    public List<XopQuestionDifficulty> getQuestionDifficulties() {
        String url = XkwConstant.QUESTION_DIFFICULTIES_URL;
        XopQuestionDifficultyListRespDto respDto = performRequest(url, Collections.emptyMap(),
                XopQuestionDifficultyListRespDto.class);
        return respDto.getData();
    }

    @Override
    public List<XopArea> getAllAreas() {
        String url = XkwConstant.AREAS_ALL_URL;
        XopAreaListRespDto respDto = performRequest(url, Collections.emptyMap(), XopAreaListRespDto.class);
        return respDto.getData();
    }

    @Override
    public List<XopArea> getProvinceAreas() {
        List<XopArea> allAreas = getAllAreas();
        return allAreas.stream()
                .filter(area -> "PROVINCE".equals(area.getLevel()))
                .collect(Collectors.toList());
    }

    private List<XopTextbookCatalogTree> buildCatalogTree(List<XopTextbookCatalog> catalogList) {
        Map<Integer, XopTextbookCatalogTree> nodeMap = new HashMap<>();
        List<XopTextbookCatalogTree> rootNodes = new ArrayList<>();

        // 先将所有节点放入 map 中
        for (XopTextbookCatalog catalog : catalogList) {
            XopTextbookCatalogTree treeNode = new XopTextbookCatalogTree();
            treeNode.setId(catalog.getId());
            treeNode.setName(catalog.getName());
            treeNode.setTextbookId(catalog.getTextbookId());
            treeNode.setParentId(catalog.getParentId());
            treeNode.setOrdinal(catalog.getOrdinal());
            treeNode.setType(catalog.getType());
            treeNode.setCreateTime(catalog.getCreateTime());
            treeNode.setUpdateTime(catalog.getUpdateTime());
            treeNode.setChildren(new ArrayList<>());
            nodeMap.put(catalog.getId(), treeNode);
        }

        // 构建树结构
        for (XopTextbookCatalog catalog : catalogList) {
            XopTextbookCatalogTree currentNode = nodeMap.get(catalog.getId());
            if (catalog.getParentId() == 0) {
                rootNodes.add(currentNode);
            } else {
                XopTextbookCatalogTree parentNode = nodeMap.get(catalog.getParentId());
                if (parentNode != null) {
                    parentNode.getChildren().add(currentNode);
                }
            }
        }

        return rootNodes;
    }

    private XopTextSearchRespVO performSearch(XkwSearchParam param, String url) {
        XopTextSearchRespDto respDto = performRequest(url, param, XopTextSearchRespDto.class);
        XopTextSearchRespVO respVO = new XopTextSearchRespVO();
        List<XopTextSearchVO> textSearchVOList = buildSearchResultList(respDto.getData());
        respVO.setList(textSearchVOList);
        return respVO;
    }

    private <T extends XopBaseResp> T performRequest(String url, Object param, Class<T> clazz) {
        xopClient = XopClientSingleton.getInstance();
        HttpResponse<String> response;
        if (param instanceof Map) {
            log.info("xopClient.get..., url: {}, param: {}", url);
            response = xopClient.get(url, (Map<String, Object>) param);
        } else {
            log.info("xopClient.post..., url: {}, param: {}", url);
            response = xopClient.post(url, null, param);
        }
        String body = response.getBody();
        log.debug("Response body: {}", JSONUtil.toJsonStr(body));
        T respDto = JSONUtil.toBean(body, clazz);
        try {
            checkResponse(respDto);
        } catch (Exception e) {
            log.error("响应验证失败：{}", e.getMessage(), e);
            Asserts.fail(e.getMessage() != null ? e.getMessage() : "响应验证失败");
        }
        return respDto;
    }

    private List<XopTextSearchVO> buildSearchResultList(List<XopTextSearchData> dataList) {
        List<XopTextSearchVO> result = new ArrayList<>();
        for (XopTextSearchData data : dataList) {
            XopTextSearchVO searchVO = new XopTextSearchVO();
            searchVO.setId(data.getId());
            searchVO.setKnowledge(formatKnowledge(data.getKpoints()));
            searchVO.setContentVOS(buildContentVOS(data));
            result.add(searchVO);
        }
        return result;
    }

    private List<XopTextSearchContentVO> buildContentVOS(XopTextSearchData data) {
        List<XopTextSearchContentVO> contentVOS = new ArrayList<>();
        try {
            // 题目
            XopTextSearchContentVO questionContent = new XopTextSearchContentVO();
            questionContent.setName("题目");
            String stemHtml = "";
            try {
                stemHtml = XkwQbmParser.formatStem(data.getStem());
            } catch (Exception ex) {
                stemHtml = data.getStem();
            }
            questionContent.setContent("<div style='font-size:1.23em; font-weight:bold'>【题目】</div>" + stemHtml);
            contentVOS.add(questionContent);

            // 考点
            if (CollUtil.isNotEmpty(data.getKpoints())) {
                XopTextSearchContentVO knowledgeContent = new XopTextSearchContentVO();
                knowledgeContent.setName("考点");
                knowledgeContent
                        .setContent("<div style='font-size:1.23em; font-weight:bold; margin-top:1.28em;'>【考点】</div>" +
                                "<div style='margin-top:8px'>" + formatKnowledge(data.getKpoints()) + "</div>");
                contentVOS.add(knowledgeContent);
            }

            // 分析
            XopTextSearchContentVO analysisContent = new XopTextSearchContentVO();
            analysisContent.setName("分析");
            analysisContent.setContent(data.getExplanation()
                    .replace("【分析】", "<div style='font-size:1.23em; font-weight:bold; margin-top:1.28em;'>【分析】</div>")
                    .replace("【详解】", "<div style='font-size:1.23em; font-weight:bold; margin-top:1.28em;'>【详解】</div>"));
            contentVOS.add(analysisContent);

            // 解答
            XopTextSearchContentVO answerContent = new XopTextSearchContentVO();
            answerContent.setName("解答");
            answerContent.setContent("<div style='font-size:1.23em; font-weight:bold; margin-top:1.28em;'>【解答】</div>" +
                    "<div style='margin-top:8px'>" + data.getAnswer() + "</div>");
            contentVOS.add(answerContent);
        } catch (Exception ex) {
            ex.printStackTrace();
        }

        return contentVOS;
    }

    private String formatKnowledge(List<IdNamePair> kpoints) {
        if (CollUtil.isEmpty(kpoints)) {
            return "";
        }
        return kpoints.stream().map(IdNamePair::getName).reduce((a, b) -> a + " " + b).orElse("");
    }

    private List<XopQuestionPushVO> buildRecommendList(List<XopSimilarRecommendData> dataList) {
        List<XopQuestionPushVO> result = new ArrayList<>();
        QuestionParser parser = new QuestionParser(new Setting());
        for (XopSimilarRecommendData data : dataList) {
            XopQuestionPushVO questionVO = new XopQuestionPushVO();
            questionVO.setId(data.getId());

            XopQuestion question = new XopQuestion();
            question.setStemHtml(XkwQbmParser.formatStem(data.getStem()));
            question.setAnswerHtml(data.getAnswer());
            if (StrUtil.isNotEmpty(data.getExplanation())) {
                question.setExplanationHtml(data.getExplanation().replaceAll("【详解】", "").replace("【分析】", ""));
            }
            questionVO.setQuestion(question);
            questionVO.setCourseId(String.valueOf(data.getCourse_id()));
            questionVO.setType(data.getType());

            if (CollUtil.isNotEmpty(data.getKpoints())) {
                question.setKpointsHtml("<div>" +
                        data.getKpoints().stream()
                                .map(kpoint -> "<span class=\"kpoint\">" + kpoint.getName() + "</span>")
                                .reduce((a, b) -> a + b).orElse("")
                        +
                        "</div>");
            }

            questionVO.setDifficultyLevel(data.getDifficulty_level());
            result.add(questionVO);
        }
        return result;
    }

    /**
     * 校验学科网接口响应
     * 状态码规则：
     * - 200 = 成功
     * - 2000000 = 成功
     * - 900161214 = 成功但是返回数为空，不计费
     * - 900161500 = 服务器端异常
     * - 900161403 = 禁止访问
     * - 900161400 = 请求参数错误
     * - 900161401 = 未授权
     * - 900161404 = 访问的资源不存在
     * - 900开头表示错误状态，最后三位是具体错误码
     *
     * @param baseResp 响应对象
     * @throws Exception 校验失败时抛出异常
     */
    private void checkResponse(XopBaseResp baseResp) throws Exception {
        if (baseResp == null) {
            throw new Exception("响应对象为空");
        }

        String code = baseResp.getCode();
        if (StrUtil.isEmpty(code)) {
            throw new Exception("响应状态码为空");
        }

        // 成功状态码
        if ("200".equals(code) || "2000000".equals(code) || "900161214".equals(code)) {
            log.debug("学科网接口调用成功，状态码: {}", code);
            return;
        }

        // 错误状态码处理
        String errorMessage = getErrorMessage(code, baseResp.getMsg());
        log.error("学科网接口调用失败，状态码: {}, 错误信息: {}", code, errorMessage);
        throw new Exception(errorMessage);
    }

    /**
     * 根据状态码获取错误信息
     *
     * @param code 状态码
     * @param msg  原始错误信息
     * @return 格式化后的错误信息
     */
    private String getErrorMessage(String code, String msg) {
        String errorMessage;
        // 如果有原始错误信息，追加显示
        if (StrUtil.isNotEmpty(msg)) {
            errorMessage = msg;
        } else {
            switch (code) {
                case "900161500":
                    errorMessage = "学科网服务器端异常";
                    break;
                case "900161403":
                    errorMessage = "学科网接口禁止访问";
                    break;
                case "900161400":
                    errorMessage = "学科网接口请求参数错误";
                    break;
                case "900161401":
                    errorMessage = "学科网接口未授权";
                    break;
                case "900161404":
                    errorMessage = "学科网接口访问的资源不存在";
                    break;
                default:
                    if (code.startsWith("900")) {
                        // 900开头的其他错误码
                        String specificCode = code.length() >= 3 ? code.substring(code.length() - 3) : code;
                        errorMessage = String.format("学科网接口调用失败，错误码: %s", specificCode);
                    } else {
                        errorMessage = "学科网接口调用失败，未知状态码: " + code;
                    }
                    break;
            }
        }

        return errorMessage;
    }

}