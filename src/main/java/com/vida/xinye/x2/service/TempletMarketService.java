package com.vida.xinye.x2.service;

import com.vida.xinye.x2.api.CommonPage;
import com.vida.xinye.x2.dto.TempletMarketDto;
import org.springframework.stereotype.Service;

/**
 * 标签服务接口
 *
 * <AUTHOR>
 * @date 2025/06/27
 */
@Service
public interface TempletMarketService {
    /**
     * 分页获取集市模板列表
     */
    CommonPage<TempletMarketDto> list(Integer pageSize, Integer pageNum, Long groupId);

    /**
     * 新增集市模板
     *
     * @return
     */
    Long create(TempletMarketDto materialParam);

    /**
     * 修改集市模板
     */
    int update(Long id, TempletMarketDto param);

    /**
     * 删除集市模板
     */
    int delete(Long templetId);

}
