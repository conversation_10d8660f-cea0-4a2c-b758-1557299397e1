package com.vida.xinye.x2.service.admin;

import cn.hutool.core.util.StrUtil;
import com.vida.xinye.x2.component.JwtTokenProvider;
import com.vida.xinye.x2.dto.TokenDto;
import com.vida.xinye.x2.dto.UserDto;
import com.vida.xinye.x2.dto.param.UserAuthenDto;
import com.vida.xinye.x2.exception.Asserts;
import com.vida.xinye.x2.mbg.mapper.SysUserMapper;
import com.vida.xinye.x2.mbg.model.SysUser;
import com.vida.xinye.x2.util.PasswordUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

/**
 * 后台管理系统
 * 身份认证 服务Service。
 *
 * <AUTHOR>
 * @date 2024/11/4
 */
@Service("adminAuthService")
@Slf4j
public class AuthService {

    @Autowired
    private AuthenticationManager authenticationManager;
    @Resource
    private SysUserMapper sysUserMapper;
    @Autowired
    private JwtTokenProvider jwtTokenProvider;


    /**
     * 根据用户名、密码登录。
     *
     * @param loginDto 用户名、密码
     * @return TokenDto
     */
    public TokenDto login(UserAuthenDto loginDto) {
        String username = loginDto.getUsername();
        String password = loginDto.getPassword();
        try {
            validateUsernameAndPassword(username, password);

            Authentication authentication = authenticationManager.authenticate(new UsernamePasswordAuthenticationToken(
                    username, password));
            SecurityContextHolder.getContext().setAuthentication(authentication);
            UserDto user = (UserDto) authentication.getPrincipal();

            // 更新用户登录时间
            SysUser updateUser = new SysUser();
            updateUser.setId(user.getId());
            updateUser.setLastLoginTime(new Date());
            sysUserMapper.updateByPrimaryKeySelective(updateUser);

            log.debug("登录成功，用户名：{}", username);
            return createTokenDto(user);
        } catch (UsernameNotFoundException e) {
            Asserts.fail("登录失败，账号不存在！");
        } catch (BadCredentialsException e) {
            log.debug("登录失败，账号不存在或密码错误！ username:{}, password:{}", username, PasswordUtil.maskPassword(password));
            Asserts.fail("账号不存在或密码错误！");
        } catch (Exception e) {
            log.debug("登录失败: {}！ username:{}", Objects.toString(e.getMessage(), e.toString()), username);
            Asserts.fail("登录失败！");
        }

        return null;
    }

    private void validateUsernameAndPassword(String username, String password) {
        if (StrUtil.isEmpty(username) || StrUtil.isEmpty(password)) {
            Asserts.fail("用户名或密码不能为空！");
        }
    }

    private TokenDto createTokenDto(UserDto user) {
        String token = jwtTokenProvider.generateToken(user.getUsername());
        TokenDto tokenDto = new TokenDto();
        tokenDto.setUserId(user.getId());
        tokenDto.setUsername(user.getUsername());
        tokenDto.setAccessToken(token);
        Date expiration = jwtTokenProvider.extractExpiration(token);
        tokenDto.setExpiration(expiration.getTime());
        return tokenDto;
    }

}
