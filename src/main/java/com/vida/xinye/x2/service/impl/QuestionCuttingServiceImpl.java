package com.vida.xinye.x2.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.vida.xinye.x2.adapter.QuestionCuttingAdapter;
import com.vida.xinye.x2.config.QuestionCuttingConfig;
import com.vida.xinye.x2.constant.QuestionCuttingConstant;
import com.vida.xinye.x2.dto.BatchQuestionCuttingResultDto;
import com.vida.xinye.x2.dto.QuestionCuttingResultDto;
import com.vida.xinye.x2.dto.internal.ThirdPartyRequestDto;
import com.vida.xinye.x2.dto.param.QuestionCuttingParam;
import com.vida.xinye.x2.enums.QuestionCuttingEnum;
import com.vida.xinye.x2.exception.Asserts;
import com.vida.xinye.x2.service.QuestionCuttingService;
import com.vida.xinye.x2.strategy.CuttingStrategyManager;
import com.vida.xinye.x2.util.ImageUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * 智能切题服务实现类
 *
 * <AUTHOR>
 * @date 2025/08/14
 */
@Slf4j
@Service
public class QuestionCuttingServiceImpl implements QuestionCuttingService {

    @Autowired
    private QuestionCuttingConfig config;

    @Autowired
    private CuttingStrategyManager strategyManager;

    @Autowired
    private List<QuestionCuttingAdapter> adapters;

    private final ExecutorService executorService = Executors.newFixedThreadPool(20);

    @Override
    public QuestionCuttingResultDto cutQuestion(QuestionCuttingParam param) {
        String requestId = IdUtil.simpleUUID();
        log.info("开始切题处理 - 请求ID: {}, 策略: {}, 提供商: {}", requestId, param.getStrategy(), param.getProvider());

        try {
            // 参数验证
            validateCuttingParam(param);

            // 构建内部请求对象
            ThirdPartyRequestDto request = buildThirdPartyRequest(param, requestId);

            // 应用默认配置
            String strategy = applyDefaultStrategy(param.getStrategy());
            String provider = applyDefaultProvider(param.getProvider(), strategy);

            // 执行切题策略
            QuestionCuttingResultDto result = strategyManager.executeCutting(
                request,
                strategy,
                provider
            );

            log.info("切题处理完成 - 请求ID: {}, 成功: {}, 题目数量: {}", 
                    requestId, result.getSuccess(), 
                    result.getQuestions() != null ? result.getQuestions().size() : 0);

            return result;

        } catch (Exception e) {
            log.error("切题处理失败 - 请求ID: {}, 错误: {}", requestId, e.getMessage(), e);
            
            QuestionCuttingResultDto result = new QuestionCuttingResultDto();
            result.setRequestId(requestId);
            result.setSuccess(false);
            result.setErrorCode(QuestionCuttingConstant.ErrorCode.INTERNAL_ERROR);
            result.setErrorMessage("切题处理失败: " + e.getMessage());
            result.setCreateTime(new Date());
            return result;
        }
    }

    @Override
    public BatchQuestionCuttingResultDto batchCutQuestion(
            List<MultipartFile> images,
            String strategy,
            String provider,
            String subject,
            String grade,
            Boolean needDetail,
            Boolean enableCache,
            Boolean parallel) {

        String batchId = IdUtil.simpleUUID();
        log.info("开始批量切题处理 - 批次ID: {}, 图片数量: {}, 策略: {}, 并行: {}", 
                batchId, images.size(), strategy, parallel);

        BatchQuestionCuttingResultDto batchResult = new BatchQuestionCuttingResultDto();
        batchResult.setBatchId(batchId);
        batchResult.setRequestId(batchId);
        batchResult.setTotalCount(images.size());
        batchResult.setCreateTime(new Date());

        try {
            // 参数验证
            validateBatchParams(images, strategy);

            // 应用默认配置
            String finalStrategy = applyDefaultStrategy(strategy);
            String finalProvider = applyDefaultProvider(provider, finalStrategy);

            List<BatchQuestionCuttingResultDto.ImageResult> imageResults = new ArrayList<>();

            if (Boolean.TRUE.equals(parallel)) {
                // 并行处理
                imageResults = processBatchParallel(images, finalStrategy, finalProvider, subject, grade, needDetail, enableCache);
            } else {
                // 串行处理
                imageResults = processBatchSequential(images, finalStrategy, finalProvider, subject, grade, needDetail, enableCache);
            }

            batchResult.setImageResults(imageResults);
            batchResult.setCompleteTime(new Date());

            // 计算统计信息
            calculateBatchStats(batchResult);

            log.info("批量切题处理完成 - 批次ID: {}, 成功: {}/{}", 
                    batchId, batchResult.getSuccessCount(), batchResult.getTotalCount());

        } catch (Exception e) {
            log.error("批量切题处理失败 - 批次ID: {}, 错误: {}", batchId, e.getMessage(), e);
            batchResult.setSuccess(false);
            batchResult.setCompleteTime(new Date());
        }

        return batchResult;
    }

    @Override
    public QuestionCuttingResultDto cutQuestionByUrl(
            String imageUrl,
            String strategy,
            String provider,
            String subject,
            String grade,
            Boolean needDetail,
            Boolean enableCache) {

        QuestionCuttingParam param = new QuestionCuttingParam();
        param.setImageUrl(imageUrl);
        param.setStrategy(applyDefaultStrategy(strategy));
        param.setProvider(applyDefaultProvider(provider, applyDefaultStrategy(strategy)));
        param.setSubject(subject);
        param.setGrade(grade);
        param.setNeedDetail(needDetail);
        param.setEnableCache(enableCache);

        return cutQuestion(param);
    }

    @Override
    public QuestionCuttingResultDto cutQuestionByBase64(
            String imageBase64,
            String strategy,
            String provider,
            String subject,
            String grade,
            Boolean needDetail,
            Boolean enableCache) {

        QuestionCuttingParam param = new QuestionCuttingParam();
        param.setImageBase64(imageBase64);
        param.setStrategy(applyDefaultStrategy(strategy));
        param.setProvider(applyDefaultProvider(provider, applyDefaultStrategy(strategy)));
        param.setSubject(subject);
        param.setGrade(grade);
        param.setNeedDetail(needDetail);
        param.setEnableCache(enableCache);

        return cutQuestion(param);
    }

    @Override
    public List<String> getSupportedProviders() {
        return adapters.stream()
            .map(adapter -> adapter.getProvider().getCode())
            .collect(Collectors.toList());
    }

    @Override
    public List<String> getSupportedStrategies() {
        return Arrays.asList(
            QuestionCuttingConstant.Strategy.BEST_QUALITY,
            QuestionCuttingConstant.Strategy.FASTEST,
            QuestionCuttingConstant.Strategy.AGGREGATED,
            QuestionCuttingConstant.Strategy.SPECIFIED,
            QuestionCuttingConstant.Strategy.ROUND_ROBIN
        );
    }

    @Override
    public boolean checkProviderStatus(String provider) {
        return adapters.stream()
            .filter(adapter -> adapter.getProvider().getCode().equals(provider))
            .findFirst()
            .map(QuestionCuttingAdapter::isAvailable)
            .orElse(false);
    }

    @Override
    public Map<String, Object> getProviderStats() {
        Map<String, Object> stats = new HashMap<>();
        
        for (QuestionCuttingAdapter adapter : adapters) {
            Map<String, Object> providerStats = new HashMap<>();
            providerStats.put("available", adapter.isAvailable());
            providerStats.put("priority", adapter.getPriority());
            providerStats.put("qualityScore", adapter.getQualityScore());
            providerStats.put("averageResponseTime", adapter.getAverageResponseTime());
            
            stats.put(adapter.getProvider().getCode(), providerStats);
        }
        
        return stats;
    }

    /**
     * 验证切题参数
     */
    private void validateCuttingParam(QuestionCuttingParam param) {
        if (param == null) {
            Asserts.fail("切题参数不能为空");
        }

        // 检查图片输入
        boolean hasImageFile = param.getImageFile() != null && !param.getImageFile().isEmpty();
        boolean hasImageUrl = StrUtil.isNotBlank(param.getImageUrl());
        boolean hasImageBase64 = StrUtil.isNotBlank(param.getImageBase64());

        if (!hasImageFile && !hasImageUrl && !hasImageBase64) {
            Asserts.fail("必须提供图片文件、图片URL或图片Base64编码中的一种");
        }

        // 验证图片文件
        if (hasImageFile) {
            validateImageFile(param.getImageFile());
        }

        // 验证策略
        if (StrUtil.isNotBlank(param.getStrategy())) {
            if (!getSupportedStrategies().contains(param.getStrategy())) {
                Asserts.fail("不支持的切题策略: " + param.getStrategy());
            }
        }

        // 验证提供商
        if (StrUtil.isNotBlank(param.getProvider())) {
            if (!getSupportedProviders().contains(param.getProvider())) {
                Asserts.fail("不支持的提供商: " + param.getProvider());
            }
        }
    }

    /**
     * 验证批量参数
     */
    private void validateBatchParams(List<MultipartFile> images, String strategy) {
        if (images == null || images.isEmpty()) {
            Asserts.fail("图片列表不能为空");
        }

        if (images.size() > 10) {
            Asserts.fail("批量处理最多支持10张图片");
        }

        // 验证每张图片
        for (int i = 0; i < images.size(); i++) {
            try {
                validateImageFile(images.get(i));
            } catch (Exception e) {
                Asserts.fail("第" + (i + 1) + "张图片验证失败: " + e.getMessage());
            }
        }
    }

    /**
     * 验证图片文件
     */
    private void validateImageFile(MultipartFile imageFile) {
        if (imageFile == null || imageFile.isEmpty()) {
            Asserts.fail("图片文件不能为空");
        }

        // 检查文件大小
        if (imageFile.getSize() > config.getCommon().getMaxImageSize()) {
            Asserts.fail("图片文件过大，最大支持: " + (config.getCommon().getMaxImageSize() / 1024 / 1024) + "MB");
        }

        // 检查文件格式
        String originalFilename = imageFile.getOriginalFilename();
        if (StrUtil.isBlank(originalFilename)) {
            Asserts.fail("无法获取图片文件名");
        }

        String extension = originalFilename.substring(originalFilename.lastIndexOf(".") + 1).toLowerCase();
        if (!QuestionCuttingEnum.ImageFormat.isSupported(extension)) {
            Asserts.fail("不支持的图片格式: " + extension);
        }
    }

    /**
     * 构建第三方请求对象
     */
    private ThirdPartyRequestDto buildThirdPartyRequest(QuestionCuttingParam param, String requestId) {
        ThirdPartyRequestDto request = new ThirdPartyRequestDto();
        request.setRequestId(requestId);
        request.setSubject(param.getSubject());
        request.setGrade(param.getGrade());
        request.setTimeoutSeconds(param.getTimeoutSeconds());
        request.setExtraParams(param.getExtraParams());

        // 处理图片数据
        if (param.getImageFile() != null && !param.getImageFile().isEmpty()) {
            try {
                String base64 = ImageUtil.convertToBase64(param.getImageFile());
                request.setImageBase64(base64);
                request.setImageFormat(getImageFormat(param.getImageFile().getOriginalFilename()));
            } catch (Exception e) {
                Asserts.fail("图片文件处理失败: " + e.getMessage());
            }
        } else if (StrUtil.isNotBlank(param.getImageUrl())) {
            request.setImageUrl(param.getImageUrl());
        } else if (StrUtil.isNotBlank(param.getImageBase64())) {
            request.setImageBase64(param.getImageBase64());
        }

        return request;
    }

    /**
     * 获取图片格式
     */
    private String getImageFormat(String filename) {
        if (StrUtil.isBlank(filename)) {
            return "jpg";
        }
        
        int lastDotIndex = filename.lastIndexOf(".");
        if (lastDotIndex == -1) {
            return "jpg";
        }
        
        return filename.substring(lastDotIndex + 1).toLowerCase();
    }

    /**
     * 并行处理批量图片
     */
    private List<BatchQuestionCuttingResultDto.ImageResult> processBatchParallel(
            List<MultipartFile> images,
            String strategy,
            String provider,
            String subject,
            String grade,
            Boolean needDetail,
            Boolean enableCache) {

        List<CompletableFuture<BatchQuestionCuttingResultDto.ImageResult>> futures = new ArrayList<>();

        for (int i = 0; i < images.size(); i++) {
            final int index = i;
            final MultipartFile image = images.get(i);

            CompletableFuture<BatchQuestionCuttingResultDto.ImageResult> future =
                CompletableFuture.supplyAsync(() -> {
                    return processImage(image, index, strategy, provider, subject, grade, needDetail, enableCache);
                }, executorService);

            futures.add(future);
        }

        return futures.stream()
            .map(CompletableFuture::join)
            .collect(Collectors.toList());
    }

    /**
     * 串行处理批量图片
     */
    private List<BatchQuestionCuttingResultDto.ImageResult> processBatchSequential(
            List<MultipartFile> images,
            String strategy,
            String provider,
            String subject,
            String grade,
            Boolean needDetail,
            Boolean enableCache) {

        List<BatchQuestionCuttingResultDto.ImageResult> results = new ArrayList<>();

        for (int i = 0; i < images.size(); i++) {
            BatchQuestionCuttingResultDto.ImageResult result =
                processImage(images.get(i), i, strategy, provider, subject, grade, needDetail, enableCache);
            results.add(result);
        }

        return results;
    }

    /**
     * 处理单张图片
     */
    private BatchQuestionCuttingResultDto.ImageResult processImage(
            MultipartFile image,
            int index,
            String strategy,
            String provider,
            String subject,
            String grade,
            Boolean needDetail,
            Boolean enableCache) {

        BatchQuestionCuttingResultDto.ImageResult imageResult = new BatchQuestionCuttingResultDto.ImageResult();
        imageResult.setIndex(index);
        imageResult.setImageId("image_" + index);
        imageResult.setStartTime(new Date());

        try {
            // 构建切题参数
            QuestionCuttingParam param = new QuestionCuttingParam();
            param.setImageFile(image);
            param.setStrategy(strategy);
            param.setProvider(provider);
            param.setSubject(subject);
            param.setGrade(grade);
            param.setNeedDetail(needDetail);
            param.setEnableCache(enableCache);

            // 执行切题
            QuestionCuttingResultDto result = cutQuestion(param);

            imageResult.setSuccess(result.getSuccess());
            imageResult.setResult(result);

            if (!result.getSuccess()) {
                imageResult.setErrorCode(result.getErrorCode());
                imageResult.setErrorMessage(result.getErrorMessage());
            }

        } catch (Exception e) {
            log.error("处理第{}张图片失败: {}", index + 1, e.getMessage(), e);
            imageResult.setSuccess(false);
            imageResult.setErrorCode(QuestionCuttingConstant.ErrorCode.INTERNAL_ERROR);
            imageResult.setErrorMessage("图片处理失败: " + e.getMessage());
        }

        imageResult.setEndTime(new Date());
        imageResult.setProcessingTime(imageResult.getEndTime().getTime() - imageResult.getStartTime().getTime());

        return imageResult;
    }

    /**
     * 计算批量处理统计信息
     */
    private void calculateBatchStats(BatchQuestionCuttingResultDto batchResult) {
        List<BatchQuestionCuttingResultDto.ImageResult> imageResults = batchResult.getImageResults();

        if (imageResults == null || imageResults.isEmpty()) {
            batchResult.setSuccess(false);
            batchResult.setSuccessCount(0);
            batchResult.setFailureCount(batchResult.getTotalCount());
            return;
        }

        // 计算成功和失败数量
        int successCount = (int) imageResults.stream().filter(BatchQuestionCuttingResultDto.ImageResult::getSuccess).count();
        int failureCount = imageResults.size() - successCount;

        batchResult.setSuccessCount(successCount);
        batchResult.setFailureCount(failureCount);
        batchResult.setSuccess(successCount > 0);

        // 计算总处理时间
        long totalProcessingTime = imageResults.stream()
            .filter(r -> r.getProcessingTime() != null)
            .mapToLong(BatchQuestionCuttingResultDto.ImageResult::getProcessingTime)
            .sum();
        batchResult.setTotalProcessingTime(totalProcessingTime);

        // 构建详细统计信息
        BatchQuestionCuttingResultDto.BatchStats batchStats = new BatchQuestionCuttingResultDto.BatchStats();

        // 平均处理时间
        if (successCount > 0) {
            double avgProcessingTime = imageResults.stream()
                .filter(BatchQuestionCuttingResultDto.ImageResult::getSuccess)
                .filter(r -> r.getProcessingTime() != null)
                .mapToLong(BatchQuestionCuttingResultDto.ImageResult::getProcessingTime)
                .average()
                .orElse(0.0);
            batchStats.setAverageProcessingTime(avgProcessingTime);
        }

        // 最快和最慢处理时间
        imageResults.stream()
            .filter(BatchQuestionCuttingResultDto.ImageResult::getSuccess)
            .filter(r -> r.getProcessingTime() != null)
            .mapToLong(BatchQuestionCuttingResultDto.ImageResult::getProcessingTime)
            .min()
            .ifPresent(batchStats::setFastestProcessingTime);

        imageResults.stream()
            .filter(BatchQuestionCuttingResultDto.ImageResult::getSuccess)
            .filter(r -> r.getProcessingTime() != null)
            .mapToLong(BatchQuestionCuttingResultDto.ImageResult::getProcessingTime)
            .max()
            .ifPresent(batchStats::setSlowestProcessingTime);

        // 成功率
        batchStats.setSuccessRate((double) successCount / imageResults.size());

        // 总题目数量
        int totalQuestionCount = imageResults.stream()
            .filter(BatchQuestionCuttingResultDto.ImageResult::getSuccess)
            .filter(r -> r.getResult() != null && r.getResult().getQuestions() != null)
            .mapToInt(r -> r.getResult().getQuestions().size())
            .sum();
        batchStats.setTotalQuestionCount(totalQuestionCount);

        // 平均质量评分
        if (successCount > 0) {
            double avgQualityScore = imageResults.stream()
                .filter(BatchQuestionCuttingResultDto.ImageResult::getSuccess)
                .filter(r -> r.getResult() != null && r.getResult().getQualityScore() != null)
                .mapToInt(r -> r.getResult().getQualityScore())
                .average()
                .orElse(0.0);
            batchStats.setAverageQualityScore(avgQualityScore);
        }

        // 平均置信度
        if (successCount > 0) {
            double avgConfidence = imageResults.stream()
                .filter(BatchQuestionCuttingResultDto.ImageResult::getSuccess)
                .filter(r -> r.getResult() != null && r.getResult().getConfidence() != null)
                .mapToDouble(r -> r.getResult().getConfidence())
                .average()
                .orElse(0.0);
            batchStats.setAverageConfidence(avgConfidence);
        }

        // 提供商使用统计
        Map<String, Integer> providerUsageStats = new HashMap<>();
        imageResults.stream()
            .filter(BatchQuestionCuttingResultDto.ImageResult::getSuccess)
            .filter(r -> r.getResult() != null && r.getResult().getProvider() != null)
            .forEach(r -> {
                String provider = r.getResult().getProvider();
                providerUsageStats.put(provider, providerUsageStats.getOrDefault(provider, 0) + 1);
            });
        batchStats.setProviderUsageStats(providerUsageStats);

        // 错误统计
        Map<String, Integer> errorStats = new HashMap<>();
        imageResults.stream()
            .filter(r -> !r.getSuccess())
            .filter(r -> r.getErrorCode() != null)
            .forEach(r -> {
                String errorCode = String.valueOf(r.getErrorCode().getCode());
                errorStats.put(errorCode, errorStats.getOrDefault(errorCode, 0) + 1);
            });
        batchStats.setErrorStats(errorStats);

        batchResult.setBatchStats(batchStats);
    }

    /**
     * 应用默认策略配置
     */
    private String applyDefaultStrategy(String strategy) {
        if (StrUtil.isBlank(strategy)) {
            String defaultStrategy = config.getCommon().getDefaultStrategy();
            // 如果配置文件中也没有配置，使用常量中的默认值
            return StrUtil.isNotBlank(defaultStrategy) ? defaultStrategy : QuestionCuttingConstant.Default.DEFAULT_STRATEGY;
        }
        return strategy;
    }

    /**
     * 应用默认提供商配置
     */
    private String applyDefaultProvider(String provider, String strategy) {
        // 如果明确指定了提供商，直接返回
        if (StrUtil.isNotBlank(provider)) {
            return provider;
        }

        // 如果没有指定提供商，检查是否配置了默认提供商
        String defaultProvider = config.getCommon() != null ? config.getCommon().getDefaultProvider() : null;
        if (StrUtil.isBlank(defaultProvider)) {
            defaultProvider = QuestionCuttingConstant.Default.DEFAULT_PROVIDER;
        }

        // 如果配置了默认提供商，则使用它
        if (StrUtil.isNotBlank(defaultProvider)) {
            log.info("使用配置的默认提供商: {}", defaultProvider);
            return defaultProvider;
        }

        // 没有配置默认提供商，返回null（让策略自己选择）
        return null;
    }
}
