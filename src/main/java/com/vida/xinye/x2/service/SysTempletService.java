package com.vida.xinye.x2.service;

import com.vida.xinye.x2.api.CommonPage;
import com.vida.xinye.x2.domain.param.SysTempletParam;
import com.vida.xinye.x2.dto.SystemTempletDto;
import org.springframework.stereotype.Service;

/**
 * 系统标签服务接口
 *
 * <AUTHOR>
 * @date 2025/06/27
 */
@Service
public interface SysTempletService {
    /**
     * 分页获取系统标签（标签宝箱）
     */
    CommonPage<SystemTempletDto> listSystemTemples(Long groupId, Integer pageSize, Integer pageNum);

    /**
     * 新增系统标签（标签宝箱 管理端用）
     */
    Long createSystemTemplet(SysTempletParam param);

    /**
     * 更新系统标签（标签宝箱 管理端用）
     */
    int updateSystemTemplet(Long id, SysTempletParam param);

    /**
     * 删除系统标签（标签宝箱 管理端用）
     */
    int deleteSystemTemplet(Long id);

}
