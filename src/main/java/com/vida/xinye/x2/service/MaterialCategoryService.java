package com.vida.xinye.x2.service;

import com.vida.xinye.x2.domain.param.MaterialCategoryParam;
import com.vida.xinye.x2.mbg.model.MaterialCategory;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 素材服务接口
 *
 * <AUTHOR>
 * @date 2024/11/11
 */
@Service
public interface MaterialCategoryService {
    /**
     * 获取素材类别列表
     */
    List<MaterialCategory> list();

    /**
     * 新增素材
     */
    Long create(MaterialCategoryParam materialParam);

    /**
     * 修改素材
     */
    int update(Long id, MaterialCategoryParam param);

    /**
     * 删除素材
     */
    int delete(Long materialId);

}
