package com.vida.xinye.x2.service;

import com.vida.xinye.x2.dto.param.TempletGroupParam;
import com.vida.xinye.x2.mbg.model.TempletGroup;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface TempletGroupService {

    /**
     * 系统标签分组列表
     */
    List<TempletGroup> listGroups();

    /**
     * 创建系统标签分组
     */
    Long createGroup(TempletGroupParam param);

    /**
     * 更新系统标签分组
     */
    int updateGroup(Long id, TempletGroupParam param);

    /**
     * 删除系统标签分组
     */
    int deleteGroup(Long id);

    /**
     * 分组排序
     */
    @Transactional
    int sortGroups(List<Long> groupIds);
}
