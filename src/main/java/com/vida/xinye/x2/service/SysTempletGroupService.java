package com.vida.xinye.x2.service;

import com.vida.xinye.x2.dto.SystemTempletGroupDto;
import com.vida.xinye.x2.dto.param.SysTempletGroupParam;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface SysTempletGroupService {

    /**
     * 系统标签分组列表
     */
    List<SystemTempletGroupDto> listGroups(String locale);

    /**
     * 创建系统标签分组
     */
    Long createGroup(SysTempletGroupParam param);

    /**
     * 更新系统标签分组
     */
    int updateGroup(Long id, SysTempletGroupParam param);

    /**
     * 删除系统标签分组
     */
    int deleteGroup(Long id);

    /**
     * 分组排序
     */
    @Transactional
    int sortGroups(List<Long> groupIds);
}
