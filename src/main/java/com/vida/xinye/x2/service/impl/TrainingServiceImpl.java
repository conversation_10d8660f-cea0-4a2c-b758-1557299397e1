package com.vida.xinye.x2.service.impl;

import com.vida.xinye.x2.domain.param.edu.AddTrainingQuestionParam;
import com.vida.xinye.x2.domain.param.xkw.ScoreImprovementRecommendParam;
import com.vida.xinye.x2.dto.edu.CourseWithWrongQuestionCountDTO;
import com.vida.xinye.x2.dto.edu.TrainingQuestionDto;
import com.vida.xinye.x2.dto.edu.XopQuestionPushVO;
import com.vida.xinye.x2.exception.Asserts;
import com.vida.xinye.x2.mbg.mapper.*;
import com.vida.xinye.x2.mbg.model.*;
import com.vida.xinye.x2.service.TrainingService;
import com.vida.xinye.x2.service.XkwService;
import com.vida.xinye.x2.util.TimeRangeUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import cn.hutool.core.collection.CollUtil;

import com.github.pagehelper.PageHelper;
import com.vida.xinye.x2.api.CommonPage;

@Service
public class TrainingServiceImpl implements TrainingService {

    @Autowired
    private XkwStageMapper xkwStageMapper;
    @Autowired
    private XkwSubjectMapper xkwSubjectMapper;
    @Autowired
    private XkwCourseMapper xkwCourseMapper;
    @Autowired
    private XkwGradeMapper xkwGradeMapper;
    @Autowired
    private XkwTextbookVersionMapper xkwTextbookVersionMapper;
    @Autowired
    private TrainingQuestionMapper trainingQuestionMapper;
    @Autowired
    private XkwService xkwService;

    @Override
    public List<XkwStage> getStages() {
        XkwStageExample example = new XkwStageExample();
        return xkwStageMapper.selectByExample(example);
    }

    @Override
    public List<XkwSubject> getSubjects() {
        XkwSubjectExample example = new XkwSubjectExample();
        return xkwSubjectMapper.selectByExample(example);
    }

    @Override
    public List<XkwCourse> getCourses(Integer stageId, Integer subjectId) {
        XkwCourseExample example = new XkwCourseExample();
        XkwCourseExample.Criteria criteria = example.createCriteria();
        if (stageId != null) {
            criteria.andStageIdEqualTo(stageId);
        }
        if (subjectId != null) {
            criteria.andSubjectIdEqualTo(subjectId);
        }
        return xkwCourseMapper.selectByExample(example);
    }

    @Override
    public List<CourseWithWrongQuestionCountDTO> getCoursesWithWrongQuestionCount(Integer stageId, Integer subjectId,
            Long userId) {
        // 获取课程列表
        List<XkwCourse> courses = getCourses(stageId, subjectId);
        List<CourseWithWrongQuestionCountDTO> result = new ArrayList<>();

        // 如果没有用户ID，则返回原始课程列表，错题数量为0
        if (userId == null) {
            for (XkwCourse course : courses) {
                CourseWithWrongQuestionCountDTO dto = new CourseWithWrongQuestionCountDTO();
                BeanUtils.copyProperties(course, dto);
                dto.setQuestionCount(0L);
                result.add(dto);
            }
            return result;
        }

        // 统计每个课程的错题数量
        for (XkwCourse course : courses) {
            CourseWithWrongQuestionCountDTO dto = new CourseWithWrongQuestionCountDTO();
            BeanUtils.copyProperties(course, dto);

            // 查询该用户在该课程下的错题数量
            TrainingQuestionExample example = new TrainingQuestionExample();
            TrainingQuestionExample.Criteria criteria = example.createCriteria();
            criteria.andUserIdEqualTo(userId);
            criteria.andCourseIdEqualTo(course.getId().longValue());
            long count = trainingQuestionMapper.countByExample(example);

            dto.setQuestionCount(count);
            result.add(dto);
        }

        return result;
    }

    @Override
    public List<XkwGrade> getGrades() {
        XkwGradeExample example = new XkwGradeExample();
        return xkwGradeMapper.selectByExample(example);
    }

    @Override
    public List<XkwTextbookVersion> getTextbookVersionsByCourseId(Integer courseId) {
        XkwTextbookVersionExample example = new XkwTextbookVersionExample();
        example.createCriteria().andCourseIdEqualTo(courseId);
        return xkwTextbookVersionMapper.selectByExample(example);
    }

    @Override
    public CommonPage<TrainingQuestion> getUserTrainingQuestions(Long userId, Long courseId, Integer difficultyLevel,
            String typeId, Integer year, String saveTimeRange, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        TrainingQuestionExample example = new TrainingQuestionExample();
        TrainingQuestionExample.Criteria criteria = example.createCriteria();
        criteria.andUserIdEqualTo(userId);
        if (courseId != null) {
            criteria.andCourseIdEqualTo(courseId);
        }
        if (difficultyLevel != null) {
            criteria.andDifficultyLevelEqualTo(difficultyLevel);
        }
        if (typeId != null && !typeId.isEmpty()) {
            criteria.andTypeIdLike(typeId + "%");
        }
        if (year != null) {
            criteria.andYearEqualTo(year);
        }
        if (saveTimeRange != null && !saveTimeRange.isEmpty()) {
            // 根据saveTimeRange添加时间范围条件
            // 注意: 此处需要根据您的数据库类型和字段名进行调整
            // 假设字段名为 create_time
            switch (saveTimeRange) {
                case "THREE_DAYS":
                    criteria.andCreateTimeGreaterThanOrEqualTo(
                            new Date(System.currentTimeMillis() - 3 * 24 * 60 * 60 * 1000));
                    break;
                case "ONE_WEEK":
                    criteria.andCreateTimeGreaterThanOrEqualTo(
                            new Date(System.currentTimeMillis() - 7 * 24 * 60 * 60 * 1000));
                    break;
                case "ONE_MONTH":
                    criteria.andCreateTimeGreaterThanOrEqualTo(
                            new Date(System.currentTimeMillis() - 30L * 24 * 60 * 60 * 1000));
                    break;
            }
        }
        example.setOrderByClause("create_time DESC");
        List<TrainingQuestion> list = trainingQuestionMapper.selectByExampleWithBLOBs(example);
        return CommonPage.restPage(list);
    }

    @Override
    public TrainingQuestion addTrainingQuestion(AddTrainingQuestionParam param) {
        TrainingQuestion trainingQuestion = new TrainingQuestion();
        trainingQuestion.setUserId(param.getUserId());
        trainingQuestion.setCourseId(param.getCourseId());
        trainingQuestion.setThumbnail(param.getThumbnail());
        trainingQuestion.setData(param.getData());
        trainingQuestion.setYear(param.getYear());
        trainingQuestion.setTypeId(param.getTypeId());
        trainingQuestion.setDifficultyLevel(param.getDifficultyLevel());
        trainingQuestion.setXkwQuestionId(param.getXkwQuestionId());

        Date now = new Date();
        trainingQuestion.setCreateTime(now);
        trainingQuestion.setUpdateTime(now);

        trainingQuestionMapper.insert(trainingQuestion);
        return trainingQuestion;
    }

    @Override
    @Transactional
    public int deleteBatch(List<Long> ids, Long userId) {
        if (CollUtil.isEmpty(ids)) {
            return 0;
        }

        // 删除训练题
        TrainingQuestionExample example = new TrainingQuestionExample();
        example.createCriteria()
                .andIdIn(ids)
                .andUserIdEqualTo(userId);

        return trainingQuestionMapper.deleteByExample(example);
    }

    @Override
    public List<XopQuestionPushVO> scoreImprovementRecommend(Long userId, ScoreImprovementRecommendParam param) {
        List<XopQuestionPushVO> result = xkwService.scoreImprovementRecommend(param);
        if (CollUtil.isNotEmpty(result)) {
            for (XopQuestionPushVO question : result) {
                // 检查用户是否已经添加过该题，设置wrongBookId字段
                TrainingQuestionExample example = new TrainingQuestionExample();
                example.createCriteria()
                        .andUserIdEqualTo(userId)
                        .andXkwQuestionIdEqualTo(question.getId());
                TrainingQuestion trainingQuestion = trainingQuestionMapper.selectByExample(example)
                        .stream()
                        .findFirst()
                        .orElse(null);
                if (trainingQuestion != null) {
                    question.setWrongBookId(trainingQuestion.getId());
                    question.setIsInWrongBook(true);
                } else {
                    question.setIsInWrongBook(false);
                }
            }
        }
        return result;
    }

    // @Override
    // public List<XkwTextbook> getTextbooks(Integer courseId, Integer gradeId,
    // Integer versionId) {
    // String url = "/xopqbm/textbooks?course_id=" + courseId + "&grade_id=" +
    // gradeId + "&version_id=" + versionId;
    // return restTemplate.getForObject(url, List.class);
    // }
    //
    // @Override
    // public List<XkwTextbookCatalog> getTextbookCatalog(Integer textbookId) {
    // String url = "/xopqbm/textbooks/catalog?textbook_id=" + textbookId;
    // return restTemplate.getForObject(url, List.class);
    // }
}