package com.vida.xinye.x2.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.PageHelper;
import com.vida.xinye.x2.api.CommonPage;
import com.vida.xinye.x2.dao.MaterialDao;
import com.vida.xinye.x2.dao.MaterialFavoriteDao;
import com.vida.xinye.x2.domain.param.MaterialParam;
import com.vida.xinye.x2.dto.MaterialDto;
import com.vida.xinye.x2.exception.Asserts;
import com.vida.xinye.x2.mbg.mapper.MaterialFavoriteMapper;
import com.vida.xinye.x2.mbg.mapper.MaterialMapper;
import com.vida.xinye.x2.mbg.model.*;
import com.vida.xinye.x2.service.MaterialService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 素材服务实现类
 *
 * <AUTHOR>
 * @date 2024/11/11
 */
@Service
public class MaterialServiceImpl implements MaterialService {
    @Resource
    private MaterialMapper materialMapper;
    @Resource
    private MaterialDao materialDao;
    @Resource
    private MaterialFavoriteMapper materialFavoriteMapper;
    @Resource
    private MaterialFavoriteDao materialFavoriteDao;

    @Override
    public CommonPage<MaterialDto> list(Integer pageSize, Integer pageNum, Long userId, Long categoryId) {
        PageHelper.startPage(pageNum, pageSize);

        List<MaterialDto> materials = new ArrayList<>();
        if (categoryId == null || categoryId.equals(0L)) { // 查询所有素材,按收藏量倒序
            materials = materialDao.listHotMaterials(userId);
        } else {
            materials = materialDao.list(userId, categoryId);
        }

        // 分页设置
        CommonPage<MaterialDto> materialPage = CommonPage.restPage(materials);
        //设置分页信息
        CommonPage<MaterialDto> resultPage = new CommonPage<>();
        resultPage.setPageNum(materialPage.getPageNum());
        resultPage.setPageSize(materialPage.getPageSize());
        resultPage.setTotal(materialPage.getTotal());
        resultPage.setTotalPage(materialPage.getTotalPage());
        resultPage.setList(materials);

        return resultPage;
    }

    @Override
    public CommonPage<Material> favorite(Integer pageSize, Integer pageNum, Long userId) {
        PageHelper.startPage(pageNum, pageSize);
        List<Material> materials = materialFavoriteDao.favorites(userId);

        // 分页设置
        CommonPage<Material> materialPage = CommonPage.restPage(materials);
        //设置分页信息
        CommonPage<Material> resultPage = new CommonPage<>();
        resultPage.setPageNum(materialPage.getPageNum());
        resultPage.setPageSize(materialPage.getPageSize());
        resultPage.setTotal(materialPage.getTotal());
        resultPage.setTotalPage(materialPage.getTotalPage());
        if (CollUtil.isEmpty(materials)) {
            resultPage.setList(new ArrayList<>());
            return resultPage;
        }

        return materialPage;
    }

    @Override
    public Long create(MaterialParam param) {
        // 判断素材名称是否已存在
        MaterialExample example = new MaterialExample();
        example.createCriteria().andNameEqualTo(param.getName());
        if (materialMapper.countByExample(example) > 0) {
            Asserts.fail("名称已存在！");
        }

        Material material = new Material();
        BeanUtil.copyProperties(param, material);
        materialMapper.insertSelective(material);
        return material.getId();
    }

    @Override
    public int update(Long id, MaterialParam param) {
        Material material = new Material();
        BeanUtil.copyProperties(param, material);
        material.setId(id);
        return materialMapper.updateByPrimaryKeySelective(material);
    }

    @Override
    public int delete(Long id) {
        return materialMapper.deleteByPrimaryKey(id);
    }

    @Override
    public boolean addFavorite(Long materialId, Long userId) {
        // 收藏素材
        MaterialFavorite materialFavorite = new MaterialFavorite();
        materialFavorite.setMaterialId(materialId);
        materialFavorite.setUserId(userId);
        return materialFavoriteDao.insert(materialFavorite) > 0;
    }

    @Override
    public boolean removeFavorite(List<Long> materialIds, Long userId) {
        // 不收藏素材
        MaterialFavoriteExample example = new MaterialFavoriteExample();
        example.createCriteria().andMaterialIdIn(materialIds).andUserIdEqualTo(userId);
        return materialFavoriteMapper.deleteByExample(example) > 0;
    }

}
