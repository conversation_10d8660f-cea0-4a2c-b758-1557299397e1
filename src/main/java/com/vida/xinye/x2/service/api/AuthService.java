package com.vida.xinye.x2.service.api;

import cn.hutool.core.util.StrUtil;
import com.vida.xinye.x2.component.JwtTokenProvider;
import com.vida.xinye.x2.constant.AccountTypeEnum;
import com.vida.xinye.x2.dto.TokenDto;
import com.vida.xinye.x2.dto.UserDto;
import com.vida.xinye.x2.dto.param.ResetPwdDto;
import com.vida.xinye.x2.dto.param.UserAuthenDto;
import com.vida.xinye.x2.dto.param.IdCardVerifyDto;
import com.vida.xinye.x2.dto.IdCardVerifyResultDto;
import com.vida.xinye.x2.exception.Asserts;
import com.vida.xinye.x2.mbg.mapper.UserMapper;
import com.vida.xinye.x2.mbg.model.User;
import com.vida.xinye.x2.mbg.model.UserExample;
import com.vida.xinye.x2.service.CaptchaService;
import com.vida.xinye.x2.util.PasswordUtil;
import com.vida.xinye.x2.util.XinYeHttpUtil;
import com.vida.xinye.x2.util.AliyunApiSignUtil;
import com.vida.xinye.x2.dto.param.AliyunIdCardProperties;
import com.vida.xinye.x2.domain.AuthRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;
import com.vida.xinye.x2.service.IdCardVerificationCacheService;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import com.vida.xinye.x2.dto.param.SmsLoginDto;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;
import java.util.HashMap;
import java.util.Map;
import java.util.LinkedHashMap;
import java.util.UUID;

/**
 * 身份认证 服务Service。
 *
 * <AUTHOR>
 * @date 2024/11/4
 */
@Service
@Slf4j
public class AuthService {

    @Autowired
    private AuthenticationManager authenticationManager;
    @Resource
    private UserMapper userMapper;
    @Autowired
    private PasswordEncoder passwordEncoder;
    @Autowired
    private JwtTokenProvider jwtTokenProvider;
    @Autowired
    private CaptchaService captchaService;
    @Autowired
    private AliyunIdCardProperties aliyunIdCardProperties;
    @Autowired
    private AliyunApiSignUtil aliyunApiSignUtil;
    @Autowired
    private IdCardVerificationCacheService idCardVerificationCacheService;
    @Autowired
    private MessageSource messageSource;

    /**
     * 根据用户名、密码登录。
     *
     * @param loginDto 用户名、密码
     * @return TokenDto
     */
    public TokenDto login(UserAuthenDto loginDto) {
        String username = loginDto.getUsername();
        String password = loginDto.getPassword();
        try {
            validateUsernameAndPassword(username, password);

            Authentication authentication = authenticationManager.authenticate(new UsernamePasswordAuthenticationToken(
                    username, password));
            SecurityContextHolder.getContext().setAuthentication(authentication);
            UserDto user = (UserDto) authentication.getPrincipal();

            // 更新用户登录时间
            User updateUser = new User();
            updateUser.setId(user.getId());
            updateUser.setLastLoginTime(new Date());
            userMapper.updateByPrimaryKeySelective(updateUser);

            log.debug("登录成功，用户名：{}", username);
            return createTokenDto(user.getId(), user.getUsername());
        } catch (BadCredentialsException e) {
            log.debug("登录失败，账号不存在或密码错误！ username:{}, password:{}", username, PasswordUtil.maskPassword(password));
            Asserts.fail("账号不存在或密码错误！");
        } catch (Exception e) {
            log.debug("登录失败: {}", Objects.toString(e.getMessage(), e.toString()));
            Asserts.fail("登录失败！");
        }

        return null;
    }

    /**
     * 注册新用户。
     *
     * @return TokenDto
     * @RequestBody UserAuthenDto registerDto
     */
    public TokenDto register(UserAuthenDto registerDto) {
        // 获取注册信息
        String username = registerDto.getUsername();
        String password = registerDto.getPassword();
        Integer type = registerDto.getType();
        try {
            // 验证用户名和密码
            validateUsernameAndPassword(username, password);
            // 验证账号类型是否为空
            if (type == null) {
                throw new IllegalArgumentException("账号类型是必需的！");
            }
            // 检查用户是否已存在
            if (userExists(username)) {
                throw new IllegalStateException("该账号已注册！");
            }
            // 创建用户对象
            User user = createUser(username, password, type);
            // 将用户信息插入数据库
            userMapper.insertSelective(user);

            // 注册成功日志记录
            log.debug("注册成功，用户名：{}", username);
            return createTokenDto(user.getId(), user.getUsername());
        } catch (Exception e) {
            log.debug("注册失败: {}", Objects.toString(e.getMessage(), e.toString()));
            Asserts.fail(e.getMessage());
            return null;
        }
    }

    /**
     * 注册新用户。
     *
     * @return TokenDto
     * @RequestBody UserAuthenDto registerDto
     */
    public boolean resetPwd(ResetPwdDto resetPwdDto) {
        // 获取注册信息
        String username = resetPwdDto.getUsername();
        String password = resetPwdDto.getNewPassword();
        String captcha = resetPwdDto.getCaptcha();
        try {
            // 检查账号是否存在
            UserExample example = new UserExample();
            example.createCriteria().andUsernameEqualTo(username);
            Optional<User> userOptional = userMapper.selectByExample(example).stream().findFirst();
            if (!userOptional.isPresent()) {
                throw new IllegalStateException("该账号未注册！");
            }

            // 校验验证码
            validateCaptcha(username, captcha);

            // 修改密码
            User user = userOptional.get();
            user.setPassword(passwordEncoder.encode(password));
            userMapper.updateByPrimaryKeySelective(user);

            // 修改密码日志记录
            log.debug("修改密码成功，用户名：{}", username);
            return true;
        } catch (IllegalStateException e) {
            Asserts.fail(e.getMessage());
        } catch (Exception e) {
            log.debug("修改密码失败: {}", Objects.toString(e.getMessage(), e.toString()));
        }
        return false;
    }

    private boolean userExists(String username) {
        UserExample example = new UserExample();
        example.createCriteria().andUsernameEqualTo(username);
        return userMapper.selectByExample(example).stream().findFirst().isPresent();
    }

    private User createUser(String username, String password, Integer type) {
        User user = new User();
        user.setUsername(username);
        user.setRegisterType(type);

        if (type == AccountTypeEnum.EMAIL.getValue()) {
            user.setEmail(user.getUsername());
        } else if (type == AccountTypeEnum.PHONE.getValue()) {
            user.setPhone(user.getUsername());
        } else {
            Asserts.fail("无效的账号类型！");
        }
        user.setPassword(passwordEncoder.encode(password));
        return user;
    }

    private void validateUsernameAndPassword(String username, String password) {
        if (StrUtil.isEmpty(username) || StrUtil.isEmpty(password)) {
            Asserts.fail("用户名或密码不能为空！");
        }
    }

    /**
     * 校验验证码
     */
    private void validateCaptcha(String username, String captcha) {
        if (!captchaService.verifyCode(username, captcha)) {
            throw new IllegalStateException("验证码已过期！");
        }
    }

    /**
     * 身份证实名认证
     * 调用阿里云云市场接口进行身份证验证
     * 优先从缓存查询，缓存未命中时调用第三方接口并缓存结果
     *
     * @param verifyDto 身份证验证参数
     * @return IdCardVerifyResultDto
     */
    public IdCardVerifyResultDto verifyIdCard(IdCardVerifyDto verifyDto) {
        String name = verifyDto.getName();
        String idCard = verifyDto.getIdcard();

        // 1. 先查询缓存
        IdCardVerifyResultDto cachedResult = idCardVerificationCacheService.getCachedVerification(name, idCard);
        if (cachedResult != null) {
            log.info("身份证验证命中缓存，姓名：{}", name);
            return cachedResult;
        }

        // 2. 缓存未命中，调用第三方接口
        log.info("身份证验证缓存未命中，调用第三方接口，姓名：{}", name);
        IdCardVerifyResultDto thirdPartyResult = callThirdPartyVerification(verifyDto);

        // 3. 如果第三方接口调用成功，保存到缓存
        if (thirdPartyResult != null && thirdPartyResult.getCode() != null) {
            try {
                idCardVerificationCacheService.saveVerificationResult(name, idCard, thirdPartyResult);
            } catch (Exception e) {
                log.warn("保存身份证验证缓存失败，但不影响正常返回，姓名：{}", name, e);
            }
        }

        return thirdPartyResult;
    }

    /**
     * 调用第三方身份证验证接口
     */
    private IdCardVerifyResultDto callThirdPartyVerification(IdCardVerifyDto verifyDto) {
        try {
            // 动态获取配置
            String url = aliyunIdCardProperties.getUrl();
            String method = aliyunIdCardProperties.getMethod();
            String appKey = aliyunIdCardProperties.getAppKey();
            String appSecret = aliyunIdCardProperties.getAppSecret();
            log.info("阿里云身份认证配置：url={}, method={}, appKey={}, appSecret={}", url, method, appKey, appSecret);

            java.net.URL urlObj = new java.net.URL(url);
            String host = urlObj.getProtocol() + "://" + urlObj.getHost();
            String path = urlObj.getPath();

            // 组装query参数（此接口无query参数，传空）
            Map<String, String> queryParams = new HashMap<>();

            // 组装body参数 - 改为form-data格式
            Map<String, String> bodyParams = new LinkedHashMap<>();
            bodyParams.put("name", verifyDto.getName());
            bodyParams.put("idcard", verifyDto.getIdcard());

            log.info("HttpUtils请求体参数：{}", bodyParams);

            // 生成header
            Map<String, String> headers = generateHeaders(appKey);

            // 组装AuthRequest用于签名
            AuthRequest authRequest = new AuthRequest();
            authRequest.setAppKey(appKey);
            authRequest.setAppSecret(appSecret);
            authRequest.setHost(urlObj.getHost());
            authRequest.setPath(path);
            authRequest.setMethod(method);
            authRequest.setHeaders(headers);
            authRequest.setQueryParams(queryParams);
            authRequest.setBody(bodyParams);

            // 校验参数
            validateIdCardRequest(authRequest);

            // 生成签名
            String signature = aliyunApiSignUtil.generateSign(authRequest);
            headers.put("X-Ca-Signature", signature);

            // 使用HttpUtils发起请求
            HttpResponse response = XinYeHttpUtil.doPost(
                    host,
                    path,
                    method,
                    headers,
                    queryParams,
                    bodyParams);

            String responseBody = EntityUtils.toString(response.getEntity());

            // 解析第三方接口响应
            ObjectMapper objectMapper = new ObjectMapper();
            IdCardVerifyResultDto thirdPartyResult = objectMapper.readValue(responseBody, IdCardVerifyResultDto.class);

            if (response.getStatusLine().getStatusCode() == 200) {
                // 处理成功响应，检查业务状态码
                if (!thirdPartyResult.getCode().equals(200)
                        || thirdPartyResult.getData().getResult().equals(1)) {
                    // 处理错误码，保持与第三方接口相同的格式
                    thirdPartyResult.setSuccess(false);
                    thirdPartyResult.setCode(500);
                    thirdPartyResult.setMsg("身份证验证失败");
                    thirdPartyResult.setData(null);
                }
                log.debug("身份证验证成功，返回结果：{}", thirdPartyResult);
                return thirdPartyResult;
            } else {
                log.error("身份证验证失败 (HttpUtils)：状态码={}, 响应体={}", response.getStatusLine().getStatusCode(),
                        responseBody);

                // 构造失败响应，保持与第三方接口相同的格式
                IdCardVerifyResultDto failedResult = new IdCardVerifyResultDto();
                failedResult.setSuccess(false);
                failedResult.setCode(response.getStatusLine().getStatusCode());
                String msg = messageSource.getMessage("idcard.verify.failed", null, "身份证验证失败",
                        LocaleContextHolder.getLocale());
                failedResult.setMsg(msg);
                failedResult.setData(null);
                return failedResult;
            }

        } catch (Exception e) {
            log.error("身份证验证异常 (HttpUtils)：", e);

            // 构造异常响应，保持与第三方接口相同的格式
            IdCardVerifyResultDto errorResult = new IdCardVerifyResultDto();
            errorResult.setSuccess(false);
            errorResult.setCode(500);
            errorResult.setMsg("身份证验证异常：" + e.getMessage());
            errorResult.setData(null);
            return errorResult;
        }
    }

    /**
     * 生成阿里云API签名所需header
     */
    private Map<String, String> generateHeaders(String appKey) {
        Map<String, String> headers = new LinkedHashMap<>();
        headers.put("Accept", "application/json; charset=utf-8");
        headers.put("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
        headers.put("X-Ca-Timestamp", String.valueOf(System.currentTimeMillis()));
        headers.put("X-Ca-Nonce", UUID.randomUUID().toString());
        headers.put("X-Ca-Key", appKey);
        headers.put("X-Ca-Signature-Method", "HmacSHA256");
        headers.put("X-Ca-Signature-Headers", "X-Ca-Key,X-Ca-Nonce,X-Ca-Signature-Method,X-Ca-Timestamp");
        return headers;
    }

    /**
     * 校验身份证验证AuthRequest参数
     */
    private void validateIdCardRequest(AuthRequest request) {
        if (!StringUtils.hasText(request.getHost())) {
            throw new IllegalArgumentException("host不能为空");
        }
        if (!StringUtils.hasText(request.getPath())) {
            throw new IllegalArgumentException("path不能为空");
        }
        if (!StringUtils.hasText(request.getMethod())) {
            throw new IllegalArgumentException("method不能为空");
        }
    }

    private TokenDto createTokenDto(Long userId, String username) {
        String token = jwtTokenProvider.generateToken(username);
        String refreshToken = jwtTokenProvider.generateRefreshToken(username);
        Date expiration = jwtTokenProvider.extractExpiration(token);
        Date refreshExpiration = jwtTokenProvider.extractRefreshExpiration(refreshToken);
        TokenDto tokenDto = new TokenDto();
        tokenDto.setUserId(userId);
        tokenDto.setUsername(username);
        tokenDto.setAccessToken(token);
        tokenDto.setExpiration(expiration.getTime());
        tokenDto.setRefreshToken(refreshToken);
        tokenDto.setRefreshTokenExpiration(refreshExpiration.getTime());
        // 查询用户家长模式信息
        User user = userMapper.selectByPrimaryKey(userId);
        if (user != null) {
            tokenDto.setParentMode(user.getParentMode());
            tokenDto.setRoleType(user.getRoleType());
            tokenDto.setGradeType(user.getGradeType());

            //新增nickname、avatar字段
            tokenDto.setNickname(user.getNickname());
            tokenDto.setAvatar(user.getAvatar());
        }
        return tokenDto;
    }

    /**
     * 手机号+验证码登录，自动注册
     *
     * @param smsLoginDto 登录参数
     * @return TokenDto
     */
    public TokenDto loginBySms(SmsLoginDto smsLoginDto) {
        String phone = smsLoginDto.getPhone();
        String smsCode = smsLoginDto.getSmsCode();
        // 校验验证码（抛出异常即失败）
        captchaService.verifyCode(phone, smsCode);
        // 查询用户是否存在
        UserExample example = new UserExample();
        example.createCriteria().andPhoneEqualTo(phone);
        User user = userMapper.selectByExample(example).stream().findFirst().orElse(null);
        if (user == null) {
            // 自动注册
            user = new User();
            user.setPhone(phone);
            user.setUsername(phone); // 用手机号作为用户名
            user.setRegisterType(AccountTypeEnum.PHONE.getValue());
            // 随机生成密码
            String randomPwd = java.util.UUID.randomUUID().toString().replaceAll("-", "").substring(0, 16);
            user.setPassword(passwordEncoder.encode(randomPwd));
            user.setCreateTime(new java.util.Date());
            userMapper.insertSelective(user);
        }
        //登录成功，设置验证码失效
        captchaService.setCaptchaExpire(phone);
        // 生成token
        return createTokenDto(user.getId(), user.getUsername());
    }

    /**
     * 刷新token
     *
     * @param refreshToken 刷新token
     * @return TokenDto
     */
    public TokenDto refreshTokenByExpiredToken(String refreshToken) {
        try {
            String username = jwtTokenProvider.extractUsername(refreshToken);
            // 校验refreshToken
            if (!jwtTokenProvider.validateRefreshToken(refreshToken, username)) {
                return null;
            }
            // 查询用户
            UserExample example = new UserExample();
            example.createCriteria().andUsernameEqualTo(username);
            User user = userMapper.selectByExample(example).stream().findFirst().orElse(null);
            if (user == null) {
                return null;
            }
            // 重新生成accessToken和refreshToken
            return createTokenDto(user.getId(), user.getUsername());
        } catch (Exception e) {
            log.error("刷新token失败: {}", e.getMessage(), e);
            return null;
        }
    }

}
