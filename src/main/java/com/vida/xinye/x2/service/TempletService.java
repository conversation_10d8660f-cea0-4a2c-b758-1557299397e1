package com.vida.xinye.x2.service;

import com.vida.xinye.x2.api.CommonPage;
import com.vida.xinye.x2.domain.param.TempletParam;
import com.vida.xinye.x2.domain.param.TempletPrintParam;
import com.vida.xinye.x2.dto.TempletDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 标签服务接口
 *
 * <AUTHOR>
 * @date 2024/11/11
 */
@Service
public interface TempletService {
    /**
     * 分页获取标签列表
     */
    CommonPage<TempletDto> list(Integer pageSize, Integer pageNum, Long userId, Byte SourceType);

    /**
     * 新增标签
     * @return
     */
    Long create(TempletParam materialParam);

    /**
     * 修改标签
     */
    int update(Long id, TempletParam param);

    /**
     * 删除标签
     */
    int delete(Long templetId);

    /**
     * 批量删除标签
     */
    int batchDelete(List<Long> templetIds, Long userId);

    /**
     * 获取打印历史记录
     */
    CommonPage<TempletDto> printHistory(Integer pageSize, Integer pageNum, Long userId, Byte SourceType);

    /**
     * 记录打印历史
     */
    @Transactional
    int print(TempletPrintParam param);

    /**
     * 删除打印历史
     */
    int deletePrintHistory(List<Long> ids, Long userId);

}
