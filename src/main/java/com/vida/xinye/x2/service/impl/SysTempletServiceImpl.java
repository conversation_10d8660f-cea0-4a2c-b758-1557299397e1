package com.vida.xinye.x2.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.github.pagehelper.PageHelper;
import com.vida.xinye.x2.api.CommonPage;
import com.vida.xinye.x2.constant.SysTempletSourceTypeEnum;
import com.vida.xinye.x2.constant.TempletSourceTypeEnum;
import com.vida.xinye.x2.dao.SysTempletDao;
import com.vida.xinye.x2.domain.param.SysTempletParam;
import com.vida.xinye.x2.dto.SystemTempletDto;
import com.vida.xinye.x2.exception.Asserts;
import com.vida.xinye.x2.mbg.mapper.*;
import com.vida.xinye.x2.mbg.model.*;
import com.vida.xinye.x2.service.SysTempletService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 标签服务实现类
 *
 * <AUTHOR>
 * @date 2025/06/27
 */
@Service
public class SysTempletServiceImpl implements SysTempletService {
    // 每个用户的最大打印历史记录数
    @Value("${constants.print.history.max:10}")
    private int MAX_PRINT_HISTORY_COUNT;

    @Resource
    private SysTempletMapper sysTempletMapper;
    @Resource
    private SysTempletGroupMapper sysTempletGroupMapper;
    @Resource
    private SysTempletDao sysTempletDao;

    @Override
    public CommonPage<SystemTempletDto> listSystemTemples(Long groupId, Integer pageSize, Integer pageNum) {
        PageHelper.startPage(pageNum, pageSize);
        List<SystemTempletDto> list = sysTempletDao.selectSystemTemples(groupId);
        return CommonPage.restPage(list);
    }

    @Override
    public Long createSystemTemplet(SysTempletParam param) {
        if (param.getGroupId() == null || param.getGroupId() == 0) {
            Asserts.fail("分组不能为空");
        }
        SysTempletGroupExample groupExample = new SysTempletGroupExample();
        groupExample.createCriteria().andIdEqualTo(param.getGroupId());
        //检查分组是否存在
        if(sysTempletGroupMapper.countByExample(groupExample) == 0){
            Asserts.fail("分组不存在");
        }

        // 检查是否存在同名系统标签
        SysTempletExample example = new SysTempletExample();
        example.createCriteria().andNameEqualTo(param.getName()).andSourceTypeEqualTo(TempletSourceTypeEnum.SYSTEM.getValue());
        List<SysTemplet> existingTemplets = sysTempletMapper.selectByExample(example);
        if (!existingTemplets.isEmpty()) {
            Asserts.fail("已存在同名系统标签");
        }

        SysTemplet templet = convertParamToEntity(param);
        templet.setSourceType(SysTempletSourceTypeEnum.Label.getValue());
        sysTempletMapper.insertSelective(templet);
        return templet.getId();
    }

    @Override
    public int updateSystemTemplet(Long id, SysTempletParam param) {
        SysTemplet existing = sysTempletMapper.selectByPrimaryKey(id);
        if(existing == null){
            Asserts.fail("标签不存在");
        }

        SysTemplet update = convertParamToEntity(param);
        update.setId(id);
        // 保留标签来源不可修改
        update.setSourceType(null);
        return sysTempletMapper.updateByPrimaryKeySelective(update);
    }

    @Override
    public int deleteSystemTemplet(Long id) {
        return sysTempletMapper.deleteByPrimaryKey(id);
    }

    private SysTemplet convertParamToEntity(SysTempletParam param) {
        SysTemplet templet = new SysTemplet();
        BeanUtil.copyProperties(param, templet);
        return templet;
    }

}
