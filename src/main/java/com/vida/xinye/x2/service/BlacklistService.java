package com.vida.xinye.x2.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * @Description: IP黑名单服务类
 * @Author: zhangwenbin
 * @Date: 2024/11/05 16:35
 */
@Service
@RefreshScope
@Slf4j
public class BlacklistService {

  @Autowired
  private RedisService redisService;

  @Autowired
  private StringRedisTemplate stringRedisTemplate;

  @Value("${security.blacklist.static-ips:}")
  private List<String> staticBlacklistIps;

  @Value("${security.blacklist.redis-prefix:blacklist:ip:}")
  private String blacklistRedisPrefix;

  @Value("${security.blacklist.request-counter-prefix:request_counter:ip:}")
  private String requestCounterPrefix;

  /**
   * 检查IP是否在黑名单中
   *
   * @param ip IP地址
   * @return true: 在黑名单中, false: 不在黑名单中
   */
  public boolean isBlacklisted(String ip) {
    if (StrUtil.isBlank(ip)) {
      return false;
    }

    // 检查静态黑名单
    if (isInStaticBlacklist(ip)) {
      log.warn("IP {} 在静态黑名单中", ip);
      return true;
    }

    // 检查Redis动态黑名单
    String key = blacklistRedisPrefix + ip;
    if (redisService.hasKey(key)) {
      log.warn("IP {} 在动态黑名单中", ip);
      return true;
    }

    return false;
  }

  /**
   * 添加IP到动态黑名单
   *
   * @param ip       IP地址
   * @param duration 封禁时长（分钟）
   * @param reason   封禁原因
   */
  public void addToBlacklist(String ip, int duration, String reason) {
    if (StrUtil.isBlank(ip)) {
      return;
    }

    String key = blacklistRedisPrefix + ip;
    String value = String.format("reason:%s,time:%d", reason, System.currentTimeMillis());

    redisService.set(key, value, duration * 60); // 转换为秒
    log.warn("IP {} 已加入黑名单，封禁时长: {} 分钟，原因: {}", ip, duration, reason);
  }

  /**
   * 从动态黑名单中移除IP
   *
   * @param ip IP地址
   */
  public void removeFromBlacklist(String ip) {
    if (StrUtil.isBlank(ip)) {
      return;
    }

    String key = blacklistRedisPrefix + ip;
    if (redisService.hasKey(key)) {
      redisService.del(key);
      log.info("IP {} 已从黑名单中移除", ip);
    }
  }

  /**
   * 记录IP请求次数并检查是否需要自动封禁
   *
   * @param ip            IP地址
   * @param threshold     阈值
   * @param windowMinutes 时间窗口（分钟）
   * @param banDuration   封禁时长（分钟）
   * @return true: 需要封禁, false: 不需要封禁
   */
  public boolean checkAndAutoBlock(String ip, int threshold, int windowMinutes, int banDuration) {
    if (StrUtil.isBlank(ip)) {
      return false;
    }

    // 如果已经在黑名单中，直接返回true
    if (isBlacklisted(ip)) {
      return true;
    }

    String counterKey = requestCounterPrefix + ip;

    try {
      // 使用当前时间窗口作为键的一部分，实现滑动窗口
      long currentWindow = System.currentTimeMillis() / (windowMinutes * 60 * 1000L);
      String windowedKey = counterKey + ":" + currentWindow;

      // 增加请求计数
      Long count = stringRedisTemplate.opsForValue().increment(windowedKey);

      // 设置过期时间（时间窗口的2倍，确保数据及时清理）
      if (count == 1) {
        stringRedisTemplate.expire(windowedKey, windowMinutes * 2, TimeUnit.MINUTES);
      }

      // 检查是否超过阈值
      if (count >= threshold) {
        // 自动封禁
        addToBlacklist(ip, banDuration,
            "Auto blocked: exceeded " + threshold + " requests in " + windowMinutes + " minutes");

        // 清除当前窗口的计数器
        stringRedisTemplate.delete(windowedKey);

        log.warn("IP {} 因超过限流阈值被自动封禁，{}分钟内请求{}次，阈值{}",
            ip, windowMinutes, count, threshold);

        return true;
      }

      log.debug("IP {} 在 {} 分钟内请求次数: {}/{}", ip, windowMinutes, count, threshold);
      return false;

    } catch (Exception e) {
      log.error("检查IP {} 自动封禁时发生错误: {}", ip, e.getMessage(), e);
      return false;
    }
  }

  /**
   * 获取所有动态黑名单IP
   *
   * @return IP列表
   */
  public Set<String> getDynamicBlacklistIps() {
    return stringRedisTemplate.keys(blacklistRedisPrefix + "*");
  }

  /**
   * 获取静态黑名单IP
   *
   * @return IP列表
   */
  public List<String> getStaticBlacklistIps() {
    return staticBlacklistIps;
  }

  /**
   * 检查IP是否在静态黑名单中
   *
   * @param ip IP地址
   * @return true: 在黑名单中, false: 不在黑名单中
   */
  private boolean isInStaticBlacklist(String ip) {
    if (CollUtil.isEmpty(staticBlacklistIps)) {
      return false;
    }

    for (String blacklistIp : staticBlacklistIps) {
      if (isIpMatch(ip, blacklistIp.trim())) {
        return true;
      }
    }
    return false;
  }

  /**
   * IP匹配检查（支持通配符和CIDR）
   *
   * @param ip      待检查的IP
   * @param pattern IP模式（支持通配符*和CIDR格式）
   * @return true: 匹配, false: 不匹配
   */
  private boolean isIpMatch(String ip, String pattern) {
    if (StrUtil.isBlank(ip) || StrUtil.isBlank(pattern)) {
      return false;
    }

    // 精确匹配
    if (ip.equals(pattern)) {
      return true;
    }

    // 通配符匹配
    if (pattern.contains("*")) {
      String regex = pattern.replace(".", "\\.").replace("*", ".*");
      return ip.matches(regex);
    }

    // 简单的网段匹配（如 192.168.1.）
    if (pattern.endsWith(".")) {
      return ip.startsWith(pattern);
    }

    // TODO: 可以扩展支持CIDR格式（如 ***********/24）

    return false;
  }
}