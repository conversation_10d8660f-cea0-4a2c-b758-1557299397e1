package com.vida.xinye.x2.service;

import com.vida.xinye.x2.dto.TempletMarketGroupDto;
import com.vida.xinye.x2.dto.param.TempletMarketGroupParam;
import com.vida.xinye.x2.mbg.model.TempletMarketGroup;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface TempletMarketGroupService {

    /**
     * 系统标签分组列表
     */
    List<TempletMarketGroup> listGroups();

    /**
     * 创建系统标签分组
     */
    Long createGroup(TempletMarketGroupParam param);

    /**
     * 更新系统标签分组
     */
    int updateGroup(Long id, TempletMarketGroupParam param);

    /**
     * 删除系统标签分组
     */
    int deleteGroup(Long id);

    /**
     * 分组排序
     */
    @Transactional
    int sortGroups(List<Long> groupIds);

    /**
     * 获取模板市场分组列表（支持多语言）
     * 
     * @param locale 语言代码
     * @return 分组列表
     */
    List<TempletMarketGroupDto> listWithLocale(String locale);
}
