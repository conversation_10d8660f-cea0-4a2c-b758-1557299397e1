package com.vida.xinye.x2.service.impl;

import com.vida.xinye.x2.constant.TempletSourceTypeEnum;
import com.vida.xinye.x2.dao.TempletGroupDao;
import com.vida.xinye.x2.dto.param.TempletGroupParam;
import com.vida.xinye.x2.exception.Asserts;
import com.vida.xinye.x2.mbg.mapper.TempletGroupMapper;
import com.vida.xinye.x2.mbg.mapper.TempletMapper;
import com.vida.xinye.x2.mbg.model.*;
import com.vida.xinye.x2.service.TempletGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class TempletGroupServiceImpl implements TempletGroupService {

    @Autowired
    private TempletGroupMapper groupMapper;
    @Autowired
    private TempletMapper templetMapper;
    @Autowired
    private TempletGroupDao templetGroupDao;

    @Override
    public List<TempletGroup> listGroups() {
        TempletGroupExample example = new TempletGroupExample();
        example.createCriteria().andSourceTypeEqualTo(TempletSourceTypeEnum.SYSTEM.getValue());
        example.setOrderByClause("sort asc");
        return groupMapper.selectByExample(example);
    }

    @Override
    public Long createGroup(TempletGroupParam param) {
        // 校验分组名称唯一性
        TempletGroupExample example = new TempletGroupExample();
        example.createCriteria().andNameEqualTo(param.getName());
        if (groupMapper.countByExample(example) > 0) {
            Asserts.fail("分组名称已存在");
        }

        TempletGroup group = new TempletGroup();
        group.setName(param.getName());
        group.setSort(param.getSort());
        group.setSourceType(TempletSourceTypeEnum.SYSTEM.getValue());
        groupMapper.insertSelective(group);

        // 多语言处理
        if (param.getI18nNames() != null && !param.getI18nNames().isEmpty()) {
            List<TempletGroupI18n> insertGroupsI18n = param.getI18nNames().stream().map(nameDto -> {
                TempletGroupI18n i18n = new TempletGroupI18n();
                i18n.setGroupId(group.getId());
                i18n.setLocale(nameDto.getLocale());
                i18n.setName(nameDto.getName());
                return i18n;
            }).collect(Collectors.toList());
            // 插入多语言记录
            templetGroupDao.insert(insertGroupsI18n);
        }

        return group.getId();
    }

    @Override
    public int updateGroup(Long id, TempletGroupParam param) {
        TempletGroup existing = groupMapper.selectByPrimaryKey(id);
        if (existing == null || existing.getSourceType() != TempletSourceTypeEnum.SYSTEM.getValue()) {
            Asserts.fail("系统分组不存在");
        }

        TempletGroup update = new TempletGroup();
        update.setId(id);
        update.setName(param.getName());
        update.setSort(param.getSort());

        // 多语言处理
        if (param.getI18nNames() != null && !param.getI18nNames().isEmpty()) {
            List<TempletGroupI18n> insertGroupsI18n = param.getI18nNames().stream().map(nameDto -> {
                TempletGroupI18n i18n = new TempletGroupI18n();
                i18n.setGroupId(id);
                i18n.setLocale(nameDto.getLocale());
                i18n.setName(nameDto.getName());
                return i18n;
            }).collect(Collectors.toList());
            // 插入多语言记录
            templetGroupDao.insert(insertGroupsI18n);
        }

        return groupMapper.updateByPrimaryKeySelective(update);
    }

    @Override
    public int deleteGroup(Long id) {
        // 检查是否存在关联标签
        TempletGroup existing = groupMapper.selectByPrimaryKey(id);
        if (existing == null || existing.getSourceType() != 1) {
            Asserts.fail("系统分组不存在");
        }

        // 检查是否存在关联标签
        TempletExample example = new TempletExample();
        example.createCriteria().andGroupIdEqualTo(id);
        if (templetMapper.countByExample(example) > 0) {
            Asserts.fail("请先移除分组下的标签");
        }
        return groupMapper.deleteByPrimaryKey(id);
    }

    @Transactional
    @Override
    public int sortGroups(List<Long> groupIds) {
        for (int i = 0; i < groupIds.size(); i++) {
            TempletGroup group = new TempletGroup();
            group.setId(groupIds.get(i));
            group.setSort(i + 1);
            groupMapper.updateByPrimaryKeySelective(group);
        }
        return groupIds.size();
    }
}
