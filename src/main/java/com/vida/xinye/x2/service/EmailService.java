package com.vida.xinye.x2.service;


import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;

/**
 * 邮箱 服务
 *
 * <AUTHOR>
 * @date 2024/11/4
 */
@Service
@RefreshScope
@Slf4j
public class EmailService {
    @Value("${spring.mail.username}")
    private String fromEmail;

    @Autowired
    private JavaMailSender mailSender;

    public void sendEmail(String to, String subject, String text) {
        try {
            MimeMessage mimeMessage = mailSender.createMimeMessage();
            MimeMessageHelper messageHelper = new MimeMessageHelper(mimeMessage, true, "UTF-8");
            messageHelper.setFrom(fromEmail);
            messageHelper.setTo(to);
            messageHelper.setSubject(subject);
            messageHelper.setText(text, true);
            mailSender.send(mimeMessage);
            log.info("发送邮件成功 - 收件人: {}, 主题: {}", maskEmail(to), subject);
        } catch (MessagingException e) {
            log.error("发送邮件失败 - 收件人: {}, 主题: {}", maskEmail(to), subject, e);
            throw new RuntimeException("Failed to send email", e);
        }
    }

    /**
     * 发送简单文本邮件
     */
    public boolean sendSimpleEmail(String to, String subject, String content) {
        try {
            sendEmail(to, subject, content);
            return true;
        } catch (Exception e) {
            log.error("发送简单邮件失败", e);
            return false;
        }
    }

    /**
     * 发送验证码邮件
     */
    public boolean sendVerificationCode(String to, String code, String purpose) {
        String subject = getEmailSubject(purpose);
        String content = getEmailContent(purpose, code);
        return sendSimpleEmail(to, subject, content);
    }

    /**
     * 获取邮件主题
     */
    private String getEmailSubject(String purpose) {
        switch (purpose) {
            case "login":
                return "【心叶教育】登录验证码";
            case "register":
                return "【心叶教育】注册验证码";
            case "reset_password":
                return "【心叶教育】密码重置验证码";
            case "bind":
                return "【心叶教育】账号绑定验证码";
            default:
                return "【心叶教育】验证码";
        }
    }

    /**
     * 获取邮件内容
     */
    private String getEmailContent(String purpose, String code) {
        String template;
        switch (purpose) {
            case "login":
                template = "您的登录验证码是：%s，有效期5分钟。如非本人操作，请忽略此邮件。";
                break;
            case "register":
                template = "您的注册验证码是：%s，有效期5分钟。如非本人操作，请忽略此邮件。";
                break;
            case "reset_password":
                template = "您的密码重置验证码是：%s，有效期5分钟。如非本人操作，请忽略此邮件。";
                break;
            case "bind":
                template = "您的账号绑定验证码是：%s，有效期5分钟。如非本人操作，请忽略此邮件。";
                break;
            default:
                template = "您的验证码是：%s，有效期5分钟。";
        }

        return String.format(template, code) + "\n\n心叶教育团队";
    }

    /**
     * 邮箱脱敏处理
     */
    private String maskEmail(String email) {
        if (email == null || !email.contains("@")) {
            return email;
        }

        int atIndex = email.indexOf('@');
        if (atIndex > 1) {
            return email.charAt(0) + "***" + email.substring(atIndex);
        }

        return email;
    }
}

