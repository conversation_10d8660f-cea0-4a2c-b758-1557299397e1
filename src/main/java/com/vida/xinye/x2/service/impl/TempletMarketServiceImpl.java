package com.vida.xinye.x2.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.PageHelper;
import com.vida.xinye.x2.api.CommonPage;
import com.vida.xinye.x2.dao.TempletMarketDao;
import com.vida.xinye.x2.dto.TempletMarketDto;
import com.vida.xinye.x2.exception.Asserts;
import com.vida.xinye.x2.mbg.mapper.*;
import com.vida.xinye.x2.mbg.model.*;
import com.vida.xinye.x2.service.TempletMarketService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 标签服务实现类
 *
 * <AUTHOR>
 * @date 2025/06/27
 */
@Service
public class TempletMarketServiceImpl implements TempletMarketService {
    @Autowired
    private TempletMarketDao templetMarketDao;
    @Autowired
    private TempletMarketMapper templetMarketMapper;

    @Override
    public CommonPage<TempletMarketDto> list(Integer pageSize, Integer pageNum, Long groupId) {
        PageHelper.startPage(pageNum, pageSize);
        List<TempletMarketDto> templets = templetMarketDao.list(groupId);

        // 分页设置
        CommonPage<TempletMarketDto> templetPage = CommonPage.restPage(templets);
        //设置分页信息
        CommonPage<TempletMarketDto> resultPage = new CommonPage<>();
        resultPage.setPageNum(templetPage.getPageNum());
        resultPage.setPageSize(templetPage.getPageSize());
        resultPage.setTotal(templetPage.getTotal());
        resultPage.setTotalPage(templetPage.getTotalPage());
        if (CollUtil.isEmpty(templets)) {
            resultPage.setList(new ArrayList<>());
            return resultPage;
        }

        return templetPage;
    }

    @Override
    @Transactional
    public Long create(TempletMarketDto template) {
        // 1. 创建模板主体
        template.setCreateTime(new Date());
        int result = templetMarketDao.insert(template);
        if (result <= 0) {
            Asserts.fail("创建集市模板失败");
        }

        return template.getId();
    }

    @Override
    @Transactional
    public int update(Long id, TempletMarketDto template) {
        // 1. 更新模板主体
        template.setId(id);
        template.setCreateTime(new Date());
        if(template.getSort()==null){
            template.setSort(0);
        }
        int result = templetMarketDao.update(template);

        if (result <= 0) {
            throw new RuntimeException("更新模板失败");
        }

        return result;
    }

    @Override
    @Transactional
    public int delete(Long templetId) {
        // 1. 删除模板主体
        int result = templetMarketMapper.deleteByPrimaryKey(templetId);
        if (result <= 0) {
            Asserts.fail("删除模板失败");
        }

        return result;
    }


}
