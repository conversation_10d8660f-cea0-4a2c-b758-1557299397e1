package com.vida.xinye.x2.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.PageHelper;
import com.vida.xinye.x2.api.CommonPage;
import com.vida.xinye.x2.dao.TempletDao;
import com.vida.xinye.x2.domain.param.TempletParam;
import com.vida.xinye.x2.domain.param.TempletPrintParam;
import com.vida.xinye.x2.dto.TempletDto;
import com.vida.xinye.x2.exception.Asserts;
import com.vida.xinye.x2.mbg.mapper.PrintHistoryMapper;
import com.vida.xinye.x2.mbg.mapper.TempletMapper;
import com.vida.xinye.x2.mbg.model.PrintHistory;
import com.vida.xinye.x2.mbg.model.PrintHistoryExample;
import com.vida.xinye.x2.mbg.model.Templet;
import com.vida.xinye.x2.mbg.model.TempletExample;
import com.vida.xinye.x2.service.TempletService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 标签服务实现类
 *
 * <AUTHOR>
 * @date 2024/11/11
 */
@Service
public class TempletServiceImpl implements TempletService {
    // 每个用户的最大打印历史记录数
    @Value("${constants.print.history.max:10}")
    private int MAX_PRINT_HISTORY_COUNT;

    @Resource
    private TempletMapper templetMapper;
    @Resource
    private TempletDao templetDao;
    @Resource
    private PrintHistoryMapper printHistoryMapper;

    @Override
    public CommonPage<TempletDto> list(Integer pageSize, Integer pageNum, Long userId, Byte SourceType) {
        PageHelper.startPage(pageNum, pageSize);
        List<TempletDto> templets = templetDao.list(SourceType, userId);

        // 分页设置
        CommonPage<TempletDto> templetPage = CommonPage.restPage(templets);
        //设置分页信息
        CommonPage<TempletDto> resultPage = new CommonPage<>();
        resultPage.setPageNum(templetPage.getPageNum());
        resultPage.setPageSize(templetPage.getPageSize());
        resultPage.setTotal(templetPage.getTotal());
        resultPage.setTotalPage(templetPage.getTotalPage());
        if (CollUtil.isEmpty(templets)) {
            resultPage.setList(new ArrayList<>());
            return resultPage;
        }

        return templetPage;
    }

    @Override
    public Long create(TempletParam param) {
        Templet templet = new Templet();
        BeanUtil.copyProperties(param, templet);
        templetMapper.insertSelective(templet);
        return templet.getId();
    }

    @Override
    public int update(Long id, TempletParam param) {
        Templet templet = new Templet();
        BeanUtil.copyProperties(param, templet);
        templet.setId(id);
        return templetMapper.updateByPrimaryKeySelective(templet);
    }

    @Override
    public int delete(Long id) {
        return templetMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int batchDelete(List<Long> templetIds, Long userId) {
        //批量删除标签
        TempletExample templetExample = new TempletExample();
        templetExample.createCriteria().andIdIn(templetIds).andUserIdEqualTo(userId);
        return templetMapper.deleteByExample(templetExample);
    }

    @Override
    public CommonPage<TempletDto> printHistory(Integer pageSize, Integer pageNum, Long userId, Byte SourceType) {
        PageHelper.startPage(pageNum, pageSize);
        List<TempletDto> templets = templetDao.getTempletHistory(SourceType, userId);

        // 分页设置
        CommonPage<TempletDto> templetPage = CommonPage.restPage(templets);
        //设置分页信息
        CommonPage<TempletDto> resultPage = new CommonPage<>();
        resultPage.setPageNum(templetPage.getPageNum());
        resultPage.setPageSize(templetPage.getPageSize());
        resultPage.setTotal(templetPage.getTotal());
        resultPage.setTotalPage(templetPage.getTotalPage());
        if (CollUtil.isEmpty(templets)) {
            resultPage.setList(new ArrayList<>());
            return resultPage;
        }

        return templetPage;
    }

    @Override
    public int print(TempletPrintParam param) {
        if (param.getSourceType() == null) {
            Asserts.fail("标签来源类型不能为空");
        }

        PrintHistory printHistory = new PrintHistory();
        printHistory.setUserId(param.getUserId());
        printHistory.setTempletCover(param.getCover());
        printHistory.setTempletData(param.getData());
        //sourceType
        printHistory.setSourceType(param.getSourceType());

        // 尝试插入打印历史记录
        int insertResult = printHistoryMapper.insertSelective(printHistory);
        if (insertResult > 0) {
            // 如果插入成功，检查是否需要删除最旧的记录
            if (templetDao.getPrintHistoryCount(param.getSourceType(), param.getUserId()) > MAX_PRINT_HISTORY_COUNT) {
                templetDao.deleteOldestPrintHistory(param.getSourceType(), param.getUserId());
            }
        }

        return insertResult;
    }

    @Override
    public int deletePrintHistory(List<Long> ids, Long userId) {
        PrintHistoryExample printHistoryExample = new PrintHistoryExample();
        printHistoryExample.createCriteria().andIdIn(ids).andUserIdEqualTo(userId);
        return printHistoryMapper.deleteByExample(printHistoryExample);
    }

}
