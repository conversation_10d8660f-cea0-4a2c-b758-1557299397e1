package com.vida.xinye.x2.service;

import com.vida.xinye.x2.dto.TokenDto;
import com.vida.xinye.x2.dto.param.*;
import com.vida.xinye.x2.dto.param.EmailResetPasswordDto;
import com.vida.xinye.x2.dto.param.PhoneResetPasswordDto;

/**
 * 扩展认证服务接口
 *
 * <AUTHOR>
 * @date 2025/08/12
 */
public interface ExtendedAuthService {
    
    /**
     * 邮箱登录（密码或验证码）
     *
     * @param emailLoginDto 邮箱登录参数
     * @return TokenDto
     */
    TokenDto emailLogin(EmailLoginDto emailLoginDto);
    
    /**
     * 手机号密码登录
     *
     * @param phonePasswordLoginDto 手机号密码登录参数
     * @return TokenDto
     */
    TokenDto phonePasswordLogin(PhonePasswordLoginDto phonePasswordLoginDto);

    /**
     * 手机号登录（密码或验证码）
     *
     * @param phoneLoginDto 手机号登录参数
     * @return TokenDto
     */
    TokenDto phoneLogin(PhoneLoginDto phoneLoginDto);
    
    /**
     * 第三方登录
     *
     * @param thirdPartyLoginDto 第三方登录参数
     * @return TokenDto
     */
    TokenDto thirdPartyLogin(ThirdPartyLoginDto thirdPartyLoginDto);
    
    /**
     * 设置密码
     *
     * @param userId 用户ID
     * @param setPasswordDto 设置密码参数
     * @return 是否成功
     */
    boolean setPassword(Long userId, SetPasswordDto setPasswordDto);
    
    /**
     * 忘记密码重置
     *
     * @param resetPwdDto 重置密码参数
     * @return 是否成功
     */
    boolean forgotPassword(ResetPwdDto resetPwdDto);
    
    /**
     * 检查用户是否需要绑定手机号
     *
     * @param userId 用户ID
     * @return 是否需要绑定手机号
     */
    boolean checkPhoneBindingRequired(Long userId);

    /**
     * 邮箱验证码重置密码
     *
     * @param dto 邮箱重置密码参数
     * @return 是否成功
     */
    boolean resetPasswordByEmail(EmailResetPasswordDto dto);

    /**
     * 手机号验证码重置密码
     *
     * @param dto 手机号重置密码参数
     * @return 是否成功
     */
    boolean resetPasswordByPhone(PhoneResetPasswordDto dto);
}
