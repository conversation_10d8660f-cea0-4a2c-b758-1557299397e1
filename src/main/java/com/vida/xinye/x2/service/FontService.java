package com.vida.xinye.x2.service;

import com.vida.xinye.x2.mbg.model.Font;
import com.vida.xinye.x2.mbg.model.Machine;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 字体服务接口
 *
 * <AUTHOR>
 * @date 2025/02/07
 */
@Service
public interface FontService {
    /**
     * 获取白名单机器列表
     */
    List<Font> list(String kind);

    /**
     * 获取白名单机器列表
     */
    List<String> kindList();

}
