package com.vida.xinye.x2.service.api;

import com.vida.xinye.x2.dto.UserDto;
import com.vida.xinye.x2.mbg.mapper.UserMapper;
import com.vida.xinye.x2.mbg.model.User;
import com.vida.xinye.x2.mbg.model.UserExample;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/5
 */
@Service
public class ApiUserDetailsService implements UserDetailsService {

    @Resource
    private UserMapper userMapper;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        username = username.trim();
        UserExample example = new UserExample();
        example.createCriteria().andUsernameEqualTo(username);
        List<User> userList = userMapper.selectByExample(example);
        if (userList == null || userList.isEmpty()) {
            throw new UsernameNotFoundException("账号不存在!");
        }

        // 获取查询结果中的第一个用户
        User user = userList.get(0);
        // 返回UserDetails对象
        UserDto userDto = new UserDto();
        userDto.setId(user.getId());
        userDto.setUsername(user.getUsername());
        userDto.setPassword(user.getPassword());
        userDto.setAccountNonLocked(true);
        userDto.setAccountNonExpired(true);
        userDto.setEnabled(true);
        userDto.setCredentialsNonExpired(true);
        userDto.setAuthorities(Collections.singletonList(new SimpleGrantedAuthority("ROLE_USER")));
        userDto.setRoleType(user.getRoleType());
        userDto.setGradeType(user.getGradeType());
        userDto.setParentMode(user.getParentMode());
        userDto.setParentName(user.getParentName());
        userDto.setParentIdcard(user.getParentIdcard());
        userDto.setTeenagerPassword(user.getTeenagerPassword());

        return userDto;
    }
}
