package com.vida.xinye.x2.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.aliyun.oss.ClientException;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.model.PutObjectRequest;
import com.vida.xinye.x2.constant.OSSConstant;
import com.vida.xinye.x2.exception.Asserts;
import com.vida.xinye.x2.service.OssService;
import com.vida.xinye.x2.util.XinYeFileUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Date;
import java.util.List;

/**
 * oss上传管理Service实现类
 * Created by macro on 2018/5/17.
 */
@Service
@RefreshScope
@Slf4j
public class OssServiceImpl implements OssService {

    @Value("${aliyun.oss.bucketName}")
    private String ALIYUN_OSS_BUCKET_NAME;
    @Value("${aliyun.oss.endpoint}")
    private String ALIYUN_OSS_ENDPOINT;
    @Value("${aliyun.oss.accessKeyId}")
    private String ALIYUN_OSS_ACCESSKEYID;
    @Value("${aliyun.oss.accessKeySecret}")
    private String ALIYUN_OSS_ACCESSKEYSECRET;
    @Value("${aliyun.oss.cdn}")
    private String ALIYUN_OSS_CDN_DOMAIN;
    @Value("${aliyun.oss.stsTokenServer}")
    private String STS_TOKEN_SERVER;
    @Value("${file.download.path}")
    private String FILE_DOWNLOAD_PATH;

    /**
     * 返回oss cdn域名访问路径
     *
     * @param files
     * @return
     */
    @Override
    public List<String> upload(MultipartFile[] files) {
        return upload("", files, true);
    }


    @Override
    public List<String> upload(String objectPrefix, MultipartFile[] files, boolean autoName) {
        //定义一个map用于接收图片信息
        List<String> uploadPaths = new ArrayList();
        objectPrefix = objectPrefix.trim();

        //循环遍历
        for (int i = 0; i < files.length; i++) {
            MultipartFile multipartFile = files[i];
            try {
                // 随机生成文件名（时间戳）
                String fileName = multipartFile.getOriginalFilename() + "_" + DateUtil.format(DateTime.now(), "yyyyMMddHHmmss");
                if (BooleanUtil.isTrue(autoName)) {
                    fileName = StrUtil.toString(DateUtil.current()) + '.' + FileNameUtil.getSuffix(multipartFile.getOriginalFilename());
                    ;
                }
                String objectName = !StrUtil.isEmpty(objectPrefix) ? objectPrefix + '/' + fileName : fileName;
                log.debug("oss上传文件：" + objectName + ";prefix " + objectPrefix);
                String ossName = upload(objectPrefix, multipartFile);
                log.debug("oss上传文件结束，获取到ossname:{}", ossName);
                uploadPaths.add(ossName);
            } catch (Exception e) {
                log.error("oss上传错误：" + e.getMessage());
                Asserts.fail("文件上传失败！");
            }
        }

        return uploadPaths;
    }


    @Override
    public String upload(String objectPrefix, File file) {
        objectPrefix = OSSConstant.OSS_OBJECT_PREFIX + '/' + objectPrefix;
        // 创建OSSClient实例。
        OSS ossClient = new OSSClientBuilder().build(ALIYUN_OSS_ENDPOINT, ALIYUN_OSS_ACCESSKEYID
                , ALIYUN_OSS_ACCESSKEYSECRET);

        return _upload(ossClient, ALIYUN_OSS_BUCKET_NAME, ALIYUN_OSS_CDN_DOMAIN, objectPrefix, file);
    }

    @Override
    public boolean hasObject(String objectPrefix, String objectName) {
        String realObjectName = !StrUtil.isEmpty(objectPrefix) ? objectPrefix + '/' + objectName : objectName;
        String ossName = OSSConstant.OSS_OBJECT_PREFIX + '/' + realObjectName;

        // 创建OSSClient实例。
        OSS ossClient = new OSSClientBuilder().build(ALIYUN_OSS_ENDPOINT, ALIYUN_OSS_ACCESSKEYID
                , ALIYUN_OSS_ACCESSKEYSECRET);

        return ossClient.doesObjectExist(ALIYUN_OSS_BUCKET_NAME, ossName);
    }

    @Override
    public String getObject(String objectPrefix, String objectName) {
        String realObjectName = !StrUtil.isEmpty(objectPrefix) ? objectPrefix + '/' + objectName : objectName;
        String ossName = OSSConstant.OSS_OBJECT_PREFIX + '/' + realObjectName;

        if (hasObject(objectPrefix, objectName)) {
            return ALIYUN_OSS_CDN_DOMAIN + ossName;
        }

        return "";
    }


    private String _upload(OSS ossClient, String bucketName, String ossDomain, String objectPrefix, File file) {
        String fileName = XinYeFileUtil.getFileNameWithoutSuffix(file.getName())
                + "_" + DateUtil.format(DateTime.now(), "yyyyMMddHHmmss") + "."
                + XinYeFileUtil.getFileExtension(file.getName());
        String objectName = !StrUtil.isEmpty(objectPrefix) ? objectPrefix + '/' + fileName : fileName;
        String ossName = objectName;
        try {
            System.out.println("oss upload local file absolutePath():" + file.getAbsolutePath());
            InputStream inputStream = new FileInputStream(file.getAbsolutePath());
            // 创建PutObjectRequest对象。
            System.out.println("upload oss, bucketName:" + bucketName
                    + "; ossName:" + ossName);
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, ossName, inputStream);
            // 如果需要上传时设置存储类型和访问权限，请参考以下示例代码。
            // ObjectMetadata metadata = new ObjectMetadata();
            // metadata.setHeader(OSSHeaders.OSS_STORAGE_CLASS, StorageClass.Standard.toString());
            // metadata.setObjectAcl(CannedAccessControlList.Private);
            // putObjectRequest.setMetadata(metadata);

            // 上传文件。
            ossClient.putObject(putObjectRequest);
        } catch (OSSException oe) {
            System.out.println("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason.");
            System.out.println("Error Message:" + oe.getErrorMessage());
            System.out.println("Error Code:" + oe.getErrorCode());
            System.out.println("Request ID:" + oe.getRequestId());
            System.out.println("Host ID:" + oe.getHostId());
        } catch (ClientException ce) {
            System.out.println("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network.");
            System.out.println("Error Message:" + ce.getMessage());
        } catch (IOException ie) {
            System.out.println("Caught an IOException, which means your request file "
                    + "encountered a internal problem while trying to getInputStream for some reason.");
            System.out.println("Error Message:" + ie.getMessage());
        } catch (Exception e) {
            System.out.println("Error Message:" + e.getMessage());
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }

        return ossDomain + ossName;
    }

    private String upload(String objectPrefix, MultipartFile file) throws Exception {
        String fileName = XinYeFileUtil.getFileNameWithoutSuffix(file.getOriginalFilename())
                + "_" + DateUtil.format(DateTime.now(), "yyyyMMddHHmmss") + "."
                + XinYeFileUtil.getFileExtension(file.getOriginalFilename());
        String objectName = !StrUtil.isEmpty(objectPrefix) ? objectPrefix + '/' + fileName : fileName;
        String ossName = OSSConstant.OSS_OBJECT_PREFIX + '/' + objectName;
        // 创建OSSClient实例。
        OSS ossClient = new OSSClientBuilder().build(ALIYUN_OSS_ENDPOINT, ALIYUN_OSS_ACCESSKEYID
                , ALIYUN_OSS_ACCESSKEYSECRET);
        try {
            InputStream inputStream = file.getInputStream();
            // 创建PutObjectRequest对象。
            PutObjectRequest putObjectRequest = new PutObjectRequest(ALIYUN_OSS_BUCKET_NAME, ossName, inputStream);
            // 如果需要上传时设置存储类型和访问权限，请参考以下示例代码。
            // ObjectMetadata metadata = new ObjectMetadata();
            // metadata.setHeader(OSSHeaders.OSS_STORAGE_CLASS, StorageClass.Standard.toString());
            // metadata.setObjectAcl(CannedAccessControlList.Private);
            // putObjectRequest.setMetadata(metadata);

            // 上传文件。
            ossClient.putObject(putObjectRequest);
        } catch (OSSException oe) {
            System.out.println("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason.");
            System.out.println("Error Message:" + oe.getErrorMessage());
            System.out.println("Error Code:" + oe.getErrorCode());
            System.out.println("Request ID:" + oe.getRequestId());
            System.out.println("Host ID:" + oe.getHostId());
            throw new Exception(oe.getErrorMessage());
        } catch (ClientException ce) {
            System.out.println("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network.");
            System.out.println("Error Message:" + ce.getMessage());
            throw new Exception(ce.getMessage());
        } catch (IOException ie) {
            System.out.println("Caught an IOException, which means your request file "
                    + "encountered a internal problem while trying to getInputStream for some reason.");
            System.out.println("Error Message:" + ie.getMessage());
            throw new Exception(ie.getMessage());
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }

        return ALIYUN_OSS_CDN_DOMAIN + ossName;
    }

    /**
     * @return
     * @Title: getAssumeRole
     * @Description:
     * <AUTHOR>
     */
    public JSONObject getAssumeRole() {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpGet request = new HttpGet(STS_TOKEN_SERVER);

        try (CloseableHttpResponse response = httpClient.execute(request)) {
            int statusCode = response.getStatusLine().getStatusCode();

            if (statusCode == 200) {
                String result = EntityUtils.toString(response.getEntity(), "UTF-8");
                System.out.println("API返回结果：" + result);
                String assumeRoleStr = result.replaceAll("\n", "");
                System.out.println("API返回结果：" + assumeRoleStr);
                JSONObject assumeRoleObject = JSONUtil.parseObj(assumeRoleStr);
                if (assumeRoleObject.get("StatusCode").equals("200")) {
                    return assumeRoleObject;
                } else {
                    Asserts.fail((String) assumeRoleObject.get("ErrorMessage"));
                }

            } else {
                System.out.println("API请求失败，错误码：" + statusCode);
            }
        } catch (ClientProtocolException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }

        Asserts.fail("请求STS临时凭证失败");
        return null;
    }

    @Override
    public String uploadImage(String base64Image, String fileName) {
        String timeStamp = DateUtil.format(new Date(), "yyyyMMddHHmmss");
        String ossfileName = StrUtil.isNotBlank(fileName) ? fileName : String.format("%s.jpg", timeStamp);
        try {
            return saveBase64ImageToOSS(base64Image, ossfileName);
        } catch (Exception ex) {
            log.error("上传图片失败：{}", ex.getMessage());
            Asserts.fail("上传图片失败!");
        }
        return null;
    }


    private String saveBase64ImageToOSS(String base64Data, String fileOriginName) throws Exception {
        if (base64Data == null || base64Data.isEmpty()) {
            Asserts.fail("图片数据为空，请重新上传!");
        }

        // 创建 OSSClient 实例。
        String fileName = XinYeFileUtil.getFileNameWithoutSuffix(fileOriginName)
                + "_" + DateUtil.format(DateTime.now(), "yyyyMMddHHmmss") + "."
                + XinYeFileUtil.getFileExtension(fileOriginName);
        String objectName = fileName;
        String ossName = OSSConstant.OSS_OBJECT_PREFIX + '/' + objectName;
        // 创建OSSClient实例。
        OSS ossClient = new OSSClientBuilder().build(ALIYUN_OSS_ENDPOINT, ALIYUN_OSS_ACCESSKEYID
                , ALIYUN_OSS_ACCESSKEYSECRET);

        try {
            // 去除 Base64 数据可能包含的前缀（如 data:image/jpeg;base64,）
            if (base64Data.startsWith("data:")) {
                base64Data = base64Data.split(",", 2)[1];
            }
            // 解码 Base64 数据
            byte[] imageData = Base64.getDecoder().decode(base64Data);

            // 创建 PutObjectRequest 对象。
            PutObjectRequest putObjectRequest = new PutObjectRequest(ALIYUN_OSS_BUCKET_NAME, ossName,
                    new ByteArrayInputStream(imageData));

            // 上传文件。
            ossClient.putObject(putObjectRequest);
        } catch (OSSException oe) {
            System.out.println("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason.");
            System.out.println("Error Message:" + oe.getErrorMessage());
            System.out.println("Error Code:" + oe.getErrorCode());
            System.out.println("Request ID:" + oe.getRequestId());
            System.out.println("Host ID:" + oe.getHostId());
            throw new Exception(oe.getErrorMessage());
        } catch (ClientException ce) {
            System.out.println("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network.");
            System.out.println("Error Message:" + ce.getMessage());
            throw new Exception(ce.getMessage());
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }

        return ALIYUN_OSS_CDN_DOMAIN + ossName;
    }


}
