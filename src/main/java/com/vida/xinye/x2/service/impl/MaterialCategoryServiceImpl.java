package com.vida.xinye.x2.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.vida.xinye.x2.domain.param.MaterialCategoryParam;
import com.vida.xinye.x2.exception.Asserts;
import com.vida.xinye.x2.mbg.mapper.MaterialCategoryMapper;
import com.vida.xinye.x2.mbg.model.*;
import com.vida.xinye.x2.service.MaterialCategoryService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 素材服务实现类
 *
 * <AUTHOR>
 * @date 2024/11/11
 */
@Service
public class MaterialCategoryServiceImpl implements MaterialCategoryService {
    @Resource
    private MaterialCategoryMapper materialCategoryMapper;

    @Override
    public List<MaterialCategory> list() {
        MaterialCategoryExample example = new MaterialCategoryExample();
        return materialCategoryMapper.selectByExample(example);
    }

    @Override
    public Long create(MaterialCategoryParam materialParam) {
        // 判断类别名称是否已存在
        MaterialCategoryExample example = new MaterialCategoryExample();
        example.createCriteria().andNameEqualTo(materialParam.getName());
        if (materialCategoryMapper.countByExample(example) > 0) {
            Asserts.fail("名称已存在！");
        }

        MaterialCategory category = new MaterialCategory();
        BeanUtil.copyProperties(materialParam, category);
        materialCategoryMapper.insertSelective(category);
        return category.getId();
    }

    @Override
    public int update(Long id, MaterialCategoryParam param) {
        MaterialCategory category = new MaterialCategory();
        BeanUtil.copyProperties(param, category);
        category.setId(id);
        return materialCategoryMapper.updateByPrimaryKeySelective(category);
    }

    @Override
    public int delete(Long id) {
        return materialCategoryMapper.deleteByPrimaryKey(id);
    }

}
