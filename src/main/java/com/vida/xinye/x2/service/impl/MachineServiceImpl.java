package com.vida.xinye.x2.service.impl;

import cn.hutool.core.util.StrUtil;
import com.vida.xinye.x2.mbg.mapper.MachineMapper;
import com.vida.xinye.x2.mbg.model.Machine;
import com.vida.xinye.x2.mbg.model.MachineExample;
import com.vida.xinye.x2.service.MachineService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 机器设备服务接口
 *
 * <AUTHOR>
 * @date 2024/11/11
 */
@Service
public class MachineServiceImpl implements MachineService {
    @Resource
    private MachineMapper machineMapper;

    @Override
    public List<Machine> list(String machineName) {
        MachineExample example = new MachineExample();
        if (!StrUtil.isEmpty(machineName)) {
            example.createCriteria().andMachineNameLike("%" + machineName + "%");
        }
        return machineMapper.selectByExample(example);
    }
}
