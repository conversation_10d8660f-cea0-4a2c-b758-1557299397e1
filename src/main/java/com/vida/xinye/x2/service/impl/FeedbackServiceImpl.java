package com.vida.xinye.x2.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.github.pagehelper.PageHelper;
import com.vida.xinye.x2.api.CommonPage;
import com.vida.xinye.x2.dao.TempletDao;
import com.vida.xinye.x2.domain.param.FeedbackParam;
import com.vida.xinye.x2.domain.param.TempletParam;
import com.vida.xinye.x2.domain.param.TempletPrintParam;
import com.vida.xinye.x2.dto.TempletDto;
import com.vida.xinye.x2.mbg.mapper.FeedbackMapper;
import com.vida.xinye.x2.mbg.mapper.PrintHistoryMapper;
import com.vida.xinye.x2.mbg.mapper.TempletMapper;
import com.vida.xinye.x2.mbg.model.Feedback;
import com.vida.xinye.x2.mbg.model.PrintHistory;
import com.vida.xinye.x2.mbg.model.Templet;
import com.vida.xinye.x2.service.FeedbackService;
import com.vida.xinye.x2.service.TempletService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 意见反馈服务实现类
 *
 * <AUTHOR>
 * @date 2024/11/11
 */
@Service
public class FeedbackServiceImpl implements FeedbackService {
    @Resource
    private FeedbackMapper feedbackMapper;

    @Override
    public Long create(FeedbackParam param) {
        Feedback feedback = new Feedback();
        BeanUtil.copyProperties(param, feedback);
        if(CollectionUtil.isNotEmpty(param.getImages())){
            feedback.setImages(JSONUtil.toJsonStr(param.getImages()));
        }
        feedbackMapper.insertSelective(feedback);
        return feedback.getId();
    }
}
