package com.vida.xinye.x2.service.impl;

import com.vida.xinye.x2.dao.TempletMarketGroupDao;
import com.vida.xinye.x2.dto.TempletMarketGroupDto;
import com.vida.xinye.x2.dto.param.TempletMarketGroupParam;
import com.vida.xinye.x2.exception.Asserts;
import com.vida.xinye.x2.mbg.mapper.TempletMarketGroupMapper;
import com.vida.xinye.x2.mbg.mapper.TempletMarketMapper;
import com.vida.xinye.x2.mbg.model.*;
import com.vida.xinye.x2.service.TempletMarketGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class TempletMarketGroupServiceImpl implements TempletMarketGroupService {

    @Autowired
    private TempletMarketGroupMapper groupMapper;
    @Autowired
    private TempletMarketMapper templetMapper;
    @Autowired
    private TempletMarketGroupDao templetMarketGroupDao;

    @Override
    public List<TempletMarketGroup> listGroups() {
        TempletMarketGroupExample example = new TempletMarketGroupExample();
        example.setOrderByClause("sort asc");
        return groupMapper.selectByExample(example);
    }

    @Override
    public Long createGroup(TempletMarketGroupParam param) {
        // 校验分组名称唯一性
        TempletMarketGroupExample example = new TempletMarketGroupExample();
        example.createCriteria().andNameEqualTo(param.getName());
        if (groupMapper.countByExample(example) > 0) {
            Asserts.fail("分组名称已存在");
        }

        TempletMarketGroup group = new TempletMarketGroup();
        group.setName(param.getName());
        group.setSort(param.getSort());
        group.setType(param.getType());
        groupMapper.insertSelective(group);

        // 多语言处理
        if (param.getI18nNames() != null && !param.getI18nNames().isEmpty()) {
            List<TempletMarketGroupI18n> insertGroupsI18n = param.getI18nNames().stream().map(nameDto -> {
                TempletMarketGroupI18n i18n = new TempletMarketGroupI18n();
                i18n.setGroupId(group.getId());
                i18n.setLocale(nameDto.getLocale());
                i18n.setName(nameDto.getName());
                return i18n;
            }).collect(Collectors.toList());
            // 插入多语言记录
            templetMarketGroupDao.insert(insertGroupsI18n);
        }

        return group.getId();
    }

    @Override
    public int updateGroup(Long id, TempletMarketGroupParam param) {
        TempletMarketGroup existing = groupMapper.selectByPrimaryKey(id);
        if (existing == null) {
            Asserts.fail("分组不存在");
        }

        TempletMarketGroup update = new TempletMarketGroup();
        update.setId(id);
        update.setName(param.getName());
        update.setSort(param.getSort());
        param.setType(param.getType());

        // 多语言处理
        if (param.getI18nNames()!= null &&!param.getI18nNames().isEmpty()) {
            List<TempletMarketGroupI18n> updateGroupsI18n = param.getI18nNames().stream().map(nameDto -> {
                TempletMarketGroupI18n i18n = new TempletMarketGroupI18n();
                i18n.setGroupId(id);
                i18n.setLocale(nameDto.getLocale());
                i18n.setName(nameDto.getName());
                return i18n;
            }).collect(Collectors.toList());
            // 插入多语言记录
            templetMarketGroupDao.insert(updateGroupsI18n);
        }
        return groupMapper.updateByPrimaryKeySelective(update);
    }

    @Override
    public int deleteGroup(Long id) {
        // 检查是否存在
        TempletMarketGroup existing = groupMapper.selectByPrimaryKey(id);
        if (existing == null) {
            Asserts.fail("分组不存在");
        }

        // 检查是否存在关联模板集市
        TempletMarketExample example = new TempletMarketExample();
        example.createCriteria().andGroupIdEqualTo(id);
        if (templetMapper.countByExample(example) > 0) {
            Asserts.fail("请先移除分组下的模板集市");
        }
        return groupMapper.deleteByPrimaryKey(id);
    }

    @Transactional
    @Override
    public int sortGroups(List<Long> groupIds) {
        for (int i = 0; i < groupIds.size(); i++) {
            TempletMarketGroup group = new TempletMarketGroup();
            group.setId(groupIds.get(i));
            group.setSort(i + 1);
            groupMapper.updateByPrimaryKeySelective(group);
        }
        return groupIds.size();
    }

    @Override
    public List<TempletMarketGroupDto> listWithLocale(String locale) {
        return templetMarketGroupDao.list(locale);
    }
}
