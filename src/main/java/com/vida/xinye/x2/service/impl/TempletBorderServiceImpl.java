package com.vida.xinye.x2.service.impl;

import com.vida.xinye.x2.dao.TempletBorderDao;
import com.vida.xinye.x2.dto.TempletBorderDto;
import com.vida.xinye.x2.exception.Asserts;
import com.vida.xinye.x2.mbg.mapper.TempletBorderGroupMapper;
import com.vida.xinye.x2.mbg.mapper.TempletBorderMapper;
import com.vida.xinye.x2.mbg.model.*;
import com.vida.xinye.x2.service.TempletBorderService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 标签服务实现类
 *
 * <AUTHOR>
 * @date 2025/06/27
 */
@Service
public class TempletBorderServiceImpl implements TempletBorderService {
    @Resource
    private TempletBorderMapper borderMapper;
    @Resource
    private TempletBorderGroupMapper groupMapper;
    @Resource
    private TempletBorderDao templetBorderDao;

    @Override
    public List<TempletBorderDto> list(Long groupId) {
        return templetBorderDao.list(groupId);
    }

    @Override
    @Transactional
    public Long create(TempletBorderDto border) {
        // 校验所属分组是否存在
        TempletBorderGroup group = groupMapper.selectByPrimaryKey(border.getGroupId());
        if (group == null) {
            Asserts.fail("所属分组不存在");
        }

        templetBorderDao.insert(border);
        return border.getId();
    }

    @Override
    public int update(Long id, TempletBorderDto border) {
        TempletBorder existing = borderMapper.selectByPrimaryKey(id);
        if (existing == null) {
            Asserts.fail("边框数据不存在");
        }
        if(border.getGroupId() == null) {
          Asserts.fail("所属分组不存在！");
        }

        border.setId(id);
        return templetBorderDao.update(border);
    }

    @Override
    @Transactional
    public int delete(Long id) {
        TempletBorder existing = borderMapper.selectByPrimaryKey(id);
        if (existing == null) {
            Asserts.fail("边框数据不存在");
        }
        return borderMapper.deleteByPrimaryKey(id);
    }

    @Override
    @Transactional
    public int batchDelete(List<Long> borderIds, Long userId) {
        int count = 0;
        for (Long id : borderIds) {
            count += delete(id); // 复用单条删除逻辑
        }
        return count;
    }

}
