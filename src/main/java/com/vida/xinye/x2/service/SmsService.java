package com.vida.xinye.x2.service;

import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import com.aliyun.teaopenapi.models.Config;
import com.vida.xinye.x2.util.PhoneUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

/**
 * 短信服务类
 *
 * <AUTHOR>
 * @date 2024/11/5
 */
@Service
@RefreshScope
@Slf4j
public class SmsService {

    @Value("${aliyun.sms.access-key-id}")
    private String accessKeyId;

    @Value("${aliyun.sms.access-key-secret}")
    private String accessKeySecret;

    @Value("${aliyun.sms.sign-name}")
    private String signName;

    @Value("${aliyun.sms.template-code}")
    private String templateCode;

    @Value("${aliyun.sms.endpoint:dysmsapi.aliyuncs.com}")
    private String endpoint;

    /**
     * 发送验证码短信
     *
     * @param phoneNumber      手机号
     * @param verificationCode 验证码
     * @return 是否发送成功
     */
    public boolean sendVerificationCode(String phoneNumber, String verificationCode) {
        try {
            // 配置阿里云SDK
            Config config = new Config()
                    .setAccessKeyId(accessKeyId)
                    .setAccessKeySecret(accessKeySecret);
            config.endpoint = endpoint;

            // 创建短信客户端
            Client client = new Client(config);

            // 构建短信发送请求
            // 注意：模板参数必须是标准JSON格式，属性名要与短信模板中的占位符一致
            String templateParam = String.format("{\"code\":\"%s\"}", verificationCode);

            SendSmsRequest sendSmsRequest = new SendSmsRequest()
                    .setPhoneNumbers(phoneNumber)
                    .setSignName(signName)
                    .setTemplateCode(templateCode)
                    .setTemplateParam(templateParam);

            // 发送短信
            log.info("发送短信验证码: phone={}, templateParam={}", PhoneUtil.maskPhone(phoneNumber), templateParam);
            SendSmsResponse response = client.sendSms(sendSmsRequest);

            // 检查发送结果
            if ("OK".equals(response.getBody().getCode())) {
                log.debug("短信验证码发送成功: phone={}", PhoneUtil.maskPhone(phoneNumber));
                return true;
            } else {
                log.error("短信验证码发送失败: phone={}, code={}, message={}",
                        PhoneUtil.maskPhone(phoneNumber), response.getBody().getCode(),
                        response.getBody().getMessage());
                return false;
            }

        } catch (Exception e) {
            log.error("短信验证码发送异常: phone={}, error={}", PhoneUtil.maskPhone(phoneNumber), e.getMessage(), e);
            return false;
        }
    }
}