package com.vida.xinye.x2.service.impl;

import cn.hutool.core.util.StrUtil;
import com.vida.xinye.x2.dto.AccountBindingDto;
import com.vida.xinye.x2.dto.AccountBindingStatusDto;
import com.vida.xinye.x2.dto.param.BindEmailDto;
import com.vida.xinye.x2.dto.param.BindPhoneDto;
import com.vida.xinye.x2.dto.param.UnbindAccountDto;
import com.vida.xinye.x2.mbg.mapper.UserMapper;
import com.vida.xinye.x2.mbg.mapper.UserThirdPartyAuthMapper;
import com.vida.xinye.x2.mbg.model.User;
import com.vida.xinye.x2.mbg.model.UserExample;
import com.vida.xinye.x2.mbg.model.UserThirdPartyAuth;
import com.vida.xinye.x2.service.AccountBindingService;
import com.vida.xinye.x2.service.CaptchaService;
import com.vida.xinye.x2.exception.Asserts;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 账号绑定服务实现类
 *
 * <AUTHOR>
 * @date 2025/08/14
 */
@Slf4j
@Service
public class AccountBindingServiceImpl implements AccountBindingService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private UserThirdPartyAuthMapper userThirdPartyAuthMapper;

    @Autowired
    private CaptchaService captchaService;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    @Transactional
    public boolean bindPhone(Long userId, BindPhoneDto bindPhoneDto) {
        try {
            // 1. 验证验证码
            if (!captchaService.verifyCode(bindPhoneDto.getPhone(), bindPhoneDto.getCaptcha())) {
                Asserts.fail("验证码错误或已过期");
            }

            // 2. 检查手机号是否已被其他用户绑定
            if (isPhoneAlreadyBound(bindPhoneDto.getPhone(), userId)) {
                Asserts.fail("该手机号已被其他用户绑定");
            }

            // 3. 更新用户手机号
            User user = userMapper.selectByPrimaryKey(userId);
            if (user == null) {
                Asserts.fail("用户不存在");
            }

            user.setPhone(bindPhoneDto.getPhone());
            user.setPhoneVerified(1); // 通过验证码验证，标记为已验证
            user.setRequirePhoneBinding(0); // 不再需要绑定手机号
            user.setUpdateTime(new Date());

            userMapper.updateByPrimaryKeySelective(user);

            // 4. 设置验证码失效
            captchaService.setCaptchaExpire(bindPhoneDto.getPhone());

            log.info("用户绑定手机号成功 - 用户ID: {}, 手机号: {}", userId, maskPhone(bindPhoneDto.getPhone()));
            return true;

        } catch (Exception e) {
            log.error("绑定手机号失败 - 用户ID: {}, 手机号: {}", userId, maskPhone(bindPhoneDto.getPhone()), e);
            throw e;
        }
    }

    @Override
    @Transactional
    public boolean bindEmail(Long userId, BindEmailDto bindEmailDto) {
        try {
            // 1. 验证验证码
            if (!captchaService.verifyCode(bindEmailDto.getEmail(), bindEmailDto.getCaptcha())) {
                Asserts.fail("验证码错误或已过期");
            }

            // 2. 检查邮箱是否已被其他用户绑定
            if (isEmailAlreadyBound(bindEmailDto.getEmail(), userId)) {
                Asserts.fail("该邮箱已被其他用户绑定");
            }

            // 3. 更新用户邮箱
            User user = userMapper.selectByPrimaryKey(userId);
            if (user == null) {
                Asserts.fail("用户不存在");
            }

            user.setEmail(bindEmailDto.getEmail());
            user.setEmailVerified(1); // 通过验证码验证，标记为已验证
            user.setUpdateTime(new Date());

            userMapper.updateByPrimaryKeySelective(user);

            // 4. 设置验证码失效
            captchaService.setCaptchaExpire(bindEmailDto.getEmail());

            log.info("用户绑定邮箱成功 - 用户ID: {}, 邮箱: {}", userId, maskEmail(bindEmailDto.getEmail()));
            return true;

        } catch (Exception e) {
            log.error("绑定邮箱失败 - 用户ID: {}, 邮箱: {}", userId, maskEmail(bindEmailDto.getEmail()), e);
            throw e;
        }
    }

    @Override
    @Transactional
    public boolean unbindAccount(Long userId, UnbindAccountDto unbindAccountDto) {
        try {
            // 1. 验证验证码
            User user = userMapper.selectByPrimaryKey(userId);
            if (user == null) {
                Asserts.fail("用户不存在");
            }

            // 2. 根据绑定类型进行解绑
            switch (unbindAccountDto.getBindingType().toLowerCase()) {
                case "phone":
                    if (StrUtil.isEmpty(user.getPhone())) {
                        Asserts.fail("未绑定手机号");
                    }
                    // 验证手机验证码
                    if (!captchaService.verifyCode(user.getPhone(), unbindAccountDto.getCaptcha())) {
                        Asserts.fail("验证码错误或已过期");
                    }
                    // 检查是否为唯一登录方式
                    if (isOnlyLoginMethod(user, "phone")) {
                        Asserts.fail("手机号是唯一登录方式，无法解绑");
                    }
                    user.setPhone(null);
                    user.setPhoneVerified(0);
                    break;

                case "email":
                    if (StrUtil.isEmpty(user.getEmail())) {
                        Asserts.fail("未绑定邮箱");
                    }
                    // 验证邮箱验证码
                    if (!captchaService.verifyCode(user.getEmail(), unbindAccountDto.getCaptcha())) {
                        Asserts.fail("验证码错误或已过期");
                    }
                    // 检查是否为唯一登录方式
                    if (isOnlyLoginMethod(user, "email")) {
                        Asserts.fail("邮箱是唯一登录方式，无法解绑");
                    }
                    user.setEmail(null);
                    user.setEmailVerified(0);
                    break;

                case "wechat":
                case "weixin":
                    return unbindThirdPartyAccount(userId, "wechat", unbindAccountDto.getCaptcha());

                case "qq":
                    return unbindThirdPartyAccount(userId, "qq", unbindAccountDto.getCaptcha());

                case "weibo":
                case "sina":
                    return unbindThirdPartyAccount(userId, "weibo", unbindAccountDto.getCaptcha());

                case "apple":
                case "appleid":
                    return unbindThirdPartyAccount(userId, "apple", unbindAccountDto.getCaptcha());

                default:
                    Asserts.fail("不支持的绑定类型: " + unbindAccountDto.getBindingType());
            }

            user.setUpdateTime(new Date());
            userMapper.updateByPrimaryKeySelective(user);

            log.info("用户解绑账号成功 - 用户ID: {}, 绑定类型: {}", userId, unbindAccountDto.getBindingType());
            return true;

        } catch (Exception e) {
            log.error("解绑账号失败 - 用户ID: {}, 绑定类型: {}", userId, unbindAccountDto.getBindingType(), e);
            throw e;
        }
    }

    @Override
    public List<AccountBindingDto> getUserBindings(Long userId) {
        List<AccountBindingDto> bindings = new ArrayList<>();
        
        User user = userMapper.selectByPrimaryKey(userId);
        if (user == null) {
            return bindings;
        }

        // 手机号绑定
        if (StrUtil.isNotEmpty(user.getPhone())) {
            AccountBindingDto phoneBinding = new AccountBindingDto();
            phoneBinding.setUserId(userId);
            phoneBinding.setAccountType(1); // 手机号类型
            phoneBinding.setAccountTypeDesc("手机号");
            phoneBinding.setAccountIdentifier(maskPhone(user.getPhone()));
            phoneBinding.setIsPrimary(true); // 假设手机号为主账号
            phoneBinding.setIsVerified(user.getPhoneVerified() == 1);
            phoneBinding.setBindTime(user.getCreateTime());
            bindings.add(phoneBinding);
        }

        // 邮箱绑定
        if (StrUtil.isNotEmpty(user.getEmail())) {
            AccountBindingDto emailBinding = new AccountBindingDto();
            emailBinding.setUserId(userId);
            emailBinding.setAccountType(2); // 邮箱类型
            emailBinding.setAccountTypeDesc("邮箱");
            emailBinding.setAccountIdentifier(maskEmail(user.getEmail()));
            emailBinding.setIsPrimary(false);
            emailBinding.setIsVerified(user.getEmailVerified() == 1);
            emailBinding.setBindTime(user.getCreateTime());
            bindings.add(emailBinding);
        }

        // 第三方绑定
        List<UserThirdPartyAuth> thirdPartyAuths = userThirdPartyAuthMapper.selectByUserId(userId);
        if (thirdPartyAuths != null) {
            for (UserThirdPartyAuth auth : thirdPartyAuths) {
                AccountBindingDto thirdPartyBinding = new AccountBindingDto();
                thirdPartyBinding.setUserId(userId);
                thirdPartyBinding.setAccountType(getAccountTypeByProvider(auth.getProvider())); // 第三方类型
                thirdPartyBinding.setAccountTypeDesc(getProviderDisplayName(auth.getProvider()));
                thirdPartyBinding.setAccountIdentifier(maskAccount(auth.getOpenId()));
                thirdPartyBinding.setIsPrimary(false);
                thirdPartyBinding.setIsVerified(true); // 第三方登录默认已验证
                thirdPartyBinding.setBindTime(auth.getBindTime());
                bindings.add(thirdPartyBinding);
            }
        }

        return bindings;
    }

    @Override
    public boolean isAccountBound(Integer accountType, String accountIdentifier) {
        UserExample example = new UserExample();
        UserExample.Criteria criteria = example.createCriteria();

        if (accountType == 1) { // 手机号
            criteria.andPhoneEqualTo(accountIdentifier);
        } else if (accountType == 2) { // 邮箱
            criteria.andEmailEqualTo(accountIdentifier);
        } else {
            return false;
        }

        return userMapper.countByExample(example) > 0;
    }

    @Override
    public boolean requiresPhoneBinding(Long userId) {
        User user = userMapper.selectByPrimaryKey(userId);
        if (user == null) {
            return false;
        }

        // 检查是否需要绑定手机号
        return user.getRequirePhoneBinding() != null && user.getRequirePhoneBinding() == 1;
    }

    @Override
    @Transactional
    public boolean setPrimaryAccount(Long userId, Integer accountType, String accountIdentifier) {
        // 这里可以实现设置主账号的逻辑
        // 当前简化实现，直接返回true
        log.info("设置主账号 - 用户ID: {}, 账号类型: {}, 账号标识: {}", userId, accountType, maskAccount(accountIdentifier));
        return true;
    }

    @Override
    public AccountBindingStatusDto checkBindingStatus(Long userId) {
        AccountBindingStatusDto status = new AccountBindingStatusDto();
        status.setUserId(userId);

        User user = userMapper.selectByPrimaryKey(userId);
        if (user == null) {
            return status;
        }

        // 手机号绑定状态
        AccountBindingStatusDto.PhoneBindingInfo phoneInfo = new AccountBindingStatusDto.PhoneBindingInfo();
        phoneInfo.setBound(StrUtil.isNotEmpty(user.getPhone()));
        phoneInfo.setPhone(phoneInfo.isBound() ? maskPhone(user.getPhone()) : null);
        phoneInfo.setVerified(user.getPhoneVerified() == 1);
        status.setPhoneBinding(phoneInfo);

        // 邮箱绑定状态
        AccountBindingStatusDto.EmailBindingInfo emailInfo = new AccountBindingStatusDto.EmailBindingInfo();
        emailInfo.setBound(StrUtil.isNotEmpty(user.getEmail()));
        emailInfo.setEmail(emailInfo.isBound() ? maskEmail(user.getEmail()) : null);
        emailInfo.setVerified(user.getEmailVerified() == 1);
        status.setEmailBinding(emailInfo);

        // 第三方绑定状态
        List<AccountBindingStatusDto.ThirdPartyBindingInfo> thirdPartyBindings = new ArrayList<>();
        List<UserThirdPartyAuth> thirdPartyAuths = userThirdPartyAuthMapper.selectByUserId(userId);
        if (thirdPartyAuths != null) {
            for (UserThirdPartyAuth auth : thirdPartyAuths) {
                AccountBindingStatusDto.ThirdPartyBindingInfo bindingInfo = new AccountBindingStatusDto.ThirdPartyBindingInfo();
                bindingInfo.setProvider(auth.getProvider());
                bindingInfo.setProviderName(getProviderDisplayName(auth.getProvider()));
                bindingInfo.setBound(true);
                bindingInfo.setBindTime(auth.getBindTime());
                bindingInfo.setLastLoginTime(auth.getLastLoginTime());
                thirdPartyBindings.add(bindingInfo);
            }
        }
        status.setThirdPartyBindings(thirdPartyBindings);

        return status;
    }

    @Override
    public boolean isPhoneAlreadyBound(String phone, Long excludeUserId) {
        UserExample example = new UserExample();
        UserExample.Criteria criteria = example.createCriteria();
        criteria.andPhoneEqualTo(phone);
        
        if (excludeUserId != null) {
            criteria.andIdNotEqualTo(excludeUserId);
        }

        return userMapper.countByExample(example) > 0;
    }

    /**
     * 检查邮箱是否已被其他用户绑定
     */
    @Override
    public boolean isEmailAlreadyBound(String email, Long excludeUserId) {
        UserExample example = new UserExample();
        UserExample.Criteria criteria = example.createCriteria();
        criteria.andEmailEqualTo(email);
        
        if (excludeUserId != null) {
            criteria.andIdNotEqualTo(excludeUserId);
        }

        return userMapper.countByExample(example) > 0;
    }

    /**
     * 解绑第三方账号
     */
    private boolean unbindThirdPartyAccount(Long userId, String provider, String captcha) {
        try {
            // 1. 检查第三方账号是否存在
            UserThirdPartyAuth thirdPartyAuth = userThirdPartyAuthMapper.selectByUserIdAndProvider(userId, provider);
            if (thirdPartyAuth == null) {
                Asserts.fail("未绑定" + getProviderDisplayName(provider) + "账号");
            }

            // 2. 验证验证码（使用用户的手机号或邮箱）
            User user = userMapper.selectByPrimaryKey(userId);
            if (user == null) {
                Asserts.fail("用户不存在");
            }

            boolean captchaValid = false;
            if (StrUtil.isNotEmpty(user.getPhone())) {
                captchaValid = captchaService.verifyCode(user.getPhone(), captcha);
            } else if (StrUtil.isNotEmpty(user.getEmail())) {
                captchaValid = captchaService.verifyCode(user.getEmail(), captcha);
            }

            if (!captchaValid) {
                Asserts.fail("验证码错误或已过期");
            }

            // 3. 检查是否为唯一登录方式
            if (isOnlyLoginMethod(user, provider)) {
                Asserts.fail(getProviderDisplayName(provider) + "是唯一登录方式，无法解绑");
            }

            // 4. 删除第三方绑定记录
            int deleteCount = userThirdPartyAuthMapper.deleteByUserIdAndProvider(userId, provider);
            if (deleteCount > 0) {
                log.info("第三方账号解绑成功 - 用户ID: {}, 平台: {}", userId, provider);
                return true;
            } else {
                log.warn("第三方账号解绑失败，未找到绑定记录 - 用户ID: {}, 平台: {}", userId, provider);
                return false;
            }

        } catch (Exception e) {
            log.error("第三方账号解绑失败 - 用户ID: {}, 平台: {}", userId, provider, e);
            throw e;
        }
    }

    /**
     * 获取平台显示名称
     */
    private String getProviderDisplayName(String provider) {
        switch (provider.toLowerCase()) {
            case "wechat":
                return "微信";
            case "qq":
                return "QQ";
            case "weibo":
                return "微博";
            case "apple":
                return "Apple ID";
            default:
                return provider;
        }
    }

    /**
     * 根据第三方平台获取账号类型
     */
    private Integer getAccountTypeByProvider(String provider) {
        switch (provider.toLowerCase()) {
            case "wechat":
                return 3; // 微信类型
            case "qq":
                return 4; // QQ类型
            case "weibo":
                return 5; // 微博类型
            case "apple":
                return 6; // Apple ID类型
            default:
                return 99; // 其他第三方类型
        }
    }

    /**
     * 检查是否为唯一登录方式
     */
    private boolean isOnlyLoginMethod(User user, String type) {
        int bindingCount = 0;

        // 检查手机号
        if (StrUtil.isNotEmpty(user.getPhone())) {
            bindingCount++;
        }

        // 检查邮箱
        if (StrUtil.isNotEmpty(user.getEmail())) {
            bindingCount++;
        }

        // 检查第三方登录方式
        List<UserThirdPartyAuth> thirdPartyAuths = userThirdPartyAuthMapper.selectByUserId(user.getId());
        if (thirdPartyAuths != null && !thirdPartyAuths.isEmpty()) {
            bindingCount += thirdPartyAuths.size();
        }

        return bindingCount <= 1;
    }

    /**
     * 手机号脱敏
     */
    private String maskPhone(String phone) {
        if (phone == null || phone.length() < 7) {
            return phone;
        }
        return phone.substring(0, 3) + "****" + phone.substring(7);
    }

    /**
     * 邮箱脱敏
     */
    private String maskEmail(String email) {
        if (email == null || !email.contains("@")) {
            return email;
        }
        int atIndex = email.indexOf('@');
        if (atIndex > 1) {
            return email.charAt(0) + "***" + email.substring(atIndex);
        }
        return email;
    }

    /**
     * 通用账号脱敏
     */
    private String maskAccount(String account) {
        if (account == null || account.length() <= 3) {
            return account;
        }
        return account.substring(0, 2) + "***" + account.substring(account.length() - 1);
    }
}
