package com.vida.xinye.x2.service;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.concurrent.TimeUnit;

/**
 * @Description: API签名验证服务（含防重放攻击）
 * @Author: zhangwenbin
 * @Date: 2024/11/05 17:05
 */
@Service
@Slf4j
public class SignatureService {

  @Value("${api.signature.secret-key:xinyeDefaultSecretKey2024}")
  private String secretKey;

  @Autowired
  private StringRedisTemplate redisTemplate;

  private static final String SIGNATURE_USED_PREFIX = "signature:used:";

  /**
   * 验证签名（含防重放攻击）
   *
   * @param ip         客户端IP
   * @param timestamp  时间戳
   * @param signature  客户端提供的签名
   * @param expireTime 签名有效期（秒）
   * @return true: 验证通过, false: 验证失败
   */
  public boolean verifySignature(String ip, String timestamp, String signature, int expireTime) {
    if (StrUtil.isBlank(ip) || StrUtil.isBlank(timestamp) || StrUtil.isBlank(signature)) {
      log.warn("签名验证失败：参数不完整 ip={}, timestamp={}, signature={}", ip, timestamp, signature != null ? "***" : null);
      return false;
    }

    // 验证时间戳有效性
    if (!isTimestampValid(timestamp, expireTime)) {
      log.warn("签名验证失败：时间戳无效 timestamp={}, expireTime={}", timestamp, expireTime);
      return false;
    }

    // 防重放攻击：检查签名是否已被使用
    String signatureKey = SIGNATURE_USED_PREFIX + signature;
    Boolean hasBeenUsed = redisTemplate.hasKey(signatureKey);
    if (Boolean.TRUE.equals(hasBeenUsed)) {
      log.warn("签名验证失败：签名已被使用（重放攻击） ip={}, timestamp={}, signature=***", ip, timestamp);
      return false;
    }

    // 生成期望的签名
    String expectedSignature = generateSignature(ip, timestamp);

    // 比较签名
    boolean isValid = expectedSignature.equals(signature);
    if (!isValid) {
      log.warn("签名验证失败：签名不匹配 ip={}, timestamp={}, expected={}, actual={}",
          ip, timestamp, expectedSignature, signature);
      return false;
    }

    // 签名验证通过，记录该签名已被使用（防重放）
    try {
      redisTemplate.opsForValue().set(signatureKey, ip + ":" + timestamp, expireTime, TimeUnit.SECONDS);
      log.debug("签名验证成功，已记录防重放 ip={}, timestamp={}", ip, timestamp);
    } catch (Exception e) {
      log.error("记录签名使用状态失败，但签名验证通过 ip={}, timestamp={}", ip, timestamp, e);
      // 即使Redis操作失败，签名验证仍然通过，不影响业务
    }

    return true;
  }

  /**
   * 生成签名
   *
   * @param ip        IP地址
   * @param timestamp 时间戳
   * @return 签名字符串
   */
  public String generateSignature(String ip, String timestamp) {
    try {
      // 构造待签名字符串：ip + timestamp + secretKey
      String data = ip + timestamp + secretKey;

      // 使用HMAC-SHA256生成签名
      Mac mac = Mac.getInstance("HmacSHA256");
      SecretKeySpec secretKeySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
      mac.init(secretKeySpec);

      byte[] hashBytes = mac.doFinal(data.getBytes(StandardCharsets.UTF_8));

      // 转换为十六进制字符串
      StringBuilder hexString = new StringBuilder();
      for (byte b : hashBytes) {
        String hex = Integer.toHexString(0xff & b);
        if (hex.length() == 1) {
          hexString.append('0');
        }
        hexString.append(hex);
      }
      return hexString.toString();

    } catch (NoSuchAlgorithmException | InvalidKeyException e) {
      log.error("签名生成失败", e);
      throw new RuntimeException("签名生成失败", e);
    }
  }

  /**
   * 验证时间戳是否在有效期内
   *
   * @param timestamp  时间戳字符串（毫秒）
   * @param expireTime 有效期（秒）
   * @return true: 有效, false: 无效
   */
  private boolean isTimestampValid(String timestamp, int expireTime) {
    try {
      long clientTime = Long.parseLong(timestamp);
      long currentTime = System.currentTimeMillis();
      long timeDiff = Math.abs(currentTime - clientTime);

      // 转换为秒进行比较
      return timeDiff <= (expireTime * 1000L);
    } catch (NumberFormatException e) {
      log.warn("时间戳格式错误: {}", timestamp);
      return false;
    }
  }

  /**
   * 生成示例签名（用于测试）
   *
   * @param ip IP地址
   * @return 包含时间戳和签名的示例
   */
  public String generateExampleSignature(String ip) {
    String timestamp = String.valueOf(System.currentTimeMillis());
    String signature = generateSignature(ip, timestamp);

    return String.format("timestamp: %s, signature: %s", timestamp, signature);
  }

  /**
   * 清理过期的签名记录（可选，Redis TTL会自动清理）
   */
  public void cleanupExpiredSignatures() {
    // Redis的TTL机制会自动清理过期的key，这个方法预留给手动清理的场景
    log.info("过期签名记录清理完成（Redis TTL自动清理）");
  }

  public static void main(String[] args) {
    SignatureService service = new SignatureService();
    // 手动设置secretKey，因为@Value注解在main方法中不会生效
    service.secretKey = "xinye_captcha_secret_key_2024_v1.0";

    String timestamp = String.valueOf(System.currentTimeMillis());
    String ip = "************";
    String signature = service.generateSignature(ip, timestamp);
    System.out.println("signature: " + signature);

    System.out.println(String.format("ip: %s, timestamp: %s, signature: %s", ip, timestamp, signature));

    // 注意：main方法中无法测试防重放功能，因为没有Redis连接
    System.out.println("注意：防重放功能需要Redis支持，main方法中无法测试");
  }
}