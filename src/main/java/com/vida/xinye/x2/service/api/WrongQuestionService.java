package com.vida.xinye.x2.service.api;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.PageHelper;
import com.vida.xinye.x2.api.CommonPage;
import com.vida.xinye.x2.constant.TagConstant;
import com.vida.xinye.x2.dao.WrongQuestionDao;
import com.vida.xinye.x2.dao.WrongQuestionTagRelationDao;
import com.vida.xinye.x2.domain.param.BatchMoveWrongQuestionsParam;
import com.vida.xinye.x2.domain.param.WrongQuestionParam;
import com.vida.xinye.x2.domain.param.WrongQuestionSubjectParam;
import com.vida.xinye.x2.dto.WrongQuestionDto;
import com.vida.xinye.x2.dto.WrongQuestionSubject;
import com.vida.xinye.x2.exception.Asserts;
import com.vida.xinye.x2.mbg.mapper.WrongQuestionMapper;
import com.vida.xinye.x2.mbg.mapper.WrongQuestionTagMapper;
import com.vida.xinye.x2.mbg.mapper.WrongQuestionTagRelationMapper;
import com.vida.xinye.x2.mbg.mapper.WrongQuestionTagTypeMapper;
import com.vida.xinye.x2.mbg.mapper.XkwCourseMapper;
import com.vida.xinye.x2.mbg.mapper.XkwSubjectMapper;
import com.vida.xinye.x2.mbg.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 错题标签服务接口
 * @Author: zhangwenbin
 * @Date: 2025/01/16 14:30
 */
@Service
@Slf4j
public class WrongQuestionService {
    @Autowired
    private WrongQuestionMapper wrongQuestionMapper;
    @Autowired
    private WrongQuestionTagMapper wrongQuestionTagMapper;
    @Autowired
    private WrongQuestionTagTypeMapper tagTypeMapper;
    @Autowired
    private WrongQuestionTagRelationMapper questionTagRelationMapper;
    @Autowired
    private XkwCourseMapper xkwCourseMapper;
    @Autowired
    private XkwSubjectMapper xkwSubjectMapper;
    @Resource
    private WrongQuestionTagRelationDao questionTagRelationDao;
    @Resource
    private WrongQuestionDao wrongQuestionDao;

    /**
     * 根据错题标签，返回标签对应的错题
     *
     * @param tagIds 错题标签 ID(多个）
     * @param userId 用户 ID
     * @return 错题标签对应的错题列表
     */
    public CommonPage<WrongQuestionDto> list(List<Long> tagIds, String sourceType, String saveTimeRange, Long userId,
                                             Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);

        List<WrongQuestionDto> wrongQuestions = wrongQuestionDao.getList(tagIds, sourceType, saveTimeRange, userId);

        // 过滤掉科目标签（tagTypeId = 1），因为前端已经单独显示科目信息
        wrongQuestions.forEach(wrongQuestion -> {
            if (wrongQuestion.getTags() != null) {
                wrongQuestion.getTags().removeIf(tag -> tag.getTagTypeId() != null && tag.getTagTypeId().equals(1L));
            }
        });

        CommonPage<WrongQuestionDto> wrongQuestionPage = CommonPage.restPage(wrongQuestions);

        // 设置分页信息
        CommonPage<WrongQuestionDto> resultPage = new CommonPage<>();
        resultPage.setPageNum(wrongQuestionPage.getPageNum());
        resultPage.setPageSize(wrongQuestionPage.getPageSize());
        resultPage.setTotal(wrongQuestionPage.getTotal());
        resultPage.setTotalPage(wrongQuestionPage.getTotalPage());
        resultPage.setList(wrongQuestions);

        return resultPage;
    }

    /**
     * 新增错题
     *
     * @param param
     */
    @Transactional
    public Long create(WrongQuestionParam param) {
        // 根据courseId自动匹配学科标签
        if (param.getCourseId() != null) {
            List<WrongQuestionTag> autoTags = getSubjectTagByCourseId(param.getCourseId(), param.getUserId());
            if (CollUtil.isNotEmpty(autoTags)) {
                // 将自动匹配的学科标签添加到传入的标签列表中
                List<WrongQuestionTag> allTags = new ArrayList<>();
                if (CollUtil.isNotEmpty(param.getTags())) {
                    allTags.addAll(param.getTags());
                }
                // 避免重复添加相同的标签
                for (WrongQuestionTag autoTag : autoTags) {
                    boolean exists = allTags.stream().anyMatch(tag -> tag.getId().equals(autoTag.getId()));
                    if (!exists) {
                        allTags.add(autoTag);
                    }
                }
                param.setTags(allTags);
            }
        }

        // 创建错题
        WrongQuestion question = param;
        question.setId(null);
        wrongQuestionMapper.insertSelective(question);

        // 给错题打标签
        allocWrongQuestionTags(question.getId(), new ArrayList<>(), param.getTags());

        return question.getId();
    }

    /**
     * 根据课程ID获取对应的学科标签
     *
     * @param courseId 课程ID
     * @param userId   用户ID
     * @return 学科标签列表
     */
    private List<WrongQuestionTag> getSubjectTagByCourseId(Long courseId, Long userId) {
        try {
            // 1. 根据courseId查询课程信息，获取subjectId
            XkwCourse course = xkwCourseMapper.selectByPrimaryKey(courseId.intValue());
            if (course == null || course.getSubjectId() == null) {
                // 课程不存在，返回"未分类"标签
                return getUncategorizedTag(userId);
            }

            // 2. 根据subjectId查询学科信息，获取学科名称
            XkwSubject subject = xkwSubjectMapper.selectByPrimaryKey(course.getSubjectId());
            if (subject == null || ObjectUtil.isEmpty(subject.getName())) {
                // 学科不存在，返回"未分类"标签
                return getUncategorizedTag(userId);
            }

            // 3. 根据学科名称查询错题标签（tag_type_id = 1 代表科目）
            WrongQuestionTagExample tagExample = new WrongQuestionTagExample();
            WrongQuestionTagExample.Criteria criteria1 = tagExample.createCriteria();
            criteria1.andTagNameEqualTo(subject.getName());
            criteria1.andTagTypeIdEqualTo(TagConstant.SUBJECT_TAG_TYPE_ID); // 科目标签类型ID为1
            criteria1.andUserIdEqualTo(0L); // 系统默认标签

            // 查询用户自定义标签
            WrongQuestionTagExample.Criteria criteria2 = tagExample.createCriteria();
            criteria2.andTagNameEqualTo(subject.getName());
            criteria2.andTagTypeIdEqualTo(TagConstant.SUBJECT_TAG_TYPE_ID);
            criteria2.andUserIdEqualTo(userId);

            tagExample.or(criteria2);

            List<WrongQuestionTag> tags = wrongQuestionTagMapper.selectByExample(tagExample);

            if (CollUtil.isNotEmpty(tags)) {
                // 优先使用用户自定义标签，其次使用系统默认标签
                return tags.stream()
                        .sorted((tag1, tag2) -> {
                            if (tag1.getUserId().equals(userId) && !tag2.getUserId().equals(userId)) {
                                return -1; // 用户标签优先
                            } else if (!tag1.getUserId().equals(userId) && tag2.getUserId().equals(userId)) {
                                return 1;
                            }
                            return 0; // 相同类型按原顺序
                        })
                        .limit(1) // 只取第一个（优先级最高的）
                        .collect(Collectors.toList());
            } else {
                // 没有匹配到任何科目标签，返回"未分类"标签
                return getUncategorizedTag(userId);
            }
        } catch (Exception e) {
            // 如果出现异常，返回"未分类"标签，不影响错题创建
            return getUncategorizedTag(userId);
        }
    }

    /**
     * 获取"未分类"标签
     *
     * @param userId 用户ID
     * @return "未分类"标签列表
     */
    private List<WrongQuestionTag> getUncategorizedTag(Long userId) {
        try {
            // 查询"未分类"标签（tag_type_id = 1 代表科目）
            WrongQuestionTagExample tagExample = new WrongQuestionTagExample();
            WrongQuestionTagExample.Criteria criteria1 = tagExample.createCriteria();
            criteria1.andTagNameEqualTo("未分类");
            criteria1.andTagTypeIdEqualTo(TagConstant.SUBJECT_TAG_TYPE_ID); // 科目标签类型ID为1
            criteria1.andUserIdEqualTo(0L); // 系统默认标签

            // 查询用户自定义的"未分类"标签
            WrongQuestionTagExample.Criteria criteria2 = tagExample.createCriteria();
            criteria2.andTagNameEqualTo("未分类");
            criteria2.andTagTypeIdEqualTo(TagConstant.SUBJECT_TAG_TYPE_ID);
            criteria2.andUserIdEqualTo(userId);

            tagExample.or(criteria2);

            List<WrongQuestionTag> tags = wrongQuestionTagMapper.selectByExample(tagExample);

            if (CollUtil.isNotEmpty(tags)) {
                // 优先使用用户自定义标签，其次使用系统默认标签
                return tags.stream()
                        .limit(1) // 只取第一个（优先级最高的）
                        .collect(Collectors.toList());
            } else {
                // 如果系统中没有"未分类"标签，返回空列表
                return new ArrayList<>();
            }
        } catch (Exception e) {
            // 如果查询"未分类"标签出现异常，返回空列表
            return new ArrayList<>();
        }
    }

    @Transactional
    public int update(Long id, WrongQuestionParam param) {
        WrongQuestion record = new WrongQuestion();
        // 判断这条数据是否存在
        WrongQuestion question = wrongQuestionMapper.selectByPrimaryKey(id);
        if (ObjectUtil.isNull(question)) {
            Asserts.fail("数据不存在！");
        }
        // 判断是否属于改用户
        if (!question.getUserId().equals(param.getUserId())) {
            Asserts.fail("数据不存在！");
        }

        // 查询该错题的标签，重新分配标签
        if (CollUtil.isNotEmpty(param.getTags())) {
            // 优化后的标签查询 - 通过一次查询获取所有标签
            List<Long> tagIds = questionTagRelationMapper.selectByExample(new WrongQuestionTagRelationExample())
                    .stream()
                    .filter(relation -> relation.getQuestionId().equals(id))
                    .map(WrongQuestionTagRelation::getTagId)
                    .collect(Collectors.toList());
            List<WrongQuestionTag> originTags = CollUtil.isEmpty(tagIds) ? new ArrayList<>()
                    : wrongQuestionTagMapper.selectByExample(new WrongQuestionTagExample() {
                {
                    createCriteria().andIdIn(tagIds);
                }
            });
            allocWrongQuestionTags(id, originTags, param.getTags());
        }

        // 设置需要更新的字段
        record.setData(param.getData());
        record.setNote(param.getNote());
        record.setThumbnail(param.getThumbnail());
        record.setUpdateTime(new Date());

        WrongQuestionExample example = new WrongQuestionExample();
        example.createCriteria()
                .andIdEqualTo(id)
                .andUserIdEqualTo(param.getUserId());

        return wrongQuestionMapper.updateByExampleSelective(record, example);
    }

    /**
     * 修改错题标签
     *
     * @param questionId
     * @param originList
     * @param targetList
     * @return
     */
    private int allocWrongQuestionTags(Long questionId, List<WrongQuestionTag> originList,
                                       List<WrongQuestionTag> targetList) {
        // 删除不存在的数据
        List<Long> deletedIds = getDeletedTagIds(originList, targetList);
        if (deletedIds.size() > 0) {
            WrongQuestionTagRelationExample example = new WrongQuestionTagRelationExample();
            example.createCriteria().andQuestionIdEqualTo(questionId).andTagIdIn(deletedIds);
            questionTagRelationMapper.deleteByExample(example);
        }

        // 批量插入新关系
        List<WrongQuestionTagRelation> questionTagRelations = new ArrayList<>();
        if (CollUtil.isNotEmpty(targetList)) {
            for (WrongQuestionTag tag : targetList) {
                WrongQuestionTagRelation relation = new WrongQuestionTagRelation();
                relation.setQuestionId(questionId);
                relation.setTagId(tag.getId());
                relation.setTagTypeId(tag.getTagTypeId());

                questionTagRelations.add(relation);
            }
            if (questionTagRelations.size() > 0) {
                questionTagRelationDao.insertList(questionTagRelations);
            }
        }

        return questionTagRelations.size();
    }

    private List<Long> getDeletedTagIds(List<WrongQuestionTag> origin, List<WrongQuestionTag> target) {
        List<Long> deleted = new ArrayList<>();
        if (CollUtil.isEmpty(origin)) {
            return deleted;
        }

        for (WrongQuestionTag tag : origin) {
            if (target.stream().noneMatch(d -> d.getId().equals(tag.getId()))) {
                deleted.add(tag.getId());
            }
        }
        return deleted;
    }

    public List<WrongQuestionSubject> findSubjects(Long userId) {
        return wrongQuestionDao.querySubjects(userId);
    }

    public Long createSubject(WrongQuestionSubjectParam param) {
        // 先查询"选择科目"所在的标签类型
        WrongQuestionTagTypeExample tagTypeExample = new WrongQuestionTagTypeExample();
        tagTypeExample.createCriteria().andTypeNameEqualTo("选择科目");
        Optional<WrongQuestionTagType> tagTypeOptional = tagTypeMapper.selectByExample(tagTypeExample).stream()
                .findFirst();
        if (!tagTypeOptional.isPresent()) {
            Asserts.fail("数据异常，新增失败！");
        }

        WrongQuestionTag model = new WrongQuestionTag();
        model.setUserId(param.getUserId());
        model.setTagName(param.getName());
        model.setTagTypeId(tagTypeOptional.get().getId());
        wrongQuestionTagMapper.insertSelective(model);

        return model.getId();
    }

    @Transactional
    public int deleteSubject(Long tagId, Long userId) {
        WrongQuestionTag tag = wrongQuestionTagMapper.selectByPrimaryKey(tagId);
        if (ObjectUtil.isNull(tag)) {
            Asserts.fail("科目不存在！");
        }
        if (tag.getUserId().longValue() != userId.longValue()) {
            Asserts.fail("您无权删除该科目！");
        }
        // 查询该科目下是否有错题
        WrongQuestionTagRelationExample questionTagRelationExample = new WrongQuestionTagRelationExample();
        questionTagRelationExample.createCriteria().andTagIdEqualTo(tagId);
        List<WrongQuestionTagRelation> questions = questionTagRelationMapper
                .selectByExample(questionTagRelationExample);
        if (CollUtil.isNotEmpty(questions)) {
//            Asserts.fail("该科目下存在错题，无法删除！");
            List<Long> questionIds = questions.stream().map(WrongQuestionTagRelation::getQuestionId).collect(Collectors.toList());
            this.deleteBatch(questionIds, userId);
        }

        return wrongQuestionTagMapper.deleteByPrimaryKey(tagId);
    }

    @Transactional
    public int deleteBatch(List<Long> ids, Long userId) {
        if (CollUtil.isEmpty(ids)) {
            return 0;
        }

        // 删除错题标签关系表的数据
        WrongQuestionTagRelationExample relationExample = new WrongQuestionTagRelationExample();
        relationExample.createCriteria().andQuestionIdIn(ids);
        questionTagRelationMapper.deleteByExample(relationExample);

        // 删除错题数据
        WrongQuestionExample example = new WrongQuestionExample();
        example.createCriteria()
                .andIdIn(ids)
                .andUserIdEqualTo(userId);

        return wrongQuestionMapper.deleteByExample(example);
    }

    @Transactional
    public int batchMove(BatchMoveWrongQuestionsParam param, Long userId) {
        log.debug("Starting batch move wrong questions for user {}. From tag: {} To tag: {}", userId,
                param.getFromSubjectTagId(), param.getToSubjectTagId());
        // 参数校验
        if (param.getToSubjectTagId() == null || userId == null || param.getFromSubjectTagId() == null) {
            Asserts.fail("参数不能为空！");
        }

        if (param.getFromSubjectTagId().equals(param.getToSubjectTagId())) {
            Asserts.fail("源科目和目标科目不能相同！");
        }

        // 验证目标科目标签是否存在且为科目类型
        WrongQuestionTag toTag = wrongQuestionTagMapper.selectByPrimaryKey(param.getToSubjectTagId());
        if (ObjectUtil.isNull(toTag) || !toTag.getTagTypeId().equals(TagConstant.SUBJECT_TAG_TYPE_ID)) {
            log.warn("Target tag validation failed. Tag ID: {}", param.getToSubjectTagId());
            Asserts.fail("目标科目标签不存在或不是科目类型！");
        }
        WrongQuestionTag fromTag = wrongQuestionTagMapper.selectByPrimaryKey(param.getFromSubjectTagId());
        if (ObjectUtil.isNull(fromTag) || !fromTag.getTagTypeId().equals(TagConstant.SUBJECT_TAG_TYPE_ID)) {
            log.warn("From tag validation failed. Tag ID: {}", param.getFromSubjectTagId());
            Asserts.fail("来源科目标签不存在或不是科目类型！");
        }
        log.debug("Source and target tags validated successfully.");

        // 验证用户权限：只能操作自己的错题或系统默认标签
        if ((!toTag.getUserId().equals(0L) && !toTag.getUserId().equals(userId))
                || (!fromTag.getUserId().equals(0L) && !fromTag.getUserId().equals(userId))) {
            log.warn("User {} has no permission to operate tags.", userId);
            Asserts.fail("您无权操作该科目标签！");
        }
        log.debug("User permission validated successfully.");

        // 验证错题是否属于当前用户
        WrongQuestionExample questionExample = new WrongQuestionExample();
        questionExample.createCriteria()
                .andIdIn(param.getIds())
                .andUserIdEqualTo(userId);

        long count = wrongQuestionMapper.countByExample(questionExample);
        if (count != param.getIds().size()) {
            Asserts.fail("包含不属于您的错题，操作失败");
        }

        // 验证错题是否都属于来源科目
        WrongQuestionTagRelationExample relationCheckExample = new WrongQuestionTagRelationExample();
        relationCheckExample.createCriteria()
                .andTagIdEqualTo(param.getFromSubjectTagId())
                .andQuestionIdIn(param.getIds());
        long relationCount = questionTagRelationMapper.countByExample(relationCheckExample);
        if (relationCount != param.getIds().size()) {
            Asserts.fail("包含不属于来源科目的错题，操作失败");
        }

        // 删除旧的科目关系
        WrongQuestionTagRelationExample deleteExample = new WrongQuestionTagRelationExample();
        deleteExample.createCriteria()
                .andQuestionIdIn(param.getIds())
                .andTagIdEqualTo(param.getFromSubjectTagId());
        questionTagRelationMapper.deleteByExample(deleteExample);

        // 批量插入新的关系
        List<WrongQuestionTagRelation> newRelations = new ArrayList<>();
        for (Long questionId : param.getIds()) {
            WrongQuestionTagRelation newRelation = new WrongQuestionTagRelation();
            newRelation.setQuestionId(questionId);
            newRelation.setTagId(param.getToSubjectTagId());
            newRelation.setTagTypeId(TagConstant.SUBJECT_TAG_TYPE_ID);
            newRelations.add(newRelation);
        }

        if (CollUtil.isNotEmpty(newRelations)) {
            questionTagRelationDao.insertList(newRelations);
            log.debug("Inserted {} new relations for the target tag.", newRelations.size());
        }

        log.info("Batch move subjects completed for user {}. Moved {} questions to tag {}.", userId,
                param.getIds().size(), param.getToSubjectTagId());
        return param.getIds().size(); // 返回移动的错题数量
    }

}
