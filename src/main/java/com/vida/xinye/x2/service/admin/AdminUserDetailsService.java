package com.vida.xinye.x2.service.admin;

import com.vida.xinye.x2.dto.UserDto;
import com.vida.xinye.x2.mbg.mapper.SysUserMapper;
import com.vida.xinye.x2.mbg.model.SysUser;
import com.vida.xinye.x2.mbg.model.SysUserExample;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/5
 */
@Service
public class AdminUserDetailsService implements UserDetailsService {

    @Resource
    private SysUserMapper sysUserMapper;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        username = username.trim();
        SysUserExample example = new SysUserExample();
        example.createCriteria().andUsernameEqualTo(username);
        List<SysUser> userList = sysUserMapper.selectByExample(example);
        if (userList == null || userList.isEmpty()) {
            throw new UsernameNotFoundException("账号不存在!");
        }

        // 获取查询结果中的第一个用户
        SysUser user = userList.get(0);
        // 返回UserDetails对象
        UserDto userDto = new UserDto();
        userDto.setId(user.getId());
        userDto.setUsername(user.getUsername());
        userDto.setPassword(user.getPassword());
        userDto.setAccountNonLocked(true);
        userDto.setAccountNonExpired(true);
        userDto.setEnabled(true);
        userDto.setCredentialsNonExpired(true);
        userDto.setAuthorities(Collections.singletonList(new SimpleGrantedAuthority("ROLE_ADMIN")));

        return userDto;
    }
}
