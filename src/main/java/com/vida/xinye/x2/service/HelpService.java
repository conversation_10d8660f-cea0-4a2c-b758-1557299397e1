package com.vida.xinye.x2.service;

import com.vida.xinye.x2.dto.HelpFaqDto;
import com.vida.xinye.x2.dto.HelpProductManualDto;
import com.vida.xinye.x2.dto.HelpTutorialDto;
import com.vida.xinye.x2.mbg.model.HelpFaq;
import com.vida.xinye.x2.mbg.model.HelpProductManual;
import com.vida.xinye.x2.mbg.model.HelpTutorial;
import com.vida.xinye.x2.mbg.model.Machine;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 帮助中心服务接口
 *
 * <AUTHOR>
 * @date 2024/11/11
 */
@Service
public interface HelpService {
    /**
     * 获取使用教程
     */
    List<HelpTutorialDto> tutorials(String locale);

    /**
     * 获取产品说明书
     */
    List<HelpProductManualDto> productManuals(String locale);

    /**
     * 获取常见问题
     */
    List<HelpFaqDto> faqs(String locale);

}
