package com.vida.xinye.x2.service;

import com.vida.xinye.x2.domain.param.xkw.KeywordSearchParam;
import com.vida.xinye.x2.domain.param.xkw.SimilarRecommendParam;
import com.vida.xinye.x2.domain.param.xkw.ScoreImprovementRecommendParam;
import com.vida.xinye.x2.domain.param.xkw.XkwSearchParam;
import com.vida.xinye.x2.dto.edu.*;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 机器设备服务接口
 *
 * <AUTHOR>
 * @date 2024/11/11
 */
@Service
public interface XkwService {
    /**
     * 拍搜题-精品
     */
    XopTextSearchRespVO searchJP(XkwSearchParam param);

    /**
     * 拍搜题-海量
     */
    XopTextSearchRespVO searchDeep(XkwSearchParam param);

    /**
     * 举一反三推题
     */
    XopSimilarRecommendRespVO similarRecommend(Long userId, SimilarRecommendParam param);

    XopSimilarRecommendRespVO similarRecommend(Long userId, SimilarRecommendParam param, Integer typeId);

    /**
     * 提分训练推题（根据章节知识点推题）
     *
     * @param param
     * @return
     */
    List<XopQuestionPushVO> scoreImprovementRecommend(ScoreImprovementRecommendParam param);

    /**
     * 根据课程、年级、教材版本查询教材列表
     *
     * @param courseId  课程 ID
     * @param gradeId   年级 ID
     * @param versionId 教材版本 ID
     * @return 教材列表
     */
    XopPageData<XopTextbook> getTextbooks(Integer courseId, Integer gradeId, Integer versionId, Integer pageIndex,
                                          Integer pageSize);

    /**
     * 根据教材 ID 查询教材（章节）目录
     *
     * @param textbookId 教材 ID
     * @return 教材（章节）目录列表
     */
    List<XopTextbookCatalog> getTextbookCatalog(Integer textbookId);

    /**
     * 关键词搜题
     *
     * @param param
     * @return
     */
    XopQuestionRecommendRespVO keywordSearch(KeywordSearchParam param);

    /**
     * 根据课程ID查询考点（知识点）
     *
     * @param courseId 课程ID
     * @return 考点（知识点）列表
     */
    List<XopKnowledgePoint> getKnowledgePoints(Integer courseId);

    /**
     * 获取指定课程ID的教材版本列表
     *
     * @param courseId 课程ID
     * @return 教材版本列表
     */
    List<XopTextbookVersion> getTextbookVersions(Integer courseId);

    /**
     * 获取题型列表
     *
     * @param courseId 课程ID，可选
     * @return 题型列表
     */
    List<XopQuestionType> getQuestionTypes(Integer courseId);

    /**
     * 获取难度等级列表
     *
     * @return 难度等级列表
     */
    List<XopQuestionDifficulty> getQuestionDifficulties();

    /**
     * 获取所有行政区列表
     *
     * @return 行政区列表
     */
    List<XopArea> getAllAreas();

    /**
     * 获取省级和直辖市级别的行政区列表
     *
     * @return 省级和直辖市级别的行政区列表
     */
    List<XopArea> getProvinceAreas();

}
