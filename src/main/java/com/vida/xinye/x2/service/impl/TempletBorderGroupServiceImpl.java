package com.vida.xinye.x2.service.impl;

import com.vida.xinye.x2.dao.TempletBorderGroupDao;
import com.vida.xinye.x2.dto.TempletBorderGroupDto;
import com.vida.xinye.x2.dto.param.TempletBorderGroupParam;
import com.vida.xinye.x2.exception.Asserts;
import com.vida.xinye.x2.mbg.mapper.TempletBorderGroupMapper;
import com.vida.xinye.x2.mbg.mapper.TempletBorderMapper;
import com.vida.xinye.x2.mbg.model.*;
import com.vida.xinye.x2.service.TempletBorderGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class TempletBorderGroupServiceImpl implements TempletBorderGroupService {
    @Autowired
    private TempletBorderGroupMapper borderGroupMapper; // 变量名同步调整
    @Autowired
    private TempletBorderMapper borderMapper;
    @Autowired
    private TempletBorderGroupDao templetBorderGroupDao;

    @Override
    public List<TempletBorderGroup> listAllGroups() {
        TempletBorderGroupExample example = new TempletBorderGroupExample();
        example.setOrderByClause("sort asc");
        return borderGroupMapper.selectByExample(example);
    }

    @Override
    public List<TempletBorderGroupDto> listWithLocale(String locale) {
        return templetBorderGroupDao.list(locale);
    }

    @Override
    public List<TempletBorder> getBordersByGroup(Long groupId) {
        TempletBorderExample example = new TempletBorderExample();
        example.createCriteria().andGroupIdEqualTo(groupId);
        return borderMapper.selectByExample(example);
    }

    @Override
    public Long createGroup(TempletBorderGroupParam param) {
        // 校验分组名称唯一性
        TempletBorderGroupExample example = new TempletBorderGroupExample();
        example.createCriteria().andNameEqualTo(param.getName());
        if (!borderGroupMapper.selectByExample(example).isEmpty()) {
            Asserts.fail("分组名称已存在");
        }

        TempletBorderGroup group = new TempletBorderGroup();
        group.setName(param.getName());
        group.setSort(param.getSort() != null ? param.getSort() : 0);
        borderGroupMapper.insertSelective(group);

        // 多语言处理
        if (param.getI18nNames()!= null &&!param.getI18nNames().isEmpty()) {
            List<TempletBorderGroupI18n> updateGroupsI18n = param.getI18nNames().stream().map(nameDto -> {
                TempletBorderGroupI18n i18n = new TempletBorderGroupI18n();
                i18n.setGroupId(group.getId());
                i18n.setLocale(nameDto.getLocale());
                i18n.setName(nameDto.getName());
                return i18n;
            }).collect(Collectors.toList());
            // 插入多语言记录
            templetBorderGroupDao.insert(updateGroupsI18n);
        }
        return group.getId();
    }

    @Override
    public int updateGroup(Long id, TempletBorderGroupParam param) {
        TempletBorderGroup existing = borderGroupMapper.selectByPrimaryKey(id);
        if (existing == null) {
            Asserts.fail("分组不存在");
        }
        // 校验分组名称唯一性
        TempletBorderGroupExample example = new TempletBorderGroupExample();
        example.createCriteria().andNameEqualTo(param.getName()).andIdNotEqualTo(id);
        if (!borderGroupMapper.selectByExample(example).isEmpty()) {
            Asserts.fail("分组名称已存在");
        }

        TempletBorderGroup update = new TempletBorderGroup();
        update.setId(id);
        update.setName(param.getName());
        update.setSort(param.getSort());

        // 多语言处理
        if (param.getI18nNames()!= null &&!param.getI18nNames().isEmpty()) {
            List<TempletBorderGroupI18n> updateGroupsI18n = param.getI18nNames().stream().map(nameDto -> {
                TempletBorderGroupI18n i18n = new TempletBorderGroupI18n();
                i18n.setGroupId(id);
                i18n.setLocale(nameDto.getLocale());
                i18n.setName(nameDto.getName());
                return i18n;
            }).collect(Collectors.toList());
            // 插入多语言记录
            templetBorderGroupDao.insert(updateGroupsI18n);
        }

        return borderGroupMapper.updateByPrimaryKeySelective(update);
    }

    @Override
    public int deleteGroup(Long id) {
        // 检查是否存在关联边框
        TempletBorderExample borderExample = new TempletBorderExample();
        borderExample.createCriteria().andGroupIdEqualTo(id);
        if (borderMapper.countByExample(borderExample) > 0) {
            Asserts.fail("请先移除分组下的边框");
        }
        // 分组是否存在
        TempletBorderGroup group = borderGroupMapper.selectByPrimaryKey(id);
        if (group == null) {
            Asserts.fail("分组不存在");
        }
        return borderGroupMapper.deleteByPrimaryKey(id);
    }

    @Transactional
    @Override
    public int sortGroups(List<Long> groupIds) {
        for (int i = 0; i < groupIds.size(); i++) {
            TempletBorderGroup group = new TempletBorderGroup();
            group.setId(groupIds.get(i));
            group.setSort(i + 1);
            borderGroupMapper.updateByPrimaryKeySelective(group);
        }
        return groupIds.size();
    }

}
