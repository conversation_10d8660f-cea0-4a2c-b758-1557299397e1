package com.vida.xinye.x2.service;

import com.vida.xinye.x2.constant.ParentModeStatus;
import com.vida.xinye.x2.dto.param.UserProfileUpdateParam;
import com.vida.xinye.x2.dto.param.ParentModeAuthParam;
import com.vida.xinye.x2.mbg.mapper.UserMapper;
import com.vida.xinye.x2.mbg.mapper.UserMapperExtend;
import com.vida.xinye.x2.mbg.model.User;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

/**
 * @Description: 用户服务
 * @Author: zhangwenbin
 * @Date: 2025/06/03 17:50
 */
@Service
public class UserService {
  private static final org.slf4j.Logger logger = LoggerFactory.getLogger(UserService.class);

  @Autowired
  private UserMapperExtend userMapper;

  public void updateProfile(Long userId, UserProfileUpdateParam param) {
    // 校验一下
    if (param.getRoleType() == null || param.getGradeType() == null) {
      logger.error("更新用户信息失败，参数错误");
      return;
    }
    // roleType 是否是 UserRoleType 里面的值
    if (!param.getRoleType().isValid()) {
      logger.error("更新用户信息失败，角色类型错误");
      return;
    }
    // gradeType 是否是 GradeType 里面的值
    if (!param.getGradeType().isValid()) {
      logger.error("更新用户信息失败，年级类型错误");
      return;
    }

    userMapper.updateProfile(userId, param.getRoleType().name(), param.getGradeType().name());
  }

  /**
   * 开启家长模式（重载，直接传参数）
   * 
   * @param userId                  用户ID
   * @param parentName              家长姓名
   * @param parentIdcard            家长身份证
   * @param encodedTeenagerPassword 加密后的青少年密码
   */
  public void enableParentMode(Long userId, String parentName, String parentIdcard, String encodedTeenagerPassword) {
    // parentMode  0 - 未认证   1 - 已开启  2 - 已关闭
    // 校验一下
    if (parentName == null || parentIdcard == null || encodedTeenagerPassword == null) {
      logger.error("开启家长模式失败，参数错误");
      return;
    }
    // 校验一下 parentName 和 parentIdcard 是否是合法的
    if (parentName.length() > 20 || parentIdcard.length() > 20) {
      logger.error("开启家长模式失败，参数错误");
      return;
    }
    // 校验一下 encodedTeenagerPassword 是否是合法的
    if (encodedTeenagerPassword.length() > 100) {
      logger.error("开启家长模式失败，参数错误");
      return;
    }
    // 开启家长模式
    userMapper.updateParentMode(userId, ParentModeStatus.ENABLED.getCode(), parentName, parentIdcard, encodedTeenagerPassword);
  }

  /**
   * 关闭家长模式
   * 
   * @param userId 用户ID
   */
  public void disableParentMode(Long userId) {
    // parentMode  0 - 未认证   1 - 已开启  2 - 已关闭
    // 关闭家长模式
    userMapper.updateParentMode(userId, ParentModeStatus.DISABLED.getCode(),  null, null, null);
  }

  public boolean checkTeenagerPassword(Long userId, String rawPassword) {
    User user = userMapper.selectByPrimaryKey(userId);
    if (user == null || user.getTeenagerPassword() == null)
      return false;
    return new BCryptPasswordEncoder().matches(rawPassword, user.getTeenagerPassword());
  }
}