package com.vida.xinye.x2.service.impl;

import com.vida.xinye.x2.service.RedisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * redis操作实现类
 * Created by wangpf on 2024/11/5
 */
public class RedisServiceImpl implements RedisService {
    @Value("${redis.database}")
    private String redisDatabase;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    public void set(String key, Object value, long time) {
        // 获取完整的key
        String fullKey = getFullKey(key);
        // 设置key-value对，并设置过期时间
        redisTemplate.opsForValue().set(fullKey, value, time, TimeUnit.SECONDS);
    }

    @Override
    public void set(String key, Object value) {
        // 获取完整的key
        String fullKey = getFullKey(key);
        // 设置key-value对
        redisTemplate.opsForValue().set(fullKey, value);
    }

    @Override
    public Object get(String key) {
        // 获取完整的key
        String fullKey = getFullKey(key);
        // 获取key对应的value
        return redisTemplate.opsForValue().get(fullKey);
    }

    @Override
    public Boolean del(String key) {
        // 获取完整的key
        String fullKey = getFullKey(key);
        // 删除key
        return redisTemplate.delete(fullKey);
    }

    @Override
    public Long del(List<String> keys) {
        // 获取完整的key列表
        List<String> fullKeys = keys.stream().map(this::getFullKey).collect(Collectors.toList());
        // 删除key列表
        return redisTemplate.delete(fullKeys);
    }

    @Override
    public void delPrefix(String prefix) {
        // 获取完整的key前缀
        String fullPrefix = getFullKey(prefix);
        // 获取所有以该前缀开头的key
        Set<String> keys = redisTemplate.keys(fullPrefix + "*");
        // 如果存在以该前缀开头的key，则删除
        if (keys != null && !keys.isEmpty()) {
            redisTemplate.delete(keys);
        }
    }

    @Override
    public Boolean expire(String key, long time) {
        // 获取完整的key
        String fullKey = getFullKey(key);
        // 设置key的过期时间
        return redisTemplate.expire(fullKey, time, TimeUnit.SECONDS);
    }

    @Override
    public Long getExpire(String key) {
        // 获取完整的key
        String fullKey = getFullKey(key);
        // 获取key的过期时间
        return redisTemplate.getExpire(fullKey, TimeUnit.SECONDS);
    }

    @Override
    public Boolean hasKey(String key) {
        // 获取完整的key
        String fullKey = getFullKey(key);
        // 判断key是否存在
        return redisTemplate.hasKey(fullKey);
    }


    private String getFullKey(String key) {
        // 获取完整的key
        return redisDatabase + ":" + key;
    }
}
