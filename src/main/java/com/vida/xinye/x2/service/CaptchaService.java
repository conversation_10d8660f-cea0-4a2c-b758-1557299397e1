package com.vida.xinye.x2.service;

import cn.hutool.core.util.StrUtil;
import com.vida.xinye.x2.constant.LoginWayConstant;
import com.vida.xinye.x2.exception.Asserts;

import com.vida.xinye.x2.util.PhoneUtil;
import com.vida.xinye.x2.validator.AccountSecurityValidator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.Random;

/**
 * 验证码服务类
 *
 * <AUTHOR>
 * @date 2024/11/5
 */
@Service
@RefreshScope
@Slf4j
public class CaptchaService {
    private static final int VERIFICATION_CODE_LENGTH = 6;
    private Random random = new Random();

    @Value("${redis.captcha.prefix}")
    private String captchaRedisPrefix;

    @Value("${redis.captcha.expiration}")
    private int captchaExpirationSeconds;

    @Autowired
    private SmsService smsService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private EmailService emailService;
    @Autowired
    private AccountSecurityValidator accountSecurityValidator;

    public boolean sendVerificationCode(String phone) {
        // 手机号格式验证
        if (!PhoneUtil.isValidChinesePhone(phone)) {
            log.warn("手机号格式不正确: phone={}", PhoneUtil.maskPhone(phone));
            return false;
        }

        String verificationCode = generateVerificationCode(VERIFICATION_CODE_LENGTH);
        try {
            // 将验证码存储到Redis，并设置过期时间（从配置文件中读取）
            String key = captchaRedisPrefix + phone;
            redisService.set(key, verificationCode, captchaExpirationSeconds);

            // 发送短信
            boolean success = smsService.sendVerificationCode(phone, verificationCode);
            if (!success) {
                return false;
            }
        } catch (Exception e) {
            return false;
        }

        return true;
    }

    public boolean verifyCode(String account, String code) {
        log.debug("验证码校验请求: account={}, code={}", maskAccount(account), code);
        if (StrUtil.isBlank(account) || StrUtil.isBlank(code)) {
            Asserts.fail("账号和验证码不能为空");
        }

        // 判断是手机号还是邮箱
        if (PhoneUtil.isValidChinesePhone(account)) {
            // 手机号验证码校验
            return verifyPhoneCode(account, code);
        } else if (accountSecurityValidator.isValidEmail(account)) {
            // 邮箱验证码校验
            return verifyEmailCode(account, code);
        } else {
            Asserts.fail("账号格式不正确");
            return false;
        }
    }

    /**
     * 验证手机号验证码
     */
    private boolean verifyPhoneCode(String phone, String code) {
        String key = captchaRedisPrefix + phone;
        if (!redisService.hasKey(key)) {
            Asserts.fail("验证码已过期！");
        }

        String realCaptcha = (String) redisService.get(key);
        if (!code.equals(realCaptcha)) {
            Asserts.fail("验证码错误！");
        }

        log.info("手机验证码校验成功: phone={}", PhoneUtil.maskPhone(phone));
        return true;
    }

    /**
     * 验证邮箱验证码
     */
    private boolean verifyEmailCode(String email, String code) {
        // 使用内置的邮箱验证码验证
        boolean result = verifyEmailCode(email, code, LoginWayConstant.CAPTCHA_PURPOSE_LOGIN);
        if (!result) {
            Asserts.fail("邮箱验证码错误或已过期！");
        }
        return result;
    }

    public boolean setCaptchaExpire(String phone) {
        String key = captchaRedisPrefix + phone;
        // 设置验证码失效
        if (redisService.expire(key, 0)) {
            log.info("验证码校验正常，设置验证码失效: phone={}", PhoneUtil.maskPhone(phone));
            return true;
        }
        return false;
    }

    /**
     * 验证邮箱验证码（指定用途）
     */
    public boolean verifyEmailCode(String email, String code, String purpose) {
        try {
            String key = buildEmailCaptchaKey(email, purpose);
            Object captchaObj = redisService.get(key);
            String storedCaptcha = captchaObj == null ? null : captchaObj.toString();

            if (storedCaptcha == null) {
                log.warn("邮箱验证码不存在或已过期 - 邮箱: {}, 用途: {}", maskAccount(email), purpose);
                return false;
            }

            if (!storedCaptcha.equals(code)) {
                log.warn("邮箱验证码错误 - 邮箱: {}, 用途: {}", maskAccount(email), purpose);
                return false;
            }

            // 验证成功，删除验证码
            redisService.del(key);
            log.info("邮箱验证码验证成功 - 邮箱: {}, 用途: {}", maskAccount(email), purpose);
            return true;

        } catch (Exception e) {
            log.error("验证邮箱验证码异常 - 邮箱: {}, 用途: {}", maskAccount(email), purpose, e);
            return false;
        }
    }

    /**
     * 生成纯数字验证码
     *
     * @param length 验证码长度
     * @return 纯数字验证码（如：123456）
     */
    public String generateVerificationCode(int length) {
        StringBuilder verificationCode = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            verificationCode.append(random.nextInt(10)); // 生成0-9的随机数字
        }
        return verificationCode.toString();
    }

    /**
     * 账号脱敏处理
     */
    private String maskAccount(String account) {
        if (StrUtil.isBlank(account)) {
            return account;
        }

        if (PhoneUtil.isValidChinesePhone(account)) {
            return PhoneUtil.maskPhone(account);
        } else if (accountSecurityValidator.isValidEmail(account)) {
            // 邮箱脱敏：u***@example.com
            int atIndex = account.indexOf('@');
            if (atIndex > 1) {
                return account.charAt(0) + "***" + account.substring(atIndex);
            }
        }

        return account;
    }

    /**
     * 发送邮箱验证码
     *
     * @param email   邮箱地址
     * @param purpose 用途（login, register, reset_password, bind）
     * @return 是否发送成功
     */
    public boolean sendEmailCode(String email, String purpose) {
        try {
            // 验证邮箱格式
            if (!accountSecurityValidator.isValidEmail(email)) {
                log.warn("邮箱格式不正确: {}", maskAccount(email));
                return false;
            }

            // 检查发送频率限制
            String frequencyKey = "email_captcha_frequency:" + email;
            if (redisService.hasKey(frequencyKey)) {
                log.warn("邮箱验证码发送过于频繁 - 邮箱: {}", maskAccount(email));
                throw new IllegalStateException("验证码发送过于频繁，请稍后再试");
            }

            // 检查单日发送次数
            String dailyKey = "email_captcha_daily:" + email;
            Object dailyCountObj = redisService.get(dailyKey);
            String dailyCountStr = dailyCountObj == null ? null : dailyCountObj.toString();
            int dailyCount = dailyCountStr == null ? 0 : Integer.parseInt(dailyCountStr);

            if (dailyCount >= LoginWayConstant.DAY_EMAIL_MAX_TOTAL_NUM) {
                throw new IllegalStateException("今日邮箱验证码发送次数已达上限");
            }

            // 生成验证码
            String code = generateVerificationCode(VERIFICATION_CODE_LENGTH);

            // 存储验证码到Redis
            String key = buildEmailCaptchaKey(email, purpose);
            redisService.set(key, code, captchaExpirationSeconds);

            // 发送邮件
            boolean sent = emailService.sendVerificationCode(email, code, purpose);

            if (sent) {
                // 设置发送频率限制（60秒）
                redisService.set(frequencyKey, "1", 60);

                // 记录单日发送次数
                recordEmailSendCount(email);

                log.info("邮箱验证码发送成功 - 邮箱: {}, 用途: {}", maskAccount(email), purpose);
                return true;
            } else {
                // 发送失败，删除已存储的验证码
                redisService.del(key);
                return false;
            }

        } catch (Exception e) {
            log.error("发送邮箱验证码失败 - 邮箱: {}, 用途: {}", maskAccount(email), purpose, e);
            throw new RuntimeException(e.getMessage());
        }
    }

    /**
     * 构建邮箱验证码Redis key
     */
    private String buildEmailCaptchaKey(String email, String purpose) {
        return "email_captcha:" + email + ":" + purpose;
    }

    /**
     * 记录邮箱验证码发送次数
     */
    private void recordEmailSendCount(String email) {
        String dailyKey = "email_captcha_daily:" + email;
        Object countObj = redisService.get(dailyKey);
        String countStr = countObj == null ? null : countObj.toString();
        int count = countStr == null ? 0 : Integer.parseInt(countStr);
        count++;

        // 设置到当天23:59:59过期
        long expireSeconds = getSecondsUntilEndOfDay();
        redisService.set(dailyKey, String.valueOf(count), expireSeconds);
    }

    /**
     * 获取到当天结束的秒数
     */
    private long getSecondsUntilEndOfDay() {
        java.time.LocalDateTime now = java.time.LocalDateTime.now();
        java.time.LocalDateTime endOfDay = now.toLocalDate().atTime(23, 59, 59);
        return java.time.Duration.between(now, endOfDay).getSeconds();
    }

}
