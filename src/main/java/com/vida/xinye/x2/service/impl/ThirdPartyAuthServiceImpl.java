package com.vida.xinye.x2.service.impl;

import com.vida.xinye.x2.dto.ThirdPartyUserInfoDto;
import com.vida.xinye.x2.dto.param.ThirdPartyLoginDto;
import com.vida.xinye.x2.service.ThirdPartyAuthService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 第三方登录服务实现类（简化版，参考其他项目）
 * 注意：前端使用SDK直接获取用户信息，后端只做简单验证
 */
@Slf4j
@Service
public class ThirdPartyAuthServiceImpl implements ThirdPartyAuthService {

    @Override
    public String getAuthorizationUrl(String provider, String redirectUri, String state) {
        // 前端使用SDK，不需要后端生成授权URL
        log.info("前端使用{}SDK进行登录，不需要后端生成授权URL", provider);
        return "请使用" + provider + "SDK进行登录";
    }

    @Override
    public ThirdPartyUserInfoDto getUserInfo(ThirdPartyLoginDto loginDto) {
        try {
            log.info("获取第三方用户信息: loginWay={}, openId={}", loginDto.getLoginWay(), loginDto.getOpenId());
            
            // 前端使用SDK已经获取了用户信息，后端直接构建DTO返回
            ThirdPartyUserInfoDto userInfo = new ThirdPartyUserInfoDto();
            userInfo.setProvider(convertLoginWayToProvider(loginDto.getLoginWay()));
            userInfo.setOpenId(loginDto.getOpenId());
            userInfo.setUnionId(loginDto.getUnionId());
            userInfo.setNickname(loginDto.getNickname());
            userInfo.setAvatarUrl(loginDto.getHeadPic());
            
            // Apple登录特殊处理
            if ("byappleid".equals(loginDto.getLoginWay())) {
                userInfo.setOpenId(loginDto.getAppleAccount());
            }
            
            log.info("第三方用户信息构建成功: provider={}, openId={}", userInfo.getProvider(), userInfo.getOpenId());
            return userInfo;
            
        } catch (Exception e) {
            log.error("获取第三方用户信息异常: loginWay={}", loginDto.getLoginWay(), e);
            return null;
        }
    }

    @Override
    public ThirdPartyUserInfoDto refreshAccessToken(String provider, String refreshToken) {
        // 前端使用SDK，不需要后端刷新token
        log.info("前端使用{}SDK，不需要后端刷新token", provider);
        return null;
    }

    /**
     * 将登录方式转换为平台标识
     */
    private String convertLoginWayToProvider(String loginWay) {
        if (loginWay == null) {
            return "unknown";
        }
        
        switch (loginWay.toLowerCase()) {
            case "byweixin":
                return "wechat";
            case "byqq":
                return "qq";
            case "bysina":
                return "sina";
            case "byappleid":
                return "apple";
            default:
                return loginWay.replace("by", "");
        }
    }
}
