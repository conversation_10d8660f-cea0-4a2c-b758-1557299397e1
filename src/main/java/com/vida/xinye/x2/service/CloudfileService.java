package com.vida.xinye.x2.service;

import com.vida.xinye.x2.api.CommonPage;
import com.vida.xinye.x2.dto.param.CloudfileParam;
import com.vida.xinye.x2.mbg.model.Cloudfile;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Author：Chenjy
 * Date:2025/7/17
 * Description:
 */
@Service
public interface CloudfileService {
    /**
     * 新增
     */
    int create(Cloudfile cloudfile);

    /**
     * 上传文件，是否替换
     * 是-替换原url的文件
     * 否-不替换，新增文件
     */
    int upload(CloudfileParam param);

    /**
     * 删除云文件
     */
    int delete(Long id, Long userId);

    int delete(List<Long> ids, Long userId);

    /**
     * 分页查询云文件
     */
    CommonPage<Cloudfile> list(Long userId, int pageSize, int pageNum);

    /**
     * 检查文件是否存在
     */
    boolean checkFilenameExists(String fileName, Long userId);
}
