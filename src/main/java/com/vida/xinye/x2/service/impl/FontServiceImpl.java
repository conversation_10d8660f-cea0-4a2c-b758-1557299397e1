package com.vida.xinye.x2.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageHelper;
import com.vida.xinye.x2.api.CommonPage;
import com.vida.xinye.x2.dao.FontDao;
import com.vida.xinye.x2.dao.MaterialDao;
import com.vida.xinye.x2.dao.MaterialFavoriteDao;
import com.vida.xinye.x2.domain.param.MaterialParam;
import com.vida.xinye.x2.dto.MaterialDto;
import com.vida.xinye.x2.exception.Asserts;
import com.vida.xinye.x2.mbg.mapper.FontMapper;
import com.vida.xinye.x2.mbg.mapper.MaterialFavoriteMapper;
import com.vida.xinye.x2.mbg.mapper.MaterialMapper;
import com.vida.xinye.x2.mbg.model.*;
import com.vida.xinye.x2.service.FontService;
import com.vida.xinye.x2.service.MaterialService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 素材服务实现类
 *
 * <AUTHOR>
 * @date 2024/11/11
 */
@Service
public class FontServiceImpl implements FontService {
    @Autowired
    private FontMapper fontMapper;
    @Autowired
    private FontDao fontDao;


    @Override
    public List<Font> list(String kind) {
        FontExample example = new FontExample();
        if (StrUtil.isNotBlank(kind)) {
            example.createCriteria().andKindEqualTo(kind);
        }
        return fontMapper.selectByExample(example);
    }

    @Override
    public List<String> kindList() {
        return fontDao.listKinds();
    }
}
