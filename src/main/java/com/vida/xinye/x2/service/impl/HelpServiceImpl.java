package com.vida.xinye.x2.service.impl;

import cn.hutool.core.util.StrUtil;
import com.vida.xinye.x2.dao.HelpDao;
import com.vida.xinye.x2.dto.HelpFaqDto;
import com.vida.xinye.x2.dto.HelpProductManualDto;
import com.vida.xinye.x2.dto.HelpTutorialDto;
import com.vida.xinye.x2.service.HelpService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 帮助中心服务接口
 *
 * <AUTHOR>
 * @date 2024/11/11
 */
@Service
public class HelpServiceImpl implements HelpService {
    @Value("constants.locale:en")
    private String DEFAULT_LOCALE;
    @Resource
    private HelpDao helpDao;

    /**
     * 获取帮助中心使用教程
     *
     * @param locale
     * @return
     */
    @Override
    public List<HelpTutorialDto> tutorials(String locale) {
        if (StrUtil.isEmpty(locale)) {
            locale = DEFAULT_LOCALE;
        }
        return helpDao.tutorials(locale);
    }

    @Override
    public List<HelpProductManualDto> productManuals(String locale) {
        if (StrUtil.isEmpty(locale)) {
            locale = DEFAULT_LOCALE;
        }
        return helpDao.productManuals(locale);
    }

    @Override
    public List<HelpFaqDto> faqs(String locale) {
        if (StrUtil.isEmpty(locale)) {
            locale = DEFAULT_LOCALE;
        }
        return helpDao.faqs(locale);
    }

}
