package com.vida.xinye.x2.service;

import com.vida.xinye.x2.api.CommonPage;
import com.vida.xinye.x2.domain.param.MaterialParam;
import com.vida.xinye.x2.mbg.model.Machine;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 机器设备服务接口
 *
 * <AUTHOR>
 * @date 2024/11/11
 */
@Service
public interface MachineService {
    /**
     * 获取白名单机器列表
     */
    List<Machine> list(String machineName);

}
