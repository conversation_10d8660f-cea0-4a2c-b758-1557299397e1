package com.vida.xinye.x2.service;

import com.vida.xinye.x2.domain.param.edu.AddTrainingQuestionParam;
import com.vida.xinye.x2.domain.param.xkw.ScoreImprovementRecommendParam;
import com.vida.xinye.x2.api.CommonPage;
import com.vida.xinye.x2.dto.edu.CourseWithWrongQuestionCountDTO;
import com.vida.xinye.x2.dto.edu.TrainingQuestionDto;
import com.vida.xinye.x2.dto.edu.XopQuestionPushVO;
import com.vida.xinye.x2.mbg.model.*;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public interface TrainingService {
    /**
     * 获取所有的学段（已过滤）
     * 
     * @return
     */
    List<XkwStage> getStages();

    /**
     * 获取所有的学科
     * 
     * @return
     */
    List<XkwSubject> getSubjects();

    /**
     * 根据学段、学科获取课程
     * 
     * @param stageId
     * @param subjectId
     * @return
     */
    List<XkwCourse> getCourses(Integer stageId, Integer subjectId);

    /**
     * 根据学段、学科获取课程，并包含用户错题数量
     * 
     * @param stageId   学段ID
     * @param subjectId 学科ID
     * @param userId    用户ID
     * @return 带有错题数量的课程列表
     */
    List<CourseWithWrongQuestionCountDTO> getCoursesWithWrongQuestionCount(Integer stageId, Integer subjectId,
            Long userId);

    /**
     * 查询所有年级
     * 
     * @return 年级列表
     */
    List<XkwGrade> getGrades();

    /**
     * 根据课程 ID 查询教材版本
     * 
     * @param courseId 课程 ID
     * @return 教材版本列表
     */
    List<XkwTextbookVersion> getTextbookVersionsByCourseId(Integer courseId);

    /**
     * 查询用户的错题本训练题
     * 
     * @param userId   用户ID
     * @param courseId 课程ID
     * @return 训练题列表
     */
    CommonPage<TrainingQuestion> getUserTrainingQuestions(Long userId, Long courseId, Integer difficultyLevel,
            String typeId, Integer year, String saveTimeRange, Integer pageNum, Integer pageSize);

    /**
     * 添加训练题到错题本
     * 
     * @param param 训练题参数
     * @return 添加的训练题
     */
    TrainingQuestion addTrainingQuestion(AddTrainingQuestionParam param);

    /**
     * 批量删除训练题
     * 
     * @param ids    训练题ID列表
     * @param userId 用户ID
     * @return 删除的记录数
     */
    int deleteBatch(List<Long> ids, Long userId);

    /**
     * 提分训练推题（根据章节知识点推题，和用户有关系）
     *
     * @param userId
     * @param param
     * @return
     */
    List<XopQuestionPushVO> scoreImprovementRecommend(Long userId, ScoreImprovementRecommendParam param);

}
