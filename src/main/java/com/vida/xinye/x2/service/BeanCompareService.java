package com.vida.xinye.x2.service;

import cn.hutool.json.JSONUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.vida.xinye.x2.annotation.CompareField;
import com.vida.xinye.x2.domain.BeanDiff;
import com.vida.xinye.x2.domain.FieldDiff;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 比较bean是否相同
 *
 * <AUTHOR>
 * @date 2024/1/3
 */
@Service
@Slf4j
public class BeanCompareService {
    public static BeanDiff getBeanDiff() {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        Object beanDiff = request.getAttribute("beanDiff");
        return beanDiff != null ? (BeanDiff) beanDiff : null;
    }

    public static BeanDiff compare(Object oldBean, Object newBean) {
        BeanDiff beanDiff = new BeanDiff();
        if (beanDiff == null) {
            beanDiff = new BeanDiff();
        }

        try {
            if (oldBean != null && newBean != null) {
                Class oldBeanClazz = oldBean.getClass();
                Class newBeanClazz = newBean.getClass();
                if (!oldBeanClazz.equals(newBeanClazz)) {
                    throw new IllegalArgumentException("The objects being compared must be of the same class");
                }
                List<Field> fields = getCompareFields(newBeanClazz);
                for (Field field : fields) {
                    field.setAccessible(true);
                    Object oldValue = field.get(oldBean);
                    Object newValue = field.get(newBean);
                    CompareField alias = field.getAnnotation(CompareField.class);
                    if (!nullableEquals(oldValue, newValue)) {
                        FieldDiff fieldDiff = new FieldDiff();
                        fieldDiff.setAttributeAlias(alias.value());
                        fieldDiff.setAttributeName(field.getName());
                        fieldDiff.setAttributeType(field.getType().getTypeName());
                        fieldDiff.setNewValue(JSONUtil.toJsonStr(newValue));
                        fieldDiff.setOldValue(JSONUtil.toJsonStr(oldValue));
                        beanDiff.addFieldDiff(fieldDiff);
                    }
                }
            }
        } catch (Exception exception) {
            log.error("bean compare: {}", exception);
        }
        if (CollectionUtils.isNotEmpty(beanDiff.getFieldDiffs())) {
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            request.setAttribute("beanDiff", beanDiff);
        }
        return beanDiff;
    }

    private static List<Field> getCompareFields(Class clazz) {
        List<Field> fields = new ArrayList<>();
        getFields(fields, clazz);
        for (int i = fields.size() - 1; i >= 0; i--) {
            CompareField CompareField = fields.get(i).getAnnotation(CompareField.class);
            if (CompareField == null) {
                fields.remove(i);
            }
        }
        return fields;
    }

    private static List<Field> getFields(List<Field> fieldList, Class clazz) {
        fieldList.addAll(Arrays.asList(clazz.getDeclaredFields()));
        Class superClazz = clazz.getSuperclass();
        if (superClazz != null) {
            getFields(fieldList, superClazz);
        }
        return fieldList;
    }

    private static boolean nullableEquals(Object a, Object b) {
        return (a == null && b == null) || (a != null && a.equals(b));
    }
}
