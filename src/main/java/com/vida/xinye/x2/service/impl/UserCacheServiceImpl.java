package com.vida.xinye.x2.service.impl;

import com.alibaba.fastjson.JSON;
import com.vida.xinye.x2.mbg.mapper.UserMapper;
import com.vida.xinye.x2.mbg.mapper.UserThirdPartyAuthMapper;
import com.vida.xinye.x2.mbg.model.User;
import com.vida.xinye.x2.mbg.model.UserThirdPartyAuth;
import com.vida.xinye.x2.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户信息缓存服务实现
 * 基于Redis实现用户信息缓存
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@Service
@Slf4j
public class UserCacheServiceImpl {

    @Autowired
    private RedisService redisService;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private UserThirdPartyAuthMapper userThirdPartyAuthMapper;

    // Redis key前缀
    private static final String USER_INFO_PREFIX = "user_info:";
    private static final String USER_THIRD_PARTY_PREFIX = "user_third_party:";
    private static final String USER_LOGIN_TOKEN_PREFIX = "user_login_token:";
    private static final String USER_EMAIL_PREFIX = "user_email:";
    private static final String USER_PHONE_PREFIX = "user_phone:";
    private static final String THIRD_PARTY_MAPPING_PREFIX = "third_party_mapping:";
    private static final String USER_UNREGISTER_PREFIX = "user_unregister:";

    // 缓存过期时间（秒）
    private static final int USER_INFO_EXPIRE = 30 * 60; // 30分钟
    private static final int THIRD_PARTY_EXPIRE = 24 * 3600; // 24小时
    private static final int LOGIN_TOKEN_EXPIRE = 7 * 24 * 3600; // 7天
    private static final int EMAIL_PHONE_MAPPING_EXPIRE = 60 * 60; // 1小时
    private static final int THIRD_PARTY_MAPPING_EXPIRE = 60 * 60; // 1小时
    private static final int UNREGISTER_FLAG_EXPIRE = 7 * 24 * 3600; // 7天（注销标记）

    // 登录方式配置（可扩展设计）
    private static final Map<String, String> LOGIN_WAY_PLATFORM_MAPPING = new HashMap<String, String>() {{
        put("byweixin", "weixin");
        put("byqq", "qq");
        put("bysina", "weibo");
        put("byappleid", "apple");
        // 新增登录方式只需要在这里添加映射即可
        // put("bygoogle", "google");
        // put("bygithub", "github");
        // put("bydingding", "dingding");
    }};

    /**
     * 缓存用户信息
     *
     * @param user 用户信息
     */
    public void cacheUserInfo(User user) {
        if (user == null || user.getId() == null) {
            return;
        }

        try {
            String key = USER_INFO_PREFIX + user.getId();
            String userJson = JSON.toJSONString(user);
            redisService.set(key, userJson, USER_INFO_EXPIRE);
            
            log.debug("缓存用户信息成功 - 用户ID: {}", user.getId());
        } catch (Exception e) {
            log.error("缓存用户信息失败 - 用户ID: {}", user.getId(), e);
        }
    }

    /**
     * 获取缓存的用户信息
     *
     * @param userId 用户ID
     * @return 用户信息，如果缓存中不存在则返回null
     */
    public User getCachedUserInfo(Long userId) {
        if (userId == null) {
            return null;
        }

        try {
            String key = USER_INFO_PREFIX + userId;
            Object userObj = redisService.get(key);
            String userJson = userObj == null ? null : userObj.toString();
            
            if (userJson != null) {
                User user = JSON.parseObject(userJson, User.class);
                log.debug("从缓存获取用户信息成功 - 用户ID: {}", userId);
                return user;
            }
        } catch (Exception e) {
            log.error("从缓存获取用户信息失败 - 用户ID: {}", userId, e);
        }
        
        return null;
    }

    /**
     * 删除用户信息缓存
     *
     * @param userId 用户ID
     */
    public void evictUserInfo(Long userId) {
        if (userId == null) {
            return;
        }

        try {
            String key = USER_INFO_PREFIX + userId;
            redisService.del(key);
            log.debug("删除用户信息缓存成功 - 用户ID: {}", userId);
        } catch (Exception e) {
            log.error("删除用户信息缓存失败 - 用户ID: {}", userId, e);
        }
    }

    /**
     * 缓存第三方认证信息
     *
     * @param thirdPartyAuth 第三方认证信息
     */
    public void cacheThirdPartyAuth(UserThirdPartyAuth thirdPartyAuth) {
        if (thirdPartyAuth == null || thirdPartyAuth.getUserId() == null) {
            return;
        }

        try {
            String key = USER_THIRD_PARTY_PREFIX + thirdPartyAuth.getUserId();
            String authJson = JSON.toJSONString(thirdPartyAuth);
            redisService.set(key, authJson, THIRD_PARTY_EXPIRE);
            
            log.debug("缓存第三方认证信息成功 - 用户ID: {}", thirdPartyAuth.getUserId());
        } catch (Exception e) {
            log.error("缓存第三方认证信息失败 - 用户ID: {}", thirdPartyAuth.getUserId(), e);
        }
    }

    /**
     * 获取缓存的第三方认证信息
     *
     * @param userId 用户ID
     * @return 第三方认证信息，如果缓存中不存在则返回null
     */
    public UserThirdPartyAuth getCachedThirdPartyAuth(Long userId) {
        if (userId == null) {
            return null;
        }

        try {
            String key = USER_THIRD_PARTY_PREFIX + userId;
            Object authObj = redisService.get(key);
            String authJson = authObj == null ? null : authObj.toString();
            
            if (authJson != null) {
                UserThirdPartyAuth auth = JSON.parseObject(authJson, UserThirdPartyAuth.class);
                log.debug("从缓存获取第三方认证信息成功 - 用户ID: {}", userId);
                return auth;
            }
        } catch (Exception e) {
            log.error("从缓存获取第三方认证信息失败 - 用户ID: {}", userId, e);
        }
        
        return null;
    }

    /**
     * 删除第三方认证信息缓存
     *
     * @param userId 用户ID
     */
    public void evictThirdPartyAuth(Long userId) {
        if (userId == null) {
            return;
        }

        try {
            String key = USER_THIRD_PARTY_PREFIX + userId;
            redisService.del(key);
            log.debug("删除第三方认证信息缓存成功 - 用户ID: {}", userId);
        } catch (Exception e) {
            log.error("删除第三方认证信息缓存失败 - 用户ID: {}", userId, e);
        }
    }

    /**
     * 缓存用户登录Token
     *
     * @param userId      用户ID
     * @param accessToken 访问令牌
     * @param deviceId    设备ID（可选）
     */
    public void cacheLoginToken(Long userId, String accessToken, String deviceId) {
        if (userId == null || accessToken == null) {
            return;
        }

        try {
            String key = USER_LOGIN_TOKEN_PREFIX + userId;
            if (deviceId != null) {
                key += ":" + deviceId;
            }
            
            redisService.set(key, accessToken, LOGIN_TOKEN_EXPIRE);
            log.debug("缓存登录Token成功 - 用户ID: {}, 设备ID: {}", userId, deviceId);
        } catch (Exception e) {
            log.error("缓存登录Token失败 - 用户ID: {}", userId, e);
        }
    }

    /**
     * 获取缓存的登录Token
     *
     * @param userId   用户ID
     * @param deviceId 设备ID（可选）
     * @return 访问令牌，如果缓存中不存在则返回null
     */
    public String getCachedLoginToken(Long userId, String deviceId) {
        if (userId == null) {
            return null;
        }

        try {
            String key = USER_LOGIN_TOKEN_PREFIX + userId;
            if (deviceId != null) {
                key += ":" + deviceId;
            }
            
            Object tokenObj = redisService.get(key);
            String token = tokenObj == null ? null : tokenObj.toString();
            if (token != null) {
                log.debug("从缓存获取登录Token成功 - 用户ID: {}, 设备ID: {}", userId, deviceId);
            }
            return token;
        } catch (Exception e) {
            log.error("从缓存获取登录Token失败 - 用户ID: {}", userId, e);
        }
        
        return null;
    }

    /**
     * 删除登录Token缓存
     *
     * @param userId   用户ID
     * @param deviceId 设备ID（可选）
     */
    public void evictLoginToken(Long userId, String deviceId) {
        if (userId == null) {
            return;
        }

        try {
            String key = USER_LOGIN_TOKEN_PREFIX + userId;
            if (deviceId != null) {
                key += ":" + deviceId;
            }
            
            redisService.del(key);
            log.debug("删除登录Token缓存成功 - 用户ID: {}, 设备ID: {}", userId, deviceId);
        } catch (Exception e) {
            log.error("删除登录Token缓存失败 - 用户ID: {}", userId, e);
        }
    }

    /**
     * 删除用户所有相关缓存
     *
     * @param userId 用户ID
     */
    public void evictAllUserCache(Long userId) {
        if (userId == null) {
            return;
        }

        evictUserInfo(userId);
        evictThirdPartyAuth(userId);
        
        // 删除所有设备的登录Token（这里需要模糊匹配删除）
        try {
            String pattern = USER_LOGIN_TOKEN_PREFIX + userId + "*";
            // 注意：这里需要RedisService支持模糊删除，或者维护设备列表
            log.debug("删除用户所有缓存 - 用户ID: {}", userId);
        } catch (Exception e) {
            log.error("删除用户所有缓存失败 - 用户ID: {}", userId, e);
        }
    }

    /**
     * 刷新用户信息缓存过期时间
     *
     * @param userId 用户ID
     */
    public void refreshUserInfoExpire(Long userId) {
        if (userId == null) {
            return;
        }

        try {
            String key = USER_INFO_PREFIX + userId;
            if (redisService.hasKey(key)) {
                redisService.expire(key, USER_INFO_EXPIRE);
                log.debug("刷新用户信息缓存过期时间 - 用户ID: {}", userId);
            }
        } catch (Exception e) {
            log.error("刷新用户信息缓存过期时间失败 - 用户ID: {}", userId, e);
        }
    }

    /**
     * 缓存邮箱到用户ID的映射（性能优化）
     *
     * @param email 邮箱
     * @param userId 用户ID
     */
    public void cacheEmailMapping(String email, Long userId) {
        if (email == null || userId == null) {
            return;
        }

        try {
            String key = USER_EMAIL_PREFIX + email.toLowerCase();
            redisService.set(key, userId.toString(), EMAIL_PHONE_MAPPING_EXPIRE);
            log.debug("缓存邮箱映射成功 - 邮箱: {}, 用户ID: {}", maskEmail(email), userId);
        } catch (Exception e) {
            log.error("缓存邮箱映射失败 - 邮箱: {}, 用户ID: {}", maskEmail(email), userId, e);
        }
    }

    /**
     * 根据邮箱获取用户ID（性能优化）
     *
     * @param email 邮箱
     * @return 用户ID，不存在返回null
     */
    public Long getUserIdByEmail(String email) {
        if (email == null) {
            return null;
        }

        try {
            String key = USER_EMAIL_PREFIX + email.toLowerCase();
            Object cached = redisService.get(key);
            if (cached != null) {
                log.debug("缓存命中邮箱映射 - 邮箱: {}", maskEmail(email));
                return Long.valueOf(cached.toString());
            }
        } catch (Exception e) {
            log.error("获取邮箱映射失败 - 邮箱: {}", maskEmail(email), e);
        }

        return null;
    }

    /**
     * 缓存手机号到用户ID的映射（性能优化）
     *
     * @param phone 手机号
     * @param userId 用户ID
     */
    public void cachePhoneMapping(String phone, Long userId) {
        if (phone == null || userId == null) {
            return;
        }

        try {
            String key = USER_PHONE_PREFIX + phone;
            redisService.set(key, userId.toString(), EMAIL_PHONE_MAPPING_EXPIRE);
            log.debug("缓存手机号映射成功 - 手机号: {}, 用户ID: {}", maskPhone(phone), userId);
        } catch (Exception e) {
            log.error("缓存手机号映射失败 - 手机号: {}, 用户ID: {}", maskPhone(phone), userId, e);
        }
    }

    /**
     * 根据手机号获取用户ID（性能优化）
     *
     * @param phone 手机号
     * @return 用户ID，不存在返回null
     */
    public Long getUserIdByPhone(String phone) {
        if (phone == null) {
            return null;
        }

        try {
            String key = USER_PHONE_PREFIX + phone;
            Object cached = redisService.get(key);
            if (cached != null) {
                log.debug("缓存命中手机号映射 - 手机号: {}", maskPhone(phone));
                return Long.valueOf(cached.toString());
            }
        } catch (Exception e) {
            log.error("获取手机号映射失败 - 手机号: {}", maskPhone(phone), e);
        }

        return null;
    }

    /**
     * 缓存第三方平台到用户ID的映射（性能优化）
     *
     * @param platform 平台（wechat, qq, weibo, apple）
     * @param identifier 标识符（unionId或openId）
     * @param userId 用户ID
     */
    public void cacheThirdPartyMapping(String platform, String identifier, Long userId) {
        if (platform == null || identifier == null || userId == null) {
            return;
        }

        try {
            String key = THIRD_PARTY_MAPPING_PREFIX + platform + ":" + identifier;
            redisService.set(key, userId.toString(), THIRD_PARTY_MAPPING_EXPIRE);
            log.debug("缓存第三方映射成功 - 平台: {}, 标识符: {}, 用户ID: {}",
                     platform, maskIdentifier(identifier), userId);
        } catch (Exception e) {
            log.error("缓存第三方映射失败 - 平台: {}, 标识符: {}, 用户ID: {}",
                     platform, maskIdentifier(identifier), userId, e);
        }
    }

    /**
     * 根据第三方信息获取用户ID（性能优化）
     *
     * @param platform 平台
     * @param identifier 标识符
     * @return 用户ID，不存在返回null
     */
    public Long getUserIdByThirdParty(String platform, String identifier) {
        if (platform == null || identifier == null) {
            return null;
        }

        try {
            String key = THIRD_PARTY_MAPPING_PREFIX + platform + ":" + identifier;
            Object cached = redisService.get(key);
            if (cached != null) {
                log.debug("缓存命中第三方映射 - 平台: {}, 标识符: {}",
                         platform, maskIdentifier(identifier));
                return Long.valueOf(cached.toString());
            }
        } catch (Exception e) {
            log.error("获取第三方映射失败 - 平台: {}, 标识符: {}",
                     platform, maskIdentifier(identifier), e);
        }

        return null;
    }

    /**
     * 预热用户缓存（性能优化）
     *
     * @param user 用户信息
     */
    public void warmUpUserCache(User user) {
        if (user == null || user.getId() == null) {
            return;
        }

        // 缓存用户基本信息
        cacheUserInfo(user);

        // 缓存邮箱映射
        if (user.getEmail() != null) {
            cacheEmailMapping(user.getEmail(), user.getId());
        }

        // 缓存手机号映射
        if (user.getPhone() != null) {
            cachePhoneMapping(user.getPhone(), user.getId());
        }

        log.info("用户缓存预热完成 - 用户ID: {}", user.getId());
    }

    /**
     * 批量预热用户缓存（性能优化）
     *
     * @param users 用户列表
     */
    public void batchWarmUpUserCache(java.util.List<User> users) {
        if (users == null || users.isEmpty()) {
            return;
        }

        for (User user : users) {
            warmUpUserCache(user);
        }

        log.info("批量用户缓存预热完成 - 用户数量: {}", users.size());
    }

    // 辅助方法：脱敏邮箱
    private String maskEmail(String email) {
        if (email == null || email.length() <= 3) {
            return email;
        }
        int atIndex = email.indexOf('@');
        if (atIndex > 3) {
            return email.substring(0, 3) + "***" + email.substring(atIndex);
        }
        return email.substring(0, 1) + "***" + email.substring(atIndex);
    }

    // 辅助方法：脱敏手机号
    private String maskPhone(String phone) {
        if (phone == null || phone.length() <= 7) {
            return phone;
        }
        return phone.substring(0, 3) + "****" + phone.substring(phone.length() - 4);
    }

    // 辅助方法：脱敏标识符
    private String maskIdentifier(String identifier) {
        if (identifier == null || identifier.length() <= 6) {
            return identifier;
        }
        return identifier.substring(0, 3) + "***" + identifier.substring(identifier.length() - 3);
    }

    // ==================== 用户注销相关缓存方法 ====================

    /**
     * 标记用户账号在7天内注销过（参考其他项目实现）
     *
     * @param account 账号（邮箱或手机号）
     */
    public void addUnRegisterFlag(String account) {
        if (account == null || account.trim().isEmpty()) {
            return;
        }

        try {
            String key = USER_UNREGISTER_PREFIX + account;
            // 为了实现删除的用户账号7天内无法二次注册，添加缓存
            redisService.set(key, account, UNREGISTER_FLAG_EXPIRE);
            log.info("标记账号注销状态成功 - 账号: {}, 7天内不可重新注册", maskAccount(account));
        } catch (Exception e) {
            log.error("标记账号注销状态失败 - 账号: {}", maskAccount(account), e);
        }
    }

    /**
     * 判断当前账号是否在一周内有做过注销动作（参考其他项目实现）
     *
     * @param account 账号（邮箱或手机号）
     * @return true-已注销，false-未注销
     */
    public boolean checkIsUnRegister(String account) {
        if (account == null || account.trim().isEmpty()) {
            return false;
        }

        try {
            String key = USER_UNREGISTER_PREFIX + account;
            boolean exists = redisService.hasKey(key);

            if (exists) {
                log.warn("检测到已注销账号尝试操作 - 账号: {}", maskAccount(account));
            }

            return exists;
        } catch (Exception e) {
            log.error("检查账号注销状态失败 - 账号: {}", maskAccount(account), e);
            return false; // 异常时不阻止操作
        }
    }

    /**
     * 清除账号注销标记（管理员操作或特殊情况）
     *
     * @param account 账号（邮箱或手机号）
     */
    public void clearUnRegisterFlag(String account) {
        if (account == null || account.trim().isEmpty()) {
            return;
        }

        try {
            String key = USER_UNREGISTER_PREFIX + account;
            redisService.del(key);
            log.info("清除账号注销标记成功 - 账号: {}", maskAccount(account));
        } catch (Exception e) {
            log.error("清除账号注销标记失败 - 账号: {}", maskAccount(account), e);
        }
    }

    /**
     * 用户注销时记录所有绑定的账号（可扩展设计）
     *
     * @param userId 用户ID
     */
    public void addUnRegisterFlagForAllAccounts(Long userId) {
        if (userId == null) {
            return;
        }

        try {
            // 获取用户信息
            User user = userMapper.selectByPrimaryKey(userId);
            if (user == null) {
                log.warn("用户不存在，无法记录注销状态: userId={}", userId);
                return;
            }

            // 使用可扩展的账号提取器
            List<String> allAccounts = extractAllUserAccounts(user, userId);

            // 批量记录注销状态
            for (String account : allAccounts) {
                addUnRegisterFlag(account);
                log.info("记录账号注销状态: {}", maskAccount(account));
            }

            log.info("用户注销状态记录完成: userId={}, 共记录{}个账号", userId, allAccounts.size());
        } catch (Exception e) {
            log.error("记录用户注销状态失败: userId={}", userId, e);
        }
    }

    /**
     * 可扩展的账号提取器 - 提取用户所有绑定的账号
     * 新增登录方式时，只需要在这里添加对应的提取逻辑
     */
    private List<String> extractAllUserAccounts(User user, Long userId) {
        List<String> accounts = new ArrayList<>();

        // 1. 基础账号（手机号、邮箱）
        addAccountIfNotEmpty(accounts, user.getPhone(), "手机号");
        addAccountIfNotEmpty(accounts, user.getEmail(), "邮箱");

        // 2. 第三方登录账号
        List<String> thirdPartyAccounts = extractThirdPartyAccounts(userId);
        accounts.addAll(thirdPartyAccounts);

        // 3. 未来扩展点：新的登录方式可以在这里添加
        // addAccountIfNotEmpty(accounts, user.getWechatUnionId(), "微信UnionId");
        // addAccountIfNotEmpty(accounts, user.getGoogleAccount(), "Google账号");
        // addAccountIfNotEmpty(accounts, user.getGithubAccount(), "GitHub账号");

        return accounts;
    }

    /**
     * 辅助方法：添加非空账号到列表
     */
    private void addAccountIfNotEmpty(List<String> accounts, String account, String accountType) {
        if (account != null && !account.trim().isEmpty()) {
            accounts.add(account.trim());
            log.debug("提取到{}账号: {}", accountType, maskAccount(account));
        }
    }

    /**
     * 提取第三方登录账号（可扩展设计）
     */
    private List<String> extractThirdPartyAccounts(Long userId) {
        List<String> thirdPartyAccounts = new ArrayList<>();

        try {
            // 查询用户的所有第三方登录记录
            List<UserThirdPartyAuth> authList = userThirdPartyAuthMapper.selectByUserId(userId);

            if (authList != null && !authList.isEmpty()) {
                for (UserThirdPartyAuth auth : authList) {
                    if (auth.getOpenId() != null && !auth.getOpenId().trim().isEmpty() &&
                        auth.getProvider() != null && !auth.getProvider().trim().isEmpty()) {

                        // 使用可扩展的账号生成器
                        String thirdPartyAccount = generateThirdPartyAccountIdentifier(
                            auth.getOpenId(), auth.getProvider());

                        if (thirdPartyAccount != null) {
                            thirdPartyAccounts.add(thirdPartyAccount);
                            log.debug("提取到第三方账号: provider={}, account={}",
                                   auth.getProvider(), maskAccount(thirdPartyAccount));
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("提取第三方账号失败: userId={}", userId, e);
        }

        return thirdPartyAccounts;
    }

    /**
     * 可扩展的第三方账号标识生成器
     * 支持多种生成策略，便于后期扩展
     */
    private String generateThirdPartyAccountIdentifier(String openId, String provider) {
        if (openId == null || provider == null) {
            return null;
        }

        // 标准格式：openId_provider
        // 未来可以根据不同平台采用不同的生成策略
        return openId.trim() + "_" + provider.trim();
    }

    /**
     * 可扩展的登录方式到平台的映射器
     * 新增登录方式时，只需要在LOGIN_WAY_PLATFORM_MAPPING中添加映射即可
     */
    public String getPlatformFromLoginWay(String loginWay) {
        if (loginWay == null) {
            return null;
        }

        // 使用配置化的映射，便于扩展
        String platform = LOGIN_WAY_PLATFORM_MAPPING.get(loginWay.toLowerCase());

        if (platform == null) {
            // 兜底策略：去掉"by"前缀
            platform = loginWay.toLowerCase().startsWith("by")
                      ? loginWay.substring(2)
                      : loginWay;

            log.warn("未找到登录方式{}的平台映射，使用兜底策略: {}", loginWay, platform);
        }

        return platform;
    }

    /**
     * 脱敏显示账号信息
     */
    private String maskAccount(String account) {
        if (account == null || account.length() <= 3) {
            return "***";
        }

        if (account.contains("@")) {
            // 邮箱脱敏
            String[] parts = account.split("@");
            String username = parts[0];
            if (username.length() <= 3) {
                return "***@" + parts[1];
            }
            return username.substring(0, 2) + "***" + username.substring(username.length() - 1) + "@" + parts[1];
        } else if (account.contains("_")) {
            // 第三方账号脱敏（openId_provider格式）
            String[] parts = account.split("_");
            if (parts.length >= 2) {
                String openId = parts[0];
                String provider = parts[parts.length - 1];
                if (openId.length() > 6) {
                    return openId.substring(0, 3) + "***" + openId.substring(openId.length() - 3) + "_" + provider;
                }
            }
            return "***_" + parts[parts.length - 1];
        } else {
            // 手机号脱敏
            if (account.length() >= 7) {
                return account.substring(0, 3) + "****" + account.substring(account.length() - 3);
            }
            return account.substring(0, 1) + "***" + account.substring(account.length() - 1);
        }
    }
}
