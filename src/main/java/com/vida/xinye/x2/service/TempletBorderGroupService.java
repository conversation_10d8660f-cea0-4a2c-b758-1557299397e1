package com.vida.xinye.x2.service;

import com.vida.xinye.x2.dto.TempletBorderGroupDto;
import com.vida.xinye.x2.dto.param.TempletBorderGroupParam;
import com.vida.xinye.x2.mbg.model.TempletBorder;
import com.vida.xinye.x2.mbg.model.TempletBorderGroup;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public interface TempletBorderGroupService {

    List<TempletBorderGroup> listAllGroups();

    /**
     * 获取边框分组列表（支持多语言）
     */
    List<TempletBorderGroupDto> listWithLocale(String locale);

    List<TempletBorder> getBordersByGroup(Long groupId);

    Long createGroup(TempletBorderGroupParam param);

    int updateGroup(Long id, TempletBorderGroupParam param);

    int deleteGroup(Long id);

    @Transactional
    int sortGroups(List<Long> groupIds);
}
