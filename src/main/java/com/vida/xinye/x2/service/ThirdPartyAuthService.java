package com.vida.xinye.x2.service;

import com.vida.xinye.x2.dto.ThirdPartyUserInfoDto;
import com.vida.xinye.x2.dto.param.ThirdPartyLoginDto;

/**
 * 第三方登录服务接口
 *
 * <AUTHOR>
 * @date 2025/08/12
 */
public interface ThirdPartyAuthService {
    
    /**
     * 获取第三方登录授权URL
     *
     * @param provider 第三方平台标识
     * @param redirectUri 回调地址
     * @param state 防CSRF攻击的随机字符串
     * @return 授权URL
     */
    String getAuthorizationUrl(String provider, String redirectUri, String state);
    
    /**
     * 通过授权码获取用户信息
     *
     * @param loginDto 第三方登录参数
     * @return 第三方用户信息
     */
    ThirdPartyUserInfoDto getUserInfo(ThirdPartyLoginDto loginDto);
    
    /**
     * 刷新访问令牌
     *
     * @param provider 第三方平台标识
     * @param refreshToken 刷新令牌
     * @return 新的用户信息
     */
    ThirdPartyUserInfoDto refreshAccessToken(String provider, String refreshToken);
}
