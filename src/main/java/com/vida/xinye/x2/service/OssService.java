package com.vida.xinye.x2.service;

import cn.hutool.json.JSONObject;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.List;

/**
 * oss上传管理Service
 * Created by macro on 2018/5/17.
 */
public interface OssService {
    /**
     * oss上传文件-简单上传
     */
    List<String> upload(MultipartFile[] files);

    /**
     * oss上传文件-指定上传路径前缀目录
     * autoName - 是否自动重命名文件
     */
    List<String> upload(String objectPrefix, MultipartFile[] files, boolean autoName);

    String upload(String objectPrefix, File file);

    boolean hasObject(String objectPrefix, String objectName);

    /**
     * 获取oss object，如果存在，返回oss地址，不存在返回空字符串
     * @param objectPrefix
     * @param objectName
     * @return
     */
    String getObject(String objectPrefix, String objectName);

    /**
     * @return
     * @Title: getAssumeRole
     * @Description:
     * <AUTHOR>
     */
    public JSONObject getAssumeRole();

    /**
     * 上传图片到oss
     *
     * @return
     */
    String uploadImage(String base64Image, String fileName);
}
