package com.vida.xinye.x2.service;

import com.vida.xinye.x2.api.CommonPage;
import com.vida.xinye.x2.domain.param.MaterialParam;
import com.vida.xinye.x2.dto.MaterialDto;
import com.vida.xinye.x2.mbg.model.Material;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 素材服务接口
 *
 * <AUTHOR>
 * @date 2024/11/11
 */
@Service
public interface MaterialService {
    /**
     * 分页获取素材列表
     * 根据素材类别查询
     */
    CommonPage<MaterialDto> list(Integer pageSize, Integer pageNum, Long userId, Long categoryId);

    /**
     * 分页获取我的收藏
     */
    CommonPage<Material> favorite(Integer pageSize, Integer pageNum, Long userId);

    /**
     * 新增素材
     * @return
     */
    Long create(MaterialParam materialParam);

    /**
     * 修改素材
     */
    int update(Long id, MaterialParam param);

    /**
     * 删除素材
     */
    int delete(Long materialId);

    /**
     * 收藏素材
     */
    boolean addFavorite(Long materialId, Long userId);

    /**
     * 移除收藏
     */
    boolean removeFavorite(List<Long> materialIds, Long userId);
}
