/**
 * @Description: 身份证验证缓存服务
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/01/16 16:15
 */
package com.vida.xinye.x2.service;

import com.vida.xinye.x2.dao.IdCardVerificationDao;
import com.vida.xinye.x2.dto.IdCardVerifyResultDto;
import com.vida.xinye.x2.mbg.model.IdCardVerification;
import com.vida.xinye.x2.util.IdCardHashUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import java.util.Calendar;
import java.util.Date;

/**
 * 身份证验证缓存服务
 * 负责管理身份证验证结果的缓存
 */
@Service
@Slf4j
public class IdCardVerificationCacheService {

  @Autowired
  private IdCardVerificationDao idCardVerificationDao;

  /**
   * 缓存有效期（天），默认30天
   */
  @Value("${idcard.verification.cache.expire-days:30}")
  private Integer cacheExpireDays;

  @Autowired
  private MessageSource messageSource;

  /**
   * 根据姓名和身份证号查询缓存的验证结果
   *
   * @param name   姓名
   * @param idCard 身份证号码
   * @return 验证结果，如果没有缓存则返回null
   */
  public IdCardVerifyResultDto getCachedVerification(String name, String idCard) {
    try {
      String idCardHash = IdCardHashUtil.hashIdCard(idCard);
      IdCardVerification cachedRecord = idCardVerificationDao.findValidRecord(name, idCardHash);

      if (cachedRecord != null) {
        log.info("找到身份证验证缓存，姓名：{}，验证时间：{}", name, cachedRecord.getVerificationTime());
        return buildResultFromCache(cachedRecord);
      }

      log.info("未找到身份证验证缓存，姓名：{}", name);
      return null;
    } catch (Exception e) {
      log.error("查询身份证验证缓存失败，姓名：{}", name, e);
      return null;
    }
  }

  /**
   * 保存验证结果到缓存
   *
   * @param name   姓名
   * @param idCard 身份证号码
   * @param result 第三方验证结果
   */
  public void saveVerificationResult(String name, String idCard, IdCardVerifyResultDto result) {
    try {
      String idCardHash = IdCardHashUtil.hashIdCard(idCard);
      IdCardVerification record = buildCacheRecord(name, idCardHash, result);

      int affectedRows = idCardVerificationDao.insertRecord(record);
      if (affectedRows > 0) {
        log.info("身份证验证结果已缓存，姓名：{}，结果：{}", name, result.getCode());
      } else {
        log.warn("身份证验证结果缓存失败，姓名：{}", name);
      }
    } catch (Exception e) {
      log.error("保存身份证验证缓存失败，姓名：{}", name, e);
    }
  }

  /**
   * 清理过期的缓存记录
   *
   * @return 清理的记录数
   */
  public int cleanExpiredRecords() {
    try {
      int deletedCount = idCardVerificationDao.deleteExpiredRecords();
      log.info("清理过期身份证验证缓存记录数：{}", deletedCount);
      return deletedCount;
    } catch (Exception e) {
      log.error("清理过期身份证验证缓存失败", e);
      return 0;
    }
  }

  /**
   * 从缓存记录构建返回结果
   */
  private IdCardVerifyResultDto buildResultFromCache(IdCardVerification record) {
    IdCardVerifyResultDto result = new IdCardVerifyResultDto();

    // 根据验证结果设置响应码和成功标志
    if (record.getVerificationResult().equals(0)) {
      // 一致
      result.setSuccess(true);
      result.setCode(200);
      result.setMsg("成功");
      // 设置详细数据
      IdCardVerifyResultDto.IdCardVerifyData data = new IdCardVerifyResultDto.IdCardVerifyData();
      data.setBirthday(record.getBirthday());
      data.setResult(record.getVerificationResult());
      data.setAddress(record.getAddress());
      data.setOrderNo(record.getOrderNo());
      data.setSex(record.getSex());
      data.setDesc(record.getDescription());

      result.setData(data);
    } else {
      // 不一致
      result.setSuccess(false);
      result.setCode(500);
      String msg = messageSource.getMessage("idcard.verify.failed", null, "身份证验证失败",
              LocaleContextHolder.getLocale());
      result.setMsg(msg);
    }

    return result;
  }

  /**
   * 从第三方验证结果构建缓存记录
   */
  private IdCardVerification buildCacheRecord(String name, String idCardHash, IdCardVerifyResultDto result) {
    IdCardVerification record = new IdCardVerification();
    Date now = new Date();

    record.setName(name);
    record.setIdCardHash(idCardHash);
    record.setVerificationTime(now);
    record.setCreateTime(now);
    record.setUpdateTime(now);
    record.setSource("aliyun");

    // 设置过期时间
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(now);
    calendar.add(Calendar.DAY_OF_MONTH, cacheExpireDays);
    record.setExpireTime(calendar.getTime());

    // 设置验证结果数据
    if (result.getData() != null) {
      IdCardVerifyResultDto.IdCardVerifyData data = result.getData();
      record.setVerificationResult(data.getResult());
      record.setBirthday(data.getBirthday());
      record.setAddress(data.getAddress());
      record.setSex(data.getSex());
      record.setDescription(data.getDesc());
      record.setOrderNo(data.getOrderNo());
    } else {
      // 异常
      record.setVerificationResult(1);
      record.setDescription(result.getMsg());
    }

    return record;
  }
}