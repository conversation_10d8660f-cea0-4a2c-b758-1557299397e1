package com.vida.xinye.x2.service.api;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.vida.xinye.x2.dao.WrongQuestionTagDao;
import com.vida.xinye.x2.dto.WrongQuestionSubject;
import com.vida.xinye.x2.exception.Asserts;
import com.vida.xinye.x2.mbg.mapper.WrongQuestionMapper;
import com.vida.xinye.x2.mbg.mapper.WrongQuestionTagMapper;
import com.vida.xinye.x2.mbg.mapper.WrongQuestionTagRelationMapper;
import com.vida.xinye.x2.mbg.mapper.WrongQuestionTagTypeMapper;
import com.vida.xinye.x2.mbg.model.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 错题标签服务接口
 *
 * <AUTHOR>
 * @date 2025/02/20
 */
@Service
public class WrongQuestionTagService {
    @Autowired
    private WrongQuestionTagTypeMapper tagTypeMapper;

    @Autowired
    private WrongQuestionTagMapper tagMapper;
    @Autowired
    private WrongQuestionTagDao tagDao;
    @Autowired
    private WrongQuestionTagRelationMapper questionTagRelationMapper;
    @Autowired
    private WrongQuestionMapper wrongQuestionMapper;


    // 查询错题标签类型
    public List<WrongQuestionTagType> findAllTagTypes() {
        WrongQuestionTagTypeExample example = new WrongQuestionTagTypeExample();
        return tagTypeMapper.selectByExample(example);
    }

    // 根据错题标签类型，返回该类型下的默认标签 + 用户添加的标签
    public List<WrongQuestionTag> findTagsByTypeId(Long typeId, Long userId) {
        return tagDao.queryTagsByTypeId(typeId, userId);
    }

    // 用户可新增标签到指定标签类型下
    public Long create(String tagName, Long typeId, Long userId) {
        // 检查标签是否存在
        WrongQuestionTagExample example = new WrongQuestionTagExample();
        example.createCriteria().andTagNameEqualTo(tagName).andTagTypeIdEqualTo(typeId);
        List<com.vida.xinye.x2.mbg.model.WrongQuestionTag> existingTags = tagMapper.selectByExample(example);
        if (!existingTags.isEmpty()) {
            Asserts.fail("标签已存在！");
        }

        // 新增标签
        com.vida.xinye.x2.mbg.model.WrongQuestionTag model = new com.vida.xinye.x2.mbg.model.WrongQuestionTag();
        model.setUserId(userId);
        model.setTagName(tagName);
        model.setTagTypeId(typeId);
        tagMapper.insertSelective(model);
        return model.getId();
    }

    // 用户可删除自己新增的标签
    @Transactional
    public int delete(Long tagId, Long userId) {
        WrongQuestionTag tag = tagMapper.selectByPrimaryKey(tagId);
        if (ObjectUtil.isNull(tag)) {
            Asserts.fail("标签不存在！");
        }
        if (tag.getUserId().longValue() != userId.longValue()) {
            Asserts.fail("您无权删除该标签！");
        }
        // 查询该标签下是否有错题
        WrongQuestionTagRelationExample questionTagRelationExample = new WrongQuestionTagRelationExample();
        questionTagRelationExample.createCriteria().andTagIdEqualTo(tagId);
        List<WrongQuestionTagRelation> questions = questionTagRelationMapper
                .selectByExample(questionTagRelationExample);
        if (CollUtil.isNotEmpty(questions)) {
//            Asserts.fail("该科目下存在错题，无法删除！");
            List<Long> questionIds = questions.stream().map(WrongQuestionTagRelation::getQuestionId).collect(Collectors.toList());
            // 删除错题标签关联
            questionTagRelationExample.createCriteria().andQuestionIdIn(questionIds);
            questionTagRelationMapper.deleteByExample(questionTagRelationExample);
            // 删除错题数据
            WrongQuestionExample example = new WrongQuestionExample();
            example.createCriteria()
                    .andIdIn(questionIds)
                    .andUserIdEqualTo(userId);
            wrongQuestionMapper.deleteByExample(example);
        }
        return tagMapper.deleteByPrimaryKey(tagId);
    }

}
