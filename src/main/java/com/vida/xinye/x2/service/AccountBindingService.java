package com.vida.xinye.x2.service;

import com.vida.xinye.x2.dto.AccountBindingDto;
import com.vida.xinye.x2.dto.AccountBindingStatusDto;
import com.vida.xinye.x2.dto.param.BindEmailDto;
import com.vida.xinye.x2.dto.param.BindPhoneDto;
import com.vida.xinye.x2.dto.param.UnbindAccountDto;
import java.util.List;

/**
 * 账号绑定服务接口
 *
 * <AUTHOR>
 * @date 2025/08/12
 */
public interface AccountBindingService {
    
    /**
     * 绑定手机号
     *
     * @param userId 用户ID
     * @param bindPhoneDto 绑定手机号参数
     * @return 是否成功
     */
    boolean bindPhone(Long userId, BindPhoneDto bindPhoneDto);
    
    /**
     * 绑定邮箱
     *
     * @param userId 用户ID
     * @param bindEmailDto 绑定邮箱参数
     * @return 是否成功
     */
    boolean bindEmail(Long userId, BindEmailDto bindEmailDto);
    
    /**
     * 解绑账号
     *
     * @param userId 用户ID
     * @param unbindAccountDto 解绑参数
     * @return 是否成功
     */
    boolean unbindAccount(Long userId, UnbindAccountDto unbindAccountDto);
    
    /**
     * 获取用户绑定的账号列表
     *
     * @param userId 用户ID
     * @return 绑定账号列表
     */
    List<AccountBindingDto> getUserBindings(Long userId);
    
    /**
     * 检查账号是否已被绑定
     *
     * @param accountType 账号类型
     * @param accountIdentifier 账号标识符
     * @return 是否已绑定
     */
    boolean isAccountBound(Integer accountType, String accountIdentifier);
    
    /**
     * 检查用户是否需要绑定手机号
     *
     * @param userId 用户ID
     * @return 是否需要绑定手机号
     */
    boolean requiresPhoneBinding(Long userId);
    
    /**
     * 设置主账号
     *
     * @param userId 用户ID
     * @param accountType 账号类型
     * @param accountIdentifier 账号标识符
     * @return 是否成功
     */
    boolean setPrimaryAccount(Long userId, Integer accountType, String accountIdentifier);

    /**
     * 检查绑定状态
     *
     * @param userId 用户ID
     * @return 绑定状态信息
     */
    AccountBindingStatusDto checkBindingStatus(Long userId);

    /**
     * 检查手机号是否已被其他用户绑定
     *
     * @param phone 手机号
     * @param excludeUserId 排除的用户ID（当前用户）
     * @return 是否已被绑定
     */
    boolean isPhoneAlreadyBound(String phone, Long excludeUserId);

    /**
     * 检查邮箱是否已被其他用户绑定
     *
     * @param email 邮箱
     * @param excludeUserId 排除的用户ID（当前用户）
     * @return 是否已被绑定
     */
    boolean isEmailAlreadyBound(String email, Long excludeUserId);
}
