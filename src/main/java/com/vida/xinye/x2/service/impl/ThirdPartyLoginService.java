package com.vida.xinye.x2.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.vida.xinye.x2.constant.LoginWayConstant;
import com.vida.xinye.x2.dto.param.ThirdPartyLoginDto;
import com.vida.xinye.x2.exception.Asserts;
import com.vida.xinye.x2.util.AppleIdUtil;
import com.vida.xinye.x2.util.QQApiUtil;
import com.vida.xinye.x2.util.WechatApiUtil;
import com.vida.xinye.x2.util.WeiboApiUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 第三方登录服务（参考snapTag项目实现）
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@Service
@Slf4j
public class ThirdPartyLoginService {

    @Autowired
    private WechatApiUtil wechatApiUtil;

    @Autowired
    private QQApiUtil qqApiUtil;

    @Autowired
    private WeiboApiUtil weiboApiUtil;

    @Autowired
    private AppleIdUtil appleIdUtil;

    /**
     * 检测第三方登录状态
     *
     * @param thirdPartyLoginDto 第三方登录参数
     */
    public void checkThirdLoginStatus(ThirdPartyLoginDto thirdPartyLoginDto) {
        String loginWay = thirdPartyLoginDto.getLoginWay();
        
        if (LoginWayConstant.LOGIN_BY_SINA.equals(loginWay)) {
            checkSinaLogin(thirdPartyLoginDto);
        } else if (LoginWayConstant.LOGIN_BY_WEIXIN.equals(loginWay)) {
            checkWeChatLogin(thirdPartyLoginDto);
        } else if (LoginWayConstant.LOGIN_BY_QQ.equals(loginWay)) {
            checkQQLogin(thirdPartyLoginDto);
        } else if (LoginWayConstant.LOGIN_BY_APPLE_ID.equals(loginWay)) {
            checkAppleIdLogin(thirdPartyLoginDto);
        } else {
            Asserts.fail("不支持的登录方式: " + loginWay);
        }
    }

    /**
     * 检查新浪微博登录
     */
    private void checkSinaLogin(ThirdPartyLoginDto dto) {
        try {
            if (!weiboApiUtil.isConfigured()) {
                Asserts.fail("微博登录未配置");
            }

            boolean isValid = weiboApiUtil.validateAccessToken(dto.getAccessToken());
            if (!isValid) {
                Asserts.fail("新浪微博授权验证失败");
            }

            log.info("新浪微博登录验证成功");
        } catch (Exception e) {
            log.error("新浪微博登录验证异常", e);
            Asserts.fail("新浪微博授权验证失败");
        }
    }

    /**
     * 检查微信登录
     */
    private void checkWeChatLogin(ThirdPartyLoginDto dto) {
        try {
            if (!wechatApiUtil.isConfigured()) {
                Asserts.fail("微信登录未配置");
            }

            // 小程序登录和APP登录验证方式不同
            if (LoginWayConstant.WECHAT_FROM_MINIPROGRAM.equals(dto.getFrom())) {
                // 小程序登录验证（通常不需要验证access_token）
                log.info("微信小程序登录，openId: {}", dto.getOpenId());
            } else {
                // APP和网页登录验证
                boolean isValid = wechatApiUtil.validateAccessToken(dto.getAccessToken(), dto.getOpenId());
                if (!isValid) {
                    Asserts.fail("微信授权验证失败");
                }

                log.info("微信登录验证成功，openId: {}", dto.getOpenId());
            }
        } catch (Exception e) {
            log.error("微信登录验证异常", e);
            Asserts.fail("微信授权验证失败");
        }
    }

    /**
     * 检查QQ登录
     */
    private void checkQQLogin(ThirdPartyLoginDto dto) {
        try {
            if (!qqApiUtil.isConfigured()) {
                Asserts.fail("QQ登录未配置");
            }

            boolean isValid = qqApiUtil.validateAccessToken(dto.getAccessToken(), dto.getOpenId(), dto.getAppId());
            if (!isValid) {
                Asserts.fail("QQ授权验证失败");
            }

            log.info("QQ登录验证成功，openId: {}", dto.getOpenId());
        } catch (Exception e) {
            log.error("QQ登录验证异常", e);
            Asserts.fail("QQ授权验证失败");
        }
    }

    /**
     * 检查Apple ID登录
     */
    private void checkAppleIdLogin(ThirdPartyLoginDto dto) {
        try {
            if (!appleIdUtil.isConfigured()) {
                Asserts.fail("Apple ID登录未配置");
            }

            // Apple ID登录需要验证JWT token
            if (StringUtils.isEmpty(dto.getJwtToken())) {
                Asserts.fail("Apple ID JWT Token不能为空");
            }

            boolean isValid = appleIdUtil.verifyJWT(dto.getJwtToken());
            if (!isValid) {
                Asserts.fail("Apple ID授权验证失败");
            }

            log.info("Apple ID登录验证成功");
        } catch (Exception e) {
            log.error("Apple ID登录验证异常", e);
            Asserts.fail("Apple ID授权验证失败");
        }
    }

    /**
     * 获取微信用户信息
     */
    public JSONObject getWeChatUserInfo(String accessToken, String openId) {
        try {
            return wechatApiUtil.getUserInfo(accessToken, openId);
        } catch (Exception e) {
            log.error("获取微信用户信息异常", e);
            return null;
        }
    }

    /**
     * 获取QQ用户信息
     */
    public JSONObject getQQUserInfo(String accessToken, String openId, String appId) {
        try {
            return qqApiUtil.getUserInfo(accessToken, openId, appId);
        } catch (Exception e) {
            log.error("获取QQ用户信息异常", e);
            return null;
        }
    }

    /**
     * 获取微博用户信息
     */
    public JSONObject getWeiboUserInfo(String accessToken, String uid) {
        try {
            return weiboApiUtil.getUserInfo(accessToken, uid);
        } catch (Exception e) {
            log.error("获取微博用户信息异常", e);
            return null;
        }
    }
}
