package com.vida.xinye.x2.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.BooleanUtil;
import com.github.pagehelper.PageHelper;
import com.vida.xinye.x2.api.CommonPage;
import com.vida.xinye.x2.dto.param.CloudfileParam;
import com.vida.xinye.x2.exception.Asserts;
import com.vida.xinye.x2.mbg.mapper.CloudfileMapper;
import com.vida.xinye.x2.mbg.model.Cloudfile;
import com.vida.xinye.x2.mbg.model.CloudfileExample;
import com.vida.xinye.x2.service.CloudfileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import com.vida.xinye.x2.api.ResultCode;

import java.util.List;

/**
 * Author：Chenjy
 * Date:2025/7/17
 * Description:
 */
@Service
public class CloudfileServiceImpl implements CloudfileService {

    @Autowired
    private CloudfileMapper cloudfileMapper;

    @Override
    public int create(Cloudfile cloudfile) {
        // 检查文件名是否重复
        if (checkFilenameExists(cloudfile.getName(), cloudfile.getUserId())) {
            Asserts.fail(ResultCode.FILE_EXISTS);
        }
        return cloudfileMapper.insertSelective(cloudfile);
    }

    @Override
    public int upload(CloudfileParam param) {
        Cloudfile cloudfile = new Cloudfile();
        BeanUtil.copyProperties(param, cloudfile);

        //第一次上传，走新增逻辑
        if (param.getOverride() == null) {
            return create(cloudfile);
        }

        // 覆盖逻辑
        if (BooleanUtil.isTrue(param.getOverride())) {
            Cloudfile existing = findByNameAndUser(param.getName(), param.getUserId());
            if (existing != null) {
                existing.setUrl(param.getUrl());
                return cloudfileMapper.updateByPrimaryKeySelective(existing);
            }

            return cloudfileMapper.insertSelective(cloudfile);
        }
        // 不覆盖时生成新文件名
        else {
            cloudfile.setName(generateUniqueFilename(cloudfile.getName(), cloudfile.getUserId()));
            return cloudfileMapper.insertSelective(cloudfile);
        }
    }

    @Override
    public int delete(List<Long> ids, Long userId) {
        CloudfileExample example = new CloudfileExample();
        example.createCriteria().andIdIn(ids).andUserIdEqualTo(userId);
        return cloudfileMapper.deleteByExample(example);
    }

    @Override
    public int delete(Long id, Long userId) {
        // 实际开发中需要添加权限校验
        // 检查文件是否存在
        Cloudfile cloudfile = cloudfileMapper.selectByPrimaryKey(id);
        if (cloudfile == null) {
            throw new RuntimeException("文件不存在！");
        }
        // 文件是否属于该userId
        if (!cloudfile.getUserId().equals(userId)) {
            throw new RuntimeException("您无权删除该文件！");
        }

        return cloudfileMapper.deleteByPrimaryKey(id);
    }

    @Override
    public CommonPage<Cloudfile> list(Long userId, int pageSize, int pageNum) {
        PageHelper.startPage(pageNum, pageSize);

        CloudfileExample example = new CloudfileExample();
        example.createCriteria().andUserIdEqualTo(userId);
        List<Cloudfile> cloudfiles = cloudfileMapper.selectByExample(example);
        return CommonPage.restPage(cloudfiles);
    }

    @Override
    public boolean checkFilenameExists(String filename, Long userId) {
        CloudfileExample example = new CloudfileExample();
        example.createCriteria().andUserIdEqualTo(userId).andNameEqualTo(filename);
        return cloudfileMapper.countByExample(example) > 0;
    }

    private Cloudfile findByNameAndUser(String filename, Long userId) {
        CloudfileExample example = new CloudfileExample();
        example.createCriteria().andUserIdEqualTo(userId).andNameEqualTo(filename);
        List<Cloudfile> cloudfiles = cloudfileMapper.selectByExample(example);
        if (cloudfiles.isEmpty()) {
            return null;
        }
        return cloudfiles.get(0);
    }

    private String generateUniqueFilename(String originalName, Long userId) {
        String newName = originalName;
        int counter = 1;

        while (checkFilenameExists(newName, userId)) {
            String extension = "";
            String baseName = originalName;

            if (originalName.contains(".")) {
                baseName = StringUtils.stripFilenameExtension(originalName);
                extension = "." + StringUtils.getFilenameExtension(originalName);
            }

            newName = baseName + "(" + counter++ + ")" + extension;
        }
        return newName;
    }
}
