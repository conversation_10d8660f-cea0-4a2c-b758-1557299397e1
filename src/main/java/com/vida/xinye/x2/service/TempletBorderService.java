package com.vida.xinye.x2.service;

import com.vida.xinye.x2.dto.TempletBorderDto;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public interface TempletBorderService {

    /**
     * 获取标签边框列表
     */
    List<TempletBorderDto> list(Long groupId);

    /**
     * 新增标签边框
     *
     * @return
     */
    Long create(TempletBorderDto materialParam);

    /**
     * 修改标签边框
     */
    int update(Long id, TempletBorderDto param);

    /**
     * 删除标签边框
     */
    int delete(Long id);

    /**
     * 批量删除标签边框
     */
    int batchDelete(List<Long> ids, Long userId);
}
