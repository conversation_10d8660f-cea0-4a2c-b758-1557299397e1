package com.vida.xinye.x2.service;

import com.vida.xinye.x2.dto.BatchQuestionCuttingResultDto;
import com.vida.xinye.x2.dto.QuestionCuttingResultDto;
import com.vida.xinye.x2.dto.param.QuestionCuttingParam;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 智能切题服务接口
 *
 * <AUTHOR>
 * @date 2025/08/14
 */
public interface QuestionCuttingService {

    /**
     * 单张图片切题
     *
     * @param param 切题参数
     * @return 切题结果
     */
    QuestionCuttingResultDto cutQuestion(QuestionCuttingParam param);

    /**
     * 批量图片切题
     *
     * @param images 图片列表
     * @param strategy 切题策略
     * @param provider 指定的提供商
     * @param subject 学科
     * @param grade 年级
     * @param needDetail 是否需要详细信息
     * @param enableCache 是否启用缓存
     * @param parallel 是否并行处理
     * @return 批量切题结果
     */
    BatchQuestionCuttingResultDto batchCutQuestion(
            List<MultipartFile> images,
            String strategy,
            String provider,
            String subject,
            String grade,
            Boolean needDetail,
            Boolean enableCache,
            Boolean parallel
    );

    /**
     * 通过URL切题
     *
     * @param imageUrl 图片URL
     * @param strategy 切题策略
     * @param provider 指定的提供商
     * @param subject 学科
     * @param grade 年级
     * @param needDetail 是否需要详细信息
     * @param enableCache 是否启用缓存
     * @return 切题结果
     */
    QuestionCuttingResultDto cutQuestionByUrl(
            String imageUrl,
            String strategy,
            String provider,
            String subject,
            String grade,
            Boolean needDetail,
            Boolean enableCache
    );

    /**
     * 通过Base64切题
     *
     * @param imageBase64 图片Base64编码
     * @param strategy 切题策略
     * @param provider 指定的提供商
     * @param subject 学科
     * @param grade 年级
     * @param needDetail 是否需要详细信息
     * @param enableCache 是否启用缓存
     * @return 切题结果
     */
    QuestionCuttingResultDto cutQuestionByBase64(
            String imageBase64,
            String strategy,
            String provider,
            String subject,
            String grade,
            Boolean needDetail,
            Boolean enableCache
    );

    /**
     * 获取支持的提供商列表
     *
     * @return 提供商列表
     */
    List<String> getSupportedProviders();

    /**
     * 获取支持的策略列表
     *
     * @return 策略列表
     */
    List<String> getSupportedStrategies();

    /**
     * 检查提供商状态
     *
     * @param provider 提供商代码
     * @return 是否可用
     */
    boolean checkProviderStatus(String provider);

    /**
     * 获取提供商统计信息
     *
     * @return 统计信息
     */
    java.util.Map<String, Object> getProviderStats();
}
