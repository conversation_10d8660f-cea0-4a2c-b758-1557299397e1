package com.vida.xinye.x2.service.impl;

import com.vida.xinye.x2.constant.LoginWayConstant;
import com.vida.xinye.x2.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;

/**
 * 登录安全服务实现
 * 基于Redis实现登录安全管理
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@Service
@Slf4j
public class LoginSecurityServiceImpl {

    @Autowired
    private RedisService redisService;

    // Redis key前缀
    private static final String LOGIN_FAILURE_PREFIX = "login_failure:";
    private static final String ACCOUNT_LOCKED_PREFIX = "account_locked:";
    private static final String LOGIN_ATTEMPT_PREFIX = "login_attempt:";

    /**
     * 记录登录失败
     *
     * @param account   账号标识符
     * @param loginWay  登录方式
     * @param clientIp  客户端IP
     * @return 当前失败次数
     */
    public int recordLoginFailure(String account, String loginWay, String clientIp) {
        String key = buildFailureKey(account, loginWay);
        Object countObj = redisService.get(key);
        String countStr = countObj == null ? null : countObj.toString();
        int count = countStr == null ? 0 : Integer.parseInt(countStr);
        count++;

        // 设置过期时间为24小时
        redisService.set(key, String.valueOf(count), 24 * 3600);

        log.warn("登录失败记录 - 账号: {}, 登录方式: {}, IP: {}, 失败次数: {}", 
                maskAccount(account), loginWay, clientIp, count);

        // 如果达到最大失败次数，锁定账号
        if (count >= LoginWayConstant.MAX_LOGIN_ATTEMPTS) {
            lockAccount(account, loginWay);
        }

        return count;
    }

    /**
     * 清除登录失败记录
     *
     * @param account  账号标识符
     * @param loginWay 登录方式
     */
    public void clearLoginFailures(String account, String loginWay) {
        String key = buildFailureKey(account, loginWay);
        redisService.del(key);
        log.info("清除登录失败记录 - 账号: {}, 登录方式: {}", maskAccount(account), loginWay);
    }

    /**
     * 锁定账号
     *
     * @param account  账号标识符
     * @param loginWay 登录方式
     */
    public void lockAccount(String account, String loginWay) {
        String key = buildLockKey(account, loginWay);
        int lockDuration = LoginWayConstant.ACCOUNT_LOCK_MINUTES * 60;
        redisService.set(key, "locked", lockDuration);
        
        log.warn("账号已锁定 - 账号: {}, 登录方式: {}, 锁定时间: {}分钟", 
                maskAccount(account), loginWay, LoginWayConstant.ACCOUNT_LOCK_MINUTES);
    }

    /**
     * 检查账号是否被锁定
     *
     * @param account  账号标识符
     * @param loginWay 登录方式
     * @return 是否被锁定
     */
    public boolean isAccountLocked(String account, String loginWay) {
        String key = buildLockKey(account, loginWay);
        return redisService.hasKey(key);
    }

    /**
     * 解锁账号
     *
     * @param account  账号标识符
     * @param loginWay 登录方式
     */
    public void unlockAccount(String account, String loginWay) {
        String lockKey = buildLockKey(account, loginWay);
        String failureKey = buildFailureKey(account, loginWay);
        
        redisService.del(lockKey);
        redisService.del(failureKey);
        
        log.info("账号已解锁 - 账号: {}, 登录方式: {}", maskAccount(account), loginWay);
    }

    /**
     * 获取登录失败次数
     *
     * @param account  账号标识符
     * @param loginWay 登录方式
     * @return 失败次数
     */
    public int getLoginFailureCount(String account, String loginWay) {
        String key = buildFailureKey(account, loginWay);
        Object countObj = redisService.get(key);
        String countStr = countObj == null ? null : countObj.toString();
        return countStr == null ? 0 : Integer.parseInt(countStr);
    }

    /**
     * 获取剩余锁定时间（秒）
     *
     * @param account  账号标识符
     * @param loginWay 登录方式
     * @return 剩余锁定时间，-1表示未锁定
     */
    public long getRemainingLockTime(String account, String loginWay) {
        String key = buildLockKey(account, loginWay);
        return redisService.getExpire(key);
    }

    /**
     * 检查是否可以尝试登录
     *
     * @param account  账号标识符
     * @param loginWay 登录方式
     * @return 是否可以登录
     */
    public boolean canAttemptLogin(String account, String loginWay) {
        return !isAccountLocked(account, loginWay);
    }

    /**
     * 获取登录安全信息
     *
     * @param account  账号标识符
     * @param loginWay 登录方式
     * @return 安全信息描述
     */
    public String getSecurityInfo(String account, String loginWay) {
        if (isAccountLocked(account, loginWay)) {
            long remainingTime = getRemainingLockTime(account, loginWay);
            return String.format("账号已被锁定，剩余时间：%d分钟", remainingTime / 60);
        }

        int failureCount = getLoginFailureCount(account, loginWay);
        if (failureCount > 0) {
            int remainingAttempts = LoginWayConstant.MAX_LOGIN_ATTEMPTS - failureCount;
            return String.format("登录失败%d次，还可尝试%d次", failureCount, remainingAttempts);
        }

        return "账号状态正常";
    }

    /**
     * 记录登录尝试（用于统计）
     *
     * @param account  账号标识符
     * @param loginWay 登录方式
     * @param clientIp 客户端IP
     * @param success  是否成功
     */
    public void recordLoginAttempt(String account, String loginWay, String clientIp, boolean success) {
        String key = LOGIN_ATTEMPT_PREFIX + account + ":" + loginWay + ":" + 
                    LocalDateTime.now().toLocalDate().toString();
        
        Object countObj = redisService.get(key);
        String countStr = countObj == null ? null : countObj.toString();
        int count = countStr == null ? 0 : Integer.parseInt(countStr);
        count++;
        
        // 设置到当天结束过期
        long expireSeconds = getSecondsUntilEndOfDay();
        redisService.set(key, String.valueOf(count), expireSeconds);
        
        log.debug("登录尝试记录 - 账号: {}, 登录方式: {}, IP: {}, 成功: {}, 今日次数: {}", 
                maskAccount(account), loginWay, clientIp, success, count);
    }

    /**
     * 构建失败记录key
     */
    private String buildFailureKey(String account, String loginWay) {
        return LOGIN_FAILURE_PREFIX + account + ":" + loginWay;
    }

    /**
     * 构建锁定key
     */
    private String buildLockKey(String account, String loginWay) {
        return ACCOUNT_LOCKED_PREFIX + account + ":" + loginWay;
    }

    /**
     * 账号脱敏处理
     */
    private String maskAccount(String account) {
        if (account == null || account.length() <= 3) {
            return account;
        }
        
        if (account.contains("@")) {
            // 邮箱脱敏
            int atIndex = account.indexOf('@');
            if (atIndex > 1) {
                return account.charAt(0) + "***" + account.substring(atIndex);
            }
        } else if (account.matches("^1[3-9]\\d{9}$")) {
            // 手机号脱敏
            return account.substring(0, 3) + "****" + account.substring(7);
        }
        
        // 其他类型脱敏
        return account.substring(0, 2) + "***" + account.substring(account.length() - 1);
    }

    /**
     * 获取到当天结束的秒数
     */
    private long getSecondsUntilEndOfDay() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime endOfDay = now.toLocalDate().atTime(23, 59, 59);
        return java.time.Duration.between(now, endOfDay).getSeconds();
    }
}
