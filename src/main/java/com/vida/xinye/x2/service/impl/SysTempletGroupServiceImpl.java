package com.vida.xinye.x2.service.impl;

import com.vida.xinye.x2.dto.SystemTempletGroupDto;
import com.vida.xinye.x2.dto.param.SysTempletGroupParam;
import com.vida.xinye.x2.exception.Asserts;
import com.vida.xinye.x2.mbg.mapper.SysTempletGroupMapper;
import com.vida.xinye.x2.mbg.mapper.SysTempletMapper;
import com.vida.xinye.x2.mbg.model.*;
import com.vida.xinye.x2.service.SysTempletGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class SysTempletGroupServiceImpl implements SysTempletGroupService {

    @Autowired
    private SysTempletGroupMapper groupMapper;
    @Resource
    private SysTempletMapper sysTempletMapper;

    @Override
    public List<SystemTempletGroupDto> listGroups(String locale) {
        SysTempletGroupExample example = new SysTempletGroupExample();
        example.createCriteria().andLocaleEqualTo(locale);
        example.setOrderByClause("sort asc");
        List<SysTempletGroup> groups = groupMapper.selectByExample(example);
        return groups.stream().map(group -> {
            SystemTempletGroupDto dto = new SystemTempletGroupDto();
            dto.setId(group.getId());
            dto.setName(group.getName());
            dto.setSort(group.getSort());
            dto.setCreateTime(group.getCreateTime());
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public Long createGroup(SysTempletGroupParam param) {
        // 校验分组名称唯一性
        SysTempletGroupExample example = new SysTempletGroupExample();
        example.createCriteria().andLocaleEqualTo(param.getLocale()).andNameEqualTo(param.getName());
        if (groupMapper.countByExample(example) > 0) {
            Asserts.fail("分组名称已存在");
        }

        SysTempletGroup group = new SysTempletGroup();
        group.setLocale(param.getLocale());
        group.setName(param.getName());
        group.setSort(param.getSort());
        groupMapper.insertSelective(group);
        return group.getId();
    }

    @Override
    public int updateGroup(Long id, SysTempletGroupParam param) {
        SysTempletGroup existing = groupMapper.selectByPrimaryKey(id);
        if (existing == null) {
            Asserts.fail("系统分组不存在");
        }

        SysTempletGroup update = new SysTempletGroup();
        update.setId(id);
        update.setLocale(param.getLocale());
        update.setName(param.getName());
        update.setSort(param.getSort());

        return groupMapper.updateByPrimaryKeySelective(update);
    }

    @Override
    public int deleteGroup(Long id) {
        // 检查是否存在关联标签
        SysTempletGroup existing = groupMapper.selectByPrimaryKey(id);
        if (existing == null) {
            Asserts.fail("系统分组不存在");
        }

        // 检查是否存在关联标签
        SysTempletExample example = new SysTempletExample();
        example.createCriteria().andGroupIdEqualTo(id);
        if (sysTempletMapper.countByExample(example) > 0) {
            Asserts.fail("请先移除分组下的标签");
        }
        return groupMapper.deleteByPrimaryKey(id);
    }

    @Transactional
    @Override
    public int sortGroups(List<Long> groupIds) {
        for (int i = 0; i < groupIds.size(); i++) {
            SysTempletGroup group = new SysTempletGroup();
            group.setId(groupIds.get(i));
            group.setSort(i + 1);
            groupMapper.updateByPrimaryKeySelective(group);
        }
        return groupIds.size();
    }
}
