package com.vida.xinye.x2.util;

import com.vida.xinye.x2.domain.AuthRequest;
import org.apache.commons.codec.binary.Base64;
import org.springframework.stereotype.Component;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.TreeMap;

@Component
public class AliyunApiSignUtil {
    // 用于测试的静态配置
    private static final String TEST_APP_KEY = "203753385";
    private static final String TEST_APP_SECRET = "jShnNmmvFBJRZ0MqWbXdTdK9ucGRHB09";
    private static final String TEST_APP_HOST = "api.aliyun.com";
    private static final String TEST_METHOD = "POST";
    private static final String TEST_PATH = "/http2test/test";

    /**
     * 生成签名
     *
     * @param request AuthRequest
     * @return 签名结果
     */
    public String generateSign(AuthRequest request) {
        try {
            // 1. 构造签名串
            String stringToSign = buildStringToSign(request);
//            System.out.println("------------------------------------------------");
//            System.out.println("签名串：");
//            System.out.println("——————");
//            System.out.println(stringToSign);
//            System.out.println("————————");
//            System.out.println();
            // 2. 计算签名
            String sign = calculateSignature(request.getAppSecret(), stringToSign);
//            System.out.println("签名结果：");
//            System.out.println(sign);
//            System.out.println("------------------------------------------------");
            return sign;
        } catch (Exception e) {
            throw new RuntimeException("生成签名失败", e);
        }
    }

    public String buildStringToSign(AuthRequest request) {
        Map<String, String> queryParams = request.getQueryParams();
        Map<String, String> headers = request.getHeaders();
        // 打印headers,循环遍历
        for (Map.Entry<String, String> entry : headers.entrySet()) {
            System.out.println(entry.getKey() + ": " + entry.getValue());
        }

        StringBuilder stringToSign = new StringBuilder();

        // 1. HTTPMethod
        stringToSign.append(request.getMethod().toUpperCase()).append("\n");
//        System.out.println("1. HTTPMethod: " + request.getMethod().toUpperCase());

        // 2. Accept
        String accept = headers.getOrDefault("Accept", "text/plain, application/json, application/*+json, */*");
        stringToSign.append(accept).append("\n");
//        System.out.println("2. Accept: " + accept);

        // 3. Content-MD5 (空行)
        stringToSign.append("\n");
//        System.out.println("3. Content-MD5: (空行)");

        // 4. Content-Type
        String contentType = headers.getOrDefault("Content-Type", "application/json");
        stringToSign.append(contentType).append("\n");
//        System.out.println("4. Content-Type: " + contentType);

        // 5. Date (空行)
        stringToSign.append("\n");
//        System.out.println("5. Date: (空行)");

        // 6. Headers
        // 按照文档中的顺序添加签名头
        stringToSign.append("X-Ca-Key:").append(headers.get("X-Ca-Key")).append("\n");
//        System.out.println("6.1 X-Ca-Key: " + headers.get("X-Ca-Key"));

        stringToSign.append("X-Ca-Nonce:").append(headers.get("X-Ca-Nonce")).append("\n");
//        System.out.println("6.2 X-Ca-Nonce: " + headers.get("X-Ca-Nonce"));

        stringToSign.append("X-Ca-Signature-Method:").append(headers.get("X-Ca-Signature-Method")).append("\n");
//        System.out.println("6.3 X-Ca-Signature-Method: " + headers.get("X-Ca-Signature-Method"));

        stringToSign.append("X-Ca-Timestamp:").append(headers.get("X-Ca-Timestamp")).append("\n");
//        System.out.println("6.4 X-Ca-Timestamp: " + headers.get("X-Ca-Timestamp"));

        // 7. PathAndParameters
        StringBuilder pathAndParams = new StringBuilder(request.getPath());
//        System.out.println("7.1 Path: " + request.getPath());

        TreeMap<String, String> allParamsForSignature = new TreeMap<>();

        // Add query parameters
        if (queryParams != null && !queryParams.isEmpty()) {
            allParamsForSignature.putAll(queryParams);
        }

        // Add form parameters if Content-Type is form-urlencoded
        Map<String, String> requestBody = request.getBody();
        if (contentType != null && contentType.startsWith("application/x-www-form-urlencoded") && requestBody != null
                && !requestBody.isEmpty()) {
            // 从Map中解析form参数
            for (Map.Entry<String, String> entry : requestBody.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue() != null ? entry.getValue() : "";
                // Rule: Query和Form存在数组参数时...取第一个Value参与签名计算
                if (!allParamsForSignature.containsKey(key)) {
                    allParamsForSignature.put(key, value);
                }
            }
        }

        if (!allParamsForSignature.isEmpty()) {
            pathAndParams.append("?");
            boolean first = true;
            for (Map.Entry<String, String> entry : allParamsForSignature.entrySet()) {
                if (!first) {
                    pathAndParams.append("&");
                }
                pathAndParams.append(entry.getKey());
                // Rule: 参数的Value为空时只保留Key参与签名，等号不需要再加入签名。
                if (entry.getValue() != null && !entry.getValue().isEmpty()) {
                    pathAndParams.append("=").append(entry.getValue());
                }
                first = false;
            }
        }

        stringToSign.append(pathAndParams.toString());
//        System.out.println("7.2 PathAndParameters: " + pathAndParams.toString());
        return stringToSign.toString();
    }

    private String calculateSignature(String appSecret, String stringToSign) throws Exception {
        Mac hmacSha256 = Mac.getInstance("HmacSHA256");
        byte[] appSecretBytes = appSecret.getBytes(StandardCharsets.UTF_8);
        hmacSha256.init(new SecretKeySpec(appSecretBytes, 0, appSecretBytes.length, "HmacSHA256"));
        byte[] md5Result = hmacSha256.doFinal(stringToSign.getBytes(StandardCharsets.UTF_8));
        return Base64.encodeBase64String(md5Result);
    }

    public static void main(String[] args) {
        try {

            // 1. 准备请求头
            Map<String, String> headers = new LinkedHashMap<>(); // 使用LinkedHashMap保持顺序
            headers.put("accept", "application/json; charset=utf-8");
            headers.put("ca_version", "1");
            headers.put("content-type", "application/x-www-form-urlencoded; charset=utf-8");
            headers.put("x-ca-timestamp", "1525872629832");
            headers.put("date", "Wed, 09 May 2018 13:30:29 GMT+00:00");
            headers.put("x-ca-nonce", "c9f15cbf-f4ac-4a6c-b54d-f51abf4b5b44");
            headers.put("x-ca-key", TEST_APP_KEY);
            headers.put("x-ca-signature-method", "HmacSHA256");
            headers.put("x-ca-signature-headers", "x-ca-timestamp,x-ca-key,x-ca-nonce,x-ca-signature-method");
            headers.put("content-length", "33");

            // 2. 准备查询参数
            Map<String, String> queryParams = new LinkedHashMap<>(); // 使用LinkedHashMap保持顺序
            queryParams.put("param1", "test");

            // 3. 准备请求体
            String body = "username=xiaoming&password=123456789";

            // 4. 创建工具类实例
            AliyunApiSignUtil signUtil = new AliyunApiSignUtil();

            // // 5. 计算Content-MD5
            // String contentMD5 = signUtil.calculateContentMD5(body);
            // headers.put("content-md5", contentMD5);

            // 6. 生成签名串
            AuthRequest request = new AuthRequest();
            request.setAppKey(TEST_APP_KEY);
            request.setAppSecret(TEST_APP_SECRET);
            request.setMethod(TEST_METHOD);
            request.setPath(TEST_PATH);
            request.setHost(TEST_APP_HOST);
            request.setHeaders(headers);
            request.setQueryParams(queryParams);

            String stringToSign = signUtil.buildStringToSign(request);
            System.out.println("签名串：");
            System.out.println("——————");
            System.out.println(stringToSign);
            System.out.println("————————");
            System.out.println();

            // 7. 生成签名
            String sign = signUtil.generateSign(request);
            System.out.println("签名结果：");
            System.out.println(sign);
            System.out.println();

            // 8. 构造完整的请求头
            headers.put("x-ca-signature", sign);

            // 9. 打印原始HTTP请求信息
            System.out.println("原始HTTP请求信息：");
            System.out.println("——————");
            // 打印请求行
            StringBuilder requestLine = new StringBuilder();
            requestLine.append(TEST_METHOD).append(" ").append(TEST_PATH);
            if (!queryParams.isEmpty()) {
                requestLine.append("?");
                boolean firstParam = true;
                for (Map.Entry<String, String> entry : queryParams.entrySet()) {
                    if (!firstParam) {
                        requestLine.append("&");
                    }
                    requestLine.append(entry.getKey()).append("=").append(entry.getValue());
                    firstParam = false;
                }
            }
            requestLine.append(" HTTP/1.1");
            System.out.println(requestLine.toString());

            // 打印请求头
            System.out.println("host:api.aliyun.com");
            // 按照文档中的顺序打印请求头
            System.out.println("accept:" + headers.get("accept"));
            System.out.println("ca_version:" + headers.get("ca_version"));
            System.out.println("content-type:" + headers.get("content-type"));
            System.out.println("x-ca-timestamp:" + headers.get("x-ca-timestamp"));
            System.out.println("date:" + headers.get("date"));
            System.out.println("user-agent:" + headers.get("user-agent"));
            System.out.println("x-ca-nonce:" + headers.get("x-ca-nonce"));
            System.out.println("x-ca-key:" + headers.get("x-ca-key"));
            System.out.println("x-ca-signature-method:" + headers.get("x-ca-signature-method"));
            System.out.println("x-ca-signature-headers:" + headers.get("x-ca-signature-headers"));
            System.out.println("x-ca-signature:" + headers.get("x-ca-signature"));
            System.out.println("content-length:" + headers.get("content-length"));

            // 打印请求体
            System.out.println();
            System.out.println(body);
            System.out.println("————————");
            System.out.println();

            // 10. 打印完整的请求信息
            System.out.println("完整的请求信息：");
            System.out.println("Method: " + TEST_METHOD);
            System.out.println("Path: " + TEST_PATH);
            System.out.println("Headers:");
            headers.forEach((key, value) -> System.out.println(key + ": " + value));
            System.out.println("Query Parameters:");
            queryParams.forEach((key, value) -> System.out.println(key + "=" + value));
            System.out.println("Body: " + body);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}