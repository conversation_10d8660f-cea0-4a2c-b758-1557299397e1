package com.vida.xinye.x2.util;

import java.util.Date;

public class TimeRangeUtil {
    public enum TimeRange {
        THREE_DAYS(3 * 24 * 60 * 60 * 1000L),
        ONE_WEEK(7 * 24 * 60 * 60 * 1000L),
        ONE_MONTH(30 * 24 * 60 * 60 * 1000L);

        private final long milliseconds;

        TimeRange(long milliseconds) {
            this.milliseconds = milliseconds;
        }

        public long getMilliseconds() {
            return milliseconds;
        }
    }

    public static Date getThresholdDate(String range) {
        try {
            TimeRange timeRange = TimeRange.valueOf(range);
            return new Date(System.currentTimeMillis() - timeRange.getMilliseconds());
        } catch (IllegalArgumentException e) {
            return null;
        }
    }
}