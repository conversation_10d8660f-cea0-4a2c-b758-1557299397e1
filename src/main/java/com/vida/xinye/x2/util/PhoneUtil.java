package com.vida.xinye.x2.util;

import cn.hutool.core.util.StrUtil;
import java.util.regex.Pattern;

/**
 * 手机号验证工具类
 *
 * <AUTHOR>
 * @date 2024/11/5
 */
public class PhoneUtil {

  /**
   * 中国大陆手机号正则表达式
   * 支持13x, 14x, 15x, 16x, 17x, 18x, 19x号段
   */
  private static final String CHINA_PHONE_REGEX = "^1[3-9]\\d{9}$";

  private static final Pattern CHINA_PHONE_PATTERN = Pattern.compile(CHINA_PHONE_REGEX);

  /**
   * 验证是否为有效的中国大陆手机号
   *
   * @param phone 手机号字符串
   * @return true: 有效的手机号, false: 无效的手机号
   */
  public static boolean isValidChinesePhone(String phone) {
    if (StrUtil.isBlank(phone)) {
      return false;
    }
    return CHINA_PHONE_PATTERN.matcher(phone.trim()).matches();
  }

  /**
   * 手机号脱敏处理
   * 例如: 13812345678 -> 138****5678
   *
   * @param phone 手机号
   * @return 脱敏后的手机号
   */
  public static String maskPhone(String phone) {
    if (StrUtil.isBlank(phone) || phone.length() != 11) {
      return phone;
    }
    return phone.substring(0, 3) + "****" + phone.substring(7);
  }

  /**
   * 格式化手机号，去除空格、横线等字符
   *
   * @param phone 原始手机号
   * @return 格式化后的手机号
   */
  public static String formatPhone(String phone) {
    if (StrUtil.isBlank(phone)) {
      return phone;
    }
    return phone.replaceAll("[\\s-()]", "");
  }
}