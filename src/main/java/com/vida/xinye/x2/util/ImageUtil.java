package com.vida.xinye.x2.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;

/**
 * 图片处理工具类
 *
 * <AUTHOR>
 * @date 2025/08/14
 */
@Slf4j
public class ImageUtil {

    /**
     * 将MultipartFile转换为Base64编码
     *
     * @param file 图片文件
     * @return Base64编码字符串
     * @throws IOException IO异常
     */
    public static String convertToBase64(MultipartFile file) throws IOException {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("图片文件不能为空");
        }

        byte[] bytes = file.getBytes();
        return Base64.getEncoder().encodeToString(bytes);
    }

    /**
     * 将字节数组转换为Base64编码
     *
     * @param bytes 字节数组
     * @return Base64编码字符串
     */
    public static String convertToBase64(byte[] bytes) {
        if (bytes == null || bytes.length == 0) {
            throw new IllegalArgumentException("字节数组不能为空");
        }

        return Base64.getEncoder().encodeToString(bytes);
    }

    /**
     * 将Base64编码转换为字节数组
     *
     * @param base64 Base64编码字符串
     * @return 字节数组
     */
    public static byte[] convertFromBase64(String base64) {
        if (base64 == null || base64.trim().isEmpty()) {
            throw new IllegalArgumentException("Base64字符串不能为空");
        }

        // 移除可能的前缀
        String cleanBase64 = base64;
        if (base64.startsWith("data:image/")) {
            int commaIndex = base64.indexOf(",");
            if (commaIndex != -1) {
                cleanBase64 = base64.substring(commaIndex + 1);
            }
        }

        return Base64.getDecoder().decode(cleanBase64);
    }

    /**
     * 获取图片尺寸信息
     *
     * @param file 图片文件
     * @return 图片尺寸数组 [width, height]
     * @throws IOException IO异常
     */
    public static int[] getImageDimensions(MultipartFile file) throws IOException {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("图片文件不能为空");
        }

        BufferedImage image = ImageIO.read(file.getInputStream());
        if (image == null) {
            throw new IOException("无法读取图片文件");
        }

        return new int[]{image.getWidth(), image.getHeight()};
    }

    /**
     * 获取图片尺寸信息
     *
     * @param bytes 图片字节数组
     * @return 图片尺寸数组 [width, height]
     * @throws IOException IO异常
     */
    public static int[] getImageDimensions(byte[] bytes) throws IOException {
        if (bytes == null || bytes.length == 0) {
            throw new IllegalArgumentException("图片字节数组不能为空");
        }

        BufferedImage image = ImageIO.read(new java.io.ByteArrayInputStream(bytes));
        if (image == null) {
            throw new IOException("无法读取图片数据");
        }

        return new int[]{image.getWidth(), image.getHeight()};
    }

    /**
     * 压缩图片
     *
     * @param file 原始图片文件
     * @param maxWidth 最大宽度
     * @param maxHeight 最大高度
     * @param quality 压缩质量 (0.0-1.0)
     * @return 压缩后的图片字节数组
     * @throws IOException IO异常
     */
    public static byte[] compressImage(MultipartFile file, int maxWidth, int maxHeight, float quality) throws IOException {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("图片文件不能为空");
        }

        BufferedImage originalImage = ImageIO.read(file.getInputStream());
        if (originalImage == null) {
            throw new IOException("无法读取图片文件");
        }

        // 计算缩放比例
        int originalWidth = originalImage.getWidth();
        int originalHeight = originalImage.getHeight();
        
        double scaleX = (double) maxWidth / originalWidth;
        double scaleY = (double) maxHeight / originalHeight;
        double scale = Math.min(scaleX, scaleY);
        
        // 如果图片已经小于目标尺寸，不需要压缩
        if (scale >= 1.0) {
            return file.getBytes();
        }

        int newWidth = (int) (originalWidth * scale);
        int newHeight = (int) (originalHeight * scale);

        // 创建缩放后的图片
        BufferedImage scaledImage = new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_INT_RGB);
        java.awt.Graphics2D g2d = scaledImage.createGraphics();
        g2d.setRenderingHint(java.awt.RenderingHints.KEY_INTERPOLATION, java.awt.RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        g2d.drawImage(originalImage, 0, 0, newWidth, newHeight, null);
        g2d.dispose();

        // 输出压缩后的图片
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        
        // 获取原始文件格式
        String formatName = getImageFormat(file.getOriginalFilename());
        if ("jpg".equals(formatName) || "jpeg".equals(formatName)) {
            // JPEG格式支持质量设置
            javax.imageio.ImageWriter writer = ImageIO.getImageWritersByFormatName("jpg").next();
            javax.imageio.ImageWriteParam param = writer.getDefaultWriteParam();
            param.setCompressionMode(javax.imageio.ImageWriteParam.MODE_EXPLICIT);
            param.setCompressionQuality(quality);
            
            javax.imageio.stream.ImageOutputStream ios = ImageIO.createImageOutputStream(baos);
            writer.setOutput(ios);
            writer.write(null, new javax.imageio.IIOImage(scaledImage, null, null), param);
            writer.dispose();
            ios.close();
        } else {
            // 其他格式直接输出
            ImageIO.write(scaledImage, formatName, baos);
        }

        return baos.toByteArray();
    }

    /**
     * 获取图片格式
     *
     * @param filename 文件名
     * @return 图片格式
     */
    public static String getImageFormat(String filename) {
        if (filename == null || filename.trim().isEmpty()) {
            return "jpg";
        }

        int lastDotIndex = filename.lastIndexOf(".");
        if (lastDotIndex == -1) {
            return "jpg";
        }

        String extension = filename.substring(lastDotIndex + 1).toLowerCase();
        
        // 标准化格式名称
        switch (extension) {
            case "jpeg":
                return "jpg";
            case "png":
            case "gif":
            case "bmp":
                return extension;
            default:
                return "jpg";
        }
    }

    /**
     * 验证图片格式
     *
     * @param filename 文件名
     * @return 是否为支持的图片格式
     */
    public static boolean isSupportedImageFormat(String filename) {
        String format = getImageFormat(filename);
        return "jpg".equals(format) || "png".equals(format) || 
               "gif".equals(format) || "bmp".equals(format);
    }

    /**
     * 获取图片MIME类型
     *
     * @param filename 文件名
     * @return MIME类型
     */
    public static String getImageMimeType(String filename) {
        String format = getImageFormat(filename);
        
        switch (format) {
            case "jpg":
                return "image/jpeg";
            case "png":
                return "image/png";
            case "gif":
                return "image/gif";
            case "bmp":
                return "image/bmp";
            default:
                return "image/jpeg";
        }
    }

    /**
     * 计算图片文件大小（字节）
     *
     * @param file 图片文件
     * @return 文件大小
     */
    public static long getImageSize(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return 0;
        }
        return file.getSize();
    }

    /**
     * 格式化文件大小
     *
     * @param size 文件大小（字节）
     * @return 格式化后的大小字符串
     */
    public static String formatFileSize(long size) {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.1f KB", size / 1024.0);
        } else if (size < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", size / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", size / (1024.0 * 1024.0 * 1024.0));
        }
    }
}
