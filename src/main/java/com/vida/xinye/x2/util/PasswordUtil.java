package com.vida.xinye.x2.util;

/**
 * 密码工具类
 *
 * <AUTHOR>
 * @date 2024/11/5
 */
public class PasswordUtil {

    public static String maskPassword(String password) {
        if (password == null || password.length() <= 2) {
            return password;
        }
        int length = password.length();
        int start = length / 3;
        int end = length - start;
        StringBuilder sb = new StringBuilder();
        sb.append(password.substring(0, start));
        for (int i = 0; i < end - start; i++) {
            sb.append("*");
        }
        sb.append(password.substring(end));
        return sb.toString();
    }

    public static void main(String[] args) {
        String password = "1234567890";
        String maskedPassword = maskPassword(password);
        System.out.println(maskedPassword);  // 输出：123****890
    }
}
