package com.vida.xinye.x2.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 新浪微博API工具类
 * 处理微博登录相关的API调用
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@Component
@Slf4j
public class WeiboApiUtil {

    @Autowired
    private HttpClientUtil httpClientUtil;

    // 微博API地址
    private static final String WEIBO_ACCESS_TOKEN_URL = "https://api.weibo.com/oauth2/access_token";
    private static final String WEIBO_TOKEN_INFO_URL = "https://api.weibo.com/oauth2/get_token_info";
    private static final String WEIBO_USER_INFO_URL = "https://api.weibo.com/2/users/show.json";
    private static final String WEIBO_AUTHORIZE_URL = "https://api.weibo.com/oauth2/authorize";

    // 配置信息（从配置文件读取）
    @Value("${xinye.login.third-party.weibo.app-key:}")
    private String appKey;

    @Value("${xinye.login.third-party.weibo.app-secret:}")
    private String appSecret;

    @Value("${xinye.login.third-party.weibo.redirect-uri:}")
    private String redirectUri;

    /**
     * 验证微博访问令牌
     *
     * @param accessToken 访问令牌
     * @return 验证结果
     */
    public boolean validateAccessToken(String accessToken) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("access_token", accessToken);

            String url = httpClientUtil.buildUrl(WEIBO_TOKEN_INFO_URL, params);
            String response = httpClientUtil.get(url);

            if (StringUtils.isEmpty(response)) {
                log.warn("微博token验证响应为空");
                return false;
            }

            JSONObject result = JSON.parseObject(response);
            
            // 如果有error字段，说明验证失败
            if (result.containsKey("error") || result.containsKey("error_code")) {
                log.warn("微博token验证失败 - error: {}, error_description: {}", 
                        result.getString("error"), result.getString("error_description"));
                return false;
            }

            // 检查token是否过期
            Long expiresIn = result.getLong("expires_in");
            if (expiresIn != null && expiresIn <= 0) {
                log.warn("微博token已过期");
                return false;
            }

            log.info("微博token验证成功 - uid: {}", result.getString("uid"));
            return true;

        } catch (Exception e) {
            log.error("微博token验证异常", e);
            return false;
        }
    }

    /**
     * 通过授权码获取访问令牌
     *
     * @param code 授权码
     * @return 访问令牌信息
     */
    public JSONObject getAccessTokenByCode(String code) {
        try {
            Map<String, String> params = new HashMap<>();
            params.put("client_id", appKey);
            params.put("client_secret", appSecret);
            params.put("grant_type", "authorization_code");
            params.put("redirect_uri", redirectUri);
            params.put("code", code);

            // 微博需要使用POST请求
            String response = httpClientUtil.postForm(WEIBO_ACCESS_TOKEN_URL, 
                    convertToMultiValueMap(params));

            if (StringUtils.isEmpty(response)) {
                log.warn("微博获取access_token响应为空");
                return null;
            }

            JSONObject result = JSON.parseObject(response);
            if (result.containsKey("error") || result.containsKey("error_code")) {
                log.warn("微博获取access_token失败 - error: {}, error_description: {}", 
                        result.getString("error"), result.getString("error_description"));
                return null;
            }

            log.info("微博获取access_token成功 - uid: {}", result.getString("uid"));
            return result;

        } catch (Exception e) {
            log.error("微博获取access_token异常 - code: {}", code, e);
            return null;
        }
    }

    /**
     * 获取微博用户信息
     *
     * @param accessToken 访问令牌
     * @param uid         用户ID
     * @return 用户信息
     */
    public JSONObject getUserInfo(String accessToken, String uid) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("access_token", accessToken);
            params.put("uid", uid);

            String url = httpClientUtil.buildUrl(WEIBO_USER_INFO_URL, params);
            String response = httpClientUtil.get(url);

            if (StringUtils.isEmpty(response)) {
                log.warn("微博获取用户信息响应为空");
                return null;
            }

            JSONObject result = JSON.parseObject(response);
            if (result.containsKey("error") || result.containsKey("error_code")) {
                log.warn("微博获取用户信息失败 - error: {}, error_description: {}", 
                        result.getString("error"), result.getString("error_description"));
                return null;
            }

            log.info("微博获取用户信息成功 - uid: {}, screen_name: {}", 
                    uid, result.getString("screen_name"));
            return result;

        } catch (Exception e) {
            log.error("微博获取用户信息异常 - uid: {}", uid, e);
            return null;
        }
    }

    /**
     * 获取访问令牌信息
     *
     * @param accessToken 访问令牌
     * @return 令牌信息
     */
    public JSONObject getTokenInfo(String accessToken) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("access_token", accessToken);

            String url = httpClientUtil.buildUrl(WEIBO_TOKEN_INFO_URL, params);
            String response = httpClientUtil.get(url);

            if (StringUtils.isEmpty(response)) {
                log.warn("微博获取token信息响应为空");
                return null;
            }

            JSONObject result = JSON.parseObject(response);
            if (result.containsKey("error") || result.containsKey("error_code")) {
                log.warn("微博获取token信息失败 - error: {}, error_description: {}", 
                        result.getString("error"), result.getString("error_description"));
                return null;
            }

            return result;

        } catch (Exception e) {
            log.error("微博获取token信息异常", e);
            return null;
        }
    }

    /**
     * 构建微博授权URL
     *
     * @param state 状态参数
     * @return 授权URL
     */
    public String buildAuthUrl(String state) {
        Map<String, Object> params = new HashMap<>();
        params.put("client_id", appKey);
        params.put("response_type", "code");
        params.put("redirect_uri", redirectUri);
        if (!StringUtils.isEmpty(state)) {
            params.put("state", state);
        }

        String url = httpClientUtil.buildUrl(WEIBO_AUTHORIZE_URL, params);
        log.info("构建微博授权URL: {}", url);
        return url;
    }

    /**
     * 检查配置是否完整
     *
     * @return 是否配置完整
     */
    public boolean isConfigured() {
        return !StringUtils.isEmpty(appKey) && !StringUtils.isEmpty(appSecret);
    }

    /**
     * 转换为MultiValueMap（用于表单提交）
     */
    private org.springframework.util.MultiValueMap<String, String> convertToMultiValueMap(Map<String, String> params) {
        org.springframework.util.LinkedMultiValueMap<String, String> multiValueMap = 
                new org.springframework.util.LinkedMultiValueMap<>();
        params.forEach(multiValueMap::add);
        return multiValueMap;
    }
}
