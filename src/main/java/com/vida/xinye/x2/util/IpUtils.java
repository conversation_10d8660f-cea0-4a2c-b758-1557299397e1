package com.vida.xinye.x2.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;

/**
 * @Description: IP工具类
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/06/03 16:20
 */
@Slf4j
public class IpUtils {

  private static final String UNKNOWN = "unknown";
  private static final String LOCALHOST_IPV4 = "127.0.0.1";
  private static final String LOCALHOST_IPV6 = "0:0:0:0:0:0:0:1";

  /**
   * 获取客户端真实IP地址
   * 
   * @param request HTTP请求
   * @return 客户端IP地址
   */
  public static String getIpAddr(HttpServletRequest request) {
    if (request == null) {
      return UNKNOWN;
    }

    String ip = null;
    try {
      // 1. X-Forwarded-For：经过代理时记录的原始IP
      ip = request.getHeader("X-Forwarded-For");
      if (isInvalidIp(ip)) {
        // 2. Proxy-Client-IP：经过Apache反向代理时的IP
        ip = request.getHeader("Proxy-Client-IP");
      }
      if (isInvalidIp(ip)) {
        // 3. WL-Proxy-Client-IP：经过WebLogic代理时的IP
        ip = request.getHeader("WL-Proxy-Client-IP");
      }
      if (isInvalidIp(ip)) {
        // 4. HTTP_CLIENT_IP：经过代理时的IP
        ip = request.getHeader("HTTP_CLIENT_IP");
      }
      if (isInvalidIp(ip)) {
        // 5. HTTP_X_FORWARDED_FOR：经过代理时的IP
        ip = request.getHeader("HTTP_X_FORWARDED_FOR");
      }
      if (isInvalidIp(ip)) {
        // 6. X-Real-IP：经过Nginx代理时的真实IP
        ip = request.getHeader("X-Real-IP");
      }
      if (isInvalidIp(ip)) {
        // 7. 最后取request的远程地址
        ip = request.getRemoteAddr();
      }
    } catch (Exception e) {
      log.error("获取IP地址异常", e);
    }

    // 处理多个IP的情况（X-Forwarded-For可能包含多个IP）
    if (StringUtils.hasText(ip) && ip.contains(",")) {
      ip = ip.split(",")[0].trim();
    }

    // 处理本地地址
    if (LOCALHOST_IPV6.equals(ip)) {
      ip = LOCALHOST_IPV4;
    }

    return StringUtils.hasText(ip) ? ip : UNKNOWN;
  }

  /**
   * 检查IP是否无效
   * 
   * @param ip IP地址
   * @return true-无效，false-有效
   */
  private static boolean isInvalidIp(String ip) {
    return !StringUtils.hasText(ip) || UNKNOWN.equalsIgnoreCase(ip);
  }

  /**
   * 检查是否为内网IP
   * 
   * @param ip IP地址
   * @return true-内网IP，false-外网IP
   */
  public static boolean isInternalIp(String ip) {
    if (!StringUtils.hasText(ip) || UNKNOWN.equals(ip)) {
      return false;
    }

    try {
      String[] parts = ip.split("\\.");
      if (parts.length != 4) {
        return false;
      }

      int first = Integer.parseInt(parts[0]);
      int second = Integer.parseInt(parts[1]);

      // 127.x.x.x - 本地回环地址
      if (first == 127) {
        return true;
      }
      // 10.x.x.x - A类私有地址
      if (first == 10) {
        return true;
      }
      // 172.16.x.x - 172.31.x.x - B类私有地址
      if (first == 172 && second >= 16 && second <= 31) {
        return true;
      }
      // 192.168.x.x - C类私有地址
      if (first == 192 && second == 168) {
        return true;
      }
    } catch (Exception e) {
      log.error("检查内网IP异常: {}", ip, e);
    }

    return false;
  }
}