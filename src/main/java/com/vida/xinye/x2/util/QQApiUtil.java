package com.vida.xinye.x2.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * QQ API工具类
 * 处理QQ登录相关的API调用
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@Component
@Slf4j
public class QQApiUtil {

    @Autowired
    private HttpClientUtil httpClientUtil;

    // QQ API地址
    private static final String QQ_ACCESS_TOKEN_URL = "https://graph.qq.com/oauth2.0/token";
    private static final String QQ_OPENID_URL = "https://graph.qq.com/oauth2.0/me";
    private static final String QQ_USER_INFO_URL = "https://graph.qq.com/user/get_user_info";
    private static final String QQ_VALIDATE_TOKEN_URL = "https://openmobile.qq.com/user/get_simple_userinfo";

    // 配置信息（从配置文件读取）
    @Value("${xinye.login.third-party.qq.android.app-id:}")
    private String androidAppId;

    @Value("${xinye.login.third-party.qq.android.app-key:}")
    private String androidAppKey;

    @Value("${xinye.login.third-party.qq.ios.app-id:}")
    private String iosAppId;

    @Value("${xinye.login.third-party.qq.ios.app-key:}")
    private String iosAppKey;

    @Value("${xinye.login.third-party.qq.web.app-id:}")
    private String webAppId;

    @Value("${xinye.login.third-party.qq.web.app-secret:}")
    private String webAppSecret;

    @Value("${xinye.login.third-party.qq.redirect-uri:}")
    private String redirectUri;

    /**
     * 验证QQ访问令牌
     *
     * @param accessToken 访问令牌
     * @param openId      用户openId
     * @param appId       应用ID
     * @return 验证结果
     */
    public boolean validateAccessToken(String accessToken, String openId, String appId) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("access_token", accessToken);
            params.put("openid", openId);
            params.put("oauth_consumer_key", appId);

            String url = httpClientUtil.buildUrl(QQ_VALIDATE_TOKEN_URL, params);
            String response = httpClientUtil.get(url);

            if (StringUtils.isEmpty(response)) {
                log.warn("QQ token验证响应为空");
                return false;
            }

            JSONObject result = JSON.parseObject(response);
            int ret = result.getIntValue("ret");

            if (ret == 0) {
                log.info("QQ token验证成功 - openId: {}", openId);
                return true;
            } else {
                log.warn("QQ token验证失败 - openId: {}, ret: {}, msg: {}", 
                        openId, ret, result.getString("msg"));
                return false;
            }

        } catch (Exception e) {
            log.error("QQ token验证异常 - openId: {}", openId, e);
            return false;
        }
    }

    /**
     * 通过授权码获取访问令牌
     *
     * @param code     授权码
     * @param platform 平台（android/ios/web）
     * @return 访问令牌信息
     */
    public JSONObject getAccessTokenByCode(String code, String platform) {
        try {
            String appId, appKey;
            if ("android".equals(platform)) {
                appId = androidAppId;
                appKey = androidAppKey;
            } else if ("ios".equals(platform)) {
                appId = iosAppId;
                appKey = iosAppKey;
            } else if ("web".equals(platform)) {
                appId = webAppId;
                appKey = webAppSecret;
            } else {
                appId = androidAppId;  // 默认使用Android配置
                appKey = androidAppKey;
            }

            Map<String, Object> params = new HashMap<>();
            params.put("grant_type", "authorization_code");
            params.put("client_id", appId);
            params.put("client_secret", appKey);
            params.put("code", code);
            params.put("redirect_uri", redirectUri);

            String url = httpClientUtil.buildUrl(QQ_ACCESS_TOKEN_URL, params);
            String response = httpClientUtil.get(url);

            if (StringUtils.isEmpty(response)) {
                log.warn("QQ获取access_token响应为空");
                return null;
            }

            // QQ返回的是URL参数格式，需要解析
            JSONObject result = parseQQResponse(response);
            if (result.containsKey("error")) {
                log.warn("QQ获取access_token失败 - error: {}, error_description: {}", 
                        result.getString("error"), result.getString("error_description"));
                return null;
            }

            log.info("QQ获取access_token成功");
            return result;

        } catch (Exception e) {
            log.error("QQ获取access_token异常 - code: {}", code, e);
            return null;
        }
    }

    /**
     * 获取QQ用户OpenID
     *
     * @param accessToken 访问令牌
     * @return OpenID信息
     */
    public JSONObject getOpenId(String accessToken) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("access_token", accessToken);

            String url = httpClientUtil.buildUrl(QQ_OPENID_URL, params);
            String response = httpClientUtil.get(url);

            if (StringUtils.isEmpty(response)) {
                log.warn("QQ获取openId响应为空");
                return null;
            }

            // QQ返回的是JSONP格式，需要提取JSON部分
            String jsonStr = extractJsonFromJsonp(response);
            if (StringUtils.isEmpty(jsonStr)) {
                log.warn("QQ获取openId响应格式错误: {}", response);
                return null;
            }

            JSONObject result = JSON.parseObject(jsonStr);
            if (result.containsKey("error")) {
                log.warn("QQ获取openId失败 - error: {}, error_description: {}", 
                        result.getString("error"), result.getString("error_description"));
                return null;
            }

            log.info("QQ获取openId成功 - openId: {}", result.getString("openid"));
            return result;

        } catch (Exception e) {
            log.error("QQ获取openId异常", e);
            return null;
        }
    }

    /**
     * 获取QQ用户信息
     *
     * @param accessToken 访问令牌
     * @param openId      用户openId
     * @param appId       应用ID
     * @return 用户信息
     */
    public JSONObject getUserInfo(String accessToken, String openId, String appId) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("access_token", accessToken);
            params.put("oauth_consumer_key", appId);
            params.put("openid", openId);

            String url = httpClientUtil.buildUrl(QQ_USER_INFO_URL, params);
            String response = httpClientUtil.get(url);

            if (StringUtils.isEmpty(response)) {
                log.warn("QQ获取用户信息响应为空");
                return null;
            }

            JSONObject result = JSON.parseObject(response);
            int ret = result.getIntValue("ret");

            if (ret == 0) {
                log.info("QQ获取用户信息成功 - openId: {}, nickname: {}", 
                        openId, result.getString("nickname"));
                return result;
            } else {
                log.warn("QQ获取用户信息失败 - openId: {}, ret: {}, msg: {}", 
                        openId, ret, result.getString("msg"));
                return null;
            }

        } catch (Exception e) {
            log.error("QQ获取用户信息异常 - openId: {}", openId, e);
            return null;
        }
    }

    /**
     * 构建QQ授权URL
     *
     * @param state    状态参数
     * @param platform 平台（android/ios/web）
     * @return 授权URL
     */
    public String buildAuthUrl(String state, String platform) {
        String appId = getAppId(platform);

        Map<String, Object> params = new HashMap<>();
        params.put("response_type", "code");
        params.put("client_id", appId);
        params.put("redirect_uri", redirectUri);
        params.put("scope", "get_user_info");
        if (!StringUtils.isEmpty(state)) {
            params.put("state", state);
        }

        String baseUrl = "https://graph.qq.com/oauth2.0/authorize";
        String url = httpClientUtil.buildUrl(baseUrl, params);
        
        log.info("构建QQ授权URL: {}", url);
        return url;
    }

    /**
     * 解析QQ返回的URL参数格式响应
     *
     * @param response 响应内容
     * @return JSON对象
     */
    private JSONObject parseQQResponse(String response) {
        JSONObject result = new JSONObject();
        
        if (response.contains("&")) {
            // URL参数格式：access_token=xxx&expires_in=xxx
            String[] pairs = response.split("&");
            for (String pair : pairs) {
                String[] keyValue = pair.split("=");
                if (keyValue.length == 2) {
                    result.put(keyValue[0], keyValue[1]);
                }
            }
        } else if (response.contains("=")) {
            // 单个参数：access_token=xxx
            String[] keyValue = response.split("=");
            if (keyValue.length == 2) {
                result.put(keyValue[0], keyValue[1]);
            }
        }
        
        return result;
    }

    /**
     * 从JSONP响应中提取JSON部分
     *
     * @param jsonp JSONP响应
     * @return JSON字符串
     */
    private String extractJsonFromJsonp(String jsonp) {
        if (StringUtils.isEmpty(jsonp)) {
            return null;
        }
        
        // QQ返回格式：callback( {"client_id":"xxx","openid":"xxx"} );
        int start = jsonp.indexOf("(");
        int end = jsonp.lastIndexOf(")");
        
        if (start > 0 && end > start) {
            return jsonp.substring(start + 1, end).trim();
        }
        
        return jsonp;
    }

    /**
     * 检查配置是否完整
     *
     * @return 是否配置完整
     */
    public boolean isConfigured() {
        return (!StringUtils.isEmpty(androidAppId) && !StringUtils.isEmpty(androidAppKey)) ||
               (!StringUtils.isEmpty(iosAppId) && !StringUtils.isEmpty(iosAppKey));
    }

    /**
     * 根据平台获取AppId
     *
     * @param platform 平台
     * @return AppId
     */
    public String getAppId(String platform) {
        if ("android".equals(platform)) {
            return androidAppId;
        } else if ("ios".equals(platform)) {
            return iosAppId;
        } else if ("web".equals(platform)) {
            return webAppId;
        } else {
            return androidAppId;  // 默认返回Android配置
        }
    }
}
