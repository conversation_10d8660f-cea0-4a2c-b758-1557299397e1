package com.vida.xinye.x2.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

/**
 * HTTP客户端工具类
 * 用于第三方API调用
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@Component
@Slf4j
public class HttpClientUtil {

    private final RestTemplate restTemplate;

    public HttpClientUtil() {
        this.restTemplate = new RestTemplate();
    }

    /**
     * GET请求
     *
     * @param url 请求URL
     * @return 响应内容
     */
    public String get(String url) {
        return get(url, null);
    }

    /**
     * GET请求（带请求头）
     *
     * @param url     请求URL
     * @param headers 请求头
     * @return 响应内容
     */
    public String get(String url, Map<String, String> headers) {
        try {
            log.debug("发送GET请求: {}", url);

            HttpHeaders httpHeaders = new HttpHeaders();
            if (headers != null) {
                headers.forEach(httpHeaders::set);
            }

            HttpEntity<String> entity = new HttpEntity<>(httpHeaders);
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);

            String result = response.getBody();
            log.debug("GET请求响应: {}", result);
            return result;

        } catch (Exception e) {
            log.error("GET请求失败: {}", url, e);
            throw new RuntimeException("HTTP GET请求失败: " + e.getMessage(), e);
        }
    }

    /**
     * POST请求（JSON格式）
     *
     * @param url  请求URL
     * @param body 请求体
     * @return 响应内容
     */
    public String postJson(String url, String body) {
        return postJson(url, body, null);
    }

    /**
     * POST请求（JSON格式，带请求头）
     *
     * @param url     请求URL
     * @param body    请求体
     * @param headers 请求头
     * @return 响应内容
     */
    public String postJson(String url, String body, Map<String, String> headers) {
        try {
            log.debug("发送POST JSON请求: {}, body: {}", url, body);

            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            if (headers != null) {
                headers.forEach(httpHeaders::set);
            }

            HttpEntity<String> entity = new HttpEntity<>(body, httpHeaders);
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, entity, String.class);

            String result = response.getBody();
            log.debug("POST JSON请求响应: {}", result);
            return result;

        } catch (Exception e) {
            log.error("POST JSON请求失败: {}", url, e);
            throw new RuntimeException("HTTP POST JSON请求失败: " + e.getMessage(), e);
        }
    }

    /**
     * POST请求（表单格式）
     *
     * @param url  请求URL
     * @param form 表单数据
     * @return 响应内容
     */
    public String postForm(String url, MultiValueMap<String, String> form) {
        return postForm(url, form, null);
    }

    /**
     * POST请求（表单格式，带请求头）
     *
     * @param url     请求URL
     * @param form    表单数据
     * @param headers 请求头
     * @return 响应内容
     */
    public String postForm(String url, MultiValueMap<String, String> form, Map<String, String> headers) {
        try {
            log.debug("发送POST FORM请求: {}, form: {}", url, form);

            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            if (headers != null) {
                headers.forEach(httpHeaders::set);
            }

            HttpEntity<MultiValueMap<String, String>> entity = new HttpEntity<>(form, httpHeaders);
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, entity, String.class);

            String result = response.getBody();
            log.debug("POST FORM请求响应: {}", result);
            return result;

        } catch (Exception e) {
            log.error("POST FORM请求失败: {}", url, e);
            throw new RuntimeException("HTTP POST FORM请求失败: " + e.getMessage(), e);
        }
    }

    /**
     * PUT请求（JSON格式）
     *
     * @param url  请求URL
     * @param body 请求体
     * @return 响应内容
     */
    public String putJson(String url, String body) {
        return putJson(url, body, null);
    }

    /**
     * PUT请求（JSON格式，带请求头）
     *
     * @param url     请求URL
     * @param body    请求体
     * @param headers 请求头
     * @return 响应内容
     */
    public String putJson(String url, String body, Map<String, String> headers) {
        try {
            log.debug("发送PUT JSON请求: {}, body: {}", url, body);

            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            if (headers != null) {
                headers.forEach(httpHeaders::set);
            }

            HttpEntity<String> entity = new HttpEntity<>(body, httpHeaders);
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.PUT, entity, String.class);

            String result = response.getBody();
            log.debug("PUT JSON请求响应: {}", result);
            return result;

        } catch (Exception e) {
            log.error("PUT JSON请求失败: {}", url, e);
            throw new RuntimeException("HTTP PUT JSON请求失败: " + e.getMessage(), e);
        }
    }

    /**
     * DELETE请求
     *
     * @param url 请求URL
     * @return 响应内容
     */
    public String delete(String url) {
        return delete(url, null);
    }

    /**
     * DELETE请求（带请求头）
     *
     * @param url     请求URL
     * @param headers 请求头
     * @return 响应内容
     */
    public String delete(String url, Map<String, String> headers) {
        try {
            log.debug("发送DELETE请求: {}", url);

            HttpHeaders httpHeaders = new HttpHeaders();
            if (headers != null) {
                headers.forEach(httpHeaders::set);
            }

            HttpEntity<String> entity = new HttpEntity<>(httpHeaders);
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.DELETE, entity, String.class);

            String result = response.getBody();
            log.debug("DELETE请求响应: {}", result);
            return result;

        } catch (Exception e) {
            log.error("DELETE请求失败: {}", url, e);
            throw new RuntimeException("HTTP DELETE请求失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建URL参数
     *
     * @param baseUrl 基础URL
     * @param params  参数Map
     * @return 完整URL
     */
    public String buildUrl(String baseUrl, Map<String, Object> params) {
        if (params == null || params.isEmpty()) {
            return baseUrl;
        }

        StringBuilder url = new StringBuilder(baseUrl);
        if (!baseUrl.contains("?")) {
            url.append("?");
        } else {
            url.append("&");
        }

        params.forEach((key, value) -> {
            if (value != null) {
                url.append(key).append("=").append(value).append("&");
            }
        });

        // 移除最后一个&
        if (url.charAt(url.length() - 1) == '&') {
            url.deleteCharAt(url.length() - 1);
        }

        return url.toString();
    }
}
