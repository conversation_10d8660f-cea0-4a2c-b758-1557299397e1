package com.vida.xinye.x2.util;


/**
 * 文件工具类
 *
 * <AUTHOR>
 * @date 2024/1/11
 */
public class XinYeFileUtil {

    /**
     * getFileNameWithoutSuffix
     *
     * @param fileOriginName
     * @return 文件名称（不带文件后缀）
     */
    public static String getFileNameWithoutSuffix(String fileOriginName) {
        // 检查文件名称是否为空或null
        if (fileOriginName == null || fileOriginName.isEmpty()) {
            return fileOriginName;
        }
        // 查找文件名称中最后一个点的位置，通常用于分隔文件名和扩展名
        int index = fileOriginName.lastIndexOf(".");
        // 如果找到了点，并且位置在字符串的开始之后，说明有扩展名
        if (index > 0) {
            // 截取字符串，不包含最后一个点及其后面的部分
            return fileOriginName.substring(0, index);
        }
        // 如果没有找到点，或者点是字符串的第一个字符（不合法的文件名），则返回原始文件名
        return fileOriginName;
    }

    /**
     * getFileExtension
     *
     * @param fileOriginName 文件原始名称（包含文件后缀）
     * @return 文件后缀
     */
    public static String getFileExtension(String fileOriginName) {
        // 检查文件名称是否为空或null
        if (fileOriginName == null || fileOriginName.isEmpty()) {
            return fileOriginName;
        }
        // 查找文件名称中最后一个点的位置，通常用于分隔文件名和扩展名
        int index = fileOriginName.lastIndexOf(".");
        // 如果找到了点，并且位置在字符串的开始之后，说明有扩展名
        if (index > 0 && index < fileOriginName.length() - 1) {
            // 截取字符串，仅包含最后一个点及其后面的部分
            return fileOriginName.substring(index + 1);
        }
        // 如果没有找到点，或者点是字符串的最后一个字符（不合法的文件名），则返回空字符串
        return "";
    }

    public static void main(String[] args) {

        String fileOriginName = "product-hub/微信截图_20240715102129.png";

        System.out.println("getFileNameWithoutSuffix: " + getFileNameWithoutSuffix(fileOriginName));
        System.out.println("getFileExtension: " + getFileExtension(fileOriginName));
    }
}
