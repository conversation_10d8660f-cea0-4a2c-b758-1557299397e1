package com.vida.xinye.x2.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.vida.xinye.x2.config.QuestionCuttingConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 有道接口配置验证工具
 *
 * <AUTHOR>
 * @date 2025/08/14
 */
@Slf4j
@Component
public class YoudaoConfigValidator {

    /**
     * 验证有道配置
     */
    public boolean validateYoudaoConfig(QuestionCuttingConfig.YoudaoConfig config) {
        if (config == null) {
            log.error("有道配置为空");
            return false;
        }

        boolean isValid = true;

        // 检查基本配置
        if (StrUtil.isBlank(config.getUrl())) {
            log.error("有道接口URL未配置");
            isValid = false;
        }

        if (StrUtil.isBlank(config.getAppKey())) {
            log.error("有道应用ID(appKey)未配置");
            isValid = false;
        }

        if (StrUtil.isBlank(config.getAppSecret())) {
            log.error("有道应用密钥(appSecret)未配置");
            isValid = false;
        }

        if (config.getTimeout() <= 0) {
            log.error("有道接口超时时间配置无效: {}", config.getTimeout());
            isValid = false;
        }

        if (config.getRetryCount() < 0) {
            log.error("有道接口重试次数配置无效: {}", config.getRetryCount());
            isValid = false;
        }

        // 检查appKey格式
        if (StrUtil.isNotBlank(config.getAppKey())) {
            if (config.getAppKey().length() < 10) {
                log.warn("有道应用ID长度可能不正确，当前长度: {}", config.getAppKey().length());
            }
        }

        // 检查appSecret格式
        if (StrUtil.isNotBlank(config.getAppSecret())) {
            if (config.getAppSecret().length() < 10) {
                log.warn("有道应用密钥长度可能不正确，当前长度: {}", config.getAppSecret().length());
            }
        }

        if (isValid) {
            log.info("有道配置验证通过 - URL: {}, AppKey: {}***, 超时: {}ms", 
                    config.getUrl(), 
                    config.getAppKey().substring(0, Math.min(6, config.getAppKey().length())),
                    config.getTimeout());
        }

        return isValid;
    }

    /**
     * 生成测试签名
     */
    public String generateTestSign(String appKey, String appSecret) {
        String salt = "1234567890";
        String curtime = String.valueOf(System.currentTimeMillis() / 1000);
        String input = ""; // OCR接口input为空
        
        String signStr = appKey + input + salt + curtime + appSecret;
        String sign = DigestUtil.md5Hex(signStr);
        
        log.info("测试签名生成 - salt: {}, curtime: {}, sign: {}", salt, curtime, sign);
        
        return sign;
    }

    /**
     * 验证签名算法
     */
    public boolean validateSignAlgorithm(String appKey, String appSecret, String salt, String curtime, String expectedSign) {
        String input = ""; // OCR接口input为空
        String signStr = appKey + input + salt + curtime + appSecret;
        String actualSign = DigestUtil.md5Hex(signStr);
        
        boolean isValid = actualSign.equals(expectedSign);
        
        if (isValid) {
            log.info("签名验证通过");
        } else {
            log.error("签名验证失败 - 期望: {}, 实际: {}", expectedSign, actualSign);
            log.error("签名字符串: {}", signStr);
        }
        
        return isValid;
    }

    /**
     * 生成调试信息
     */
    public void printDebugInfo(QuestionCuttingConfig.YoudaoConfig config) {
        if (config == null) {
            log.info("=== 有道配置调试信息 ===");
            log.info("配置对象: null");
            return;
        }

        log.info("=== 有道配置调试信息 ===");
        log.info("URL: {}", config.getUrl());
        log.info("AppKey: {}***", StrUtil.isNotBlank(config.getAppKey()) ? 
                config.getAppKey().substring(0, Math.min(6, config.getAppKey().length())) : "未配置");
        log.info("AppSecret: {}***", StrUtil.isNotBlank(config.getAppSecret()) ? 
                config.getAppSecret().substring(0, Math.min(6, config.getAppSecret().length())) : "未配置");
        log.info("超时时间: {}ms", config.getTimeout());
        log.info("重试次数: {}", config.getRetryCount());
        log.info("是否启用: {}", config.isEnabled());
        
        // 生成测试签名
        if (StrUtil.isNotBlank(config.getAppKey()) && StrUtil.isNotBlank(config.getAppSecret())) {
            generateTestSign(config.getAppKey(), config.getAppSecret());
        }
        
        log.info("=== 调试信息结束 ===");
    }

    /**
     * 检查常见配置问题
     */
    public void checkCommonIssues(QuestionCuttingConfig.YoudaoConfig config) {
        if (config == null) {
            log.warn("有道配置为空，请检查application.yml中的question.cutting.youdao配置");
            return;
        }

        log.info("=== 有道配置问题检查 ===");

        // 检查URL
        if (StrUtil.isBlank(config.getUrl())) {
            log.warn("问题1: URL未配置，应该设置为: https://openapi.youdao.com/ocrapi");
        } else if (!config.getUrl().contains("openapi.youdao.com")) {
            log.warn("问题1: URL可能不正确，标准URL为: https://openapi.youdao.com/ocrapi");
        }

        // 检查AppKey
        if (StrUtil.isBlank(config.getAppKey())) {
            log.warn("问题2: AppKey未配置，请在有道智云控制台获取应用ID");
        } else if (config.getAppKey().contains("your_") || config.getAppKey().contains("test")) {
            log.warn("问题2: AppKey看起来是示例值，请使用真实的应用ID");
        }

        // 检查AppSecret
        if (StrUtil.isBlank(config.getAppSecret())) {
            log.warn("问题3: AppSecret未配置，请在有道智云控制台获取应用密钥");
        } else if (config.getAppSecret().contains("your_") || config.getAppSecret().contains("test")) {
            log.warn("问题3: AppSecret看起来是示例值，请使用真实的应用密钥");
        }

        // 检查启用状态
        if (!config.isEnabled()) {
            log.warn("问题4: 有道接口已禁用，请设置enabled: true");
        }

        // 检查超时时间
        if (config.getTimeout() < 10000) {
            log.warn("问题5: 超时时间可能过短({} ms)，建议设置为30000ms以上", config.getTimeout());
        }

        log.info("=== 问题检查结束 ===");
    }

    /**
     * 提供解决方案建议
     */
    public void provideSolutions() {
        log.info("=== 有道接口101错误解决方案 ===");
        log.info("1. 检查环境变量:");
        log.info("   export YOUDAO_APP_KEY=\"your_actual_app_key\"");
        log.info("   export YOUDAO_APP_SECRET=\"your_actual_app_secret\"");
        log.info("");
        log.info("2. 检查application.yml配置:");
        log.info("   question:");
        log.info("     cutting:");
        log.info("       youdao:");
        log.info("         url: https://openapi.youdao.com/ocrapi");
        log.info("         app-key: ${YOUDAO_APP_KEY:your_youdao_app_key}");
        log.info("         app-secret: ${YOUDAO_APP_SECRET:your_youdao_app_secret}");
        log.info("         enabled: true");
        log.info("");
        log.info("3. 登录有道智云控制台检查:");
        log.info("   - 应用状态是否正常");
        log.info("   - 余额是否充足");
        log.info("   - OCR服务是否已开通");
        log.info("   - IP白名单是否正确配置");
        log.info("");
        log.info("4. 如果问题仍然存在，请联系有道客服: 400-099-9988");
        log.info("=== 解决方案结束 ===");
    }
}
