package com.vida.xinye.x2.util;

import java.net.*;
import java.nio.charset.StandardCharsets;
/**
 * 文件工具类
 *
 * <AUTHOR>
 * @date 2024/1/11
 */
public class XinYeURLUtil {

    /**
     * Encodes the last component of a URL path.
     *
     * @param url The URL to be processed.
     * @return The URL with the last path component encoded.
     */
    public static String encodeLastPathComponent(String url) {
        try {
            // Find the last occurrence of '/'
            int lastSlashIndex = url.lastIndexOf('/');

            // If there is no '/', return the original URL
            if (lastSlashIndex == -1) {
                return url;
            }

            // Split the URL into two parts: before and after the last '/'
            String start = url.substring(0, lastSlashIndex + 1);
            String end = url.substring(lastSlashIndex + 1);

            // Encode the part after the last '/'
            String encodedEnd = URLEncoder.encode(end, StandardCharsets.UTF_8.toString());

            // Reassemble the URL
            return start + encodedEnd;
        } catch (java.io.UnsupportedEncodingException e) {
            // In case of an exception, return the original URL
            return url;
        }
    }

    public static void main(String[] args) {

        String url = "https://img.ycjqb.com/xqboa/xinye-product-hub/product-hub/260~!@#$%^&()_';.,+速度三接口主板.png";
        String encodedUrl = encodeLastPathComponent(url);
        System.out.println("Encoded URL: " + encodedUrl);
    }
}
