package com.vida.xinye.x2.util;

import cn.hutool.core.collection.CollUtil;
import com.xkw.xop.qbmsdk.QuestionParser;
import com.xkw.xop.qbmsdk.Setting;
import com.xkw.xop.qbmsdk.model.stem.Stem;

/**
 * 学科网题干解析工具类
 *
 * <AUTHOR>
 * @date 2024/1/15
 */
public class XkwQbmParser {

    public static String formatStem(String stemStr) {
        Setting setting = new Setting();
        QuestionParser parser = new QuestionParser(setting);
        Stem stem = parser.splitStem(stemStr);
        StringBuffer res = new StringBuffer(stem.getHtml());
        if (stem.getSqs()!=null && CollUtil.isNotEmpty(stem.getSqs())) {
            for (int i = 0; i < stem.getSqs().size(); i++) {
                Stem item = stem.getSqs().get(i);
                res.append(item.getHtml()==null? String.valueOf(i+1)+"、 " : item.getHtml());
                if (item.getOg()!=null && CollUtil.isNotEmpty(item.getOg().getOgOps())) {
                    item.getOg().getOgOps().forEach(items -> {
                        res.append(items.getIndex() + "." + items.getHtml() + "  ");
                    });
                    res.append("<br>");
                }
            }
        }
        if (stem.getOg() != null && CollUtil.isNotEmpty(stem.getOg().getOgOps())) {
            stem.getOg().getOgOps().forEach(item -> {
                res.append(item.getIndex() + "." + item.getHtml() + "<br>");
            });
        }
        return res.toString();
    }

    public static void main(String[] args) {

    }
}
