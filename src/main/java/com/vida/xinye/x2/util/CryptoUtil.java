package com.vida.xinye.x2.util;

import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.security.SecureRandom;
import java.util.Base64;

/**
 * 提供密码加密和验证的工具类。
 *
 * <AUTHOR>
 * @date 2024/11/4
 */
public class CryptoUtil {

    private static final PasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
    private static final SecureRandom secureRandom = new SecureRandom();

    /**
     * 生成一个盐值。
     *
     * @return 生成的盐值
     */
    public static String generateSalt() {
        byte[] salt = new byte[16];
        secureRandom.nextBytes(salt);
        return Base64.getEncoder().encodeToString(salt);
    }

    /**
     * 使用盐值对密码进行哈希。
     *
     * @param salt    盐值
     * @param password 原始密码
     * @return 哈希后的密码
     */
    public static String hashPassword(String salt, String password) {
        return passwordEncoder.encode(password + salt);
    }

    /**
     * 使用盐值验证密码。
     *
     * @param salt        盐值
     * @param rawPassword 原始密码
     * @param encodedPassword 哈希后的密码
     * @return 如果密码匹配，则返回 true，否则返回 false
     */
    public static boolean verifyPassword(String salt, String rawPassword, String encodedPassword) {
        return passwordEncoder.matches(rawPassword + salt, encodedPassword);
    }
}
