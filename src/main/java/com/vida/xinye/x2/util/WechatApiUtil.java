package com.vida.xinye.x2.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 微信API工具类
 * 处理微信登录相关的API调用
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@Component
@Slf4j
public class WechatApiUtil {

    @Autowired
    private HttpClientUtil httpClientUtil;

    // 微信API地址
    private static final String WECHAT_AUTH_URL = "https://api.weixin.qq.com/sns/auth";
    private static final String WECHAT_ACCESS_TOKEN_URL = "https://api.weixin.qq.com/sns/oauth2/access_token";
    private static final String WECHAT_USER_INFO_URL = "https://api.weixin.qq.com/sns/userinfo";
    private static final String WECHAT_REFRESH_TOKEN_URL = "https://api.weixin.qq.com/sns/oauth2/refresh_token";

    // 微信小程序API
    private static final String MINIPROGRAM_JSCODE2SESSION_URL = "https://api.weixin.qq.com/sns/jscode2session";

    // 配置信息（从配置文件读取）
    @Value("${xinye.login.third-party.wechat.app.app-id:}")
    private String appAppId;

    @Value("${xinye.login.third-party.wechat.app.app-secret:}")
    private String appAppSecret;

    @Value("${xinye.login.third-party.wechat.miniprogram.app-id:}")
    private String miniprogramAppId;

    @Value("${xinye.login.third-party.wechat.miniprogram.app-secret:}")
    private String miniprogramAppSecret;

    /**
     * 验证微信访问令牌
     *
     * @param accessToken 访问令牌
     * @param openId      用户openId
     * @return 验证结果
     */
    public boolean validateAccessToken(String accessToken, String openId) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("access_token", accessToken);
            params.put("openid", openId);

            String url = httpClientUtil.buildUrl(WECHAT_AUTH_URL, params);
            String response = httpClientUtil.get(url);

            if (StringUtils.isEmpty(response)) {
                log.warn("微信token验证响应为空");
                return false;
            }

            JSONObject result = JSON.parseObject(response);
            int errcode = result.getIntValue("errcode");

            if (errcode == 0) {
                log.info("微信token验证成功 - openId: {}", openId);
                return true;
            } else {
                log.warn("微信token验证失败 - openId: {}, errcode: {}, errmsg: {}", 
                        openId, errcode, result.getString("errmsg"));
                return false;
            }

        } catch (Exception e) {
            log.error("微信token验证异常 - openId: {}", openId, e);
            return false;
        }
    }

    /**
     * 通过授权码获取访问令牌
     *
     * @param code 授权码
     * @param from 来源（app/web）
     * @return 访问令牌信息
     */
    public JSONObject getAccessTokenByCode(String code, String from) {
        try {
            String appId = "app".equals(from) ? appAppId : appAppId; // 这里可以根据from区分不同的appId
            String appSecret = "app".equals(from) ? appAppSecret : appAppSecret;

            Map<String, Object> params = new HashMap<>();
            params.put("appid", appId);
            params.put("secret", appSecret);
            params.put("code", code);
            params.put("grant_type", "authorization_code");

            String url = httpClientUtil.buildUrl(WECHAT_ACCESS_TOKEN_URL, params);
            String response = httpClientUtil.get(url);

            if (StringUtils.isEmpty(response)) {
                log.warn("微信获取access_token响应为空");
                return null;
            }

            JSONObject result = JSON.parseObject(response);
            if (result.containsKey("errcode")) {
                log.warn("微信获取access_token失败 - errcode: {}, errmsg: {}", 
                        result.getIntValue("errcode"), result.getString("errmsg"));
                return null;
            }

            log.info("微信获取access_token成功 - openId: {}", result.getString("openid"));
            return result;

        } catch (Exception e) {
            log.error("微信获取access_token异常 - code: {}", code, e);
            return null;
        }
    }

    /**
     * 获取微信用户信息
     *
     * @param accessToken 访问令牌
     * @param openId      用户openId
     * @return 用户信息
     */
    public JSONObject getUserInfo(String accessToken, String openId) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("access_token", accessToken);
            params.put("openid", openId);
            params.put("lang", "zh_CN");

            String url = httpClientUtil.buildUrl(WECHAT_USER_INFO_URL, params);
            String response = httpClientUtil.get(url);

            if (StringUtils.isEmpty(response)) {
                log.warn("微信获取用户信息响应为空");
                return null;
            }

            JSONObject result = JSON.parseObject(response);
            if (result.containsKey("errcode")) {
                log.warn("微信获取用户信息失败 - errcode: {}, errmsg: {}", 
                        result.getIntValue("errcode"), result.getString("errmsg"));
                return null;
            }

            log.info("微信获取用户信息成功 - openId: {}, nickname: {}", 
                    openId, result.getString("nickname"));
            return result;

        } catch (Exception e) {
            log.error("微信获取用户信息异常 - openId: {}", openId, e);
            return null;
        }
    }

    /**
     * 刷新访问令牌
     *
     * @param refreshToken 刷新令牌
     * @return 新的访问令牌信息
     */
    public JSONObject refreshAccessToken(String refreshToken) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("appid", appAppId);
            params.put("grant_type", "refresh_token");
            params.put("refresh_token", refreshToken);

            String url = httpClientUtil.buildUrl(WECHAT_REFRESH_TOKEN_URL, params);
            String response = httpClientUtil.get(url);

            if (StringUtils.isEmpty(response)) {
                log.warn("微信刷新token响应为空");
                return null;
            }

            JSONObject result = JSON.parseObject(response);
            if (result.containsKey("errcode")) {
                log.warn("微信刷新token失败 - errcode: {}, errmsg: {}", 
                        result.getIntValue("errcode"), result.getString("errmsg"));
                return null;
            }

            log.info("微信刷新token成功 - openId: {}", result.getString("openid"));
            return result;

        } catch (Exception e) {
            log.error("微信刷新token异常", e);
            return null;
        }
    }

    /**
     * 小程序登录（jscode2session）
     *
     * @param jsCode 小程序登录时获取的code
     * @return 会话信息
     */
    public JSONObject miniprogramLogin(String jsCode) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("appid", miniprogramAppId);
            params.put("secret", miniprogramAppSecret);
            params.put("js_code", jsCode);
            params.put("grant_type", "authorization_code");

            String url = httpClientUtil.buildUrl(MINIPROGRAM_JSCODE2SESSION_URL, params);
            String response = httpClientUtil.get(url);

            if (StringUtils.isEmpty(response)) {
                log.warn("微信小程序登录响应为空");
                return null;
            }

            JSONObject result = JSON.parseObject(response);
            if (result.containsKey("errcode")) {
                log.warn("微信小程序登录失败 - errcode: {}, errmsg: {}", 
                        result.getIntValue("errcode"), result.getString("errmsg"));
                return null;
            }

            log.info("微信小程序登录成功 - openId: {}", result.getString("openid"));
            return result;

        } catch (Exception e) {
            log.error("微信小程序登录异常 - jsCode: {}", jsCode, e);
            return null;
        }
    }

    /**
     * 构建微信授权URL
     *
     * @param redirectUri 回调地址
     * @param state       状态参数
     * @param scope       授权范围
     * @return 授权URL
     */
    public String buildAuthUrl(String redirectUri, String state, String scope) {
        if (StringUtils.isEmpty(scope)) {
            scope = "snsapi_userinfo";
        }

        Map<String, Object> params = new HashMap<>();
        params.put("appid", appAppId);
        params.put("redirect_uri", redirectUri);
        params.put("response_type", "code");
        params.put("scope", scope);
        if (!StringUtils.isEmpty(state)) {
            params.put("state", state);
        }

        String baseUrl = "https://open.weixin.qq.com/connect/oauth2/authorize";
        String url = httpClientUtil.buildUrl(baseUrl, params);
        
        log.info("构建微信授权URL: {}", url);
        return url + "#wechat_redirect";
    }

    /**
     * 检查配置是否完整
     *
     * @return 是否配置完整
     */
    public boolean isConfigured() {
        return !StringUtils.isEmpty(appAppId) && !StringUtils.isEmpty(appAppSecret);
    }
}
