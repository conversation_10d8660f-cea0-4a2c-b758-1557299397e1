package com.vida.xinye.x2.util;

import com.vida.xinye.x2.component.JwtTokenProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;

/**
 * JWT工具类
 * 简化控制器中JWT token的使用
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@Component
public class JwtUtil {

    @Autowired
    private JwtTokenProvider jwtTokenProvider;

//    /**
//     * 从HTTP请求中获取用户ID
//     *
//     * @param request HTTP请求
//     * @return 用户ID，如果解析失败则返回null
//     */
//    public Long getUserIdFromRequest(HttpServletRequest request) {
//        String authorizationHeader = request.getHeader("Authorization");
//        return jwtTokenProvider.extractUserIdFromHeader(authorizationHeader);
//    }
//
//    /**
//     * 从HTTP请求中获取用户名
//     *
//     * @param request HTTP请求
//     * @return 用户名，如果解析失败则返回null
//     */
//    public String getUsernameFromRequest(HttpServletRequest request) {
//        try {
//            String authorizationHeader = request.getHeader("Authorization");
//            String token = jwtTokenProvider.extractTokenFromHeader(authorizationHeader);
//            if (token != null) {
//                return jwtTokenProvider.extractUsername(token);
//            }
//        } catch (Exception e) {
//            // Token解析失败
//        }
//        return null;
//    }
//
//    /**
//     * 验证请求中的Token是否有效
//     *
//     * @param request HTTP请求
//     * @return 是否有效
//     */
//    public boolean validateTokenFromRequest(HttpServletRequest request) {
//        try {
//            String authorizationHeader = request.getHeader("Authorization");
//            String token = jwtTokenProvider.extractTokenFromHeader(authorizationHeader);
//            if (token != null) {
//                String username = jwtTokenProvider.extractUsername(token);
//                return jwtTokenProvider.validateToken(token, username);
//            }
//        } catch (Exception e) {
//            // Token验证失败
//        }
//        return false;
//    }
//
//    /**
//     * 从Token字符串中提取用户ID
//     *
//     * @param token JWT Token
//     * @return 用户ID
//     */
//    public Long getUserIdFromToken(String token) {
//        if (StringUtils.isEmpty(token)) {
//            return null;
//        }
//        try {
//            return jwtTokenProvider.extractUserId(token);
//        } catch (Exception e) {
//            return null;
//        }
//    }
//
//    /**
//     * 从Token字符串中提取用户名
//     *
//     * @param token JWT Token
//     * @return 用户名
//     */
//    public String getUsernameFromToken(String token) {
//        if (StringUtils.isEmpty(token)) {
//            return null;
//        }
//        try {
//            return jwtTokenProvider.extractUsername(token);
//        } catch (Exception e) {
//            return null;
//        }
//    }
//
//    /**
//     * 检查Token是否过期
//     *
//     * @param token JWT Token
//     * @return 是否过期
//     */
//    public boolean isTokenExpired(String token) {
//        if (StringUtils.isEmpty(token)) {
//            return true;
//        }
//        try {
//            return jwtTokenProvider.isTokenExpired(token);
//        } catch (Exception e) {
//            return true;
//        }
//    }
}
