/**
 * @Description: 身份证号码哈希工具类
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/01/16 16:10
 */
package com.vida.xinye.x2.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * 身份证号码哈希工具类
 * 用于对敏感的身份证号码进行哈希处理，保护用户隐私
 */
public class IdCardHashUtil {
  private static final Logger log = LoggerFactory.getLogger(IdCardHashUtil.class);

  private static final String ALGORITHM = "SHA-256";
  private static final String SALT = "xinye_idcard_salt_2025";

  /**
   * 对身份证号码进行SHA256哈希
   *
   * @param idCard 身份证号码
   * @return 哈希值（64位十六进制字符串）
   */
  public static String hashIdCard(String idCard) {
    if (idCard == null || idCard.trim().isEmpty()) {
      throw new IllegalArgumentException("身份证号码不能为空");
    }

    try {
      MessageDigest digest = MessageDigest.getInstance(ALGORITHM);
      // 加盐处理，增强安全性
      String saltedIdCard = idCard.trim().toUpperCase() + SALT;
      byte[] hashBytes = digest.digest(saltedIdCard.getBytes(StandardCharsets.UTF_8));

      // 转换为十六进制字符串
      StringBuilder hexString = new StringBuilder();
      for (byte b : hashBytes) {
        String hex = Integer.toHexString(0xff & b);
        if (hex.length() == 1) {
          hexString.append('0');
        }
        hexString.append(hex);
      }

      return hexString.toString();
    } catch (NoSuchAlgorithmException e) {
      log.error("SHA-256算法不可用", e);
      throw new RuntimeException("哈希算法初始化失败", e);
    }
  }

  /**
   * 验证身份证号码与哈希值是否匹配
   *
   * @param idCard 身份证号码
   * @param hash   哈希值
   * @return 是否匹配
   */
  public static boolean verifyIdCard(String idCard, String hash) {
    if (idCard == null || hash == null) {
      return false;
    }

    try {
      String calculatedHash = hashIdCard(idCard);
      return calculatedHash.equals(hash);
    } catch (Exception e) {
      log.error("验证身份证哈希失败", e);
      return false;
    }
  }
}