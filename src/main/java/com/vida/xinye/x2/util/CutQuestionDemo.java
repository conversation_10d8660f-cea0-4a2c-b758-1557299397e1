package com.vida.xinye.x2.util;

import com.aliyun.log.thirdparty.org.apache.http.HttpEntity;
import com.aliyun.log.thirdparty.org.apache.http.NameValuePair;
import com.aliyun.log.thirdparty.org.apache.http.client.entity.UrlEncodedFormEntity;
import com.aliyun.log.thirdparty.org.apache.http.client.methods.CloseableHttpResponse;
import com.aliyun.log.thirdparty.org.apache.http.client.methods.HttpPost;
import com.aliyun.log.thirdparty.org.apache.http.impl.client.CloseableHttpClient;
import com.aliyun.log.thirdparty.org.apache.http.impl.client.HttpClients;
import com.aliyun.log.thirdparty.org.apache.http.message.BasicNameValuePair;
import com.aliyun.log.thirdparty.org.apache.http.util.EntityUtils;
import org.mybatis.logging.Logger;
import org.mybatis.logging.LoggerFactory;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;

/**
 * 网易有道智云题目识别切分服务api调用demo
 * api接口: https://openapi.youdao.com/cut_question
 */

public class CutQuestionDemo {
    private static Logger logger = LoggerFactory.getLogger(CutQuestionDemo.class);

    private static final String YOUDAO_URL = "https://openapi.youdao.com/cut_question";

    private static final String APP_KEY = "6735066aa7dd797d";

    private static final String APP_SECRET = "wUZ8mxlcfJLiYF9GDKAaml7EPtUstGxa";

    public static void main(String[] args) throws IOException {

        Map<String,String> params = new HashMap<String,String>();
        String q = loadAsBase64("C:\\Users\\<USER>\\Downloads\\题目.jpg");
        String salt = String.valueOf(System.currentTimeMillis());
        String imageType = "1";
        params.put("imageType", imageType);
        params.put("q", q);
        params.put("docType", "json");
        params.put("signType", "v3");
        String curtime = String.valueOf(System.currentTimeMillis() / 1000);
        params.put("curtime", curtime);
        String signStr = APP_KEY + truncate(q) + salt + curtime + APP_SECRET;
        String sign = getDigest(signStr);
        params.put("appKey", APP_KEY);
        params.put("salt", salt);
        params.put("sign", sign);
        String result = requestForHttp(YOUDAO_URL,params);
        /** 处理结果 */
        System.out.println(result);
    }

    public static String requestForHttp(String url,Map<String,String> params) throws IOException {
        String result = "";

        /** 创建HttpClient */
        CloseableHttpClient httpClient = HttpClients.createDefault();

        /** httpPost */
        HttpPost httpPost = new HttpPost(url);
        List<NameValuePair> paramsList = new ArrayList<NameValuePair>();
        Iterator<Map.Entry<String,String>> it = params.entrySet().iterator();
        while(it.hasNext()){
            Map.Entry<String,String> en = it.next();
            String key = en.getKey();
            String value = en.getValue();
            paramsList.add(new BasicNameValuePair(key,value));
        }
        httpPost.setEntity(new UrlEncodedFormEntity(paramsList,"UTF-8"));
        CloseableHttpResponse httpResponse = httpClient.execute(httpPost);
        try{
            HttpEntity httpEntity = httpResponse.getEntity();
            result = EntityUtils.toString(httpEntity,"UTF-8");
            EntityUtils.consume(httpEntity);
        }finally {
            try{
                if(httpResponse!=null){
                    httpResponse.close();
                }
            }catch(IOException e){
//                logger.info("## release resouce error ##" + e);
            }
        }
        return result;
    }

    /**
     * 生成加密字段
     */
    public static String getDigest(String string) {
        if (string == null) {
            return null;
        }
        char hexDigits[] = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'};
        byte[] btInput = string.getBytes(StandardCharsets.UTF_8);
        try {
            MessageDigest mdInst = MessageDigest.getInstance("SHA-256");
            mdInst.update(btInput);
            byte[] md = mdInst.digest();
            int j = md.length;
            char str[] = new char[j * 2];
            int k = 0;
            for (byte byte0 : md) {
                str[k++] = hexDigits[byte0 >>> 4 & 0xf];
                str[k++] = hexDigits[byte0 & 0xf];
            }
            return new String(str);
        } catch (NoSuchAlgorithmException e) {
            return null;
        }
    }

    public static String loadAsBase64(String imgFile)
    {//将图片文件转化为字节数组字符串，并对其进行Base64编码处理

        File file = new File(imgFile);
        if(!file.exists()){
//            logger.error("文件不存在");
            return null;
        }
        InputStream in = null;
        byte[] data = null;
        //读取图片字节数组
        try
        {
            in = new FileInputStream(imgFile);
            data = new byte[in.available()];
            in.read(data);
            in.close();
        }
        catch (IOException e)
        {
            e.printStackTrace();
        }
        //对字节数组Base64编码
        return Base64.getEncoder().encodeToString(data);//返回Base64编码过的字节数组字符串
    }

    public static String truncate(String q) {
        if (q == null) {
            return null;
        }
        int len = q.length();
        String result;
        return len <= 20 ? q : (q.substring(0, 10) + len + q.substring(len - 10, len));
    }
}
