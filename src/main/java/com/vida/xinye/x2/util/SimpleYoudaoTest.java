package com.vida.xinye.x2.util;

import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * 最简化的有道接口测试
 * 参考有道官方demo实现
 *
 * <AUTHOR>
 * @date 2025/08/14
 */
@Slf4j
public class SimpleYoudaoTest {

    /**
     * 最简单的有道OCR测试
     */
    public static void testYoudaoOCR(String appKey, String appSecret) {
        try {
            // 有道OCR接口地址
            String url = "https://openapi.youdao.com/ocrapi";

            // 测试用的1x1像素透明PNG图片的Base64
            String testImage = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==";

            // 生成时间戳和随机数
            String salt = String.valueOf(System.currentTimeMillis());
            String curtime = String.valueOf(System.currentTimeMillis() / 1000);

            // 计算input（根据官方文档规则）
            String input = calculateInput(testImage);

            // 生成签名（根据官方文档：sha256(应用ID+input+salt+curtime+应用密钥)）
            String signStr = appKey + input + salt + curtime + appSecret;
            String sign = DigestUtil.sha256Hex(signStr);
            
            // 构建请求参数（根据官方文档）
            Map<String, Object> params = new HashMap<>();
            params.put("appKey", appKey);
            params.put("salt", salt);
            params.put("curtime", curtime);
            params.put("sign", sign);
            params.put("imageType", "1");      // 图片类型，固定为1
            params.put("docType", "json");     // 响应类型，固定为json
            params.put("signType", "v3");      // 签名类型，固定为v3
            params.put("q", testImage);        // 图片Base64数据
            
            log.info("=== 简化版有道OCR测试 ===");
            log.info("URL: {}", url);
            log.info("AppKey: {}***", appKey.substring(0, Math.min(6, appKey.length())));
            log.info("Salt: {}", salt);
            log.info("Curtime: {}", curtime);
            log.info("Sign: {}", sign);
            log.info("发送请求...");
            
            // 发送POST请求
            HttpResponse response = HttpRequest.post(url)
                    .form(params)
                    .timeout(30000)
                    .execute();
            
            String responseBody = response.body();
            int statusCode = response.getStatus();
            
            log.info("响应状态码: {}", statusCode);
            log.info("响应内容: {}", responseBody);
            
            // 简单的结果判断
            if (responseBody.contains("\"errorCode\":\"0\"")) {
                log.info("✅ 有道接口调用成功！");
            } else if (responseBody.contains("\"errorCode\":\"101\"")) {
                log.error("❌ 有道接口返回101错误：缺少必填参数");
                log.error("请检查：1.appKey是否正确 2.appSecret是否正确 3.有道控制台应用状态");
            } else {
                log.warn("⚠️ 有道接口返回其他错误，请查看响应内容");
            }
            
        } catch (Exception e) {
            log.error("有道接口测试失败", e);
        }
    }
    
    /**
     * 生成curl命令用于手动测试
     */
    /**
     * 计算input值（根据官方文档规则）
     */
    private static String calculateInput(String q) {
        if (q == null || q.isEmpty()) {
            return "";
        }

        int qLength = q.length();

        if (qLength <= 20) {
            // 当q长度小于等于20时，input=q字符串
            return q;
        } else {
            // 当q长度大于20时，input=q前10个字符 + q长度 + q后10个字符
            String prefix = q.substring(0, 10);
            String suffix = q.substring(qLength - 10);
            return prefix + qLength + suffix;
        }
    }

    public static String generateCurlCommand(String appKey, String appSecret) {
        String testImage = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==";
        String salt = String.valueOf(System.currentTimeMillis());
        String curtime = String.valueOf(System.currentTimeMillis() / 1000);
        String input = calculateInput(testImage);
        String signStr = appKey + input + salt + curtime + appSecret;
        String sign = DigestUtil.sha256Hex(signStr);

        StringBuilder curl = new StringBuilder();
        curl.append("curl -X POST \"https://openapi.youdao.com/ocrapi\" \\\n");
        curl.append("  -H \"Content-Type: application/x-www-form-urlencoded\" \\\n");
        curl.append("  -d \"appKey=").append(appKey).append("\" \\\n");
        curl.append("  -d \"salt=").append(salt).append("\" \\\n");
        curl.append("  -d \"curtime=").append(curtime).append("\" \\\n");
        curl.append("  -d \"sign=").append(sign).append("\" \\\n");
        curl.append("  -d \"imageType=1\" \\\n");
        curl.append("  -d \"docType=json\" \\\n");
        curl.append("  -d \"signType=v3\" \\\n");
        curl.append("  -d \"q=").append(testImage).append("\"");

        return curl.toString();
    }
    
    /**
     * 主方法，用于独立测试
     */
    public static void main(String[] args) {
        // 替换为您的真实密钥
        String appKey = "6735066aa7dd797d";
        String appSecret = "wUZ8mxlcfJLiYF9GDKAaml7EPtUstGxa";
        
        System.out.println("开始测试有道OCR接口...");
        testYoudaoOCR(appKey, appSecret);
        
        System.out.println("\n生成的curl命令：");
        System.out.println(generateCurlCommand(appKey, appSecret));
    }
}
