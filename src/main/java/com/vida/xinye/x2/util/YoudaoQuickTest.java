package com.vida.xinye.x2.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.vida.xinye.x2.config.QuestionCuttingConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 有道接口快速测试工具
 *
 * <AUTHOR>
 * @date 2025/08/14
 */
@Slf4j
@Component
public class YoudaoQuickTest {

    @Autowired
    private QuestionCuttingConfig config;

    /**
     * 快速测试有道接口
     */
    public Map<String, Object> quickTest() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            QuestionCuttingConfig.YoudaoConfig youdaoConfig = config.getYoudao();
            
            if (youdaoConfig == null) {
                result.put("success", false);
                result.put("error", "有道配置为空");
                return result;
            }

            if (!youdaoConfig.isEnabled()) {
                result.put("success", false);
                result.put("error", "有道接口已禁用");
                return result;
            }

            String appKey = youdaoConfig.getAppKey();
            String appSecret = youdaoConfig.getAppSecret();
            String url = youdaoConfig.getUrl();

            if (StrUtil.isBlank(appKey) || StrUtil.isBlank(appSecret) || StrUtil.isBlank(url)) {
                result.put("success", false);
                result.put("error", "有道配置不完整");
                return result;
            }

            // 生成测试参数
            String salt = String.valueOf(System.currentTimeMillis());
            String curtime = String.valueOf(System.currentTimeMillis() / 1000);
            String input = ""; // OCR接口input为空
            
            // 生成签名
            String signStr = appKey + input + salt + curtime + appSecret;
            String sign = DigestUtil.md5Hex(signStr);

            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("appKey", appKey);
            params.put("salt", salt);
            params.put("curtime", curtime);
            params.put("sign", sign);
            params.put("type", "1");
            params.put("detectType", "10012");
            params.put("imageType", "1");
            params.put("img", "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==");

            log.info("=== 有道接口快速测试 ===");
            log.info("URL: {}", url);
            log.info("AppKey: {}***", appKey.substring(0, Math.min(8, appKey.length())));
            log.info("Salt: {}", salt);
            log.info("Curtime: {}", curtime);
            log.info("Sign: {}", sign);

            // 发送请求
            HttpResponse response = HttpRequest.post(url)
                    .form(params)
                    .timeout(youdaoConfig.getTimeout())
                    .execute();

            String responseBody = response.body();
            int statusCode = response.getStatus();

            log.info("响应状态码: {}", statusCode);
            log.info("响应内容: {}", responseBody);

            result.put("success", true);
            result.put("statusCode", statusCode);
            result.put("responseBody", responseBody);
            result.put("testParams", createSafeParams(params));

            // 解析响应
            if (responseBody.contains("\"errorCode\":\"0\"")) {
                result.put("youdaoStatus", "SUCCESS");
                result.put("message", "有道接口调用成功");
            } else if (responseBody.contains("\"errorCode\":\"101\"")) {
                result.put("youdaoStatus", "ERROR_101");
                result.put("message", "有道接口返回101错误，可能是服务端问题");
            } else if (responseBody.contains("errorCode")) {
                result.put("youdaoStatus", "ERROR_OTHER");
                result.put("message", "有道接口返回其他错误");
            } else {
                result.put("youdaoStatus", "UNKNOWN");
                result.put("message", "无法解析有道接口响应");
            }

            return result;

        } catch (Exception e) {
            log.error("有道接口测试失败", e);
            result.put("success", false);
            result.put("error", "测试失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 创建安全的参数显示（隐藏敏感信息）
     */
    private Map<String, Object> createSafeParams(Map<String, Object> params) {
        Map<String, Object> safeParams = new HashMap<>();
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            
            if ("appKey".equals(key)) {
                String appKey = (String) value;
                safeParams.put(key, appKey.substring(0, Math.min(8, appKey.length())) + "***");
            } else if ("sign".equals(key)) {
                safeParams.put(key, value);
            } else if ("img".equals(key)) {
                safeParams.put(key, "Base64图片数据(" + ((String) value).length() + "字符)");
            } else {
                safeParams.put(key, value);
            }
        }
        return safeParams;
    }

    /**
     * 生成curl测试命令
     */
    public String generateCurlCommand() {
        try {
            QuestionCuttingConfig.YoudaoConfig youdaoConfig = config.getYoudao();
            
            if (youdaoConfig == null || !youdaoConfig.isEnabled()) {
                return "有道配置无效，无法生成测试命令";
            }

            String appKey = youdaoConfig.getAppKey();
            String appSecret = youdaoConfig.getAppSecret();
            String url = youdaoConfig.getUrl();

            if (StrUtil.isBlank(appKey) || StrUtil.isBlank(appSecret) || StrUtil.isBlank(url)) {
                return "有道配置不完整，无法生成测试命令";
            }

            // 生成测试参数
            String salt = String.valueOf(System.currentTimeMillis());
            String curtime = String.valueOf(System.currentTimeMillis() / 1000);
            String input = "";
            String signStr = appKey + input + salt + curtime + appSecret;
            String sign = DigestUtil.md5Hex(signStr);

            StringBuilder curl = new StringBuilder();
            curl.append("curl -X POST \"").append(url).append("\" \\\n");
            curl.append("  -H \"Content-Type: application/x-www-form-urlencoded\" \\\n");
            curl.append("  -d \"appKey=").append(appKey).append("\" \\\n");
            curl.append("  -d \"salt=").append(salt).append("\" \\\n");
            curl.append("  -d \"curtime=").append(curtime).append("\" \\\n");
            curl.append("  -d \"sign=").append(sign).append("\" \\\n");
            curl.append("  -d \"type=1\" \\\n");
            curl.append("  -d \"detectType=10012\" \\\n");
            curl.append("  -d \"imageType=1\" \\\n");
            curl.append("  -d \"img=iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==\"");

            return curl.toString();

        } catch (Exception e) {
            log.error("生成curl命令失败", e);
            return "生成curl命令失败: " + e.getMessage();
        }
    }

    /**
     * 检查网络连通性
     */
    public Map<String, Object> checkNetworkConnectivity() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            QuestionCuttingConfig.YoudaoConfig youdaoConfig = config.getYoudao();
            String url = youdaoConfig != null ? youdaoConfig.getUrl() : "https://openapi.youdao.com/ocrapi";

            log.info("检查网络连通性: {}", url);

            // 简单的连通性测试
            HttpResponse response = HttpRequest.get(url)
                    .timeout(5000)
                    .execute();

            result.put("success", true);
            result.put("statusCode", response.getStatus());
            result.put("message", "网络连通性正常");

        } catch (Exception e) {
            log.error("网络连通性检查失败", e);
            result.put("success", false);
            result.put("error", "网络连通性检查失败: " + e.getMessage());
            result.put("message", "可能的原因：网络不通、防火墙阻止、DNS解析失败");
        }

        return result;
    }
}
