package com.vida.xinye.x2.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.security.KeyFactory;
import java.security.PublicKey;
import java.util.Base64;

/**
 * Apple ID验证工具类
 * 处理Apple ID登录相关的JWT验证
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@Component
@Slf4j
public class AppleIdUtil {

    @Autowired
    private HttpClientUtil httpClientUtil;

    // Apple公钥获取地址
    private static final String APPLE_KEYS_URL = "https://appleid.apple.com/auth/keys";

    // 配置信息
    @Value("${xinye.login.third-party.apple.client-id:}")
    private String clientId;

    @Value("${xinye.login.third-party.apple.team-id:}")
    private String teamId;

    /**
     * 验证Apple ID JWT Token
     *
     * @param jwtToken JWT Token
     * @return 验证结果
     */
    public boolean verifyJWT(String jwtToken) {
        try {
            if (StringUtils.isEmpty(jwtToken)) {
                log.warn("Apple ID JWT Token为空");
                return false;
            }

            // 解析JWT Header获取kid
            String[] parts = jwtToken.split("\\.");
            if (parts.length != 3) {
                log.warn("Apple ID JWT Token格式错误");
                return false;
            }

            String headerJson = new String(Base64.getUrlDecoder().decode(parts[0]));
            JSONObject header = JSON.parseObject(headerJson);
            String kid = header.getString("kid");

            if (StringUtils.isEmpty(kid)) {
                log.warn("Apple ID JWT Token缺少kid");
                return false;
            }

            // 获取Apple公钥
            PublicKey publicKey = getApplePublicKey(kid);
            if (publicKey == null) {
                log.warn("无法获取Apple公钥");
                return false;
            }

            // 验证JWT
            Claims claims = Jwts.parser()
                    .setSigningKey(publicKey)
                    .parseClaimsJws(jwtToken)
                    .getBody();

            // 验证aud（audience）
            String audience = claims.getAudience();
            if (!clientId.equals(audience)) {
                log.warn("Apple ID JWT audience验证失败: expected={}, actual={}", clientId, audience);
                return false;
            }

            // 验证iss（issuer）
            String issuer = claims.getIssuer();
            if (!"https://appleid.apple.com".equals(issuer)) {
                log.warn("Apple ID JWT issuer验证失败: {}", issuer);
                return false;
            }

            // 验证过期时间
            if (claims.getExpiration().getTime() < System.currentTimeMillis()) {
                log.warn("Apple ID JWT已过期");
                return false;
            }

            log.info("Apple ID JWT验证成功 - sub: {}", claims.getSubject());
            return true;

        } catch (Exception e) {
            log.error("Apple ID JWT验证异常", e);
            return false;
        }
    }

    /**
     * 从Apple ID JWT中提取用户信息
     *
     * @param jwtToken JWT Token
     * @return 用户信息
     */
    public JSONObject extractUserInfo(String jwtToken) {
        try {
            if (!verifyJWT(jwtToken)) {
                return null;
            }

            String[] parts = jwtToken.split("\\.");
            String payloadJson = new String(Base64.getUrlDecoder().decode(parts[1]));
            JSONObject payload = JSON.parseObject(payloadJson);

            JSONObject userInfo = new JSONObject();
            userInfo.put("sub", payload.getString("sub")); // 用户唯一标识
            userInfo.put("email", payload.getString("email"));
            userInfo.put("email_verified", payload.getString("email_verified"));
            userInfo.put("is_private_email", payload.getString("is_private_email"));
            userInfo.put("real_user_status", payload.getString("real_user_status"));

            return userInfo;

        } catch (Exception e) {
            log.error("提取Apple ID用户信息异常", e);
            return null;
        }
    }

    /**
     * 获取Apple公钥
     *
     * @param kid Key ID
     * @return 公钥
     */
    private PublicKey getApplePublicKey(String kid) {
        try {
            // 获取Apple公钥列表
            String response = httpClientUtil.get(APPLE_KEYS_URL);
            if (StringUtils.isEmpty(response)) {
                log.warn("获取Apple公钥响应为空");
                return null;
            }

            JSONObject keysResponse = JSON.parseObject(response);
            JSONArray keys = keysResponse.getJSONArray("keys");

            // 查找对应的公钥
            for (int i = 0; i < keys.size(); i++) {
                JSONObject key = keys.getJSONObject(i);
                if (kid.equals(key.getString("kid"))) {
                    return buildPublicKey(key);
                }
            }

            log.warn("未找到对应的Apple公钥: {}", kid);
            return null;

        } catch (Exception e) {
            log.error("获取Apple公钥异常", e);
            return null;
        }
    }

    /**
     * 构建公钥对象
     *
     * @param keyJson 公钥JSON
     * @return 公钥对象
     */
    private PublicKey buildPublicKey(JSONObject keyJson) {
        try {
            String n = keyJson.getString("n"); // modulus
            String e = keyJson.getString("e"); // exponent

            byte[] nBytes = Base64.getUrlDecoder().decode(n);
            byte[] eBytes = Base64.getUrlDecoder().decode(e);

            // 构建RSA公钥
            java.math.BigInteger modulus = new java.math.BigInteger(1, nBytes);
            java.math.BigInteger exponent = new java.math.BigInteger(1, eBytes);

            java.security.spec.RSAPublicKeySpec spec = new java.security.spec.RSAPublicKeySpec(modulus, exponent);
            KeyFactory factory = KeyFactory.getInstance("RSA");
            return factory.generatePublic(spec);

        } catch (Exception e) {
            log.error("构建Apple公钥异常", e);
            return null;
        }
    }

    /**
     * 检查配置是否完整
     *
     * @return 是否配置完整
     */
    public boolean isConfigured() {
        return !StringUtils.isEmpty(clientId) && !StringUtils.isEmpty(teamId);
    }
}
