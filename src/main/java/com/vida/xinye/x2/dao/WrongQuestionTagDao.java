package com.vida.xinye.x2.dao;

import com.vida.xinye.x2.dto.WrongQuestionSubject;
import com.vida.xinye.x2.mbg.model.WrongQuestionTag;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 错题标签Dao
 *
 * <AUTHOR>
 * @date 2025/02/20
 */
public interface WrongQuestionTagDao {

    /**
     * 根据标签类型查询默认标签和用户添加的标签
     *
     * @param typeId   标签类型ID
     * @return 标签列表
     */
    List<WrongQuestionTag> queryTagsByTypeId(@Param("typeId") Long typeId, @Param("userId") Long userId);

}
