package com.vida.xinye.x2.dao;

import com.vida.xinye.x2.dto.TempletMarketDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 标签Dao
 *
 * <AUTHOR>
 * @date 2025/06/27
 */
public interface TempletMarketDao {

    /**
     * 模板集市列表
     */
    List<TempletMarketDto> list(@Param("groupId") Long groupId);

    /**
     * 新增模板集市
     */
    int insert(TempletMarketDto templetMarketDto);

    /**
     * 修改模板集市
     */
    int update(TempletMarketDto templetMarketDto);

}
