package com.vida.xinye.x2.dao;

import com.vida.xinye.x2.dto.TempletMarketGroupDto;
import com.vida.xinye.x2.mbg.model.TempletMarketGroupI18n;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 模板超市分组数据访问接口
 *
 * <AUTHOR>
 * @date 2025/06/27
 */
@Mapper
public interface TempletMarketGroupDao {

  /**
   * 根据语言获取模板超市分组列表
   *
   * @param locale 语言代码
   * @return 模板超市分组列表
   */
  List<TempletMarketGroupDto> list(@Param("locale") String locale);

  /**
   * 插入标签分组（insert ignore)
   *
   */
  void insert(List<TempletMarketGroupI18n> groups);
}