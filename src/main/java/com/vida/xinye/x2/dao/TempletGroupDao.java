package com.vida.xinye.x2.dao;

import com.vida.xinye.x2.mbg.model.SysTempletGroup;
import com.vida.xinye.x2.mbg.model.TempletGroupI18n;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 标签分组Dao
 *
 * <AUTHOR>
 * @date 2025/06/27
 */
public interface TempletGroupDao {

    /**
     * 标签宝箱分组列表
     * @return
     */
    List<SysTempletGroup> selectSystemGroups(@Param("locale") String locale);

    /**
     * 插入标签分组（insert ignore)
     *
     */
    void insert(List<TempletGroupI18n> groups);
}
