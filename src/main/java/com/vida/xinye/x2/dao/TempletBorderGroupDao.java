package com.vida.xinye.x2.dao;

import com.vida.xinye.x2.dto.TempletBorderGroupDto;
import com.vida.xinye.x2.mbg.model.TempletBorderGroupI18n;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 边框分组数据访问接口
 *
 * <AUTHOR>
 * @date 2025/06/27
 */
@Mapper
public interface TempletBorderGroupDao {

  /**
   * 根据语言获取边框分组列表
   *
   * @param locale 语言代码
   * @return 边框分组列表
   */
  List<TempletBorderGroupDto> list(@Param("locale") String locale);

  /**
   * 插入标签分组（insert ignore)
   *
   */
  void insert(List<TempletBorderGroupI18n> groups);
}