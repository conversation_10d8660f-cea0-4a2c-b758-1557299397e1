/**
 * @Description: 身份证认证缓存数据访问接口
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/01/16 16:05
 */
package com.vida.xinye.x2.dao;

import com.vida.xinye.x2.mbg.model.IdCardVerification;
import org.apache.ibatis.annotations.Param;

/**
 * 身份证认证缓存数据访问接口
 */
public interface IdCardVerificationDao {

  /**
   * 根据姓名和身份证哈希查询有效的认证记录
   *
   * @param name       姓名
   * @param idCardHash 身份证号码哈希值
   * @return 认证记录
   */
  IdCardVerification findValidRecord(@Param("name") String name, @Param("idCardHash") String idCardHash);

  /**
   * 插入认证记录
   *
   * @param record 认证记录
   * @return 影响行数
   */
  int insertRecord(IdCardVerification record);

  /**
   * 清理过期记录
   *
   * @return 影响行数
   */
  int deleteExpiredRecords();
}