package com.vida.xinye.x2.dao;

import com.vida.xinye.x2.dto.WrongQuestionDto;
import com.vida.xinye.x2.dto.WrongQuestionSubject;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 错题标签Dao
 *
 * <AUTHOR>
 * @date 2025/02/20
 */
public interface WrongQuestionDao {

    /**
     * 查询某个标签对应的所有错题
     *
     * @param tagIds 标签，例如 科目属于"语文"
     * @param userId 用户 ID
     * @return 标签列表
     */
    List<WrongQuestionDto> getList(@Param("tagIds") List<Long> tagIds, @Param("sourceType") String sourceType,
            @Param("saveTimeRange") String saveTimeRange, @Param("userId") Long userId);

    /**
     * 查询用户的错题科目(包含的错题数量）
     *
     * @param userId 用户ID
     * @return 标签列表
     */
    List<WrongQuestionSubject> querySubjects(@Param("userId") Long userId);
}
