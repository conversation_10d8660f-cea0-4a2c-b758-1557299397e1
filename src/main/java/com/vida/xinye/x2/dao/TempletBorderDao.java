package com.vida.xinye.x2.dao;

import com.vida.xinye.x2.dto.TempletBorderDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 标签Dao
 *
 * <AUTHOR>
 * @date 2025/06/27
 */
public interface TempletBorderDao {

    /**
     * 标签列表
     */
    List<TempletBorderDto> list(@Param("groupId") Long groupId);

    /**
     * 新增模板边框
     */
    int insert(TempletBorderDto templetBorderDto);

    /**
     * 修改模板边框
     */
    int update(TempletBorderDto templetBorderDto);
}
