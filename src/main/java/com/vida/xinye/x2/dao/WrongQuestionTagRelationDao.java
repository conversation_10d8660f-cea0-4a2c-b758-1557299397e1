package com.vida.xinye.x2.dao;

import com.vida.xinye.x2.dto.WrongQuestionDto;
import com.vida.xinye.x2.mbg.model.WrongQuestionTagRelation;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 错题标签Dao
 *
 * <AUTHOR>
 * @date 2025/02/20
 */
public interface WrongQuestionTagRelationDao {

    /**
     * 查询某个标签对应的所有错题
     *
     * @param relations 关系列表
     *
     * @return 标签列表
     */
    int insertList(@Param("list")List<WrongQuestionTagRelation> relations);

}
