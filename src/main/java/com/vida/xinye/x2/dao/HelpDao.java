package com.vida.xinye.x2.dao;

import com.vida.xinye.x2.dto.HelpFaqDto;
import com.vida.xinye.x2.dto.HelpProductManualDto;
import com.vida.xinye.x2.dto.HelpTutorialDto;
import com.vida.xinye.x2.dto.MaterialDto;
import com.vida.xinye.x2.mbg.model.HelpFaq;
import com.vida.xinye.x2.mbg.model.HelpProductManual;
import com.vida.xinye.x2.mbg.model.HelpTutorial;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 素材Dao
 *
 * <AUTHOR>
 * @date 2024/11/11
 */
public interface HelpDao {

    /**
     * 使用教程
     */
    List<HelpTutorialDto> tutorials(@Param("locale") String locale);

    /**
     * 产品说明书
     */
    List<HelpProductManualDto> productManuals(@Param("locale") String locale);

    /**
     * 常见问题
     */
    List<HelpFaqDto> faqs(@Param("locale") String locale);

}
