package com.vida.xinye.x2.dao;

import com.vida.xinye.x2.dto.MaterialDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 素材Dao
 *
 * <AUTHOR>
 * @date 2024/11/11
 */
public interface MaterialDao {

    /**
     * 素材列表
     */
    List<MaterialDto> list(@Param("userId") Long userId, @Param("categoryId") Long categoryId);

    /**
     * 热门素材列表
     */
    List<MaterialDto> listHotMaterials(@Param("userId") Long userId);

}
