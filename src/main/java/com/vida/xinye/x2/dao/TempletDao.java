package com.vida.xinye.x2.dao;

import com.vida.xinye.x2.dto.TempletDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 素材Dao
 *
 * <AUTHOR>
 * @date 2024/11/11
 */
public interface TempletDao {

    /**
     * 标签列表
     */
    List<TempletDto> list(@Param("sourceType") byte sourceType, @Param("userId") Long userId);

    /**
     * 获取标签打印历史
     */
    List<TempletDto> getTempletHistory(@Param("sourceType") byte sourceType, @Param("userId") Long userId);

    /**
     * 获取当前用户打印历史记录数
     */
    int getPrintHistoryCount(@Param("sourceType") byte sourceType, @Param("userId") Long userId);

    /**
     * 删除最老的打印历史数据
     */
    void deleteOldestPrintHistory(@Param("sourceType") byte sourceType, @Param("userId") Long userId);

}
