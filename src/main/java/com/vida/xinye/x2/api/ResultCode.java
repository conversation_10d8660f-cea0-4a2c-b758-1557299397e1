package com.vida.xinye.x2.api;

/**
 * 枚举了一些常用API操作码
 * Created by wangpf on 2022/4/19.
 */
public enum ResultCode implements IErrorCode {
    SUCCESS(200, "操作成功"),
    FAILED(500, "操作失败"),
    VALIDATE_FAILED(400, "参数检验失败"),
    UNAUTHORIZED(401, "暂未登录或登录已过期"),
    FORBIDDEN(403, "没有相关权限"),
    EXCEL_INVALID(10001, "excel校验失败"),
    FILE_EXISTS(5003, "文件已存在"),

    // 智能切题相关错误码 (20001-20099)
    QUESTION_CUTTING_UNSUPPORTED_IMAGE_FORMAT(20001, "图片格式不支持"),
    QUESTION_CUTTING_IMAGE_SIZE_TOO_LARGE(20002, "图片尺寸过大"),
    QUESTION_CUTTING_EMPTY_IMAGE_CONTENT(20003, "图片内容为空"),
    QUESTION_CUTTING_API_CALL_FAILED(20004, "接口调用失败"),
    QUESTION_CUTTING_API_TIMEOUT(20005, "接口响应超时"),
    QUESTION_CUTTING_INVALID_API_RESPONSE(20006, "接口返回格式错误"),
    QUESTION_CUTTING_CONFIG_ERROR(20007, "配置错误"),
    QUESTION_CUTTING_NO_AVAILABLE_PROVIDER(20008, "未找到可用的接口"),
    QUESTION_CUTTING_EMPTY_CUTTING_RESULT(20009, "切题结果为空"),
    QUESTION_CUTTING_INTERNAL_ERROR(20099, "系统内部错误");


    private long code;
    private String message;

    private ResultCode(long code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public long getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }
}
