package com.vida.xinye.x2.component;

import org.springframework.security.web.util.matcher.RequestMatcher;

import javax.servlet.http.HttpServletRequest;

/**
 * api登录请求匹配器
 *
 * <AUTHOR>
 * @date 2024/11/11
 */
public class ApiLoginRequestMatcher implements RequestMatcher {
    @Override
    public boolean matches(HttpServletRequest request) {
        return "/api/authen/login".equals(request.getRequestURI());
    }
}
