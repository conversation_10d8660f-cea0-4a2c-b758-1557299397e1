package com.vida.xinye.x2.component;

import com.vida.xinye.x2.annotation.RateLimit;
import com.vida.xinye.x2.exception.RateLimitException;
import com.vida.xinye.x2.util.IpUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.concurrent.TimeUnit;

/**
 * @Description: 限流切面
 * @Author: zhangwenbin
 * @Date: 2025/06/03 16:17
 */
@Aspect
@Component
@Slf4j
public class RateLimitAspect {

  @Autowired
  private StringRedisTemplate stringRedisTemplate;

  @Autowired
  private MessageSource messageSource;

  @Before("@annotation(rateLimit)")
  public void doBefore(JoinPoint point, RateLimit rateLimit) {
    // 构造限流key
    String key = buildKey(rateLimit, point);

    // 获取限流参数
    int count = rateLimit.count();
    long time = rateLimit.time(); // time value from annotation
    TimeUnit timeUnit = rateLimit.timeUnit(); // time unit from annotation

    log.debug("限流检查 (原生Redis): key={}, count={}, time={}, timeUnit={}", key, count, time, timeUnit.name());

    // 执行限流检查
    try {
      Long currentCount = stringRedisTemplate.opsForValue().increment(key);

      if (currentCount != null) {
        // 如果是第一次设置该key (即currentCount为1)，则设置过期时间
        if (currentCount == 1) {
          stringRedisTemplate.expire(key, time, timeUnit);
          log.debug("首次设置限流key: {}, 过期时间: {} {}", key, time, timeUnit.name());
        }

        // 如果当前计数大于限制次数
        if (currentCount > count) {
          String message = resolveMessage(rateLimit.message());
          log.warn("触发限流 (原生Redis): key={}, currentCount={}, limit={}, message={}", key, currentCount, count, message);
          throw new RateLimitException(message);
        }
        log.debug("限流检查通过 (原生Redis): key={}, currentCount={}, limit={}", key, currentCount, count);
      } else {
        // Increment 命令不应返回 null，但作为防御性编程，记录错误
        log.error("限流检查异常 (原生Redis): increment 返回 null, key={}", key);
        // 根据业务需求，可以选择抛出异常或允许通过。此处选择记录日志并允许（依赖后续可能的错误）。
        // 或者更严格地抛出异常：
        // throw new IllegalStateException("Redis increment operation returned null for
        // key: " + key);
      }

    } catch (Exception e) {
      if (e instanceof RateLimitException) {
        throw e; // 直接抛出自定义的限流异常
      }
      // 对于其他Redis操作异常，记录日志，可以选择不影响主业务流程
      log.error("限流检查异常 (原生Redis): key={}", key, e);
      // 注意：如果Redis服务不可用，这里将不会进行限流，可能会导致超过预期请求量。
      // 可以根据业务容忍度决定是否在此处抛出异常中断业务。
    }
  }

  /**
   * 构建限流key
   * 
   * @param rateLimit 限流注解
   * @param point     切点
   * @return 限流key
   */
  private String buildKey(RateLimit rateLimit, JoinPoint point) {
    // Redis集群兼容：所有key加上相同的hash tag
    StringBuilder key = new StringBuilder("{rate_limit}:");

    // 添加注解中指定的key
    if (!rateLimit.key().isEmpty()) {
      key.append(rateLimit.key()).append(":");
    } else {
      // 默认使用方法名
      MethodSignature signature = (MethodSignature) point.getSignature();
      Method method = signature.getMethod();
      key.append(method.getDeclaringClass().getSimpleName())
          .append(".")
          .append(method.getName())
          .append(":");
    }

    // 根据限流类型添加标识
    switch (rateLimit.limitType()) {
      case IP:
        key.append("ip:").append(getClientIp());
        break;
      case USER:
        key.append("user:").append(getCurrentUsername());
        break;
      default:
        key.append("global");
        break;
    }

    return key.toString();
  }

  /**
   * 获取客户端IP
   * 
   * @return 客户端IP
   */
  private String getClientIp() {
    try {
      ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
      if (attributes != null) {
        HttpServletRequest request = attributes.getRequest();
        return IpUtils.getIpAddr(request);
      }
    } catch (Exception e) {
      log.warn("获取客户端IP失败", e);
    }
    return "unknown";
  }

  /**
   * 获取当前用户名
   * 
   * @return 当前用户名
   */
  private String getCurrentUsername() {
    try {
      Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
      if (authentication != null && authentication.isAuthenticated()) {
        return authentication.getName();
      }
    } catch (Exception e) {
      log.warn("获取当前用户失败", e);
    }
    return "anonymous";
  }

  /**
   * 解析国际化消息
   * 
   * @param keyOrMsg 注解message参数
   * @return 国际化内容或原文
   */
  private String resolveMessage(String keyOrMsg) {
    if (keyOrMsg == null)
      return "";
    // 约定以rate.limit.开头的为国际化key
    if (keyOrMsg.startsWith("rate.limit.")) {
      try {
        log.debug("尝试解析国际化消息: key={}", keyOrMsg);
        // 使用LocaleContextHolder获取当前Locale
        // 如果key不存在，返回key本身
        log.debug(String.valueOf(LocaleContextHolder.getLocale()));
        return messageSource.getMessage(keyOrMsg, null, keyOrMsg, LocaleContextHolder.getLocale());
      } catch (Exception e) {
        // key不存在时返回key本身
        log.warn("国际化消息解析失败: key={}", keyOrMsg, e);
        return keyOrMsg;
      }
    }
    return keyOrMsg;
  }
}