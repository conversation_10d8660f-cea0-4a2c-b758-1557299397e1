package com.vida.xinye.x2.component;

import com.vida.xinye.x2.service.admin.AdminUserDetailsService;
import com.vida.xinye.x2.service.api.ApiUserDetailsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;

import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * TODO.
 *
 * <AUTHOR>
 * @date 2024/11/11
 */
@Component
@Slf4j
public class CustomAuthenticationProvider implements AuthenticationProvider {
    @Autowired
    private ApiUserDetailsService apiUserDetailsService;
    @Autowired
    private AdminUserDetailsService adminUserDetailsService;
    @Autowired
    private PasswordEncoder passwordEncoder;

    private final ApiLoginRequestMatcher apiLoginRequestMatcher = new ApiLoginRequestMatcher();
    private final AdminLoginRequestMatcher adminLoginRequestMatcher = new AdminLoginRequestMatcher();

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        String username = authentication.getName();
        String password = (String) authentication.getCredentials();

        UserDetails userDetails = null;
        HttpServletRequest request = getRequest();

        if (apiLoginRequestMatcher.matches(request)) {
            log.debug("API login request");
            userDetails = apiUserDetailsService.loadUserByUsername(username);
        } else if (adminLoginRequestMatcher.matches(request)) {
            log.debug("Admin login request");
            userDetails = adminUserDetailsService.loadUserByUsername(username);
        } else {
            throw new BadCredentialsException("Invalid login request");
        }

        if (userDetails == null) {
            throw new BadCredentialsException("User not found with username: " + username);
        }

        if (!passwordEncoder.matches(password, userDetails.getPassword())) {
            throw new BadCredentialsException("Password does not match for user: " + username);
        }

        return new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return UsernamePasswordAuthenticationToken.class.isAssignableFrom(authentication);
    }

    private HttpServletRequest getRequest() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        return attributes.getRequest();
    }


}
