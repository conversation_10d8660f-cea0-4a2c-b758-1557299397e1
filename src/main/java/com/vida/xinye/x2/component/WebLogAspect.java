package com.vida.xinye.x2.component;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.json.JSONUtil;
import com.vida.xinye.x2.domain.WebLogItem;
import com.vida.xinye.x2.util.ParameterUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * 记录请求日志AOP
 *
 * <AUTHOR>
 * @date 2024/1/3
 */
@Aspect
@Component
@Slf4j
public class WebLogAspect {
    @Autowired
    private AliLogClient logClient;

    @Pointcut("execution(public * com.vida.xinye.x2.controller.*.*(..))||execution(public * com.vida.xinye.*.controller.*.*(..))")
    public void webLog() {
    }

    @Before("webLog()")
    public void doBefore(JoinPoint joinPoint) throws Throwable {
    }

    @AfterReturning(value = "webLog()", returning = "ret")
    public void doAfterReturning(Object ret) throws Throwable {
    }

    @Around("webLog()")
    public Object doAround(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        //获取当前请求对象
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        // 请求参数对象
       Object requestParam = ParameterUtil.getNameAndValue(joinPoint);

        WebLogItem logItem = new WebLogItem();
        String urlStr = request.getRequestURL().toString();
        logItem.setBasePath(StrUtil.removeSuffix(urlStr, URLUtil.url(urlStr).getPath()));
        logItem.setIp(request.getRemoteUser());
        logItem.setMethod(request.getMethod());
        logItem.setParam(JSONUtil.toJsonStr(requestParam));
        logItem.setCreateTime(DateUtil.date(DateUtil.current()));
        logItem.setUri(request.getRequestURI());

        Object result;
        try {
            result = joinPoint.proceed();
            logItem.setResult(JSONUtil.toJsonStr(result));

        } catch (Exception exception) {
            logItem.setResult(exception.getMessage());
            throw exception;
        } finally {
            long end = System.currentTimeMillis();
            long cost = (end - startTime);
            logItem.setRequestTime(cost);

            // 上传日志到阿里云sls
            logClient.send("api", logItem);
        }
        return result;
    }

}
