/**
 * @Description: 身份证验证缓存定时任务
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/01/16 16:25
 */
package com.vida.xinye.x2.component;

import com.vida.xinye.x2.service.IdCardVerificationCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 身份证验证缓存定时任务
 * 定期清理过期的缓存记录
 */
@Component
@Slf4j
@ConditionalOnProperty(name = "idcard.verification.cache.cleanup.enabled", havingValue = "true", matchIfMissing = true)
public class IdCardVerificationScheduler {

  @Autowired
  private IdCardVerificationCacheService idCardVerificationCacheService;

  /**
   * 清理过期的身份证验证缓存记录
   * 每天凌晨2点执行
   */
  @Scheduled(cron = "0 0 2 * * ?")
  public void cleanExpiredRecords() {
    log.info("开始清理过期的身份证验证缓存记录");

    try {
      int deletedCount = idCardVerificationCacheService.cleanExpiredRecords();
      log.info("清理过期身份证验证缓存记录完成，清理数量：{}", deletedCount);
    } catch (Exception e) {
      log.error("清理过期身份证验证缓存记录失败", e);
    }
  }
}