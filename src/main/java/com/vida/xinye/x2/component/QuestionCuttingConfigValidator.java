package com.vida.xinye.x2.component;

import cn.hutool.core.util.StrUtil;
import com.vida.xinye.x2.config.QuestionCuttingConfig;
import com.vida.xinye.x2.constant.QuestionCuttingConstant;
import com.vida.xinye.x2.util.YoudaoConfigValidator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 智能切题配置验证器
 *
 * <AUTHOR>
 * @date 2025/08/14
 */
@Slf4j
@Component
public class QuestionCuttingConfigValidator {

    @Autowired
    private QuestionCuttingConfig config;

    @Autowired
    private YoudaoConfigValidator youdaoConfigValidator;

    /**
     * 应用启动完成后验证配置
     */
    @EventListener(ApplicationReadyEvent.class)
    public void validateConfiguration() {
        log.info("开始验证智能切题配置...");

        try {
            validateCommonConfig();
            validateProviderConfigs();
            
            log.info("智能切题配置验证完成");
        } catch (Exception e) {
            log.error("智能切题配置验证失败: {}", e.getMessage(), e);
            // 这里可以选择是否要抛出异常来阻止应用启动
            // throw new IllegalStateException("智能切题配置验证失败", e);
        }
    }

    /**
     * 验证通用配置
     */
    private void validateCommonConfig() {
        QuestionCuttingConfig.CommonConfig commonConfig = config.getCommon();
        
        if (commonConfig == null) {
            log.warn("通用配置为空，将使用默认值");
            return;
        }

        // 验证默认策略
        String defaultStrategy = commonConfig.getDefaultStrategy();
        if (StrUtil.isBlank(defaultStrategy)) {
            log.warn("默认策略未配置，将使用: {}", QuestionCuttingConstant.Default.DEFAULT_STRATEGY);
        } else if (!isValidStrategy(defaultStrategy)) {
            log.warn("默认策略配置无效: {}, 支持的策略: {}", defaultStrategy, getSupportedStrategies());
        }

        // 验证默认提供商
        String defaultProvider = commonConfig.getDefaultProvider();
        if (StrUtil.isBlank(defaultProvider)) {
            log.warn("默认提供商未配置，将使用: {}", QuestionCuttingConstant.Default.DEFAULT_PROVIDER);
        } else if (!isValidProvider(defaultProvider)) {
            log.warn("默认提供商配置无效: {}, 支持的提供商: {}", defaultProvider, getSupportedProviders());
        }

        // 验证图片大小限制
        long maxImageSize = commonConfig.getMaxImageSize();
        if (maxImageSize <= 0) {
            log.warn("图片大小限制配置无效: {}, 将使用默认值: {}", maxImageSize, QuestionCuttingConstant.Default.MAX_IMAGE_SIZE);
        } else if (maxImageSize > 50 * 1024 * 1024) { // 50MB
            log.warn("图片大小限制过大: {}MB, 建议不超过50MB", maxImageSize / 1024 / 1024);
        }

        // 验证并发请求数
        int maxConcurrentRequests = commonConfig.getMaxConcurrentRequests();
        if (maxConcurrentRequests <= 0) {
            log.warn("最大并发请求数配置无效: {}, 将使用默认值: {}", maxConcurrentRequests, QuestionCuttingConstant.Default.MAX_CONCURRENT_REQUESTS);
        } else if (maxConcurrentRequests > 100) {
            log.warn("最大并发请求数过大: {}, 建议不超过100", maxConcurrentRequests);
        }

        // 验证缓存过期时间
        int cacheExpireSeconds = commonConfig.getCacheExpireSeconds();
        if (cacheExpireSeconds < 0) {
            log.warn("缓存过期时间配置无效: {}, 将使用默认值: {}", cacheExpireSeconds, QuestionCuttingConstant.Default.CACHE_EXPIRE_SECONDS);
        }

        // 验证支持的图片格式
        String[] supportedFormats = commonConfig.getSupportedFormats();
        if (supportedFormats == null || supportedFormats.length == 0) {
            log.warn("支持的图片格式未配置，将使用默认值: {}", Arrays.toString(QuestionCuttingConstant.Default.SUPPORTED_FORMATS));
        }

        log.info("通用配置验证完成");
    }

    /**
     * 验证提供商配置
     */
    private void validateProviderConfigs() {
        int availableProviders = 0;

        // 验证天壤配置
        if (validateTianrangConfig()) {
            availableProviders++;
        }

        // 验证有道配置
        if (validateYoudaoConfig()) {
            availableProviders++;
        }

        // 验证阿里云配置
        if (validateAliyunConfig()) {
            availableProviders++;
        }

        if (availableProviders == 0) {
            log.error("没有可用的切题接口提供商，请检查配置");
        } else {
            log.info("共有 {} 个可用的切题接口提供商", availableProviders);
        }
    }

    /**
     * 验证天壤配置
     */
    private boolean validateTianrangConfig() {
        QuestionCuttingConfig.TianrangConfig tianrangConfig = config.getTianrang();
        
        if (tianrangConfig == null) {
            log.warn("天壤配置为空");
            return false;
        }

        if (!tianrangConfig.isEnabled()) {
            log.info("天壤接口已禁用");
            return false;
        }

        boolean isValid = true;

        if (StrUtil.isBlank(tianrangConfig.getUrl())) {
            log.warn("天壤接口URL未配置");
            isValid = false;
        }

        if (StrUtil.isBlank(tianrangConfig.getApiKey())) {
            log.warn("天壤API密钥未配置");
            isValid = false;
        }

        if (tianrangConfig.getTimeout() <= 0) {
            log.warn("天壤接口超时时间配置无效: {}", tianrangConfig.getTimeout());
            isValid = false;
        }

        if (tianrangConfig.getRetryCount() < 0) {
            log.warn("天壤接口重试次数配置无效: {}", tianrangConfig.getRetryCount());
            isValid = false;
        }

        if (isValid) {
            log.info("天壤配置验证通过");
        }

        return isValid;
    }

    /**
     * 验证有道配置
     */
    private boolean validateYoudaoConfig() {
        QuestionCuttingConfig.YoudaoConfig youdaoConfig = config.getYoudao();

        if (youdaoConfig == null) {
            log.warn("有道配置为空");
            youdaoConfigValidator.provideSolutions();
            return false;
        }

        if (!youdaoConfig.isEnabled()) {
            log.info("有道接口已禁用");
            return false;
        }

        // 使用专门的验证器进行详细验证
        boolean isValid = youdaoConfigValidator.validateYoudaoConfig(youdaoConfig);

        if (!isValid) {
            log.error("有道配置验证失败，请检查以下问题:");
            youdaoConfigValidator.checkCommonIssues(youdaoConfig);
            youdaoConfigValidator.provideSolutions();
        } else {
            log.info("有道配置验证通过");
        }

        return isValid;
    }

    /**
     * 验证阿里云配置
     */
    private boolean validateAliyunConfig() {
        QuestionCuttingConfig.AliyunConfig aliyunConfig = config.getAliyun();
        
        if (aliyunConfig == null) {
            log.warn("阿里云配置为空");
            return false;
        }

        if (!aliyunConfig.isEnabled()) {
            log.info("阿里云接口已禁用");
            return false;
        }

        boolean isValid = true;

        if (StrUtil.isBlank(aliyunConfig.getEndpoint())) {
            log.warn("阿里云接口端点未配置");
            isValid = false;
        }

        if (StrUtil.isBlank(aliyunConfig.getAccessKeyId())) {
            log.warn("阿里云访问密钥ID未配置");
            isValid = false;
        }

        if (StrUtil.isBlank(aliyunConfig.getAccessKeySecret())) {
            log.warn("阿里云访问密钥Secret未配置");
            isValid = false;
        }

        if (aliyunConfig.getTimeout() <= 0) {
            log.warn("阿里云接口超时时间配置无效: {}", aliyunConfig.getTimeout());
            isValid = false;
        }

        if (aliyunConfig.getRetryCount() < 0) {
            log.warn("阿里云接口重试次数配置无效: {}", aliyunConfig.getRetryCount());
            isValid = false;
        }

        if (isValid) {
            log.info("阿里云配置验证通过");
        }

        return isValid;
    }

    /**
     * 验证策略是否有效
     */
    private boolean isValidStrategy(String strategy) {
        return getSupportedStrategies().contains(strategy);
    }

    /**
     * 验证提供商是否有效
     */
    private boolean isValidProvider(String provider) {
        return getSupportedProviders().contains(provider);
    }

    /**
     * 获取支持的策略列表
     */
    private List<String> getSupportedStrategies() {
        return Arrays.asList(
            QuestionCuttingConstant.Strategy.BEST_QUALITY,
            QuestionCuttingConstant.Strategy.FASTEST,
            QuestionCuttingConstant.Strategy.AGGREGATED,
            QuestionCuttingConstant.Strategy.SPECIFIED,
            QuestionCuttingConstant.Strategy.ROUND_ROBIN
        );
    }

    /**
     * 获取支持的提供商列表
     */
    private List<String> getSupportedProviders() {
        return Arrays.asList(
            QuestionCuttingConstant.Provider.TIANRANG,
            QuestionCuttingConstant.Provider.YOUDAO,
            QuestionCuttingConstant.Provider.ALIYUN
        );
    }
}
