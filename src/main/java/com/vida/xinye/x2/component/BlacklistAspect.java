package com.vida.xinye.x2.component;

import com.vida.xinye.x2.annotation.BlacklistCheck;
import com.vida.xinye.x2.exception.BlacklistException;
import com.vida.xinye.x2.service.BlacklistService;
import com.vida.xinye.x2.util.IpUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * @Description: IP黑名单检查切面
 * @Author: zhangwenbin
 * @Date: 2024/11/05 16:40
 */
@Aspect
@Component
@Slf4j
public class BlacklistAspect {

  @Autowired
  private BlacklistService blacklistService;

  @Before("@annotation(blacklistCheck)")
  public void checkBlacklist(JoinPoint point, BlacklistCheck blacklistCheck) {
    String ip = getClientIp();

    // 检查是否在黑名单中
    if (blacklistService.isBlacklisted(ip)) {
      log.warn("IP {} 在黑名单中，拒绝访问", ip);
      throw new BlacklistException(blacklistCheck.message());
    }

    // 如果启用自动封禁，检查请求频率
    if (blacklistCheck.autoBlock()) {
      boolean blocked = blacklistService.checkAndAutoBlock(
          ip,
          blacklistCheck.autoBlockThreshold(),
          blacklistCheck.autoBlockWindow(),
          blacklistCheck.autoBlockDuration());

      if (blocked) {
        log.warn("IP {} 触发自动封禁规则，已加入黑名单", ip);
        throw new BlacklistException("Too many requests, IP has been blocked");
      }
    }

    log.debug("IP {} 黑名单检查通过", ip);
  }

  /**
   * 获取客户端IP
   * 
   * @return 客户端IP
   */
  private String getClientIp() {
    try {
      ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
      if (attributes != null) {
        HttpServletRequest request = attributes.getRequest();
        return IpUtils.getIpAddr(request);
      }
    } catch (Exception e) {
      log.warn("获取客户端IP失败", e);
    }
    return "unknown";
  }

  /**
   * 获取当前用户名
   * 
   * @return 当前用户名
   */
  private String getCurrentUsername() {
    try {
      Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
      if (authentication != null && authentication.isAuthenticated()) {
        return authentication.getName();
      }
    } catch (Exception e) {
      log.warn("获取当前用户失败", e);
    }
    return "anonymous";
  }
}