package com.vida.xinye.x2.component;

import com.vida.xinye.x2.annotation.SignatureCheck;
import com.vida.xinye.x2.exception.ApiException;
import com.vida.xinye.x2.service.SignatureService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * @Description: API签名验证切面
 * @Author: zhangwenbin
 * @Date: 2024/11/05 17:00
 */
@Aspect
@Component
@Order(1) // 最高优先级，在其他切面之前执行
@Slf4j
public class SignatureAspect {

  @Autowired
  private SignatureService signatureService;

  @Around("@annotation(signatureCheck)")
  public Object checkSignature(ProceedingJoinPoint joinPoint, SignatureCheck signatureCheck) throws Throwable {
    ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
    if (attributes == null) {
      throw new ApiException(signatureCheck.message());
    }

    HttpServletRequest request = attributes.getRequest();

    // 从header中获取签名参数
    String ip = request.getHeader("ip");
    String timestamp = request.getHeader("timestamp");
    String sign = request.getHeader("sign");

    // 记录请求信息（隐私保护）
    String clientIp = getClientIpAddress(request);
    log.info("API签名验证 - 请求IP: {}, Header-IP: {}, Timestamp: {}, Sign: {}",
        clientIp,
        ip != null ? maskSensitiveData(ip) : "null",
        timestamp,
        sign != null ? "***" : "null");

    // 验证必要参数
    if (ip == null || timestamp == null || sign == null) {
      log.warn("签名验证失败 - 缺少必要参数: ip={}, timestamp={}, sign={}",
          ip != null ? "有值" : "空",
          timestamp != null ? "有值" : "空",
          sign != null ? "有值" : "空");
      throw new ApiException(signatureCheck.message());
    }

    // IP一致性验证（可选）
    if (signatureCheck.verifyIpConsistency()) {
      if (!ip.equals(clientIp)) {
        log.warn("IP一致性验证失败 - Header-IP: {}, Client-IP: {}",
            maskSensitiveData(ip), maskSensitiveData(clientIp));
        throw new ApiException(signatureCheck.message());
      }
    }

    // 验证签名（使用注解中的expireTime）
    boolean isValid = signatureService.verifySignature(ip, timestamp, sign, signatureCheck.expireTime());
    if (!isValid) {
      log.warn("签名验证失败 - IP: {}, Timestamp: {}", maskSensitiveData(ip), timestamp);
      throw new ApiException(signatureCheck.message());
    }

    log.debug("签名验证成功 - IP: {}, Timestamp: {}", maskSensitiveData(ip), timestamp);

    // 继续执行目标方法
    return joinPoint.proceed();
  }

  /**
   * 获取客户端真实IP地址
   */
  private String getClientIpAddress(HttpServletRequest request) {
    String ip = request.getHeader("X-Forwarded-For");
    if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
      ip = request.getHeader("Proxy-Client-IP");
    }
    if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
      ip = request.getHeader("WL-Proxy-Client-IP");
    }
    if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
      ip = request.getHeader("HTTP_CLIENT_IP");
    }
    if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
      ip = request.getHeader("HTTP_X_FORWARDED_FOR");
    }
    if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
      ip = request.getRemoteAddr();
    }

    // 处理多个IP的情况（取第一个）
    if (ip != null && ip.contains(",")) {
      ip = ip.split(",")[0].trim();
    }

    return ip;
  }

  /**
   * 隐私保护 - 遮盖敏感数据
   */
  private String maskSensitiveData(String data) {
    if (data == null || data.length() <= 6) {
      return "***";
    }
    return data.substring(0, 3) + "***" + data.substring(data.length() - 3);
  }
}