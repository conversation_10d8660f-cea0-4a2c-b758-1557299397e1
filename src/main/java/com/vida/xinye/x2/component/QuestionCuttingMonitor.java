package com.vida.xinye.x2.component;

import com.vida.xinye.x2.adapter.QuestionCuttingAdapter;
import com.vida.xinye.x2.dto.QuestionCuttingResultDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 智能切题监控组件
 *
 * <AUTHOR>
 * @date 2025/08/14
 */
@Slf4j
@Component
public class QuestionCuttingMonitor {

    @Autowired
    private List<QuestionCuttingAdapter> adapters;

    // 统计数据
    private final Map<String, AtomicLong> requestCounts = new ConcurrentHashMap<>();
    private final Map<String, AtomicLong> successCounts = new ConcurrentHashMap<>();
    private final Map<String, AtomicLong> failureCounts = new ConcurrentHashMap<>();
    private final Map<String, AtomicLong> totalResponseTimes = new ConcurrentHashMap<>();
    private final Map<String, AtomicLong> totalQuestionCounts = new ConcurrentHashMap<>();

    /**
     * 记录请求开始
     *
     * @param provider 提供商
     * @param requestId 请求ID
     */
    public void recordRequestStart(String provider, String requestId) {
        log.info("切题请求开始 - 提供商: {}, 请求ID: {}", provider, requestId);
        
        requestCounts.computeIfAbsent(provider, k -> new AtomicLong(0)).incrementAndGet();
        requestCounts.computeIfAbsent("total", k -> new AtomicLong(0)).incrementAndGet();
    }

    /**
     * 记录请求完成
     *
     * @param provider 提供商
     * @param requestId 请求ID
     * @param result 切题结果
     */
    public void recordRequestComplete(String provider, String requestId, QuestionCuttingResultDto result) {
        if (result.getSuccess()) {
            recordSuccess(provider, requestId, result);
        } else {
            recordFailure(provider, requestId, result);
        }
    }

    /**
     * 记录成功请求
     */
    private void recordSuccess(String provider, String requestId, QuestionCuttingResultDto result) {
        log.info("切题请求成功 - 提供商: {}, 请求ID: {}, 处理时间: {}ms, 题目数量: {}, 质量评分: {}", 
                provider, requestId, result.getProcessingTime(), 
                result.getQuestions() != null ? result.getQuestions().size() : 0,
                result.getQualityScore());

        successCounts.computeIfAbsent(provider, k -> new AtomicLong(0)).incrementAndGet();
        successCounts.computeIfAbsent("total", k -> new AtomicLong(0)).incrementAndGet();

        if (result.getProcessingTime() != null) {
            totalResponseTimes.computeIfAbsent(provider, k -> new AtomicLong(0))
                    .addAndGet(result.getProcessingTime());
            totalResponseTimes.computeIfAbsent("total", k -> new AtomicLong(0))
                    .addAndGet(result.getProcessingTime());
        }

        if (result.getQuestions() != null) {
            totalQuestionCounts.computeIfAbsent(provider, k -> new AtomicLong(0))
                    .addAndGet(result.getQuestions().size());
            totalQuestionCounts.computeIfAbsent("total", k -> new AtomicLong(0))
                    .addAndGet(result.getQuestions().size());
        }
    }

    /**
     * 记录失败请求
     */
    private void recordFailure(String provider, String requestId, QuestionCuttingResultDto result) {
        log.warn("切题请求失败 - 提供商: {}, 请求ID: {}, 错误码: {}, 错误信息: {}", 
                provider, requestId, result.getErrorCode(), result.getErrorMessage());

        failureCounts.computeIfAbsent(provider, k -> new AtomicLong(0)).incrementAndGet();
        failureCounts.computeIfAbsent("total", k -> new AtomicLong(0)).incrementAndGet();

        if (result.getProcessingTime() != null) {
            totalResponseTimes.computeIfAbsent(provider, k -> new AtomicLong(0))
                    .addAndGet(result.getProcessingTime());
            totalResponseTimes.computeIfAbsent("total", k -> new AtomicLong(0))
                    .addAndGet(result.getProcessingTime());
        }
    }

    /**
     * 记录批量请求
     *
     * @param batchId 批次ID
     * @param totalCount 总数量
     * @param successCount 成功数量
     * @param failureCount 失败数量
     * @param totalTime 总处理时间
     */
    public void recordBatchRequest(String batchId, int totalCount, int successCount, int failureCount, long totalTime) {
        log.info("批量切题完成 - 批次ID: {}, 总数: {}, 成功: {}, 失败: {}, 总时间: {}ms", 
                batchId, totalCount, successCount, failureCount, totalTime);

        requestCounts.computeIfAbsent("batch", k -> new AtomicLong(0)).incrementAndGet();
        successCounts.computeIfAbsent("batch", k -> new AtomicLong(0)).addAndGet(successCount);
        failureCounts.computeIfAbsent("batch", k -> new AtomicLong(0)).addAndGet(failureCount);
        totalResponseTimes.computeIfAbsent("batch", k -> new AtomicLong(0)).addAndGet(totalTime);
    }

    /**
     * 记录异常
     *
     * @param provider 提供商
     * @param requestId 请求ID
     * @param exception 异常
     */
    public void recordException(String provider, String requestId, Exception exception) {
        log.error("切题异常 - 提供商: {}, 请求ID: {}, 异常: {}", provider, requestId, exception.getMessage(), exception);
        
        failureCounts.computeIfAbsent(provider, k -> new AtomicLong(0)).incrementAndGet();
        failureCounts.computeIfAbsent("total", k -> new AtomicLong(0)).incrementAndGet();
    }

    /**
     * 获取统计信息
     *
     * @return 统计信息
     */
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new ConcurrentHashMap<>();

        // 总体统计
        Map<String, Object> totalStats = new ConcurrentHashMap<>();
        totalStats.put("requestCount", requestCounts.getOrDefault("total", new AtomicLong(0)).get());
        totalStats.put("successCount", successCounts.getOrDefault("total", new AtomicLong(0)).get());
        totalStats.put("failureCount", failureCounts.getOrDefault("total", new AtomicLong(0)).get());
        
        long totalRequests = requestCounts.getOrDefault("total", new AtomicLong(0)).get();
        long totalSuccesses = successCounts.getOrDefault("total", new AtomicLong(0)).get();
        if (totalRequests > 0) {
            totalStats.put("successRate", (double) totalSuccesses / totalRequests);
        }
        
        long totalResponseTime = totalResponseTimes.getOrDefault("total", new AtomicLong(0)).get();
        if (totalSuccesses > 0) {
            totalStats.put("averageResponseTime", (double) totalResponseTime / totalSuccesses);
        }
        
        totalStats.put("totalQuestionCount", totalQuestionCounts.getOrDefault("total", new AtomicLong(0)).get());
        
        stats.put("total", totalStats);

        // 各提供商统计
        Map<String, Object> providerStats = new ConcurrentHashMap<>();
        for (QuestionCuttingAdapter adapter : adapters) {
            String provider = adapter.getProvider().getCode();
            Map<String, Object> providerStat = new ConcurrentHashMap<>();
            
            providerStat.put("available", adapter.isAvailable());
            providerStat.put("priority", adapter.getPriority());
            providerStat.put("qualityScore", adapter.getQualityScore());
            providerStat.put("averageResponseTime", adapter.getAverageResponseTime());
            
            long requests = requestCounts.getOrDefault(provider, new AtomicLong(0)).get();
            long successes = successCounts.getOrDefault(provider, new AtomicLong(0)).get();
            long failures = failureCounts.getOrDefault(provider, new AtomicLong(0)).get();
            
            providerStat.put("requestCount", requests);
            providerStat.put("successCount", successes);
            providerStat.put("failureCount", failures);
            
            if (requests > 0) {
                providerStat.put("successRate", (double) successes / requests);
            }
            
            long responseTime = totalResponseTimes.getOrDefault(provider, new AtomicLong(0)).get();
            if (successes > 0) {
                providerStat.put("actualAverageResponseTime", (double) responseTime / successes);
            }
            
            providerStat.put("totalQuestionCount", totalQuestionCounts.getOrDefault(provider, new AtomicLong(0)).get());
            
            providerStats.put(provider, providerStat);
        }
        
        stats.put("providers", providerStats);

        // 批量处理统计
        Map<String, Object> batchStats = new ConcurrentHashMap<>();
        batchStats.put("batchCount", requestCounts.getOrDefault("batch", new AtomicLong(0)).get());
        batchStats.put("totalSuccessCount", successCounts.getOrDefault("batch", new AtomicLong(0)).get());
        batchStats.put("totalFailureCount", failureCounts.getOrDefault("batch", new AtomicLong(0)).get());
        batchStats.put("totalProcessingTime", totalResponseTimes.getOrDefault("batch", new AtomicLong(0)).get());
        
        stats.put("batch", batchStats);

        return stats;
    }

    /**
     * 重置统计信息
     */
    public void resetStatistics() {
        log.info("重置切题统计信息");
        requestCounts.clear();
        successCounts.clear();
        failureCounts.clear();
        totalResponseTimes.clear();
        totalQuestionCounts.clear();
    }

    /**
     * 检查提供商健康状态
     *
     * @return 健康状态报告
     */
    public Map<String, Object> checkProviderHealth() {
        Map<String, Object> healthReport = new ConcurrentHashMap<>();
        
        for (QuestionCuttingAdapter adapter : adapters) {
            String provider = adapter.getProvider().getCode();
            Map<String, Object> health = new ConcurrentHashMap<>();
            
            health.put("available", adapter.isAvailable());
            health.put("name", adapter.getProvider().getName());
            health.put("description", adapter.getProvider().getDescription());
            
            long requests = requestCounts.getOrDefault(provider, new AtomicLong(0)).get();
            long successes = successCounts.getOrDefault(provider, new AtomicLong(0)).get();
            long failures = failureCounts.getOrDefault(provider, new AtomicLong(0)).get();
            
            if (requests > 0) {
                double successRate = (double) successes / requests;
                health.put("successRate", successRate);
                health.put("healthy", successRate >= 0.8); // 成功率80%以上认为健康
            } else {
                health.put("healthy", adapter.isAvailable());
            }
            
            healthReport.put(provider, health);
        }
        
        return healthReport;
    }
}
