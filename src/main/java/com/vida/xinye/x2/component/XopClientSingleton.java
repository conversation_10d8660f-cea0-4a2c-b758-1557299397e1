package com.vida.xinye.x2.component;

import com.xkw.xop.client.XopHttpClient;
import org.springframework.stereotype.Component;

/**
 * TODO.
 *
 * <AUTHOR>
 * @date 2025/1/14
 */
@Component
public class XopClientSingleton {
    private static volatile XopHttpClient xopClient;

    // 私有构造函数，防止外部实例化
    private XopClientSingleton() {}

    // 提供全局访问点，使用双重检查锁定
    public static XopHttpClient getInstance() {
        if (xopClient == null) {
            synchronized (XopClientSingleton.class) {
                if (xopClient == null) {
                    // TODO: 替换为nacos配置
                    String appId = "106931723445800800";
                    String appSecret = "gMKUMzIB5DLuHGf8G29v7B28eP4UfhJE";

                    xopClient = new XopHttpClient.Builder()
                            .appId(appId).secret(appSecret)
                            .timeout(10).maxConnectionPerRoute(10)
                            .build();
                }
            }
        }
        return xopClient;
    }
}
