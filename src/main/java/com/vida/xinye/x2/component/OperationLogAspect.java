package com.vida.xinye.x2.component;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.vida.xinye.x2.annotation.OperationLog;
import com.vida.xinye.x2.constant.OperationLogTypeEnum;
import com.vida.xinye.x2.domain.BeanDiff;
import com.vida.xinye.x2.domain.OperationLogItem;
import com.vida.xinye.x2.dto.UserDto;
import com.vida.xinye.x2.service.BeanCompareService;
import com.vida.xinye.x2.util.ParameterUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.stream.Collectors;

/**
 * 记录请求日志AOP
 *
 * <AUTHOR>
 * @date 2024/1/3
 */
@Aspect
@Component
@Slf4j
public class OperationLogAspect {
    @Autowired
    private AliLogClient logClient;


    @Pointcut("@annotation(com.vida.xinye.x2.annotation.OperationLog)")
    public void operationLog() {
    }

    @Around("operationLog()&&@annotation(opLog)")
    public Object doAround(ProceedingJoinPoint joinPoint, OperationLog opLog) throws Throwable {
        //获取当前请求对象
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        String uri = request.getRequestURI();
        // 请求参数对象
        Object requestParam = ParameterUtil.getNameAndValue(joinPoint);

        long start = System.currentTimeMillis();
        OperationLogItem logItem = new OperationLogItem();
        logItem.setCreateTime(DateUtil.date(DateUtil.current()));

        Object[] args = joinPoint.getArgs();
        for (Object arg : args) {
            if (arg instanceof UserDto) {
                UserDto user = (UserDto) arg;
                logItem.setUserId(user.getId().toString());
                logItem.setUsername(user.getUsername());
            }
        }
//        UmsAdminExt admin = adminService.getCurrentAdminNoRole();
//        if (admin != null) {
//            logItem.setUserId(admin.getId().toString());
//            logItem.setUsername(admin.getUsername());
//        }
        String opCatetory = opLog.category();
        String opSubcategory = opLog.subcategory();
        String opDesc = opLog.desc();
        OperationLogTypeEnum opType = opLog.type();
        logItem.setCategory(opCatetory);
        logItem.setSubCategory(opSubcategory);
        logItem.setDesc(opDesc);
        logItem.setUri(uri);
        logItem.setType(opType.getType());
        String className = joinPoint.getTarget().getClass().getName();
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        String methodName = className + "." + method.getName();
        logItem.setMethod(methodName);
        String params = JSONUtil.toJsonStr(requestParam);
        logItem.setParam(params);
        Object result;
        try {
            result = joinPoint.proceed();
            logItem.setResult(JSONUtil.toJsonStr(result));
        } catch (Exception exception) {
            logItem.setResult(exception.getMessage());
            throw exception;
        } finally {
            long end = System.currentTimeMillis();
            long cost = (end - start);
            logItem.setRequestTime(cost);

            // 记录数据变更
            BeanDiff beanDiff = BeanCompareService.getBeanDiff();
            if (beanDiff != null) {
                logItem.setType(OperationLogTypeEnum.UPDATE.getType());
                if (!CollectionUtils.isEmpty(beanDiff.getFieldDiffs())) {
                    logItem.setExtend("数据变更：" + beanDiff.getFieldDiffs().stream().map(
                            fieldDiff -> String.format("%s[%s]:%s -> %s", fieldDiff.getAttributeAlias(), fieldDiff.getAttributeName()
                                    , fieldDiff.getOldValue(), fieldDiff.getNewValue())).collect(Collectors.joining(" ; ")));
                }
            }
            // 上传日志到阿里云sls
            log.info("上传操作日志到阿里云sls");
            logClient.send("operation", logItem);
        }
        return result;
    }

}
