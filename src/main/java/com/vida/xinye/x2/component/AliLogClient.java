package com.vida.xinye.x2.component;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.aliyun.openservices.aliyun.log.producer.LogProducer;
import com.aliyun.openservices.aliyun.log.producer.Producer;
import com.aliyun.openservices.aliyun.log.producer.ProducerConfig;
import com.aliyun.openservices.aliyun.log.producer.ProjectConfig;
import com.aliyun.openservices.aliyun.log.producer.errors.ProducerException;
import com.aliyun.openservices.log.common.LogItem;
import com.vida.xinye.x2.domain.AliyunLogCallback;
import com.vida.xinye.x2.domain.BaseLogItem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.concurrent.atomic.AtomicLong;

/**
 * 阿里云日志-客户端
 *
 * <AUTHOR>
 * @date 2024/1/3
 */
@Component
@RefreshScope
@Slf4j
public class AliLogClient {
    @Value("${spring.application.name}")
    private String APPLICATION_NAME;
    @Value("${spring.profiles.active}")
    private String PROFILE_ACTIVE;
    @Value("${aliyun.log.endpoint}")
    private String ALIYUN_LOG_ENDPOINT;
    @Value("${aliyun.log.accessKeyId}")
    private String ALIYUN_LOG_ACCESSKEYID;
    @Value("${aliyun.log.accessKeySecret}")
    private String ALIYUN_LOG_ACCESSKEYSECRET;
    @Value("${aliyun.log.producer.project}")
    private String ALIYUN_LOG_PRODUCER_PROJECT;
    @Value("${aliyun.log.producer.logstore}")
    private String ALIYUN_LOG_PRODUCER_LOGSTORE;

    /**
     * 上传日志到阿里云sls
     */
    @Async("asyncLogExecutor")
    public void send(String topic, BaseLogItem baseLogItem) {
        LogItem logItem = new LogItem();
        JSONObject jsonObject = JSONUtil.parseObj(baseLogItem);
        jsonObject.keySet().forEach(key -> {
            logItem.PushBack(key, jsonObject.getStr(key));
        });
        send(topic, logItem);
    }

    @Async("asyncLogExecutor")
    public void send(BaseLogItem baseLogItem) {
        send("", baseLogItem);
    }

    private void send(String topic, LogItem logItem) {
        ProducerConfig producerConfig = new ProducerConfig();
        final Producer producer = new LogProducer(producerConfig);
        producer.putProjectConfig(new ProjectConfig(ALIYUN_LOG_PRODUCER_PROJECT, ALIYUN_LOG_ENDPOINT, ALIYUN_LOG_ACCESSKEYID, ALIYUN_LOG_ACCESSKEYSECRET));

        // The number of logs that have finished (either successfully send, or failed).
        final AtomicLong completed = new AtomicLong(0);
        try {
            producer.send(
                    ALIYUN_LOG_PRODUCER_PROJECT,
                    ALIYUN_LOG_PRODUCER_LOGSTORE,
                    topic,
                    APPLICATION_NAME + ":" + PROFILE_ACTIVE,
                    logItem,
                    new AliyunLogCallback(ALIYUN_LOG_PRODUCER_PROJECT, ALIYUN_LOG_PRODUCER_LOGSTORE, logItem, completed));
        } catch (InterruptedException e) {
            log.warn("The current thread has been interrupted during send logs.");
        } catch (Exception e) {
            log.error("Failed to send log, logItem={}, e=", logItem, e);
        } finally {
        }

        try {
            producer.close();
        } catch (InterruptedException e) {
            log.warn("The current thread has been interrupted from close.");
        } catch (ProducerException e) {
            log.debug("Failed to close producer, e=", e);
        }

    }

}


