package com.vida.xinye.x2.component;

import org.springframework.security.web.util.matcher.RequestMatcher;

import javax.servlet.http.HttpServletRequest;

/**
 * 判断是否是管理员登录请求
 *
 * <AUTHOR>
 * @date 2024/11/11
 */
public class AdminLoginRequestMatcher implements RequestMatcher {
    @Override
    public boolean matches(HttpServletRequest request) {
        return "/admin/authen/login".equals(request.getRequestURI());
    }
}
