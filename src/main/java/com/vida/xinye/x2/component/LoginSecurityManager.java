package com.vida.xinye.x2.component;

import com.vida.xinye.x2.service.RedisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 登录安全管理器
 *
 * <AUTHOR>
 * @date 2025/08/12
 */
@Component
public class LoginSecurityManager {

    @Autowired
    private RedisService redisService;

    // 登录失败次数限制
    private static final int MAX_LOGIN_ATTEMPTS = 5;
    // 锁定时间（秒）
    private static final int LOCK_DURATION = 1800; // 30分钟
    // 登录失败记录过期时间（秒）
    private static final int ATTEMPT_RECORD_DURATION = 3600; // 1小时

    /**
     * 记录登录失败
     *
     * @param accountIdentifier 账号标识符
     * @return 当前失败次数
     */
    public int recordLoginFailure(String accountIdentifier) {
        String key = "login_failure:" + accountIdentifier;
        Object countObj = redisService.get(key);
        String countStr = countObj == null ? null : countObj.toString();
        int count = countStr == null ? 0 : Integer.parseInt(countStr);
        count++;
        
        redisService.set(key, String.valueOf(count), ATTEMPT_RECORD_DURATION);
        
        // 如果达到最大失败次数，锁定账号
        if (count >= MAX_LOGIN_ATTEMPTS) {
            lockAccount(accountIdentifier);
        }
        
        return count;
    }

    /**
     * 清除登录失败记录
     *
     * @param accountIdentifier 账号标识符
     */
    public void clearLoginFailures(String accountIdentifier) {
        String key = "login_failure:" + accountIdentifier;
        redisService.del(key);
    }

    /**
     * 锁定账号
     *
     * @param accountIdentifier 账号标识符
     */
    public void lockAccount(String accountIdentifier) {
        String key = "account_locked:" + accountIdentifier;
        redisService.set(key, "locked", LOCK_DURATION);
    }

    /**
     * 检查账号是否被锁定
     *
     * @param accountIdentifier 账号标识符
     * @return 是否被锁定
     */
    public boolean isAccountLocked(String accountIdentifier) {
        String key = "account_locked:" + accountIdentifier;
        return redisService.hasKey(key);
    }

    /**
     * 解锁账号
     *
     * @param accountIdentifier 账号标识符
     */
    public void unlockAccount(String accountIdentifier) {
        String lockKey = "account_locked:" + accountIdentifier;
        String failureKey = "login_failure:" + accountIdentifier;
        redisService.del(lockKey);
        redisService.del(failureKey);
    }

    /**
     * 获取登录失败次数
     *
     * @param accountIdentifier 账号标识符
     * @return 失败次数
     */
    public int getLoginFailureCount(String accountIdentifier) {
        String key = "login_failure:" + accountIdentifier;
        Object countObj = redisService.get(key);
        String countStr = countObj == null ? null : countObj.toString();
        return countStr == null ? 0 : Integer.parseInt(countStr);
    }

    /**
     * 获取剩余锁定时间（秒）
     *
     * @param accountIdentifier 账号标识符
     * @return 剩余锁定时间，-1表示未锁定
     */
    public long getRemainingLockTime(String accountIdentifier) {
        String key = "account_locked:" + accountIdentifier;
        return redisService.getExpire(key);
    }

    /**
     * 检查是否可以尝试登录
     *
     * @param accountIdentifier 账号标识符
     * @return 是否可以登录
     */
    public boolean canAttemptLogin(String accountIdentifier) {
        return !isAccountLocked(accountIdentifier);
    }

    /**
     * 获取登录安全信息
     *
     * @param accountIdentifier 账号标识符
     * @return 安全信息描述
     */
    public String getSecurityInfo(String accountIdentifier) {
        if (isAccountLocked(accountIdentifier)) {
            long remainingTime = getRemainingLockTime(accountIdentifier);
            return String.format("账号已被锁定，剩余时间：%d分钟", remainingTime / 60);
        }
        
        int failureCount = getLoginFailureCount(accountIdentifier);
        if (failureCount > 0) {
            int remainingAttempts = MAX_LOGIN_ATTEMPTS - failureCount;
            return String.format("登录失败%d次，还可尝试%d次", failureCount, remainingAttempts);
        }
        
        return "账号状态正常";
    }
}
