package com.vida.xinye.x2.component;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;

/**
 * 登录性能监控组件
 * 监控登录接口的性能指标
 *
 * <AUTHOR>
 * @date 2025/08/12
 */
@Slf4j
@Component
public class LoginPerformanceMonitor {

    // 性能统计数据
    private final ConcurrentHashMap<String, PerformanceMetrics> metricsMap = new ConcurrentHashMap<>();
    
    // 缓存命中率统计
    private final ConcurrentHashMap<String, CacheMetrics> cacheMetricsMap = new ConcurrentHashMap<>();

    /**
     * 记录登录性能指标
     *
     * @param loginMethod 登录方式
     * @param responseTime 响应时间（毫秒）
     * @param success 是否成功
     */
    public void recordLoginMetrics(String loginMethod, long responseTime, boolean success) {
        PerformanceMetrics metrics = metricsMap.computeIfAbsent(loginMethod, k -> new PerformanceMetrics());
        
        metrics.totalRequests.increment();
        if (success) {
            metrics.successRequests.increment();
        } else {
            metrics.failedRequests.increment();
        }
        
        metrics.totalResponseTime.add(responseTime);
        
        // 更新最大和最小响应时间
        updateMinMaxResponseTime(metrics, responseTime);
        
        // 记录慢请求
        if (responseTime > 1000) { // 超过1秒的请求
            metrics.slowRequests.increment();
            log.warn("检测到慢登录请求 - 登录方式: {}, 响应时间: {}ms", loginMethod, responseTime);
        }
        
        log.debug("记录登录性能指标 - 登录方式: {}, 响应时间: {}ms, 成功: {}", 
                 loginMethod, responseTime, success);
    }

    /**
     * 记录缓存命中率
     *
     * @param cacheType 缓存类型
     * @param hit 是否命中
     */
    public void recordCacheHit(String cacheType, boolean hit) {
        CacheMetrics metrics = cacheMetricsMap.computeIfAbsent(cacheType, k -> new CacheMetrics());
        
        metrics.totalAccess.increment();
        if (hit) {
            metrics.hitCount.increment();
        } else {
            metrics.missCount.increment();
        }
        
        log.debug("记录缓存指标 - 缓存类型: {}, 命中: {}", cacheType, hit);
    }

    /**
     * 获取登录性能报告
     *
     * @return 性能报告
     */
    public String getPerformanceReport() {
        StringBuilder report = new StringBuilder();
        report.append("\n=== 登录性能监控报告 ===\n");
        
        for (String loginMethod : metricsMap.keySet()) {
            PerformanceMetrics metrics = metricsMap.get(loginMethod);
            report.append(formatLoginMetrics(loginMethod, metrics));
        }
        
        report.append("\n=== 缓存性能监控报告 ===\n");
        for (String cacheType : cacheMetricsMap.keySet()) {
            CacheMetrics metrics = cacheMetricsMap.get(cacheType);
            report.append(formatCacheMetrics(cacheType, metrics));
        }
        
        return report.toString();
    }

    /**
     * 获取特定登录方式的性能指标
     *
     * @param loginMethod 登录方式
     * @return 性能指标
     */
    public PerformanceMetrics getLoginMetrics(String loginMethod) {
        return metricsMap.get(loginMethod);
    }

    /**
     * 获取特定缓存类型的性能指标
     *
     * @param cacheType 缓存类型
     * @return 缓存指标
     */
    public CacheMetrics getCacheMetrics(String cacheType) {
        return cacheMetricsMap.get(cacheType);
    }

    /**
     * 重置所有性能指标
     */
    public void resetMetrics() {
        metricsMap.clear();
        cacheMetricsMap.clear();
        log.info("性能监控指标已重置");
    }

    /**
     * 检查性能告警
     */
    public void checkPerformanceAlerts() {
        for (String loginMethod : metricsMap.keySet()) {
            PerformanceMetrics metrics = metricsMap.get(loginMethod);
            
            // 检查成功率告警
            double successRate = calculateSuccessRate(metrics);
            if (successRate < 0.95) { // 成功率低于95%
                log.warn("登录成功率告警 - 登录方式: {}, 成功率: {:.2f}%", loginMethod, successRate * 100);
            }
            
            // 检查平均响应时间告警
            double avgResponseTime = calculateAverageResponseTime(metrics);
            if (avgResponseTime > 500) { // 平均响应时间超过500ms
                log.warn("登录响应时间告警 - 登录方式: {}, 平均响应时间: {:.2f}ms", loginMethod, avgResponseTime);
            }
            
            // 检查慢请求比例告警
            double slowRequestRate = calculateSlowRequestRate(metrics);
            if (slowRequestRate > 0.1) { // 慢请求比例超过10%
                log.warn("慢请求比例告警 - 登录方式: {}, 慢请求比例: {:.2f}%", loginMethod, slowRequestRate * 100);
            }
        }
        
        // 检查缓存命中率告警
        for (String cacheType : cacheMetricsMap.keySet()) {
            CacheMetrics metrics = cacheMetricsMap.get(cacheType);
            double hitRate = calculateCacheHitRate(metrics);
            if (hitRate < 0.8) { // 缓存命中率低于80%
                log.warn("缓存命中率告警 - 缓存类型: {}, 命中率: {:.2f}%", cacheType, hitRate * 100);
            }
        }
    }

    /**
     * 更新最大最小响应时间
     */
    private void updateMinMaxResponseTime(PerformanceMetrics metrics, long responseTime) {
        // 更新最小响应时间
        long currentMin = metrics.minResponseTime.get();
        while (responseTime < currentMin) {
            if (metrics.minResponseTime.compareAndSet(currentMin, responseTime)) {
                break;
            }
            currentMin = metrics.minResponseTime.get();
        }
        
        // 更新最大响应时间
        long currentMax = metrics.maxResponseTime.get();
        while (responseTime > currentMax) {
            if (metrics.maxResponseTime.compareAndSet(currentMax, responseTime)) {
                break;
            }
            currentMax = metrics.maxResponseTime.get();
        }
    }

    /**
     * 格式化登录性能指标
     */
    private String formatLoginMetrics(String loginMethod, PerformanceMetrics metrics) {
        double successRate = calculateSuccessRate(metrics);
        double avgResponseTime = calculateAverageResponseTime(metrics);
        double slowRequestRate = calculateSlowRequestRate(metrics);
        
        return String.format(
            "登录方式: %s\n" +
            "  总请求数: %d\n" +
            "  成功请求: %d\n" +
            "  失败请求: %d\n" +
            "  成功率: %.2f%%\n" +
            "  平均响应时间: %.2fms\n" +
            "  最小响应时间: %dms\n" +
            "  最大响应时间: %dms\n" +
            "  慢请求数: %d\n" +
            "  慢请求比例: %.2f%%\n\n",
            loginMethod,
            metrics.totalRequests.sum(),
            metrics.successRequests.sum(),
            metrics.failedRequests.sum(),
            successRate * 100,
            avgResponseTime,
            metrics.minResponseTime.get(),
            metrics.maxResponseTime.get(),
            metrics.slowRequests.sum(),
            slowRequestRate * 100
        );
    }

    /**
     * 格式化缓存性能指标
     */
    private String formatCacheMetrics(String cacheType, CacheMetrics metrics) {
        double hitRate = calculateCacheHitRate(metrics);
        
        return String.format(
            "缓存类型: %s\n" +
            "  总访问数: %d\n" +
            "  命中次数: %d\n" +
            "  未命中次数: %d\n" +
            "  命中率: %.2f%%\n\n",
            cacheType,
            metrics.totalAccess.sum(),
            metrics.hitCount.sum(),
            metrics.missCount.sum(),
            hitRate * 100
        );
    }

    /**
     * 计算成功率
     */
    private double calculateSuccessRate(PerformanceMetrics metrics) {
        long total = metrics.totalRequests.sum();
        if (total == 0) return 0.0;
        return (double) metrics.successRequests.sum() / total;
    }

    /**
     * 计算平均响应时间
     */
    private double calculateAverageResponseTime(PerformanceMetrics metrics) {
        long total = metrics.totalRequests.sum();
        if (total == 0) return 0.0;
        return (double) metrics.totalResponseTime.sum() / total;
    }

    /**
     * 计算慢请求比例
     */
    private double calculateSlowRequestRate(PerformanceMetrics metrics) {
        long total = metrics.totalRequests.sum();
        if (total == 0) return 0.0;
        return (double) metrics.slowRequests.sum() / total;
    }

    /**
     * 计算缓存命中率
     */
    private double calculateCacheHitRate(CacheMetrics metrics) {
        long total = metrics.totalAccess.sum();
        if (total == 0) return 0.0;
        return (double) metrics.hitCount.sum() / total;
    }

    /**
     * 性能指标数据结构
     */
    public static class PerformanceMetrics {
        public final LongAdder totalRequests = new LongAdder();
        public final LongAdder successRequests = new LongAdder();
        public final LongAdder failedRequests = new LongAdder();
        public final LongAdder totalResponseTime = new LongAdder();
        public final LongAdder slowRequests = new LongAdder();
        public final AtomicLong minResponseTime = new AtomicLong(Long.MAX_VALUE);
        public final AtomicLong maxResponseTime = new AtomicLong(0);
    }

    /**
     * 缓存指标数据结构
     */
    public static class CacheMetrics {
        public final LongAdder totalAccess = new LongAdder();
        public final LongAdder hitCount = new LongAdder();
        public final LongAdder missCount = new LongAdder();
    }
}
