package com.vida.xinye.x2.mbg.mapper;

import com.vida.xinye.x2.mbg.model.XkwGrade;
import com.vida.xinye.x2.mbg.model.XkwGradeExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface XkwGradeMapper {
    long countByExample(XkwGradeExample example);

    int deleteByExample(XkwGradeExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(XkwGrade row);

    int insertSelective(XkwGrade row);

    List<XkwGrade> selectByExample(XkwGradeExample example);

    XkwGrade selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("row") XkwGrade row, @Param("example") XkwGradeExample example);

    int updateByExample(@Param("row") XkwGrade row, @Param("example") XkwGradeExample example);

    int updateByPrimaryKeySelective(XkwGrade row);

    int updateByPrimaryKey(XkwGrade row);
}