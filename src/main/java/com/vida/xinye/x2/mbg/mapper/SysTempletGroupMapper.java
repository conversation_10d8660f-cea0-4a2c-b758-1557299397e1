package com.vida.xinye.x2.mbg.mapper;

import com.vida.xinye.x2.mbg.model.SysTempletGroup;
import com.vida.xinye.x2.mbg.model.SysTempletGroupExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SysTempletGroupMapper {
    long countByExample(SysTempletGroupExample example);

    int deleteByExample(SysTempletGroupExample example);

    int deleteByPrimaryKey(Long id);

    int insert(SysTempletGroup row);

    int insertSelective(SysTempletGroup row);

    List<SysTempletGroup> selectByExample(SysTempletGroupExample example);

    SysTempletGroup selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("row") SysTempletGroup row, @Param("example") SysTempletGroupExample example);

    int updateByExample(@Param("row") SysTempletGroup row, @Param("example") SysTempletGroupExample example);

    int updateByPrimaryKeySelective(SysTempletGroup row);

    int updateByPrimaryKey(SysTempletGroup row);
}