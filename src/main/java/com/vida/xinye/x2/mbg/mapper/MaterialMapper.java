package com.vida.xinye.x2.mbg.mapper;

import com.vida.xinye.x2.mbg.model.Material;
import com.vida.xinye.x2.mbg.model.MaterialExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MaterialMapper {
    long countByExample(MaterialExample example);

    int deleteByExample(MaterialExample example);

    int deleteByPrimaryKey(Long id);

    int insert(Material row);

    int insertSelective(Material row);

    List<Material> selectByExample(MaterialExample example);

    Material selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("row") Material row, @Param("example") MaterialExample example);

    int updateByExample(@Param("row") Material row, @Param("example") MaterialExample example);

    int updateByPrimaryKeySelective(Material row);

    int updateByPrimaryKey(Material row);
}