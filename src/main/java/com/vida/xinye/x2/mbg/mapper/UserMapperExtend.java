package com.vida.xinye.x2.mbg.mapper;

import com.vida.xinye.x2.mbg.model.User;
import com.vida.xinye.x2.mbg.model.UserExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface UserMapperExtend extends UserMapper {
    int updateProfile(@Param("userId") Long userId,
                      @Param("roleType") String roleType,
                      @Param("gradeType") String gradeType);

    int updateParentMode(@Param("userId") Long userId,
                         @Param("parentMode") Integer parentMode,
                         @Param("parentName") String parentName,
                         @Param("parentIdcard") String parentIdcard,
                         @Param("teenagerPassword") String teenagerPassword);
}