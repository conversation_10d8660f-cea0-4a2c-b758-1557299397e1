package com.vida.xinye.x2.mbg.mapper;

import com.vida.xinye.x2.mbg.model.UserThirdPartyAuth;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户第三方登录信息Mapper（新版统一设计）
 * 
 * <AUTHOR>
 * @date 2025-08-13
 */
@Mapper
public interface UserThirdPartyAuthMapper {
    
    /**
     * 插入记录
     */
    int insert(UserThirdPartyAuth record);
    
    /**
     * 选择性插入记录
     */
    int insertSelective(UserThirdPartyAuth record);
    
    /**
     * 根据主键查询
     */
    UserThirdPartyAuth selectByPrimaryKey(Long id);
    
    /**
     * 选择性更新
     */
    int updateByPrimaryKeySelective(UserThirdPartyAuth record);
    
    /**
     * 根据主键更新
     */
    int updateByPrimaryKey(UserThirdPartyAuth record);
    
    /**
     * 根据主键删除
     */
    int deleteByPrimaryKey(Long id);
    
    // ========== 业务查询方法 ==========
    
    /**
     * 根据用户ID查询所有第三方绑定信息
     */
    List<UserThirdPartyAuth> selectByUserId(@Param("userId") Long userId);
    
    /**
     * 根据用户ID和平台查询
     */
    UserThirdPartyAuth selectByUserIdAndProvider(@Param("userId") Long userId, 
                                                 @Param("provider") String provider);
    
    /**
     * 根据平台和openId查询
     */
    UserThirdPartyAuth selectByProviderAndOpenId(@Param("provider") String provider, 
                                                 @Param("openId") String openId);
    
    /**
     * 根据平台和unionId查询
     */
    UserThirdPartyAuth selectByProviderAndUnionId(@Param("provider") String provider, 
                                                  @Param("unionId") String unionId);
    
    /**
     * 根据unionId查询（跨平台）
     */
    List<UserThirdPartyAuth> selectByUnionId(@Param("unionId") String unionId);
    
    /**
     * 更新最后登录时间
     */
    int updateLastLoginTime(@Param("id") Long id, @Param("lastLoginTime") java.util.Date lastLoginTime);
    
    /**
     * 根据用户ID和平台删除绑定
     */
    int deleteByUserIdAndProvider(@Param("userId") Long userId, @Param("provider") String provider);
    
    /**
     * 统计用户绑定的第三方平台数量
     */
    int countByUserId(@Param("userId") Long userId);
    
    /**
     * 查询用户绑定的所有平台
     */
    List<String> selectProvidersByUserId(@Param("userId") Long userId);
}
