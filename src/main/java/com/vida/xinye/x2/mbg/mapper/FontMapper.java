package com.vida.xinye.x2.mbg.mapper;

import com.vida.xinye.x2.mbg.model.Font;
import com.vida.xinye.x2.mbg.model.FontExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface FontMapper {
    long countByExample(FontExample example);

    int deleteByExample(FontExample example);

    int deleteByPrimaryKey(Long id);

    int insert(Font row);

    int insertSelective(Font row);

    List<Font> selectByExample(FontExample example);

    Font selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("row") Font row, @Param("example") FontExample example);

    int updateByExample(@Param("row") Font row, @Param("example") FontExample example);

    int updateByPrimaryKeySelective(Font row);

    int updateByPrimaryKey(Font row);
}