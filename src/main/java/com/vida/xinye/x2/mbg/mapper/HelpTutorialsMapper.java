package com.vida.xinye.x2.mbg.mapper;

import com.vida.xinye.x2.mbg.model.HelpTutorials;
import com.vida.xinye.x2.mbg.model.HelpTutorialsExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface HelpTutorialsMapper {
    long countByExample(HelpTutorialsExample example);

    int deleteByExample(HelpTutorialsExample example);

    int deleteByPrimaryKey(Long id);

    int insert(HelpTutorials row);

    int insertSelective(HelpTutorials row);

    List<HelpTutorials> selectByExample(HelpTutorialsExample example);

    HelpTutorials selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("row") HelpTutorials row, @Param("example") HelpTutorialsExample example);

    int updateByExample(@Param("row") HelpTutorials row, @Param("example") HelpTutorialsExample example);

    int updateByPrimaryKeySelective(HelpTutorials row);

    int updateByPrimaryKey(HelpTutorials row);
}