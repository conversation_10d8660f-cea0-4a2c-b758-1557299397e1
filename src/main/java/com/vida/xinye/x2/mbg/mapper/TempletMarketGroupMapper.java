package com.vida.xinye.x2.mbg.mapper;

import com.vida.xinye.x2.mbg.model.TempletMarketGroup;
import com.vida.xinye.x2.mbg.model.TempletMarketGroupExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TempletMarketGroupMapper {
    long countByExample(TempletMarketGroupExample example);

    int deleteByExample(TempletMarketGroupExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TempletMarketGroup row);

    int insertSelective(TempletMarketGroup row);

    List<TempletMarketGroup> selectByExample(TempletMarketGroupExample example);

    TempletMarketGroup selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("row") TempletMarketGroup row, @Param("example") TempletMarketGroupExample example);

    int updateByExample(@Param("row") TempletMarketGroup row, @Param("example") TempletMarketGroupExample example);

    int updateByPrimaryKeySelective(TempletMarketGroup row);

    int updateByPrimaryKey(TempletMarketGroup row);
}