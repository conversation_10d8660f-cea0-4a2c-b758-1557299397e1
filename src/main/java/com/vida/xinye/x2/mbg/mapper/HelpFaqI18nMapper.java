package com.vida.xinye.x2.mbg.mapper;

import com.vida.xinye.x2.mbg.model.HelpFaqI18n;
import com.vida.xinye.x2.mbg.model.HelpFaqI18nExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface HelpFaqI18nMapper {
    long countByExample(HelpFaqI18nExample example);

    int deleteByExample(HelpFaqI18nExample example);

    int deleteByPrimaryKey(Long id);

    int insert(HelpFaqI18n row);

    int insertSelective(HelpFaqI18n row);

    List<HelpFaqI18n> selectByExampleWithBLOBs(HelpFaqI18nExample example);

    List<HelpFaqI18n> selectByExample(HelpFaqI18nExample example);

    HelpFaqI18n selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("row") HelpFaqI18n row, @Param("example") HelpFaqI18nExample example);

    int updateByExampleWithBLOBs(@Param("row") HelpFaqI18n row, @Param("example") HelpFaqI18nExample example);

    int updateByExample(@Param("row") HelpFaqI18n row, @Param("example") HelpFaqI18nExample example);

    int updateByPrimaryKeySelective(HelpFaqI18n row);

    int updateByPrimaryKeyWithBLOBs(HelpFaqI18n row);

    int updateByPrimaryKey(HelpFaqI18n row);
}