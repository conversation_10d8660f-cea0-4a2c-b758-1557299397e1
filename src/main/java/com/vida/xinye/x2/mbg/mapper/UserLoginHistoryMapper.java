package com.vida.xinye.x2.mbg.mapper;

import com.vida.xinye.x2.mbg.model.UserLoginHistory;
import com.vida.xinye.x2.mbg.model.UserLoginHistoryExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface UserLoginHistoryMapper {
    long countByExample(UserLoginHistoryExample example);

    int deleteByExample(UserLoginHistoryExample example);

    int deleteByPrimaryKey(Long id);

    int insert(UserLoginHistory record);

    int insertSelective(UserLoginHistory record);

    List<UserLoginHistory> selectByExample(UserLoginHistoryExample example);

    UserLoginHistory selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") UserLoginHistory record, @Param("example") UserLoginHistoryExample example);

    int updateByExample(@Param("record") UserLoginHistory record, @Param("example") UserLoginHistoryExample example);

    int updateByPrimaryKeySelective(UserLoginHistory record);

    int updateByPrimaryKey(UserLoginHistory record);

    /**
     * 根据用户ID查询登录历史
     */
    List<UserLoginHistory> selectByUserId(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 查询用户最近一次登录记录
     */
    UserLoginHistory selectLatestByUserId(Long userId);

    /**
     * 根据登录方式统计登录次数
     */
    long countByUserIdAndLoginWay(@Param("userId") Long userId, @Param("loginWay") String loginWay);

    /**
     * 查询指定时间范围内的登录记录
     */
    List<UserLoginHistory> selectByTimeRange(@Param("userId") Long userId, 
                                           @Param("startTime") java.util.Date startTime, 
                                           @Param("endTime") java.util.Date endTime);
}
