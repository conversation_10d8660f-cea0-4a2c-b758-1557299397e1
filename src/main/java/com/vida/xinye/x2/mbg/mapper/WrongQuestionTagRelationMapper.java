package com.vida.xinye.x2.mbg.mapper;

import com.vida.xinye.x2.mbg.model.WrongQuestionTagRelation;
import com.vida.xinye.x2.mbg.model.WrongQuestionTagRelationExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface WrongQuestionTagRelationMapper {
    long countByExample(WrongQuestionTagRelationExample example);

    int deleteByExample(WrongQuestionTagRelationExample example);

    int deleteByPrimaryKey(Long id);

    int insert(WrongQuestionTagRelation row);

    int insertSelective(WrongQuestionTagRelation row);

    List<WrongQuestionTagRelation> selectByExample(WrongQuestionTagRelationExample example);

    WrongQuestionTagRelation selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("row") WrongQuestionTagRelation row, @Param("example") WrongQuestionTagRelationExample example);

    int updateByExample(@Param("row") WrongQuestionTagRelation row, @Param("example") WrongQuestionTagRelationExample example);

    int updateByPrimaryKeySelective(WrongQuestionTagRelation row);

    int updateByPrimaryKey(WrongQuestionTagRelation row);
}