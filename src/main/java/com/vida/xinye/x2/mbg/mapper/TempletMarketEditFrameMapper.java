package com.vida.xinye.x2.mbg.mapper;

import com.vida.xinye.x2.mbg.model.TempletMarketEditFrame;
import com.vida.xinye.x2.mbg.model.TempletMarketEditFrameExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TempletMarketEditFrameMapper {
    long countByExample(TempletMarketEditFrameExample example);

    int deleteByExample(TempletMarketEditFrameExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TempletMarketEditFrame row);

    int insertSelective(TempletMarketEditFrame row);

    List<TempletMarketEditFrame> selectByExample(TempletMarketEditFrameExample example);

    TempletMarketEditFrame selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("row") TempletMarketEditFrame row, @Param("example") TempletMarketEditFrameExample example);

    int updateByExample(@Param("row") TempletMarketEditFrame row, @Param("example") TempletMarketEditFrameExample example);

    int updateByPrimaryKeySelective(TempletMarketEditFrame row);

    int updateByPrimaryKey(TempletMarketEditFrame row);
}