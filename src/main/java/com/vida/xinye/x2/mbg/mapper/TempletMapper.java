package com.vida.xinye.x2.mbg.mapper;

import com.vida.xinye.x2.mbg.model.Templet;
import com.vida.xinye.x2.mbg.model.TempletExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TempletMapper {
    long countByExample(TempletExample example);

    int deleteByExample(TempletExample example);

    int deleteByPrimaryKey(Long id);

    int insert(Templet row);

    int insertSelective(Templet row);

    List<Templet> selectByExampleWithBLOBs(TempletExample example);

    List<Templet> selectByExample(TempletExample example);

    Templet selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("row") Templet row, @Param("example") TempletExample example);

    int updateByExampleWithBLOBs(@Param("row") Templet row, @Param("example") TempletExample example);

    int updateByExample(@Param("row") Templet row, @Param("example") TempletExample example);

    int updateByPrimaryKeySelective(Templet row);

    int updateByPrimaryKeyWithBLOBs(Templet row);

    int updateByPrimaryKey(Templet row);
}