package com.vida.xinye.x2.mbg.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TempletMarketImageTemplateExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public TempletMarketImageTemplateExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andGroupIdIsNull() {
            addCriterion("group_id is null");
            return (Criteria) this;
        }

        public Criteria andGroupIdIsNotNull() {
            addCriterion("group_id is not null");
            return (Criteria) this;
        }

        public Criteria andGroupIdEqualTo(Long value) {
            addCriterion("group_id =", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdNotEqualTo(Long value) {
            addCriterion("group_id <>", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdGreaterThan(Long value) {
            addCriterion("group_id >", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdGreaterThanOrEqualTo(Long value) {
            addCriterion("group_id >=", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdLessThan(Long value) {
            addCriterion("group_id <", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdLessThanOrEqualTo(Long value) {
            addCriterion("group_id <=", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdIn(List<Long> values) {
            addCriterion("group_id in", values, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdNotIn(List<Long> values) {
            addCriterion("group_id not in", values, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdBetween(Long value1, Long value2) {
            addCriterion("group_id between", value1, value2, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdNotBetween(Long value1, Long value2) {
            addCriterion("group_id not between", value1, value2, "groupId");
            return (Criteria) this;
        }

        public Criteria andThumbPicIsNull() {
            addCriterion("thumb_pic is null");
            return (Criteria) this;
        }

        public Criteria andThumbPicIsNotNull() {
            addCriterion("thumb_pic is not null");
            return (Criteria) this;
        }

        public Criteria andThumbPicEqualTo(String value) {
            addCriterion("thumb_pic =", value, "thumbPic");
            return (Criteria) this;
        }

        public Criteria andThumbPicNotEqualTo(String value) {
            addCriterion("thumb_pic <>", value, "thumbPic");
            return (Criteria) this;
        }

        public Criteria andThumbPicGreaterThan(String value) {
            addCriterion("thumb_pic >", value, "thumbPic");
            return (Criteria) this;
        }

        public Criteria andThumbPicGreaterThanOrEqualTo(String value) {
            addCriterion("thumb_pic >=", value, "thumbPic");
            return (Criteria) this;
        }

        public Criteria andThumbPicLessThan(String value) {
            addCriterion("thumb_pic <", value, "thumbPic");
            return (Criteria) this;
        }

        public Criteria andThumbPicLessThanOrEqualTo(String value) {
            addCriterion("thumb_pic <=", value, "thumbPic");
            return (Criteria) this;
        }

        public Criteria andThumbPicLike(String value) {
            addCriterion("thumb_pic like", value, "thumbPic");
            return (Criteria) this;
        }

        public Criteria andThumbPicNotLike(String value) {
            addCriterion("thumb_pic not like", value, "thumbPic");
            return (Criteria) this;
        }

        public Criteria andThumbPicIn(List<String> values) {
            addCriterion("thumb_pic in", values, "thumbPic");
            return (Criteria) this;
        }

        public Criteria andThumbPicNotIn(List<String> values) {
            addCriterion("thumb_pic not in", values, "thumbPic");
            return (Criteria) this;
        }

        public Criteria andThumbPicBetween(String value1, String value2) {
            addCriterion("thumb_pic between", value1, value2, "thumbPic");
            return (Criteria) this;
        }

        public Criteria andThumbPicNotBetween(String value1, String value2) {
            addCriterion("thumb_pic not between", value1, value2, "thumbPic");
            return (Criteria) this;
        }

        public Criteria andCellWidthIsNull() {
            addCriterion("cell_width is null");
            return (Criteria) this;
        }

        public Criteria andCellWidthIsNotNull() {
            addCriterion("cell_width is not null");
            return (Criteria) this;
        }

        public Criteria andCellWidthEqualTo(Integer value) {
            addCriterion("cell_width =", value, "cellWidth");
            return (Criteria) this;
        }

        public Criteria andCellWidthNotEqualTo(Integer value) {
            addCriterion("cell_width <>", value, "cellWidth");
            return (Criteria) this;
        }

        public Criteria andCellWidthGreaterThan(Integer value) {
            addCriterion("cell_width >", value, "cellWidth");
            return (Criteria) this;
        }

        public Criteria andCellWidthGreaterThanOrEqualTo(Integer value) {
            addCriterion("cell_width >=", value, "cellWidth");
            return (Criteria) this;
        }

        public Criteria andCellWidthLessThan(Integer value) {
            addCriterion("cell_width <", value, "cellWidth");
            return (Criteria) this;
        }

        public Criteria andCellWidthLessThanOrEqualTo(Integer value) {
            addCriterion("cell_width <=", value, "cellWidth");
            return (Criteria) this;
        }

        public Criteria andCellWidthIn(List<Integer> values) {
            addCriterion("cell_width in", values, "cellWidth");
            return (Criteria) this;
        }

        public Criteria andCellWidthNotIn(List<Integer> values) {
            addCriterion("cell_width not in", values, "cellWidth");
            return (Criteria) this;
        }

        public Criteria andCellWidthBetween(Integer value1, Integer value2) {
            addCriterion("cell_width between", value1, value2, "cellWidth");
            return (Criteria) this;
        }

        public Criteria andCellWidthNotBetween(Integer value1, Integer value2) {
            addCriterion("cell_width not between", value1, value2, "cellWidth");
            return (Criteria) this;
        }

        public Criteria andCellHeightIsNull() {
            addCriterion("cell_height is null");
            return (Criteria) this;
        }

        public Criteria andCellHeightIsNotNull() {
            addCriterion("cell_height is not null");
            return (Criteria) this;
        }

        public Criteria andCellHeightEqualTo(Integer value) {
            addCriterion("cell_height =", value, "cellHeight");
            return (Criteria) this;
        }

        public Criteria andCellHeightNotEqualTo(Integer value) {
            addCriterion("cell_height <>", value, "cellHeight");
            return (Criteria) this;
        }

        public Criteria andCellHeightGreaterThan(Integer value) {
            addCriterion("cell_height >", value, "cellHeight");
            return (Criteria) this;
        }

        public Criteria andCellHeightGreaterThanOrEqualTo(Integer value) {
            addCriterion("cell_height >=", value, "cellHeight");
            return (Criteria) this;
        }

        public Criteria andCellHeightLessThan(Integer value) {
            addCriterion("cell_height <", value, "cellHeight");
            return (Criteria) this;
        }

        public Criteria andCellHeightLessThanOrEqualTo(Integer value) {
            addCriterion("cell_height <=", value, "cellHeight");
            return (Criteria) this;
        }

        public Criteria andCellHeightIn(List<Integer> values) {
            addCriterion("cell_height in", values, "cellHeight");
            return (Criteria) this;
        }

        public Criteria andCellHeightNotIn(List<Integer> values) {
            addCriterion("cell_height not in", values, "cellHeight");
            return (Criteria) this;
        }

        public Criteria andCellHeightBetween(Integer value1, Integer value2) {
            addCriterion("cell_height between", value1, value2, "cellHeight");
            return (Criteria) this;
        }

        public Criteria andCellHeightNotBetween(Integer value1, Integer value2) {
            addCriterion("cell_height not between", value1, value2, "cellHeight");
            return (Criteria) this;
        }

        public Criteria andEditPicIsNull() {
            addCriterion("edit_pic is null");
            return (Criteria) this;
        }

        public Criteria andEditPicIsNotNull() {
            addCriterion("edit_pic is not null");
            return (Criteria) this;
        }

        public Criteria andEditPicEqualTo(String value) {
            addCriterion("edit_pic =", value, "editPic");
            return (Criteria) this;
        }

        public Criteria andEditPicNotEqualTo(String value) {
            addCriterion("edit_pic <>", value, "editPic");
            return (Criteria) this;
        }

        public Criteria andEditPicGreaterThan(String value) {
            addCriterion("edit_pic >", value, "editPic");
            return (Criteria) this;
        }

        public Criteria andEditPicGreaterThanOrEqualTo(String value) {
            addCriterion("edit_pic >=", value, "editPic");
            return (Criteria) this;
        }

        public Criteria andEditPicLessThan(String value) {
            addCriterion("edit_pic <", value, "editPic");
            return (Criteria) this;
        }

        public Criteria andEditPicLessThanOrEqualTo(String value) {
            addCriterion("edit_pic <=", value, "editPic");
            return (Criteria) this;
        }

        public Criteria andEditPicLike(String value) {
            addCriterion("edit_pic like", value, "editPic");
            return (Criteria) this;
        }

        public Criteria andEditPicNotLike(String value) {
            addCriterion("edit_pic not like", value, "editPic");
            return (Criteria) this;
        }

        public Criteria andEditPicIn(List<String> values) {
            addCriterion("edit_pic in", values, "editPic");
            return (Criteria) this;
        }

        public Criteria andEditPicNotIn(List<String> values) {
            addCriterion("edit_pic not in", values, "editPic");
            return (Criteria) this;
        }

        public Criteria andEditPicBetween(String value1, String value2) {
            addCriterion("edit_pic between", value1, value2, "editPic");
            return (Criteria) this;
        }

        public Criteria andEditPicNotBetween(String value1, String value2) {
            addCriterion("edit_pic not between", value1, value2, "editPic");
            return (Criteria) this;
        }

        public Criteria andFullLineIsNull() {
            addCriterion("full_line is null");
            return (Criteria) this;
        }

        public Criteria andFullLineIsNotNull() {
            addCriterion("full_line is not null");
            return (Criteria) this;
        }

        public Criteria andFullLineEqualTo(Boolean value) {
            addCriterion("full_line =", value, "fullLine");
            return (Criteria) this;
        }

        public Criteria andFullLineNotEqualTo(Boolean value) {
            addCriterion("full_line <>", value, "fullLine");
            return (Criteria) this;
        }

        public Criteria andFullLineGreaterThan(Boolean value) {
            addCriterion("full_line >", value, "fullLine");
            return (Criteria) this;
        }

        public Criteria andFullLineGreaterThanOrEqualTo(Boolean value) {
            addCriterion("full_line >=", value, "fullLine");
            return (Criteria) this;
        }

        public Criteria andFullLineLessThan(Boolean value) {
            addCriterion("full_line <", value, "fullLine");
            return (Criteria) this;
        }

        public Criteria andFullLineLessThanOrEqualTo(Boolean value) {
            addCriterion("full_line <=", value, "fullLine");
            return (Criteria) this;
        }

        public Criteria andFullLineIn(List<Boolean> values) {
            addCriterion("full_line in", values, "fullLine");
            return (Criteria) this;
        }

        public Criteria andFullLineNotIn(List<Boolean> values) {
            addCriterion("full_line not in", values, "fullLine");
            return (Criteria) this;
        }

        public Criteria andFullLineBetween(Boolean value1, Boolean value2) {
            addCriterion("full_line between", value1, value2, "fullLine");
            return (Criteria) this;
        }

        public Criteria andFullLineNotBetween(Boolean value1, Boolean value2) {
            addCriterion("full_line not between", value1, value2, "fullLine");
            return (Criteria) this;
        }

        public Criteria andBgviewHeightIsNull() {
            addCriterion("bgview_height is null");
            return (Criteria) this;
        }

        public Criteria andBgviewHeightIsNotNull() {
            addCriterion("bgview_height is not null");
            return (Criteria) this;
        }

        public Criteria andBgviewHeightEqualTo(Integer value) {
            addCriterion("bgview_height =", value, "bgviewHeight");
            return (Criteria) this;
        }

        public Criteria andBgviewHeightNotEqualTo(Integer value) {
            addCriterion("bgview_height <>", value, "bgviewHeight");
            return (Criteria) this;
        }

        public Criteria andBgviewHeightGreaterThan(Integer value) {
            addCriterion("bgview_height >", value, "bgviewHeight");
            return (Criteria) this;
        }

        public Criteria andBgviewHeightGreaterThanOrEqualTo(Integer value) {
            addCriterion("bgview_height >=", value, "bgviewHeight");
            return (Criteria) this;
        }

        public Criteria andBgviewHeightLessThan(Integer value) {
            addCriterion("bgview_height <", value, "bgviewHeight");
            return (Criteria) this;
        }

        public Criteria andBgviewHeightLessThanOrEqualTo(Integer value) {
            addCriterion("bgview_height <=", value, "bgviewHeight");
            return (Criteria) this;
        }

        public Criteria andBgviewHeightIn(List<Integer> values) {
            addCriterion("bgview_height in", values, "bgviewHeight");
            return (Criteria) this;
        }

        public Criteria andBgviewHeightNotIn(List<Integer> values) {
            addCriterion("bgview_height not in", values, "bgviewHeight");
            return (Criteria) this;
        }

        public Criteria andBgviewHeightBetween(Integer value1, Integer value2) {
            addCriterion("bgview_height between", value1, value2, "bgviewHeight");
            return (Criteria) this;
        }

        public Criteria andBgviewHeightNotBetween(Integer value1, Integer value2) {
            addCriterion("bgview_height not between", value1, value2, "bgviewHeight");
            return (Criteria) this;
        }

        public Criteria andRowsIsNull() {
            addCriterion("rows is null");
            return (Criteria) this;
        }

        public Criteria andRowsIsNotNull() {
            addCriterion("rows is not null");
            return (Criteria) this;
        }

        public Criteria andRowsEqualTo(Integer value) {
            addCriterion("rows =", value, "rows");
            return (Criteria) this;
        }

        public Criteria andRowsNotEqualTo(Integer value) {
            addCriterion("rows <>", value, "rows");
            return (Criteria) this;
        }

        public Criteria andRowsGreaterThan(Integer value) {
            addCriterion("rows >", value, "rows");
            return (Criteria) this;
        }

        public Criteria andRowsGreaterThanOrEqualTo(Integer value) {
            addCriterion("rows >=", value, "rows");
            return (Criteria) this;
        }

        public Criteria andRowsLessThan(Integer value) {
            addCriterion("rows <", value, "rows");
            return (Criteria) this;
        }

        public Criteria andRowsLessThanOrEqualTo(Integer value) {
            addCriterion("rows <=", value, "rows");
            return (Criteria) this;
        }

        public Criteria andRowsIn(List<Integer> values) {
            addCriterion("rows in", values, "rows");
            return (Criteria) this;
        }

        public Criteria andRowsNotIn(List<Integer> values) {
            addCriterion("rows not in", values, "rows");
            return (Criteria) this;
        }

        public Criteria andRowsBetween(Integer value1, Integer value2) {
            addCriterion("rows between", value1, value2, "rows");
            return (Criteria) this;
        }

        public Criteria andRowsNotBetween(Integer value1, Integer value2) {
            addCriterion("rows not between", value1, value2, "rows");
            return (Criteria) this;
        }

        public Criteria andColumnsIsNull() {
            addCriterion("columns is null");
            return (Criteria) this;
        }

        public Criteria andColumnsIsNotNull() {
            addCriterion("columns is not null");
            return (Criteria) this;
        }

        public Criteria andColumnsEqualTo(Integer value) {
            addCriterion("columns =", value, "columns");
            return (Criteria) this;
        }

        public Criteria andColumnsNotEqualTo(Integer value) {
            addCriterion("columns <>", value, "columns");
            return (Criteria) this;
        }

        public Criteria andColumnsGreaterThan(Integer value) {
            addCriterion("columns >", value, "columns");
            return (Criteria) this;
        }

        public Criteria andColumnsGreaterThanOrEqualTo(Integer value) {
            addCriterion("columns >=", value, "columns");
            return (Criteria) this;
        }

        public Criteria andColumnsLessThan(Integer value) {
            addCriterion("columns <", value, "columns");
            return (Criteria) this;
        }

        public Criteria andColumnsLessThanOrEqualTo(Integer value) {
            addCriterion("columns <=", value, "columns");
            return (Criteria) this;
        }

        public Criteria andColumnsIn(List<Integer> values) {
            addCriterion("columns in", values, "columns");
            return (Criteria) this;
        }

        public Criteria andColumnsNotIn(List<Integer> values) {
            addCriterion("columns not in", values, "columns");
            return (Criteria) this;
        }

        public Criteria andColumnsBetween(Integer value1, Integer value2) {
            addCriterion("columns between", value1, value2, "columns");
            return (Criteria) this;
        }

        public Criteria andColumnsNotBetween(Integer value1, Integer value2) {
            addCriterion("columns not between", value1, value2, "columns");
            return (Criteria) this;
        }

        public Criteria andLineWidthIsNull() {
            addCriterion("line_width is null");
            return (Criteria) this;
        }

        public Criteria andLineWidthIsNotNull() {
            addCriterion("line_width is not null");
            return (Criteria) this;
        }

        public Criteria andLineWidthEqualTo(Integer value) {
            addCriterion("line_width =", value, "lineWidth");
            return (Criteria) this;
        }

        public Criteria andLineWidthNotEqualTo(Integer value) {
            addCriterion("line_width <>", value, "lineWidth");
            return (Criteria) this;
        }

        public Criteria andLineWidthGreaterThan(Integer value) {
            addCriterion("line_width >", value, "lineWidth");
            return (Criteria) this;
        }

        public Criteria andLineWidthGreaterThanOrEqualTo(Integer value) {
            addCriterion("line_width >=", value, "lineWidth");
            return (Criteria) this;
        }

        public Criteria andLineWidthLessThan(Integer value) {
            addCriterion("line_width <", value, "lineWidth");
            return (Criteria) this;
        }

        public Criteria andLineWidthLessThanOrEqualTo(Integer value) {
            addCriterion("line_width <=", value, "lineWidth");
            return (Criteria) this;
        }

        public Criteria andLineWidthIn(List<Integer> values) {
            addCriterion("line_width in", values, "lineWidth");
            return (Criteria) this;
        }

        public Criteria andLineWidthNotIn(List<Integer> values) {
            addCriterion("line_width not in", values, "lineWidth");
            return (Criteria) this;
        }

        public Criteria andLineWidthBetween(Integer value1, Integer value2) {
            addCriterion("line_width between", value1, value2, "lineWidth");
            return (Criteria) this;
        }

        public Criteria andLineWidthNotBetween(Integer value1, Integer value2) {
            addCriterion("line_width not between", value1, value2, "lineWidth");
            return (Criteria) this;
        }

        public Criteria andSortIsNull() {
            addCriterion("sort is null");
            return (Criteria) this;
        }

        public Criteria andSortIsNotNull() {
            addCriterion("sort is not null");
            return (Criteria) this;
        }

        public Criteria andSortEqualTo(Integer value) {
            addCriterion("sort =", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortNotEqualTo(Integer value) {
            addCriterion("sort <>", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortGreaterThan(Integer value) {
            addCriterion("sort >", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortGreaterThanOrEqualTo(Integer value) {
            addCriterion("sort >=", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortLessThan(Integer value) {
            addCriterion("sort <", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortLessThanOrEqualTo(Integer value) {
            addCriterion("sort <=", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortIn(List<Integer> values) {
            addCriterion("sort in", values, "sort");
            return (Criteria) this;
        }

        public Criteria andSortNotIn(List<Integer> values) {
            addCriterion("sort not in", values, "sort");
            return (Criteria) this;
        }

        public Criteria andSortBetween(Integer value1, Integer value2) {
            addCriterion("sort between", value1, value2, "sort");
            return (Criteria) this;
        }

        public Criteria andSortNotBetween(Integer value1, Integer value2) {
            addCriterion("sort not between", value1, value2, "sort");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}