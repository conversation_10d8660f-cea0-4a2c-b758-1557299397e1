package com.vida.xinye.x2.mbg.mapper;

import com.vida.xinye.x2.mbg.model.TempletBorder;
import com.vida.xinye.x2.mbg.model.TempletBorderExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TempletBorderMapper {

    long countByExample(TempletBorderExample example);

    int deleteByExample(TempletBorderExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TempletBorder row);

    int insertSelective(TempletBorder row);

    List<TempletBorder> selectByExample(TempletBorderExample example);

    TempletBorder selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("row") TempletBorder row, @Param("example") TempletBorderExample example);

    int updateByExample(@Param("row") TempletBorder row, @Param("example") TempletBorderExample example);

    int updateByPrimaryKeySelective(TempletBorder row);

    int updateByPrimaryKey(TempletBorder row);
}
