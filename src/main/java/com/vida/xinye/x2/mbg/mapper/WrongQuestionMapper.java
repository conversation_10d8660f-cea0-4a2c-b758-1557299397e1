package com.vida.xinye.x2.mbg.mapper;

import com.vida.xinye.x2.mbg.model.WrongQuestion;
import com.vida.xinye.x2.mbg.model.WrongQuestionExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface WrongQuestionMapper {
    long countByExample(WrongQuestionExample example);

    int deleteByExample(WrongQuestionExample example);

    int deleteByPrimaryKey(Long id);

    int insert(WrongQuestion row);

    int insertSelective(WrongQuestion row);

    List<WrongQuestion> selectByExampleWithBLOBs(WrongQuestionExample example);

    List<WrongQuestion> selectByExample(WrongQuestionExample example);

    WrongQuestion selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("row") WrongQuestion row, @Param("example") WrongQuestionExample example);

    int updateByExampleWithBLOBs(@Param("row") WrongQuestion row, @Param("example") WrongQuestionExample example);

    int updateByExample(@Param("row") WrongQuestion row, @Param("example") WrongQuestionExample example);

    int updateByPrimaryKeySelective(WrongQuestion row);

    int updateByPrimaryKeyWithBLOBs(WrongQuestion row);

    int updateByPrimaryKey(WrongQuestion row);
}