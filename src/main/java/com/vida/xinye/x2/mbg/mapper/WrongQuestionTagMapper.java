package com.vida.xinye.x2.mbg.mapper;

import com.vida.xinye.x2.mbg.model.WrongQuestionTag;
import com.vida.xinye.x2.mbg.model.WrongQuestionTagExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface WrongQuestionTagMapper {
    long countByExample(WrongQuestionTagExample example);

    int deleteByExample(WrongQuestionTagExample example);

    int deleteByPrimaryKey(Long id);

    int insert(WrongQuestionTag row);

    int insertSelective(WrongQuestionTag row);

    List<WrongQuestionTag> selectByExample(WrongQuestionTagExample example);

    WrongQuestionTag selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("row") WrongQuestionTag row, @Param("example") WrongQuestionTagExample example);

    int updateByExample(@Param("row") WrongQuestionTag row, @Param("example") WrongQuestionTagExample example);

    int updateByPrimaryKeySelective(WrongQuestionTag row);

    int updateByPrimaryKey(WrongQuestionTag row);
}