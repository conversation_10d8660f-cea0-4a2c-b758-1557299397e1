package com.vida.xinye.x2.mbg.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TempletMarketExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public TempletMarketExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andGroupIdIsNull() {
            addCriterion("group_id is null");
            return (Criteria) this;
        }

        public Criteria andGroupIdIsNotNull() {
            addCriterion("group_id is not null");
            return (Criteria) this;
        }

        public Criteria andGroupIdEqualTo(Long value) {
            addCriterion("group_id =", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdNotEqualTo(Long value) {
            addCriterion("group_id <>", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdGreaterThan(Long value) {
            addCriterion("group_id >", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdGreaterThanOrEqualTo(Long value) {
            addCriterion("group_id >=", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdLessThan(Long value) {
            addCriterion("group_id <", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdLessThanOrEqualTo(Long value) {
            addCriterion("group_id <=", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdIn(List<Long> values) {
            addCriterion("group_id in", values, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdNotIn(List<Long> values) {
            addCriterion("group_id not in", values, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdBetween(Long value1, Long value2) {
            addCriterion("group_id between", value1, value2, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdNotBetween(Long value1, Long value2) {
            addCriterion("group_id not between", value1, value2, "groupId");
            return (Criteria) this;
        }

        public Criteria andResurlWidthIsNull() {
            addCriterion("resUrl_width is null");
            return (Criteria) this;
        }

        public Criteria andResurlWidthIsNotNull() {
            addCriterion("resUrl_width is not null");
            return (Criteria) this;
        }

        public Criteria andResurlWidthEqualTo(Integer value) {
            addCriterion("resUrl_width =", value, "resurlWidth");
            return (Criteria) this;
        }

        public Criteria andResurlWidthNotEqualTo(Integer value) {
            addCriterion("resUrl_width <>", value, "resurlWidth");
            return (Criteria) this;
        }

        public Criteria andResurlWidthGreaterThan(Integer value) {
            addCriterion("resUrl_width >", value, "resurlWidth");
            return (Criteria) this;
        }

        public Criteria andResurlWidthGreaterThanOrEqualTo(Integer value) {
            addCriterion("resUrl_width >=", value, "resurlWidth");
            return (Criteria) this;
        }

        public Criteria andResurlWidthLessThan(Integer value) {
            addCriterion("resUrl_width <", value, "resurlWidth");
            return (Criteria) this;
        }

        public Criteria andResurlWidthLessThanOrEqualTo(Integer value) {
            addCriterion("resUrl_width <=", value, "resurlWidth");
            return (Criteria) this;
        }

        public Criteria andResurlWidthIn(List<Integer> values) {
            addCriterion("resUrl_width in", values, "resurlWidth");
            return (Criteria) this;
        }

        public Criteria andResurlWidthNotIn(List<Integer> values) {
            addCriterion("resUrl_width not in", values, "resurlWidth");
            return (Criteria) this;
        }

        public Criteria andResurlWidthBetween(Integer value1, Integer value2) {
            addCriterion("resUrl_width between", value1, value2, "resurlWidth");
            return (Criteria) this;
        }

        public Criteria andResurlWidthNotBetween(Integer value1, Integer value2) {
            addCriterion("resUrl_width not between", value1, value2, "resurlWidth");
            return (Criteria) this;
        }

        public Criteria andResurlPicIsNull() {
            addCriterion("resUrl_pic is null");
            return (Criteria) this;
        }

        public Criteria andResurlPicIsNotNull() {
            addCriterion("resUrl_pic is not null");
            return (Criteria) this;
        }

        public Criteria andResurlPicEqualTo(String value) {
            addCriterion("resUrl_pic =", value, "resurlPic");
            return (Criteria) this;
        }

        public Criteria andResurlPicNotEqualTo(String value) {
            addCriterion("resUrl_pic <>", value, "resurlPic");
            return (Criteria) this;
        }

        public Criteria andResurlPicGreaterThan(String value) {
            addCriterion("resUrl_pic >", value, "resurlPic");
            return (Criteria) this;
        }

        public Criteria andResurlPicGreaterThanOrEqualTo(String value) {
            addCriterion("resUrl_pic >=", value, "resurlPic");
            return (Criteria) this;
        }

        public Criteria andResurlPicLessThan(String value) {
            addCriterion("resUrl_pic <", value, "resurlPic");
            return (Criteria) this;
        }

        public Criteria andResurlPicLessThanOrEqualTo(String value) {
            addCriterion("resUrl_pic <=", value, "resurlPic");
            return (Criteria) this;
        }

        public Criteria andResurlPicLike(String value) {
            addCriterion("resUrl_pic like", value, "resurlPic");
            return (Criteria) this;
        }

        public Criteria andResurlPicNotLike(String value) {
            addCriterion("resUrl_pic not like", value, "resurlPic");
            return (Criteria) this;
        }

        public Criteria andResurlPicIn(List<String> values) {
            addCriterion("resUrl_pic in", values, "resurlPic");
            return (Criteria) this;
        }

        public Criteria andResurlPicNotIn(List<String> values) {
            addCriterion("resUrl_pic not in", values, "resurlPic");
            return (Criteria) this;
        }

        public Criteria andResurlPicBetween(String value1, String value2) {
            addCriterion("resUrl_pic between", value1, value2, "resurlPic");
            return (Criteria) this;
        }

        public Criteria andResurlPicNotBetween(String value1, String value2) {
            addCriterion("resUrl_pic not between", value1, value2, "resurlPic");
            return (Criteria) this;
        }

        public Criteria andResurlHeightIsNull() {
            addCriterion("resUrl_height is null");
            return (Criteria) this;
        }

        public Criteria andResurlHeightIsNotNull() {
            addCriterion("resUrl_height is not null");
            return (Criteria) this;
        }

        public Criteria andResurlHeightEqualTo(Integer value) {
            addCriterion("resUrl_height =", value, "resurlHeight");
            return (Criteria) this;
        }

        public Criteria andResurlHeightNotEqualTo(Integer value) {
            addCriterion("resUrl_height <>", value, "resurlHeight");
            return (Criteria) this;
        }

        public Criteria andResurlHeightGreaterThan(Integer value) {
            addCriterion("resUrl_height >", value, "resurlHeight");
            return (Criteria) this;
        }

        public Criteria andResurlHeightGreaterThanOrEqualTo(Integer value) {
            addCriterion("resUrl_height >=", value, "resurlHeight");
            return (Criteria) this;
        }

        public Criteria andResurlHeightLessThan(Integer value) {
            addCriterion("resUrl_height <", value, "resurlHeight");
            return (Criteria) this;
        }

        public Criteria andResurlHeightLessThanOrEqualTo(Integer value) {
            addCriterion("resUrl_height <=", value, "resurlHeight");
            return (Criteria) this;
        }

        public Criteria andResurlHeightIn(List<Integer> values) {
            addCriterion("resUrl_height in", values, "resurlHeight");
            return (Criteria) this;
        }

        public Criteria andResurlHeightNotIn(List<Integer> values) {
            addCriterion("resUrl_height not in", values, "resurlHeight");
            return (Criteria) this;
        }

        public Criteria andResurlHeightBetween(Integer value1, Integer value2) {
            addCriterion("resUrl_height between", value1, value2, "resurlHeight");
            return (Criteria) this;
        }

        public Criteria andResurlHeightNotBetween(Integer value1, Integer value2) {
            addCriterion("resUrl_height not between", value1, value2, "resurlHeight");
            return (Criteria) this;
        }

        public Criteria andDownurlWidthIsNull() {
            addCriterion("downUrl_width is null");
            return (Criteria) this;
        }

        public Criteria andDownurlWidthIsNotNull() {
            addCriterion("downUrl_width is not null");
            return (Criteria) this;
        }

        public Criteria andDownurlWidthEqualTo(Integer value) {
            addCriterion("downUrl_width =", value, "downurlWidth");
            return (Criteria) this;
        }

        public Criteria andDownurlWidthNotEqualTo(Integer value) {
            addCriterion("downUrl_width <>", value, "downurlWidth");
            return (Criteria) this;
        }

        public Criteria andDownurlWidthGreaterThan(Integer value) {
            addCriterion("downUrl_width >", value, "downurlWidth");
            return (Criteria) this;
        }

        public Criteria andDownurlWidthGreaterThanOrEqualTo(Integer value) {
            addCriterion("downUrl_width >=", value, "downurlWidth");
            return (Criteria) this;
        }

        public Criteria andDownurlWidthLessThan(Integer value) {
            addCriterion("downUrl_width <", value, "downurlWidth");
            return (Criteria) this;
        }

        public Criteria andDownurlWidthLessThanOrEqualTo(Integer value) {
            addCriterion("downUrl_width <=", value, "downurlWidth");
            return (Criteria) this;
        }

        public Criteria andDownurlWidthIn(List<Integer> values) {
            addCriterion("downUrl_width in", values, "downurlWidth");
            return (Criteria) this;
        }

        public Criteria andDownurlWidthNotIn(List<Integer> values) {
            addCriterion("downUrl_width not in", values, "downurlWidth");
            return (Criteria) this;
        }

        public Criteria andDownurlWidthBetween(Integer value1, Integer value2) {
            addCriterion("downUrl_width between", value1, value2, "downurlWidth");
            return (Criteria) this;
        }

        public Criteria andDownurlWidthNotBetween(Integer value1, Integer value2) {
            addCriterion("downUrl_width not between", value1, value2, "downurlWidth");
            return (Criteria) this;
        }

        public Criteria andDownurlPicIsNull() {
            addCriterion("downUrl_pic is null");
            return (Criteria) this;
        }

        public Criteria andDownurlPicIsNotNull() {
            addCriterion("downUrl_pic is not null");
            return (Criteria) this;
        }

        public Criteria andDownurlPicEqualTo(String value) {
            addCriterion("downUrl_pic =", value, "downurlPic");
            return (Criteria) this;
        }

        public Criteria andDownurlPicNotEqualTo(String value) {
            addCriterion("downUrl_pic <>", value, "downurlPic");
            return (Criteria) this;
        }

        public Criteria andDownurlPicGreaterThan(String value) {
            addCriterion("downUrl_pic >", value, "downurlPic");
            return (Criteria) this;
        }

        public Criteria andDownurlPicGreaterThanOrEqualTo(String value) {
            addCriterion("downUrl_pic >=", value, "downurlPic");
            return (Criteria) this;
        }

        public Criteria andDownurlPicLessThan(String value) {
            addCriterion("downUrl_pic <", value, "downurlPic");
            return (Criteria) this;
        }

        public Criteria andDownurlPicLessThanOrEqualTo(String value) {
            addCriterion("downUrl_pic <=", value, "downurlPic");
            return (Criteria) this;
        }

        public Criteria andDownurlPicLike(String value) {
            addCriterion("downUrl_pic like", value, "downurlPic");
            return (Criteria) this;
        }

        public Criteria andDownurlPicNotLike(String value) {
            addCriterion("downUrl_pic not like", value, "downurlPic");
            return (Criteria) this;
        }

        public Criteria andDownurlPicIn(List<String> values) {
            addCriterion("downUrl_pic in", values, "downurlPic");
            return (Criteria) this;
        }

        public Criteria andDownurlPicNotIn(List<String> values) {
            addCriterion("downUrl_pic not in", values, "downurlPic");
            return (Criteria) this;
        }

        public Criteria andDownurlPicBetween(String value1, String value2) {
            addCriterion("downUrl_pic between", value1, value2, "downurlPic");
            return (Criteria) this;
        }

        public Criteria andDownurlPicNotBetween(String value1, String value2) {
            addCriterion("downUrl_pic not between", value1, value2, "downurlPic");
            return (Criteria) this;
        }

        public Criteria andDownurlHeightIsNull() {
            addCriterion("downUrl_height is null");
            return (Criteria) this;
        }

        public Criteria andDownurlHeightIsNotNull() {
            addCriterion("downUrl_height is not null");
            return (Criteria) this;
        }

        public Criteria andDownurlHeightEqualTo(Integer value) {
            addCriterion("downUrl_height =", value, "downurlHeight");
            return (Criteria) this;
        }

        public Criteria andDownurlHeightNotEqualTo(Integer value) {
            addCriterion("downUrl_height <>", value, "downurlHeight");
            return (Criteria) this;
        }

        public Criteria andDownurlHeightGreaterThan(Integer value) {
            addCriterion("downUrl_height >", value, "downurlHeight");
            return (Criteria) this;
        }

        public Criteria andDownurlHeightGreaterThanOrEqualTo(Integer value) {
            addCriterion("downUrl_height >=", value, "downurlHeight");
            return (Criteria) this;
        }

        public Criteria andDownurlHeightLessThan(Integer value) {
            addCriterion("downUrl_height <", value, "downurlHeight");
            return (Criteria) this;
        }

        public Criteria andDownurlHeightLessThanOrEqualTo(Integer value) {
            addCriterion("downUrl_height <=", value, "downurlHeight");
            return (Criteria) this;
        }

        public Criteria andDownurlHeightIn(List<Integer> values) {
            addCriterion("downUrl_height in", values, "downurlHeight");
            return (Criteria) this;
        }

        public Criteria andDownurlHeightNotIn(List<Integer> values) {
            addCriterion("downUrl_height not in", values, "downurlHeight");
            return (Criteria) this;
        }

        public Criteria andDownurlHeightBetween(Integer value1, Integer value2) {
            addCriterion("downUrl_height between", value1, value2, "downurlHeight");
            return (Criteria) this;
        }

        public Criteria andDownurlHeightNotBetween(Integer value1, Integer value2) {
            addCriterion("downUrl_height not between", value1, value2, "downurlHeight");
            return (Criteria) this;
        }

        public Criteria andRighturlWidthIsNull() {
            addCriterion("rightUrl_width is null");
            return (Criteria) this;
        }

        public Criteria andRighturlWidthIsNotNull() {
            addCriterion("rightUrl_width is not null");
            return (Criteria) this;
        }

        public Criteria andRighturlWidthEqualTo(Integer value) {
            addCriterion("rightUrl_width =", value, "righturlWidth");
            return (Criteria) this;
        }

        public Criteria andRighturlWidthNotEqualTo(Integer value) {
            addCriterion("rightUrl_width <>", value, "righturlWidth");
            return (Criteria) this;
        }

        public Criteria andRighturlWidthGreaterThan(Integer value) {
            addCriterion("rightUrl_width >", value, "righturlWidth");
            return (Criteria) this;
        }

        public Criteria andRighturlWidthGreaterThanOrEqualTo(Integer value) {
            addCriterion("rightUrl_width >=", value, "righturlWidth");
            return (Criteria) this;
        }

        public Criteria andRighturlWidthLessThan(Integer value) {
            addCriterion("rightUrl_width <", value, "righturlWidth");
            return (Criteria) this;
        }

        public Criteria andRighturlWidthLessThanOrEqualTo(Integer value) {
            addCriterion("rightUrl_width <=", value, "righturlWidth");
            return (Criteria) this;
        }

        public Criteria andRighturlWidthIn(List<Integer> values) {
            addCriterion("rightUrl_width in", values, "righturlWidth");
            return (Criteria) this;
        }

        public Criteria andRighturlWidthNotIn(List<Integer> values) {
            addCriterion("rightUrl_width not in", values, "righturlWidth");
            return (Criteria) this;
        }

        public Criteria andRighturlWidthBetween(Integer value1, Integer value2) {
            addCriterion("rightUrl_width between", value1, value2, "righturlWidth");
            return (Criteria) this;
        }

        public Criteria andRighturlWidthNotBetween(Integer value1, Integer value2) {
            addCriterion("rightUrl_width not between", value1, value2, "righturlWidth");
            return (Criteria) this;
        }

        public Criteria andRighturlPicIsNull() {
            addCriterion("rightUrl_pic is null");
            return (Criteria) this;
        }

        public Criteria andRighturlPicIsNotNull() {
            addCriterion("rightUrl_pic is not null");
            return (Criteria) this;
        }

        public Criteria andRighturlPicEqualTo(String value) {
            addCriterion("rightUrl_pic =", value, "righturlPic");
            return (Criteria) this;
        }

        public Criteria andRighturlPicNotEqualTo(String value) {
            addCriterion("rightUrl_pic <>", value, "righturlPic");
            return (Criteria) this;
        }

        public Criteria andRighturlPicGreaterThan(String value) {
            addCriterion("rightUrl_pic >", value, "righturlPic");
            return (Criteria) this;
        }

        public Criteria andRighturlPicGreaterThanOrEqualTo(String value) {
            addCriterion("rightUrl_pic >=", value, "righturlPic");
            return (Criteria) this;
        }

        public Criteria andRighturlPicLessThan(String value) {
            addCriterion("rightUrl_pic <", value, "righturlPic");
            return (Criteria) this;
        }

        public Criteria andRighturlPicLessThanOrEqualTo(String value) {
            addCriterion("rightUrl_pic <=", value, "righturlPic");
            return (Criteria) this;
        }

        public Criteria andRighturlPicLike(String value) {
            addCriterion("rightUrl_pic like", value, "righturlPic");
            return (Criteria) this;
        }

        public Criteria andRighturlPicNotLike(String value) {
            addCriterion("rightUrl_pic not like", value, "righturlPic");
            return (Criteria) this;
        }

        public Criteria andRighturlPicIn(List<String> values) {
            addCriterion("rightUrl_pic in", values, "righturlPic");
            return (Criteria) this;
        }

        public Criteria andRighturlPicNotIn(List<String> values) {
            addCriterion("rightUrl_pic not in", values, "righturlPic");
            return (Criteria) this;
        }

        public Criteria andRighturlPicBetween(String value1, String value2) {
            addCriterion("rightUrl_pic between", value1, value2, "righturlPic");
            return (Criteria) this;
        }

        public Criteria andRighturlPicNotBetween(String value1, String value2) {
            addCriterion("rightUrl_pic not between", value1, value2, "righturlPic");
            return (Criteria) this;
        }

        public Criteria andRighturlHeightIsNull() {
            addCriterion("rightUrl_height is null");
            return (Criteria) this;
        }

        public Criteria andRighturlHeightIsNotNull() {
            addCriterion("rightUrl_height is not null");
            return (Criteria) this;
        }

        public Criteria andRighturlHeightEqualTo(Integer value) {
            addCriterion("rightUrl_height =", value, "righturlHeight");
            return (Criteria) this;
        }

        public Criteria andRighturlHeightNotEqualTo(Integer value) {
            addCriterion("rightUrl_height <>", value, "righturlHeight");
            return (Criteria) this;
        }

        public Criteria andRighturlHeightGreaterThan(Integer value) {
            addCriterion("rightUrl_height >", value, "righturlHeight");
            return (Criteria) this;
        }

        public Criteria andRighturlHeightGreaterThanOrEqualTo(Integer value) {
            addCriterion("rightUrl_height >=", value, "righturlHeight");
            return (Criteria) this;
        }

        public Criteria andRighturlHeightLessThan(Integer value) {
            addCriterion("rightUrl_height <", value, "righturlHeight");
            return (Criteria) this;
        }

        public Criteria andRighturlHeightLessThanOrEqualTo(Integer value) {
            addCriterion("rightUrl_height <=", value, "righturlHeight");
            return (Criteria) this;
        }

        public Criteria andRighturlHeightIn(List<Integer> values) {
            addCriterion("rightUrl_height in", values, "righturlHeight");
            return (Criteria) this;
        }

        public Criteria andRighturlHeightNotIn(List<Integer> values) {
            addCriterion("rightUrl_height not in", values, "righturlHeight");
            return (Criteria) this;
        }

        public Criteria andRighturlHeightBetween(Integer value1, Integer value2) {
            addCriterion("rightUrl_height between", value1, value2, "righturlHeight");
            return (Criteria) this;
        }

        public Criteria andRighturlHeightNotBetween(Integer value1, Integer value2) {
            addCriterion("rightUrl_height not between", value1, value2, "righturlHeight");
            return (Criteria) this;
        }

        public Criteria andLefturlWidthIsNull() {
            addCriterion("leftUrl_width is null");
            return (Criteria) this;
        }

        public Criteria andLefturlWidthIsNotNull() {
            addCriterion("leftUrl_width is not null");
            return (Criteria) this;
        }

        public Criteria andLefturlWidthEqualTo(Integer value) {
            addCriterion("leftUrl_width =", value, "lefturlWidth");
            return (Criteria) this;
        }

        public Criteria andLefturlWidthNotEqualTo(Integer value) {
            addCriterion("leftUrl_width <>", value, "lefturlWidth");
            return (Criteria) this;
        }

        public Criteria andLefturlWidthGreaterThan(Integer value) {
            addCriterion("leftUrl_width >", value, "lefturlWidth");
            return (Criteria) this;
        }

        public Criteria andLefturlWidthGreaterThanOrEqualTo(Integer value) {
            addCriterion("leftUrl_width >=", value, "lefturlWidth");
            return (Criteria) this;
        }

        public Criteria andLefturlWidthLessThan(Integer value) {
            addCriterion("leftUrl_width <", value, "lefturlWidth");
            return (Criteria) this;
        }

        public Criteria andLefturlWidthLessThanOrEqualTo(Integer value) {
            addCriterion("leftUrl_width <=", value, "lefturlWidth");
            return (Criteria) this;
        }

        public Criteria andLefturlWidthIn(List<Integer> values) {
            addCriterion("leftUrl_width in", values, "lefturlWidth");
            return (Criteria) this;
        }

        public Criteria andLefturlWidthNotIn(List<Integer> values) {
            addCriterion("leftUrl_width not in", values, "lefturlWidth");
            return (Criteria) this;
        }

        public Criteria andLefturlWidthBetween(Integer value1, Integer value2) {
            addCriterion("leftUrl_width between", value1, value2, "lefturlWidth");
            return (Criteria) this;
        }

        public Criteria andLefturlWidthNotBetween(Integer value1, Integer value2) {
            addCriterion("leftUrl_width not between", value1, value2, "lefturlWidth");
            return (Criteria) this;
        }

        public Criteria andLefturlPicIsNull() {
            addCriterion("leftUrl_pic is null");
            return (Criteria) this;
        }

        public Criteria andLefturlPicIsNotNull() {
            addCriterion("leftUrl_pic is not null");
            return (Criteria) this;
        }

        public Criteria andLefturlPicEqualTo(String value) {
            addCriterion("leftUrl_pic =", value, "lefturlPic");
            return (Criteria) this;
        }

        public Criteria andLefturlPicNotEqualTo(String value) {
            addCriterion("leftUrl_pic <>", value, "lefturlPic");
            return (Criteria) this;
        }

        public Criteria andLefturlPicGreaterThan(String value) {
            addCriterion("leftUrl_pic >", value, "lefturlPic");
            return (Criteria) this;
        }

        public Criteria andLefturlPicGreaterThanOrEqualTo(String value) {
            addCriterion("leftUrl_pic >=", value, "lefturlPic");
            return (Criteria) this;
        }

        public Criteria andLefturlPicLessThan(String value) {
            addCriterion("leftUrl_pic <", value, "lefturlPic");
            return (Criteria) this;
        }

        public Criteria andLefturlPicLessThanOrEqualTo(String value) {
            addCriterion("leftUrl_pic <=", value, "lefturlPic");
            return (Criteria) this;
        }

        public Criteria andLefturlPicLike(String value) {
            addCriterion("leftUrl_pic like", value, "lefturlPic");
            return (Criteria) this;
        }

        public Criteria andLefturlPicNotLike(String value) {
            addCriterion("leftUrl_pic not like", value, "lefturlPic");
            return (Criteria) this;
        }

        public Criteria andLefturlPicIn(List<String> values) {
            addCriterion("leftUrl_pic in", values, "lefturlPic");
            return (Criteria) this;
        }

        public Criteria andLefturlPicNotIn(List<String> values) {
            addCriterion("leftUrl_pic not in", values, "lefturlPic");
            return (Criteria) this;
        }

        public Criteria andLefturlPicBetween(String value1, String value2) {
            addCriterion("leftUrl_pic between", value1, value2, "lefturlPic");
            return (Criteria) this;
        }

        public Criteria andLefturlPicNotBetween(String value1, String value2) {
            addCriterion("leftUrl_pic not between", value1, value2, "lefturlPic");
            return (Criteria) this;
        }

        public Criteria andLefturlHeightIsNull() {
            addCriterion("leftUrl_height is null");
            return (Criteria) this;
        }

        public Criteria andLefturlHeightIsNotNull() {
            addCriterion("leftUrl_height is not null");
            return (Criteria) this;
        }

        public Criteria andLefturlHeightEqualTo(Integer value) {
            addCriterion("leftUrl_height =", value, "lefturlHeight");
            return (Criteria) this;
        }

        public Criteria andLefturlHeightNotEqualTo(Integer value) {
            addCriterion("leftUrl_height <>", value, "lefturlHeight");
            return (Criteria) this;
        }

        public Criteria andLefturlHeightGreaterThan(Integer value) {
            addCriterion("leftUrl_height >", value, "lefturlHeight");
            return (Criteria) this;
        }

        public Criteria andLefturlHeightGreaterThanOrEqualTo(Integer value) {
            addCriterion("leftUrl_height >=", value, "lefturlHeight");
            return (Criteria) this;
        }

        public Criteria andLefturlHeightLessThan(Integer value) {
            addCriterion("leftUrl_height <", value, "lefturlHeight");
            return (Criteria) this;
        }

        public Criteria andLefturlHeightLessThanOrEqualTo(Integer value) {
            addCriterion("leftUrl_height <=", value, "lefturlHeight");
            return (Criteria) this;
        }

        public Criteria andLefturlHeightIn(List<Integer> values) {
            addCriterion("leftUrl_height in", values, "lefturlHeight");
            return (Criteria) this;
        }

        public Criteria andLefturlHeightNotIn(List<Integer> values) {
            addCriterion("leftUrl_height not in", values, "lefturlHeight");
            return (Criteria) this;
        }

        public Criteria andLefturlHeightBetween(Integer value1, Integer value2) {
            addCriterion("leftUrl_height between", value1, value2, "lefturlHeight");
            return (Criteria) this;
        }

        public Criteria andLefturlHeightNotBetween(Integer value1, Integer value2) {
            addCriterion("leftUrl_height not between", value1, value2, "lefturlHeight");
            return (Criteria) this;
        }

        public Criteria andTopurlWidthIsNull() {
            addCriterion("topUrl_width is null");
            return (Criteria) this;
        }

        public Criteria andTopurlWidthIsNotNull() {
            addCriterion("topUrl_width is not null");
            return (Criteria) this;
        }

        public Criteria andTopurlWidthEqualTo(Integer value) {
            addCriterion("topUrl_width =", value, "topurlWidth");
            return (Criteria) this;
        }

        public Criteria andTopurlWidthNotEqualTo(Integer value) {
            addCriterion("topUrl_width <>", value, "topurlWidth");
            return (Criteria) this;
        }

        public Criteria andTopurlWidthGreaterThan(Integer value) {
            addCriterion("topUrl_width >", value, "topurlWidth");
            return (Criteria) this;
        }

        public Criteria andTopurlWidthGreaterThanOrEqualTo(Integer value) {
            addCriterion("topUrl_width >=", value, "topurlWidth");
            return (Criteria) this;
        }

        public Criteria andTopurlWidthLessThan(Integer value) {
            addCriterion("topUrl_width <", value, "topurlWidth");
            return (Criteria) this;
        }

        public Criteria andTopurlWidthLessThanOrEqualTo(Integer value) {
            addCriterion("topUrl_width <=", value, "topurlWidth");
            return (Criteria) this;
        }

        public Criteria andTopurlWidthIn(List<Integer> values) {
            addCriterion("topUrl_width in", values, "topurlWidth");
            return (Criteria) this;
        }

        public Criteria andTopurlWidthNotIn(List<Integer> values) {
            addCriterion("topUrl_width not in", values, "topurlWidth");
            return (Criteria) this;
        }

        public Criteria andTopurlWidthBetween(Integer value1, Integer value2) {
            addCriterion("topUrl_width between", value1, value2, "topurlWidth");
            return (Criteria) this;
        }

        public Criteria andTopurlWidthNotBetween(Integer value1, Integer value2) {
            addCriterion("topUrl_width not between", value1, value2, "topurlWidth");
            return (Criteria) this;
        }

        public Criteria andTopurlPicIsNull() {
            addCriterion("topUrl_pic is null");
            return (Criteria) this;
        }

        public Criteria andTopurlPicIsNotNull() {
            addCriterion("topUrl_pic is not null");
            return (Criteria) this;
        }

        public Criteria andTopurlPicEqualTo(String value) {
            addCriterion("topUrl_pic =", value, "topurlPic");
            return (Criteria) this;
        }

        public Criteria andTopurlPicNotEqualTo(String value) {
            addCriterion("topUrl_pic <>", value, "topurlPic");
            return (Criteria) this;
        }

        public Criteria andTopurlPicGreaterThan(String value) {
            addCriterion("topUrl_pic >", value, "topurlPic");
            return (Criteria) this;
        }

        public Criteria andTopurlPicGreaterThanOrEqualTo(String value) {
            addCriterion("topUrl_pic >=", value, "topurlPic");
            return (Criteria) this;
        }

        public Criteria andTopurlPicLessThan(String value) {
            addCriterion("topUrl_pic <", value, "topurlPic");
            return (Criteria) this;
        }

        public Criteria andTopurlPicLessThanOrEqualTo(String value) {
            addCriterion("topUrl_pic <=", value, "topurlPic");
            return (Criteria) this;
        }

        public Criteria andTopurlPicLike(String value) {
            addCriterion("topUrl_pic like", value, "topurlPic");
            return (Criteria) this;
        }

        public Criteria andTopurlPicNotLike(String value) {
            addCriterion("topUrl_pic not like", value, "topurlPic");
            return (Criteria) this;
        }

        public Criteria andTopurlPicIn(List<String> values) {
            addCriterion("topUrl_pic in", values, "topurlPic");
            return (Criteria) this;
        }

        public Criteria andTopurlPicNotIn(List<String> values) {
            addCriterion("topUrl_pic not in", values, "topurlPic");
            return (Criteria) this;
        }

        public Criteria andTopurlPicBetween(String value1, String value2) {
            addCriterion("topUrl_pic between", value1, value2, "topurlPic");
            return (Criteria) this;
        }

        public Criteria andTopurlPicNotBetween(String value1, String value2) {
            addCriterion("topUrl_pic not between", value1, value2, "topurlPic");
            return (Criteria) this;
        }

        public Criteria andTopurlHeightIsNull() {
            addCriterion("topUrl_height is null");
            return (Criteria) this;
        }

        public Criteria andTopurlHeightIsNotNull() {
            addCriterion("topUrl_height is not null");
            return (Criteria) this;
        }

        public Criteria andTopurlHeightEqualTo(Integer value) {
            addCriterion("topUrl_height =", value, "topurlHeight");
            return (Criteria) this;
        }

        public Criteria andTopurlHeightNotEqualTo(Integer value) {
            addCriterion("topUrl_height <>", value, "topurlHeight");
            return (Criteria) this;
        }

        public Criteria andTopurlHeightGreaterThan(Integer value) {
            addCriterion("topUrl_height >", value, "topurlHeight");
            return (Criteria) this;
        }

        public Criteria andTopurlHeightGreaterThanOrEqualTo(Integer value) {
            addCriterion("topUrl_height >=", value, "topurlHeight");
            return (Criteria) this;
        }

        public Criteria andTopurlHeightLessThan(Integer value) {
            addCriterion("topUrl_height <", value, "topurlHeight");
            return (Criteria) this;
        }

        public Criteria andTopurlHeightLessThanOrEqualTo(Integer value) {
            addCriterion("topUrl_height <=", value, "topurlHeight");
            return (Criteria) this;
        }

        public Criteria andTopurlHeightIn(List<Integer> values) {
            addCriterion("topUrl_height in", values, "topurlHeight");
            return (Criteria) this;
        }

        public Criteria andTopurlHeightNotIn(List<Integer> values) {
            addCriterion("topUrl_height not in", values, "topurlHeight");
            return (Criteria) this;
        }

        public Criteria andTopurlHeightBetween(Integer value1, Integer value2) {
            addCriterion("topUrl_height between", value1, value2, "topurlHeight");
            return (Criteria) this;
        }

        public Criteria andTopurlHeightNotBetween(Integer value1, Integer value2) {
            addCriterion("topUrl_height not between", value1, value2, "topurlHeight");
            return (Criteria) this;
        }

        public Criteria andListurlWidthIsNull() {
            addCriterion("listUrl_width is null");
            return (Criteria) this;
        }

        public Criteria andListurlWidthIsNotNull() {
            addCriterion("listUrl_width is not null");
            return (Criteria) this;
        }

        public Criteria andListurlWidthEqualTo(Integer value) {
            addCriterion("listUrl_width =", value, "listurlWidth");
            return (Criteria) this;
        }

        public Criteria andListurlWidthNotEqualTo(Integer value) {
            addCriterion("listUrl_width <>", value, "listurlWidth");
            return (Criteria) this;
        }

        public Criteria andListurlWidthGreaterThan(Integer value) {
            addCriterion("listUrl_width >", value, "listurlWidth");
            return (Criteria) this;
        }

        public Criteria andListurlWidthGreaterThanOrEqualTo(Integer value) {
            addCriterion("listUrl_width >=", value, "listurlWidth");
            return (Criteria) this;
        }

        public Criteria andListurlWidthLessThan(Integer value) {
            addCriterion("listUrl_width <", value, "listurlWidth");
            return (Criteria) this;
        }

        public Criteria andListurlWidthLessThanOrEqualTo(Integer value) {
            addCriterion("listUrl_width <=", value, "listurlWidth");
            return (Criteria) this;
        }

        public Criteria andListurlWidthIn(List<Integer> values) {
            addCriterion("listUrl_width in", values, "listurlWidth");
            return (Criteria) this;
        }

        public Criteria andListurlWidthNotIn(List<Integer> values) {
            addCriterion("listUrl_width not in", values, "listurlWidth");
            return (Criteria) this;
        }

        public Criteria andListurlWidthBetween(Integer value1, Integer value2) {
            addCriterion("listUrl_width between", value1, value2, "listurlWidth");
            return (Criteria) this;
        }

        public Criteria andListurlWidthNotBetween(Integer value1, Integer value2) {
            addCriterion("listUrl_width not between", value1, value2, "listurlWidth");
            return (Criteria) this;
        }

        public Criteria andListurlPicIsNull() {
            addCriterion("listUrl_pic is null");
            return (Criteria) this;
        }

        public Criteria andListurlPicIsNotNull() {
            addCriterion("listUrl_pic is not null");
            return (Criteria) this;
        }

        public Criteria andListurlPicEqualTo(String value) {
            addCriterion("listUrl_pic =", value, "listurlPic");
            return (Criteria) this;
        }

        public Criteria andListurlPicNotEqualTo(String value) {
            addCriterion("listUrl_pic <>", value, "listurlPic");
            return (Criteria) this;
        }

        public Criteria andListurlPicGreaterThan(String value) {
            addCriterion("listUrl_pic >", value, "listurlPic");
            return (Criteria) this;
        }

        public Criteria andListurlPicGreaterThanOrEqualTo(String value) {
            addCriterion("listUrl_pic >=", value, "listurlPic");
            return (Criteria) this;
        }

        public Criteria andListurlPicLessThan(String value) {
            addCriterion("listUrl_pic <", value, "listurlPic");
            return (Criteria) this;
        }

        public Criteria andListurlPicLessThanOrEqualTo(String value) {
            addCriterion("listUrl_pic <=", value, "listurlPic");
            return (Criteria) this;
        }

        public Criteria andListurlPicLike(String value) {
            addCriterion("listUrl_pic like", value, "listurlPic");
            return (Criteria) this;
        }

        public Criteria andListurlPicNotLike(String value) {
            addCriterion("listUrl_pic not like", value, "listurlPic");
            return (Criteria) this;
        }

        public Criteria andListurlPicIn(List<String> values) {
            addCriterion("listUrl_pic in", values, "listurlPic");
            return (Criteria) this;
        }

        public Criteria andListurlPicNotIn(List<String> values) {
            addCriterion("listUrl_pic not in", values, "listurlPic");
            return (Criteria) this;
        }

        public Criteria andListurlPicBetween(String value1, String value2) {
            addCriterion("listUrl_pic between", value1, value2, "listurlPic");
            return (Criteria) this;
        }

        public Criteria andListurlPicNotBetween(String value1, String value2) {
            addCriterion("listUrl_pic not between", value1, value2, "listurlPic");
            return (Criteria) this;
        }

        public Criteria andListurlHeightIsNull() {
            addCriterion("listUrl_height is null");
            return (Criteria) this;
        }

        public Criteria andListurlHeightIsNotNull() {
            addCriterion("listUrl_height is not null");
            return (Criteria) this;
        }

        public Criteria andListurlHeightEqualTo(Integer value) {
            addCriterion("listUrl_height =", value, "listurlHeight");
            return (Criteria) this;
        }

        public Criteria andListurlHeightNotEqualTo(Integer value) {
            addCriterion("listUrl_height <>", value, "listurlHeight");
            return (Criteria) this;
        }

        public Criteria andListurlHeightGreaterThan(Integer value) {
            addCriterion("listUrl_height >", value, "listurlHeight");
            return (Criteria) this;
        }

        public Criteria andListurlHeightGreaterThanOrEqualTo(Integer value) {
            addCriterion("listUrl_height >=", value, "listurlHeight");
            return (Criteria) this;
        }

        public Criteria andListurlHeightLessThan(Integer value) {
            addCriterion("listUrl_height <", value, "listurlHeight");
            return (Criteria) this;
        }

        public Criteria andListurlHeightLessThanOrEqualTo(Integer value) {
            addCriterion("listUrl_height <=", value, "listurlHeight");
            return (Criteria) this;
        }

        public Criteria andListurlHeightIn(List<Integer> values) {
            addCriterion("listUrl_height in", values, "listurlHeight");
            return (Criteria) this;
        }

        public Criteria andListurlHeightNotIn(List<Integer> values) {
            addCriterion("listUrl_height not in", values, "listurlHeight");
            return (Criteria) this;
        }

        public Criteria andListurlHeightBetween(Integer value1, Integer value2) {
            addCriterion("listUrl_height between", value1, value2, "listurlHeight");
            return (Criteria) this;
        }

        public Criteria andListurlHeightNotBetween(Integer value1, Integer value2) {
            addCriterion("listUrl_height not between", value1, value2, "listurlHeight");
            return (Criteria) this;
        }

        public Criteria andAddlefturlWidthIsNull() {
            addCriterion("addLeftUrl_width is null");
            return (Criteria) this;
        }

        public Criteria andAddlefturlWidthIsNotNull() {
            addCriterion("addLeftUrl_width is not null");
            return (Criteria) this;
        }

        public Criteria andAddlefturlWidthEqualTo(Integer value) {
            addCriterion("addLeftUrl_width =", value, "addlefturlWidth");
            return (Criteria) this;
        }

        public Criteria andAddlefturlWidthNotEqualTo(Integer value) {
            addCriterion("addLeftUrl_width <>", value, "addlefturlWidth");
            return (Criteria) this;
        }

        public Criteria andAddlefturlWidthGreaterThan(Integer value) {
            addCriterion("addLeftUrl_width >", value, "addlefturlWidth");
            return (Criteria) this;
        }

        public Criteria andAddlefturlWidthGreaterThanOrEqualTo(Integer value) {
            addCriterion("addLeftUrl_width >=", value, "addlefturlWidth");
            return (Criteria) this;
        }

        public Criteria andAddlefturlWidthLessThan(Integer value) {
            addCriterion("addLeftUrl_width <", value, "addlefturlWidth");
            return (Criteria) this;
        }

        public Criteria andAddlefturlWidthLessThanOrEqualTo(Integer value) {
            addCriterion("addLeftUrl_width <=", value, "addlefturlWidth");
            return (Criteria) this;
        }

        public Criteria andAddlefturlWidthIn(List<Integer> values) {
            addCriterion("addLeftUrl_width in", values, "addlefturlWidth");
            return (Criteria) this;
        }

        public Criteria andAddlefturlWidthNotIn(List<Integer> values) {
            addCriterion("addLeftUrl_width not in", values, "addlefturlWidth");
            return (Criteria) this;
        }

        public Criteria andAddlefturlWidthBetween(Integer value1, Integer value2) {
            addCriterion("addLeftUrl_width between", value1, value2, "addlefturlWidth");
            return (Criteria) this;
        }

        public Criteria andAddlefturlWidthNotBetween(Integer value1, Integer value2) {
            addCriterion("addLeftUrl_width not between", value1, value2, "addlefturlWidth");
            return (Criteria) this;
        }

        public Criteria andAddlefturlPicIsNull() {
            addCriterion("addLeftUrl_pic is null");
            return (Criteria) this;
        }

        public Criteria andAddlefturlPicIsNotNull() {
            addCriterion("addLeftUrl_pic is not null");
            return (Criteria) this;
        }

        public Criteria andAddlefturlPicEqualTo(String value) {
            addCriterion("addLeftUrl_pic =", value, "addlefturlPic");
            return (Criteria) this;
        }

        public Criteria andAddlefturlPicNotEqualTo(String value) {
            addCriterion("addLeftUrl_pic <>", value, "addlefturlPic");
            return (Criteria) this;
        }

        public Criteria andAddlefturlPicGreaterThan(String value) {
            addCriterion("addLeftUrl_pic >", value, "addlefturlPic");
            return (Criteria) this;
        }

        public Criteria andAddlefturlPicGreaterThanOrEqualTo(String value) {
            addCriterion("addLeftUrl_pic >=", value, "addlefturlPic");
            return (Criteria) this;
        }

        public Criteria andAddlefturlPicLessThan(String value) {
            addCriterion("addLeftUrl_pic <", value, "addlefturlPic");
            return (Criteria) this;
        }

        public Criteria andAddlefturlPicLessThanOrEqualTo(String value) {
            addCriterion("addLeftUrl_pic <=", value, "addlefturlPic");
            return (Criteria) this;
        }

        public Criteria andAddlefturlPicLike(String value) {
            addCriterion("addLeftUrl_pic like", value, "addlefturlPic");
            return (Criteria) this;
        }

        public Criteria andAddlefturlPicNotLike(String value) {
            addCriterion("addLeftUrl_pic not like", value, "addlefturlPic");
            return (Criteria) this;
        }

        public Criteria andAddlefturlPicIn(List<String> values) {
            addCriterion("addLeftUrl_pic in", values, "addlefturlPic");
            return (Criteria) this;
        }

        public Criteria andAddlefturlPicNotIn(List<String> values) {
            addCriterion("addLeftUrl_pic not in", values, "addlefturlPic");
            return (Criteria) this;
        }

        public Criteria andAddlefturlPicBetween(String value1, String value2) {
            addCriterion("addLeftUrl_pic between", value1, value2, "addlefturlPic");
            return (Criteria) this;
        }

        public Criteria andAddlefturlPicNotBetween(String value1, String value2) {
            addCriterion("addLeftUrl_pic not between", value1, value2, "addlefturlPic");
            return (Criteria) this;
        }

        public Criteria andAddlefturlHeightIsNull() {
            addCriterion("addLeftUrl_height is null");
            return (Criteria) this;
        }

        public Criteria andAddlefturlHeightIsNotNull() {
            addCriterion("addLeftUrl_height is not null");
            return (Criteria) this;
        }

        public Criteria andAddlefturlHeightEqualTo(Integer value) {
            addCriterion("addLeftUrl_height =", value, "addlefturlHeight");
            return (Criteria) this;
        }

        public Criteria andAddlefturlHeightNotEqualTo(Integer value) {
            addCriterion("addLeftUrl_height <>", value, "addlefturlHeight");
            return (Criteria) this;
        }

        public Criteria andAddlefturlHeightGreaterThan(Integer value) {
            addCriterion("addLeftUrl_height >", value, "addlefturlHeight");
            return (Criteria) this;
        }

        public Criteria andAddlefturlHeightGreaterThanOrEqualTo(Integer value) {
            addCriterion("addLeftUrl_height >=", value, "addlefturlHeight");
            return (Criteria) this;
        }

        public Criteria andAddlefturlHeightLessThan(Integer value) {
            addCriterion("addLeftUrl_height <", value, "addlefturlHeight");
            return (Criteria) this;
        }

        public Criteria andAddlefturlHeightLessThanOrEqualTo(Integer value) {
            addCriterion("addLeftUrl_height <=", value, "addlefturlHeight");
            return (Criteria) this;
        }

        public Criteria andAddlefturlHeightIn(List<Integer> values) {
            addCriterion("addLeftUrl_height in", values, "addlefturlHeight");
            return (Criteria) this;
        }

        public Criteria andAddlefturlHeightNotIn(List<Integer> values) {
            addCriterion("addLeftUrl_height not in", values, "addlefturlHeight");
            return (Criteria) this;
        }

        public Criteria andAddlefturlHeightBetween(Integer value1, Integer value2) {
            addCriterion("addLeftUrl_height between", value1, value2, "addlefturlHeight");
            return (Criteria) this;
        }

        public Criteria andAddlefturlHeightNotBetween(Integer value1, Integer value2) {
            addCriterion("addLeftUrl_height not between", value1, value2, "addlefturlHeight");
            return (Criteria) this;
        }

        public Criteria andSortIsNull() {
            addCriterion("sort is null");
            return (Criteria) this;
        }

        public Criteria andSortIsNotNull() {
            addCriterion("sort is not null");
            return (Criteria) this;
        }

        public Criteria andSortEqualTo(Integer value) {
            addCriterion("sort =", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortNotEqualTo(Integer value) {
            addCriterion("sort <>", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortGreaterThan(Integer value) {
            addCriterion("sort >", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortGreaterThanOrEqualTo(Integer value) {
            addCriterion("sort >=", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortLessThan(Integer value) {
            addCriterion("sort <", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortLessThanOrEqualTo(Integer value) {
            addCriterion("sort <=", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortIn(List<Integer> values) {
            addCriterion("sort in", values, "sort");
            return (Criteria) this;
        }

        public Criteria andSortNotIn(List<Integer> values) {
            addCriterion("sort not in", values, "sort");
            return (Criteria) this;
        }

        public Criteria andSortBetween(Integer value1, Integer value2) {
            addCriterion("sort between", value1, value2, "sort");
            return (Criteria) this;
        }

        public Criteria andSortNotBetween(Integer value1, Integer value2) {
            addCriterion("sort not between", value1, value2, "sort");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}