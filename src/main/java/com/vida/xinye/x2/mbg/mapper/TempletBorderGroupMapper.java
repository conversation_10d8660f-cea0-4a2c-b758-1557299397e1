package com.vida.xinye.x2.mbg.mapper;

import com.vida.xinye.x2.mbg.model.TempletBorderGroup;
import com.vida.xinye.x2.mbg.model.TempletBorderGroupExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TempletBorderGroupMapper {
    long countByExample(TempletBorderGroupExample example);

    int deleteByExample(TempletBorderGroupExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TempletBorderGroup row);

    int insertSelective(TempletBorderGroup row);

    List<TempletBorderGroup> selectByExample(TempletBorderGroupExample example);

    TempletBorderGroup selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("row") TempletBorderGroup row, @Param("example") TempletBorderGroupExample example);

    int updateByExample(@Param("row") TempletBorderGroup row, @Param("example") TempletBorderGroupExample example);

    int updateByPrimaryKeySelective(TempletBorderGroup row);

    int updateByPrimaryKey(TempletBorderGroup row);
}