package com.vida.xinye.x2.mbg.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class PrintHistoryExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public PrintHistoryExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNull() {
            addCriterion("user_id is null");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNotNull() {
            addCriterion("user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUserIdEqualTo(Long value) {
            addCriterion("user_id =", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualTo(Long value) {
            addCriterion("user_id <>", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThan(Long value) {
            addCriterion("user_id >", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("user_id >=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThan(Long value) {
            addCriterion("user_id <", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualTo(Long value) {
            addCriterion("user_id <=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdIn(List<Long> values) {
            addCriterion("user_id in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotIn(List<Long> values) {
            addCriterion("user_id not in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdBetween(Long value1, Long value2) {
            addCriterion("user_id between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotBetween(Long value1, Long value2) {
            addCriterion("user_id not between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andTempletNameIsNull() {
            addCriterion("templet_name is null");
            return (Criteria) this;
        }

        public Criteria andTempletNameIsNotNull() {
            addCriterion("templet_name is not null");
            return (Criteria) this;
        }

        public Criteria andTempletNameEqualTo(String value) {
            addCriterion("templet_name =", value, "templetName");
            return (Criteria) this;
        }

        public Criteria andTempletNameNotEqualTo(String value) {
            addCriterion("templet_name <>", value, "templetName");
            return (Criteria) this;
        }

        public Criteria andTempletNameGreaterThan(String value) {
            addCriterion("templet_name >", value, "templetName");
            return (Criteria) this;
        }

        public Criteria andTempletNameGreaterThanOrEqualTo(String value) {
            addCriterion("templet_name >=", value, "templetName");
            return (Criteria) this;
        }

        public Criteria andTempletNameLessThan(String value) {
            addCriterion("templet_name <", value, "templetName");
            return (Criteria) this;
        }

        public Criteria andTempletNameLessThanOrEqualTo(String value) {
            addCriterion("templet_name <=", value, "templetName");
            return (Criteria) this;
        }

        public Criteria andTempletNameLike(String value) {
            addCriterion("templet_name like", value, "templetName");
            return (Criteria) this;
        }

        public Criteria andTempletNameNotLike(String value) {
            addCriterion("templet_name not like", value, "templetName");
            return (Criteria) this;
        }

        public Criteria andTempletNameIn(List<String> values) {
            addCriterion("templet_name in", values, "templetName");
            return (Criteria) this;
        }

        public Criteria andTempletNameNotIn(List<String> values) {
            addCriterion("templet_name not in", values, "templetName");
            return (Criteria) this;
        }

        public Criteria andTempletNameBetween(String value1, String value2) {
            addCriterion("templet_name between", value1, value2, "templetName");
            return (Criteria) this;
        }

        public Criteria andTempletNameNotBetween(String value1, String value2) {
            addCriterion("templet_name not between", value1, value2, "templetName");
            return (Criteria) this;
        }

        public Criteria andTempletCoverIsNull() {
            addCriterion("templet_cover is null");
            return (Criteria) this;
        }

        public Criteria andTempletCoverIsNotNull() {
            addCriterion("templet_cover is not null");
            return (Criteria) this;
        }

        public Criteria andTempletCoverEqualTo(String value) {
            addCriterion("templet_cover =", value, "templetCover");
            return (Criteria) this;
        }

        public Criteria andTempletCoverNotEqualTo(String value) {
            addCriterion("templet_cover <>", value, "templetCover");
            return (Criteria) this;
        }

        public Criteria andTempletCoverGreaterThan(String value) {
            addCriterion("templet_cover >", value, "templetCover");
            return (Criteria) this;
        }

        public Criteria andTempletCoverGreaterThanOrEqualTo(String value) {
            addCriterion("templet_cover >=", value, "templetCover");
            return (Criteria) this;
        }

        public Criteria andTempletCoverLessThan(String value) {
            addCriterion("templet_cover <", value, "templetCover");
            return (Criteria) this;
        }

        public Criteria andTempletCoverLessThanOrEqualTo(String value) {
            addCriterion("templet_cover <=", value, "templetCover");
            return (Criteria) this;
        }

        public Criteria andTempletCoverLike(String value) {
            addCriterion("templet_cover like", value, "templetCover");
            return (Criteria) this;
        }

        public Criteria andTempletCoverNotLike(String value) {
            addCriterion("templet_cover not like", value, "templetCover");
            return (Criteria) this;
        }

        public Criteria andTempletCoverIn(List<String> values) {
            addCriterion("templet_cover in", values, "templetCover");
            return (Criteria) this;
        }

        public Criteria andTempletCoverNotIn(List<String> values) {
            addCriterion("templet_cover not in", values, "templetCover");
            return (Criteria) this;
        }

        public Criteria andTempletCoverBetween(String value1, String value2) {
            addCriterion("templet_cover between", value1, value2, "templetCover");
            return (Criteria) this;
        }

        public Criteria andTempletCoverNotBetween(String value1, String value2) {
            addCriterion("templet_cover not between", value1, value2, "templetCover");
            return (Criteria) this;
        }

        public Criteria andPrintTimeIsNull() {
            addCriterion("print_time is null");
            return (Criteria) this;
        }

        public Criteria andPrintTimeIsNotNull() {
            addCriterion("print_time is not null");
            return (Criteria) this;
        }

        public Criteria andPrintTimeEqualTo(Date value) {
            addCriterion("print_time =", value, "printTime");
            return (Criteria) this;
        }

        public Criteria andPrintTimeNotEqualTo(Date value) {
            addCriterion("print_time <>", value, "printTime");
            return (Criteria) this;
        }

        public Criteria andPrintTimeGreaterThan(Date value) {
            addCriterion("print_time >", value, "printTime");
            return (Criteria) this;
        }

        public Criteria andPrintTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("print_time >=", value, "printTime");
            return (Criteria) this;
        }

        public Criteria andPrintTimeLessThan(Date value) {
            addCriterion("print_time <", value, "printTime");
            return (Criteria) this;
        }

        public Criteria andPrintTimeLessThanOrEqualTo(Date value) {
            addCriterion("print_time <=", value, "printTime");
            return (Criteria) this;
        }

        public Criteria andPrintTimeIn(List<Date> values) {
            addCriterion("print_time in", values, "printTime");
            return (Criteria) this;
        }

        public Criteria andPrintTimeNotIn(List<Date> values) {
            addCriterion("print_time not in", values, "printTime");
            return (Criteria) this;
        }

        public Criteria andPrintTimeBetween(Date value1, Date value2) {
            addCriterion("print_time between", value1, value2, "printTime");
            return (Criteria) this;
        }

        public Criteria andPrintTimeNotBetween(Date value1, Date value2) {
            addCriterion("print_time not between", value1, value2, "printTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}