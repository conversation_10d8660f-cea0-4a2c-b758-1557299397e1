package com.vida.xinye.x2.mbg.mapper;

import com.vida.xinye.x2.mbg.model.HelpTutorialI18n;
import com.vida.xinye.x2.mbg.model.HelpTutorialI18nExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface HelpTutorialI18nMapper {
    long countByExample(HelpTutorialI18nExample example);

    int deleteByExample(HelpTutorialI18nExample example);

    int deleteByPrimaryKey(Long id);

    int insert(HelpTutorialI18n row);

    int insertSelective(HelpTutorialI18n row);

    List<HelpTutorialI18n> selectByExample(HelpTutorialI18nExample example);

    HelpTutorialI18n selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("row") HelpTutorialI18n row, @Param("example") HelpTutorialI18nExample example);

    int updateByExample(@Param("row") HelpTutorialI18n row, @Param("example") HelpTutorialI18nExample example);

    int updateByPrimaryKeySelective(HelpTutorialI18n row);

    int updateByPrimaryKey(HelpTutorialI18n row);
}