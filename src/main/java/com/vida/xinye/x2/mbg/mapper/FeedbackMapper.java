package com.vida.xinye.x2.mbg.mapper;

import com.vida.xinye.x2.mbg.model.Feedback;
import com.vida.xinye.x2.mbg.model.FeedbackExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface FeedbackMapper {
    long countByExample(FeedbackExample example);

    int deleteByExample(FeedbackExample example);

    int deleteByPrimaryKey(Long id);

    int insert(Feedback row);

    int insertSelective(Feedback row);

    List<Feedback> selectByExampleWithBLOBs(FeedbackExample example);

    List<Feedback> selectByExample(FeedbackExample example);

    Feedback selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("row") Feedback row, @Param("example") FeedbackExample example);

    int updateByExampleWithBLOBs(@Param("row") Feedback row, @Param("example") FeedbackExample example);

    int updateByExample(@Param("row") Feedback row, @Param("example") FeedbackExample example);

    int updateByPrimaryKeySelective(Feedback row);

    int updateByPrimaryKeyWithBLOBs(Feedback row);

    int updateByPrimaryKey(Feedback row);
}