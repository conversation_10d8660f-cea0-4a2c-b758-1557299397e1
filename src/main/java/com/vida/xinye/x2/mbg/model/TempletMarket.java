package com.vida.xinye.x2.mbg.model;

import java.io.Serializable;
import java.util.Date;

public class TempletMarket implements Serializable {
    private Long id;

    private Long groupId;

    private Integer resurlWidth;

    private String resurlPic;

    private Integer resurlHeight;

    private Integer downurlWidth;

    private String downurlPic;

    private Integer downurlHeight;

    private Integer righturlWidth;

    private String righturlPic;

    private Integer righturlHeight;

    private Integer lefturlWidth;

    private String lefturlPic;

    private Integer lefturlHeight;

    private Integer topurlWidth;

    private String topurlPic;

    private Integer topurlHeight;

    private Integer listurlWidth;

    private String listurlPic;

    private Integer listurlHeight;

    private Integer addlefturlWidth;

    private String addlefturlPic;

    private Integer addlefturlHeight;

    private Integer sort;

    private Date createTime;

    private String data;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getGroupId() {
        return groupId;
    }

    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

    public Integer getResurlWidth() {
        return resurlWidth;
    }

    public void setResurlWidth(Integer resurlWidth) {
        this.resurlWidth = resurlWidth;
    }

    public String getResurlPic() {
        return resurlPic;
    }

    public void setResurlPic(String resurlPic) {
        this.resurlPic = resurlPic;
    }

    public Integer getResurlHeight() {
        return resurlHeight;
    }

    public void setResurlHeight(Integer resurlHeight) {
        this.resurlHeight = resurlHeight;
    }

    public Integer getDownurlWidth() {
        return downurlWidth;
    }

    public void setDownurlWidth(Integer downurlWidth) {
        this.downurlWidth = downurlWidth;
    }

    public String getDownurlPic() {
        return downurlPic;
    }

    public void setDownurlPic(String downurlPic) {
        this.downurlPic = downurlPic;
    }

    public Integer getDownurlHeight() {
        return downurlHeight;
    }

    public void setDownurlHeight(Integer downurlHeight) {
        this.downurlHeight = downurlHeight;
    }

    public Integer getRighturlWidth() {
        return righturlWidth;
    }

    public void setRighturlWidth(Integer righturlWidth) {
        this.righturlWidth = righturlWidth;
    }

    public String getRighturlPic() {
        return righturlPic;
    }

    public void setRighturlPic(String righturlPic) {
        this.righturlPic = righturlPic;
    }

    public Integer getRighturlHeight() {
        return righturlHeight;
    }

    public void setRighturlHeight(Integer righturlHeight) {
        this.righturlHeight = righturlHeight;
    }

    public Integer getLefturlWidth() {
        return lefturlWidth;
    }

    public void setLefturlWidth(Integer lefturlWidth) {
        this.lefturlWidth = lefturlWidth;
    }

    public String getLefturlPic() {
        return lefturlPic;
    }

    public void setLefturlPic(String lefturlPic) {
        this.lefturlPic = lefturlPic;
    }

    public Integer getLefturlHeight() {
        return lefturlHeight;
    }

    public void setLefturlHeight(Integer lefturlHeight) {
        this.lefturlHeight = lefturlHeight;
    }

    public Integer getTopurlWidth() {
        return topurlWidth;
    }

    public void setTopurlWidth(Integer topurlWidth) {
        this.topurlWidth = topurlWidth;
    }

    public String getTopurlPic() {
        return topurlPic;
    }

    public void setTopurlPic(String topurlPic) {
        this.topurlPic = topurlPic;
    }

    public Integer getTopurlHeight() {
        return topurlHeight;
    }

    public void setTopurlHeight(Integer topurlHeight) {
        this.topurlHeight = topurlHeight;
    }

    public Integer getListurlWidth() {
        return listurlWidth;
    }

    public void setListurlWidth(Integer listurlWidth) {
        this.listurlWidth = listurlWidth;
    }

    public String getListurlPic() {
        return listurlPic;
    }

    public void setListurlPic(String listurlPic) {
        this.listurlPic = listurlPic;
    }

    public Integer getListurlHeight() {
        return listurlHeight;
    }

    public void setListurlHeight(Integer listurlHeight) {
        this.listurlHeight = listurlHeight;
    }

    public Integer getAddlefturlWidth() {
        return addlefturlWidth;
    }

    public void setAddlefturlWidth(Integer addlefturlWidth) {
        this.addlefturlWidth = addlefturlWidth;
    }

    public String getAddlefturlPic() {
        return addlefturlPic;
    }

    public void setAddlefturlPic(String addlefturlPic) {
        this.addlefturlPic = addlefturlPic;
    }

    public Integer getAddlefturlHeight() {
        return addlefturlHeight;
    }

    public void setAddlefturlHeight(Integer addlefturlHeight) {
        this.addlefturlHeight = addlefturlHeight;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", groupId=").append(groupId);
        sb.append(", resurlWidth=").append(resurlWidth);
        sb.append(", resurlPic=").append(resurlPic);
        sb.append(", resurlHeight=").append(resurlHeight);
        sb.append(", downurlWidth=").append(downurlWidth);
        sb.append(", downurlPic=").append(downurlPic);
        sb.append(", downurlHeight=").append(downurlHeight);
        sb.append(", righturlWidth=").append(righturlWidth);
        sb.append(", righturlPic=").append(righturlPic);
        sb.append(", righturlHeight=").append(righturlHeight);
        sb.append(", lefturlWidth=").append(lefturlWidth);
        sb.append(", lefturlPic=").append(lefturlPic);
        sb.append(", lefturlHeight=").append(lefturlHeight);
        sb.append(", topurlWidth=").append(topurlWidth);
        sb.append(", topurlPic=").append(topurlPic);
        sb.append(", topurlHeight=").append(topurlHeight);
        sb.append(", listurlWidth=").append(listurlWidth);
        sb.append(", listurlPic=").append(listurlPic);
        sb.append(", listurlHeight=").append(listurlHeight);
        sb.append(", addlefturlWidth=").append(addlefturlWidth);
        sb.append(", addlefturlPic=").append(addlefturlPic);
        sb.append(", addlefturlHeight=").append(addlefturlHeight);
        sb.append(", sort=").append(sort);
        sb.append(", createTime=").append(createTime);
        sb.append(", data=").append(data);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}