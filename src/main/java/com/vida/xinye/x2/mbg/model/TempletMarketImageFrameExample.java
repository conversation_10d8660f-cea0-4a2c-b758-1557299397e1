package com.vida.xinye.x2.mbg.model;

import java.util.ArrayList;
import java.util.List;

public class TempletMarketImageFrameExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public TempletMarketImageFrameExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTemplateIdIsNull() {
            addCriterion("template_id is null");
            return (Criteria) this;
        }

        public Criteria andTemplateIdIsNotNull() {
            addCriterion("template_id is not null");
            return (Criteria) this;
        }

        public Criteria andTemplateIdEqualTo(Long value) {
            addCriterion("template_id =", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdNotEqualTo(Long value) {
            addCriterion("template_id <>", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdGreaterThan(Long value) {
            addCriterion("template_id >", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdGreaterThanOrEqualTo(Long value) {
            addCriterion("template_id >=", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdLessThan(Long value) {
            addCriterion("template_id <", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdLessThanOrEqualTo(Long value) {
            addCriterion("template_id <=", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdIn(List<Long> values) {
            addCriterion("template_id in", values, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdNotIn(List<Long> values) {
            addCriterion("template_id not in", values, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdBetween(Long value1, Long value2) {
            addCriterion("template_id between", value1, value2, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdNotBetween(Long value1, Long value2) {
            addCriterion("template_id not between", value1, value2, "templateId");
            return (Criteria) this;
        }

        public Criteria andFrameXIsNull() {
            addCriterion("frame_x is null");
            return (Criteria) this;
        }

        public Criteria andFrameXIsNotNull() {
            addCriterion("frame_x is not null");
            return (Criteria) this;
        }

        public Criteria andFrameXEqualTo(Integer value) {
            addCriterion("frame_x =", value, "frameX");
            return (Criteria) this;
        }

        public Criteria andFrameXNotEqualTo(Integer value) {
            addCriterion("frame_x <>", value, "frameX");
            return (Criteria) this;
        }

        public Criteria andFrameXGreaterThan(Integer value) {
            addCriterion("frame_x >", value, "frameX");
            return (Criteria) this;
        }

        public Criteria andFrameXGreaterThanOrEqualTo(Integer value) {
            addCriterion("frame_x >=", value, "frameX");
            return (Criteria) this;
        }

        public Criteria andFrameXLessThan(Integer value) {
            addCriterion("frame_x <", value, "frameX");
            return (Criteria) this;
        }

        public Criteria andFrameXLessThanOrEqualTo(Integer value) {
            addCriterion("frame_x <=", value, "frameX");
            return (Criteria) this;
        }

        public Criteria andFrameXIn(List<Integer> values) {
            addCriterion("frame_x in", values, "frameX");
            return (Criteria) this;
        }

        public Criteria andFrameXNotIn(List<Integer> values) {
            addCriterion("frame_x not in", values, "frameX");
            return (Criteria) this;
        }

        public Criteria andFrameXBetween(Integer value1, Integer value2) {
            addCriterion("frame_x between", value1, value2, "frameX");
            return (Criteria) this;
        }

        public Criteria andFrameXNotBetween(Integer value1, Integer value2) {
            addCriterion("frame_x not between", value1, value2, "frameX");
            return (Criteria) this;
        }

        public Criteria andFrameYIsNull() {
            addCriterion("frame_y is null");
            return (Criteria) this;
        }

        public Criteria andFrameYIsNotNull() {
            addCriterion("frame_y is not null");
            return (Criteria) this;
        }

        public Criteria andFrameYEqualTo(Integer value) {
            addCriterion("frame_y =", value, "frameY");
            return (Criteria) this;
        }

        public Criteria andFrameYNotEqualTo(Integer value) {
            addCriterion("frame_y <>", value, "frameY");
            return (Criteria) this;
        }

        public Criteria andFrameYGreaterThan(Integer value) {
            addCriterion("frame_y >", value, "frameY");
            return (Criteria) this;
        }

        public Criteria andFrameYGreaterThanOrEqualTo(Integer value) {
            addCriterion("frame_y >=", value, "frameY");
            return (Criteria) this;
        }

        public Criteria andFrameYLessThan(Integer value) {
            addCriterion("frame_y <", value, "frameY");
            return (Criteria) this;
        }

        public Criteria andFrameYLessThanOrEqualTo(Integer value) {
            addCriterion("frame_y <=", value, "frameY");
            return (Criteria) this;
        }

        public Criteria andFrameYIn(List<Integer> values) {
            addCriterion("frame_y in", values, "frameY");
            return (Criteria) this;
        }

        public Criteria andFrameYNotIn(List<Integer> values) {
            addCriterion("frame_y not in", values, "frameY");
            return (Criteria) this;
        }

        public Criteria andFrameYBetween(Integer value1, Integer value2) {
            addCriterion("frame_y between", value1, value2, "frameY");
            return (Criteria) this;
        }

        public Criteria andFrameYNotBetween(Integer value1, Integer value2) {
            addCriterion("frame_y not between", value1, value2, "frameY");
            return (Criteria) this;
        }

        public Criteria andFrameWidthIsNull() {
            addCriterion("frame_width is null");
            return (Criteria) this;
        }

        public Criteria andFrameWidthIsNotNull() {
            addCriterion("frame_width is not null");
            return (Criteria) this;
        }

        public Criteria andFrameWidthEqualTo(Integer value) {
            addCriterion("frame_width =", value, "frameWidth");
            return (Criteria) this;
        }

        public Criteria andFrameWidthNotEqualTo(Integer value) {
            addCriterion("frame_width <>", value, "frameWidth");
            return (Criteria) this;
        }

        public Criteria andFrameWidthGreaterThan(Integer value) {
            addCriterion("frame_width >", value, "frameWidth");
            return (Criteria) this;
        }

        public Criteria andFrameWidthGreaterThanOrEqualTo(Integer value) {
            addCriterion("frame_width >=", value, "frameWidth");
            return (Criteria) this;
        }

        public Criteria andFrameWidthLessThan(Integer value) {
            addCriterion("frame_width <", value, "frameWidth");
            return (Criteria) this;
        }

        public Criteria andFrameWidthLessThanOrEqualTo(Integer value) {
            addCriterion("frame_width <=", value, "frameWidth");
            return (Criteria) this;
        }

        public Criteria andFrameWidthIn(List<Integer> values) {
            addCriterion("frame_width in", values, "frameWidth");
            return (Criteria) this;
        }

        public Criteria andFrameWidthNotIn(List<Integer> values) {
            addCriterion("frame_width not in", values, "frameWidth");
            return (Criteria) this;
        }

        public Criteria andFrameWidthBetween(Integer value1, Integer value2) {
            addCriterion("frame_width between", value1, value2, "frameWidth");
            return (Criteria) this;
        }

        public Criteria andFrameWidthNotBetween(Integer value1, Integer value2) {
            addCriterion("frame_width not between", value1, value2, "frameWidth");
            return (Criteria) this;
        }

        public Criteria andFrameHeightIsNull() {
            addCriterion("frame_height is null");
            return (Criteria) this;
        }

        public Criteria andFrameHeightIsNotNull() {
            addCriterion("frame_height is not null");
            return (Criteria) this;
        }

        public Criteria andFrameHeightEqualTo(Integer value) {
            addCriterion("frame_height =", value, "frameHeight");
            return (Criteria) this;
        }

        public Criteria andFrameHeightNotEqualTo(Integer value) {
            addCriterion("frame_height <>", value, "frameHeight");
            return (Criteria) this;
        }

        public Criteria andFrameHeightGreaterThan(Integer value) {
            addCriterion("frame_height >", value, "frameHeight");
            return (Criteria) this;
        }

        public Criteria andFrameHeightGreaterThanOrEqualTo(Integer value) {
            addCriterion("frame_height >=", value, "frameHeight");
            return (Criteria) this;
        }

        public Criteria andFrameHeightLessThan(Integer value) {
            addCriterion("frame_height <", value, "frameHeight");
            return (Criteria) this;
        }

        public Criteria andFrameHeightLessThanOrEqualTo(Integer value) {
            addCriterion("frame_height <=", value, "frameHeight");
            return (Criteria) this;
        }

        public Criteria andFrameHeightIn(List<Integer> values) {
            addCriterion("frame_height in", values, "frameHeight");
            return (Criteria) this;
        }

        public Criteria andFrameHeightNotIn(List<Integer> values) {
            addCriterion("frame_height not in", values, "frameHeight");
            return (Criteria) this;
        }

        public Criteria andFrameHeightBetween(Integer value1, Integer value2) {
            addCriterion("frame_height between", value1, value2, "frameHeight");
            return (Criteria) this;
        }

        public Criteria andFrameHeightNotBetween(Integer value1, Integer value2) {
            addCriterion("frame_height not between", value1, value2, "frameHeight");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}