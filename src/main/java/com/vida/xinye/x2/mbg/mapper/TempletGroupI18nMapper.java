package com.vida.xinye.x2.mbg.mapper;

import com.vida.xinye.x2.mbg.model.TempletGroupI18n;
import com.vida.xinye.x2.mbg.model.TempletGroupI18nExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TempletGroupI18nMapper {
    long countByExample(TempletGroupI18nExample example);

    int deleteByExample(TempletGroupI18nExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TempletGroupI18n row);

    int insertSelective(TempletGroupI18n row);

    List<TempletGroupI18n> selectByExample(TempletGroupI18nExample example);

    TempletGroupI18n selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("row") TempletGroupI18n row, @Param("example") TempletGroupI18nExample example);

    int updateByExample(@Param("row") TempletGroupI18n row, @Param("example") TempletGroupI18nExample example);

    int updateByPrimaryKeySelective(TempletGroupI18n row);

    int updateByPrimaryKey(TempletGroupI18n row);
}