package com.vida.xinye.x2.mbg.mapper;

import com.vida.xinye.x2.mbg.model.XkwSubject;
import com.vida.xinye.x2.mbg.model.XkwSubjectExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface XkwSubjectMapper {
    long countByExample(XkwSubjectExample example);

    int deleteByExample(XkwSubjectExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(XkwSubject row);

    int insertSelective(XkwSubject row);

    List<XkwSubject> selectByExample(XkwSubjectExample example);

    XkwSubject selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("row") XkwSubject row, @Param("example") XkwSubjectExample example);

    int updateByExample(@Param("row") XkwSubject row, @Param("example") XkwSubjectExample example);

    int updateByPrimaryKeySelective(XkwSubject row);

    int updateByPrimaryKey(XkwSubject row);
}