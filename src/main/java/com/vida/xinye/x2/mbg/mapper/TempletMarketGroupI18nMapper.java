package com.vida.xinye.x2.mbg.mapper;

import com.vida.xinye.x2.mbg.model.TempletMarketGroupI18n;
import com.vida.xinye.x2.mbg.model.TempletMarketGroupI18nExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TempletMarketGroupI18nMapper {
    long countByExample(TempletMarketGroupI18nExample example);

    int deleteByExample(TempletMarketGroupI18nExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TempletMarketGroupI18n row);

    int insertSelective(TempletMarketGroupI18n row);

    List<TempletMarketGroupI18n> selectByExample(TempletMarketGroupI18nExample example);

    TempletMarketGroupI18n selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("row") TempletMarketGroupI18n row, @Param("example") TempletMarketGroupI18nExample example);

    int updateByExample(@Param("row") TempletMarketGroupI18n row, @Param("example") TempletMarketGroupI18nExample example);

    int updateByPrimaryKeySelective(TempletMarketGroupI18n row);

    int updateByPrimaryKey(TempletMarketGroupI18n row);
}