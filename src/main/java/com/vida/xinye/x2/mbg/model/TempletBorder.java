package com.vida.xinye.x2.mbg.model;

import java.io.Serializable;
import java.util.Date;

public class TempletBorder implements Serializable {
    private Long id;

    private Long groupId;

    private Integer lefturlWidth;

    private String lefturlPic;

    private Integer lefturlHeight;

    private Integer righturlWidth;

    private String righturlPic;

    private Integer righturlHeight;

    private Integer topurlWidth;

    private String topurlPic;

    private Integer topurlHeight;

    private Integer downurlWidth;

    private String downurlPic;

    private Integer downurlHeight;

    private Integer lefttopurlWidth;

    private String lefttopurlPic;

    private Integer lefttopurlHeight;

    private Integer leftdownurlWidth;

    private String leftdownurlPic;

    private Integer leftdownurlHeight;

    private Integer righttopurlWidth;

    private String righttopurlPic;

    private Integer righttopurlHeight;

    private Integer rightdownurlWidth;

    private String rightdownurlPic;

    private Integer rightdownurlHeight;

    private Integer resurlWidth;

    private String resurlPic;

    private Integer resurlHeight;

    private Integer listurlWidth;

    private String listurlPic;

    private Integer listurlHeight;

    private Date createTime;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getGroupId() {
        return groupId;
    }

    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

    public Integer getLefturlWidth() {
        return lefturlWidth;
    }

    public void setLefturlWidth(Integer lefturlWidth) {
        this.lefturlWidth = lefturlWidth;
    }

    public String getLefturlPic() {
        return lefturlPic;
    }

    public void setLefturlPic(String lefturlPic) {
        this.lefturlPic = lefturlPic;
    }

    public Integer getLefturlHeight() {
        return lefturlHeight;
    }

    public void setLefturlHeight(Integer lefturlHeight) {
        this.lefturlHeight = lefturlHeight;
    }

    public Integer getRighturlWidth() {
        return righturlWidth;
    }

    public void setRighturlWidth(Integer righturlWidth) {
        this.righturlWidth = righturlWidth;
    }

    public String getRighturlPic() {
        return righturlPic;
    }

    public void setRighturlPic(String righturlPic) {
        this.righturlPic = righturlPic;
    }

    public Integer getRighturlHeight() {
        return righturlHeight;
    }

    public void setRighturlHeight(Integer righturlHeight) {
        this.righturlHeight = righturlHeight;
    }

    public Integer getTopurlWidth() {
        return topurlWidth;
    }

    public void setTopurlWidth(Integer topurlWidth) {
        this.topurlWidth = topurlWidth;
    }

    public String getTopurlPic() {
        return topurlPic;
    }

    public void setTopurlPic(String topurlPic) {
        this.topurlPic = topurlPic;
    }

    public Integer getTopurlHeight() {
        return topurlHeight;
    }

    public void setTopurlHeight(Integer topurlHeight) {
        this.topurlHeight = topurlHeight;
    }

    public Integer getDownurlWidth() {
        return downurlWidth;
    }

    public void setDownurlWidth(Integer downurlWidth) {
        this.downurlWidth = downurlWidth;
    }

    public String getDownurlPic() {
        return downurlPic;
    }

    public void setDownurlPic(String downurlPic) {
        this.downurlPic = downurlPic;
    }

    public Integer getDownurlHeight() {
        return downurlHeight;
    }

    public void setDownurlHeight(Integer downurlHeight) {
        this.downurlHeight = downurlHeight;
    }

    public Integer getLefttopurlWidth() {
        return lefttopurlWidth;
    }

    public void setLefttopurlWidth(Integer lefttopurlWidth) {
        this.lefttopurlWidth = lefttopurlWidth;
    }

    public String getLefttopurlPic() {
        return lefttopurlPic;
    }

    public void setLefttopurlPic(String lefttopurlPic) {
        this.lefttopurlPic = lefttopurlPic;
    }

    public Integer getLefttopurlHeight() {
        return lefttopurlHeight;
    }

    public void setLefttopurlHeight(Integer lefttopurlHeight) {
        this.lefttopurlHeight = lefttopurlHeight;
    }

    public Integer getLeftdownurlWidth() {
        return leftdownurlWidth;
    }

    public void setLeftdownurlWidth(Integer leftdownurlWidth) {
        this.leftdownurlWidth = leftdownurlWidth;
    }

    public String getLeftdownurlPic() {
        return leftdownurlPic;
    }

    public void setLeftdownurlPic(String leftdownurlPic) {
        this.leftdownurlPic = leftdownurlPic;
    }

    public Integer getLeftdownurlHeight() {
        return leftdownurlHeight;
    }

    public void setLeftdownurlHeight(Integer leftdownurlHeight) {
        this.leftdownurlHeight = leftdownurlHeight;
    }

    public Integer getRighttopurlWidth() {
        return righttopurlWidth;
    }

    public void setRighttopurlWidth(Integer righttopurlWidth) {
        this.righttopurlWidth = righttopurlWidth;
    }

    public String getRighttopurlPic() {
        return righttopurlPic;
    }

    public void setRighttopurlPic(String righttopurlPic) {
        this.righttopurlPic = righttopurlPic;
    }

    public Integer getRighttopurlHeight() {
        return righttopurlHeight;
    }

    public void setRighttopurlHeight(Integer righttopurlHeight) {
        this.righttopurlHeight = righttopurlHeight;
    }

    public Integer getRightdownurlWidth() {
        return rightdownurlWidth;
    }

    public void setRightdownurlWidth(Integer rightdownurlWidth) {
        this.rightdownurlWidth = rightdownurlWidth;
    }

    public String getRightdownurlPic() {
        return rightdownurlPic;
    }

    public void setRightdownurlPic(String rightdownurlPic) {
        this.rightdownurlPic = rightdownurlPic;
    }

    public Integer getRightdownurlHeight() {
        return rightdownurlHeight;
    }

    public void setRightdownurlHeight(Integer rightdownurlHeight) {
        this.rightdownurlHeight = rightdownurlHeight;
    }

    public Integer getResurlWidth() {
        return resurlWidth;
    }

    public void setResurlWidth(Integer resurlWidth) {
        this.resurlWidth = resurlWidth;
    }

    public String getResurlPic() {
        return resurlPic;
    }

    public void setResurlPic(String resurlPic) {
        this.resurlPic = resurlPic;
    }

    public Integer getResurlHeight() {
        return resurlHeight;
    }

    public void setResurlHeight(Integer resurlHeight) {
        this.resurlHeight = resurlHeight;
    }

    public Integer getListurlWidth() {
        return listurlWidth;
    }

    public void setListurlWidth(Integer listurlWidth) {
        this.listurlWidth = listurlWidth;
    }

    public String getListurlPic() {
        return listurlPic;
    }

    public void setListurlPic(String listurlPic) {
        this.listurlPic = listurlPic;
    }

    public Integer getListurlHeight() {
        return listurlHeight;
    }

    public void setListurlHeight(Integer listurlHeight) {
        this.listurlHeight = listurlHeight;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", groupId=").append(groupId);
        sb.append(", lefturlWidth=").append(lefturlWidth);
        sb.append(", lefturlPic=").append(lefturlPic);
        sb.append(", lefturlHeight=").append(lefturlHeight);
        sb.append(", righturlWidth=").append(righturlWidth);
        sb.append(", righturlPic=").append(righturlPic);
        sb.append(", righturlHeight=").append(righturlHeight);
        sb.append(", topurlWidth=").append(topurlWidth);
        sb.append(", topurlPic=").append(topurlPic);
        sb.append(", topurlHeight=").append(topurlHeight);
        sb.append(", downurlWidth=").append(downurlWidth);
        sb.append(", downurlPic=").append(downurlPic);
        sb.append(", downurlHeight=").append(downurlHeight);
        sb.append(", lefttopurlWidth=").append(lefttopurlWidth);
        sb.append(", lefttopurlPic=").append(lefttopurlPic);
        sb.append(", lefttopurlHeight=").append(lefttopurlHeight);
        sb.append(", leftdownurlWidth=").append(leftdownurlWidth);
        sb.append(", leftdownurlPic=").append(leftdownurlPic);
        sb.append(", leftdownurlHeight=").append(leftdownurlHeight);
        sb.append(", righttopurlWidth=").append(righttopurlWidth);
        sb.append(", righttopurlPic=").append(righttopurlPic);
        sb.append(", righttopurlHeight=").append(righttopurlHeight);
        sb.append(", rightdownurlWidth=").append(rightdownurlWidth);
        sb.append(", rightdownurlPic=").append(rightdownurlPic);
        sb.append(", rightdownurlHeight=").append(rightdownurlHeight);
        sb.append(", resurlWidth=").append(resurlWidth);
        sb.append(", resurlPic=").append(resurlPic);
        sb.append(", resurlHeight=").append(resurlHeight);
        sb.append(", listurlWidth=").append(listurlWidth);
        sb.append(", listurlPic=").append(listurlPic);
        sb.append(", listurlHeight=").append(listurlHeight);
        sb.append(", createTime=").append(createTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}
