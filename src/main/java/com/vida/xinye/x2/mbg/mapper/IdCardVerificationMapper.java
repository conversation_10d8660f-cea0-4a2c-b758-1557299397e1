package com.vida.xinye.x2.mbg.mapper;

import com.vida.xinye.x2.mbg.model.IdCardVerification;
import com.vida.xinye.x2.mbg.model.IdCardVerificationExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface IdCardVerificationMapper {
    long countByExample(IdCardVerificationExample example);

    int deleteByExample(IdCardVerificationExample example);

    int deleteByPrimaryKey(Long id);

    int insert(IdCardVerification row);

    int insertSelective(IdCardVerification row);

    List<IdCardVerification> selectByExample(IdCardVerificationExample example);

    IdCardVerification selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("row") IdCardVerification row, @Param("example") IdCardVerificationExample example);

    int updateByExample(@Param("row") IdCardVerification row, @Param("example") IdCardVerificationExample example);

    int updateByPrimaryKeySelective(IdCardVerification row);

    int updateByPrimaryKey(IdCardVerification row);
}