package com.vida.xinye.x2.mbg.model;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户登录历史表
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
public class UserLoginHistory implements Serializable {
    private Long id;

    private Long userId;

    /**
     * 登录方式：bymobile,byemail,byweixin,byqq等
     */
    private String loginWay;

    /**
     * 登录IP
     */
    private String loginIp;

    /**
     * 登录设备
     */
    private String loginDevice;

    /**
     * 登录地点
     */
    private String loginLocation;

    /**
     * 用户代理信息
     */
    private String userAgent;

    /**
     * 登录状态：success,failed
     */
    private String loginStatus;

    /**
     * 登录时间
     */
    private Date loginTime;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getLoginWay() {
        return loginWay;
    }

    public void setLoginWay(String loginWay) {
        this.loginWay = loginWay;
    }

    public String getLoginIp() {
        return loginIp;
    }

    public void setLoginIp(String loginIp) {
        this.loginIp = loginIp;
    }

    public String getLoginDevice() {
        return loginDevice;
    }

    public void setLoginDevice(String loginDevice) {
        this.loginDevice = loginDevice;
    }

    public String getLoginLocation() {
        return loginLocation;
    }

    public void setLoginLocation(String loginLocation) {
        this.loginLocation = loginLocation;
    }

    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public String getLoginStatus() {
        return loginStatus;
    }

    public void setLoginStatus(String loginStatus) {
        this.loginStatus = loginStatus;
    }

    public Date getLoginTime() {
        return loginTime;
    }

    public void setLoginTime(Date loginTime) {
        this.loginTime = loginTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", userId=").append(userId);
        sb.append(", loginWay=").append(loginWay);
        sb.append(", loginIp=").append(loginIp);
        sb.append(", loginDevice=").append(loginDevice);
        sb.append(", loginLocation=").append(loginLocation);
        sb.append(", userAgent=").append(userAgent);
        sb.append(", loginStatus=").append(loginStatus);
        sb.append(", loginTime=").append(loginTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}
