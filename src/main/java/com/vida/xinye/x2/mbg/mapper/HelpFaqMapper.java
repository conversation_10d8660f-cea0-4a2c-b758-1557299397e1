package com.vida.xinye.x2.mbg.mapper;

import com.vida.xinye.x2.mbg.model.HelpFaq;
import com.vida.xinye.x2.mbg.model.HelpFaqExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface HelpFaqMapper {
    long countByExample(HelpFaqExample example);

    int deleteByExample(HelpFaqExample example);

    int deleteByPrimaryKey(Long id);

    int insert(HelpFaq row);

    int insertSelective(HelpFaq row);

    List<HelpFaq> selectByExampleWithBLOBs(HelpFaqExample example);

    List<HelpFaq> selectByExample(HelpFaqExample example);

    HelpFaq selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("row") HelpFaq row, @Param("example") HelpFaqExample example);

    int updateByExampleWithBLOBs(@Param("row") HelpFaq row, @Param("example") HelpFaqExample example);

    int updateByExample(@Param("row") HelpFaq row, @Param("example") HelpFaqExample example);

    int updateByPrimaryKeySelective(HelpFaq row);

    int updateByPrimaryKeyWithBLOBs(HelpFaq row);

    int updateByPrimaryKey(HelpFaq row);
}