package com.vida.xinye.x2.mbg.mapper;

import com.vida.xinye.x2.mbg.model.TempletMarket;
import com.vida.xinye.x2.mbg.model.TempletMarketExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TempletMarketMapper {
    long countByExample(TempletMarketExample example);

    int deleteByExample(TempletMarketExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TempletMarket row);

    int insertSelective(TempletMarket row);

    List<TempletMarket> selectByExampleWithBLOBs(TempletMarketExample example);

    List<TempletMarket> selectByExample(TempletMarketExample example);

    TempletMarket selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("row") TempletMarket row, @Param("example") TempletMarketExample example);

    int updateByExampleWithBLOBs(@Param("row") TempletMarket row, @Param("example") TempletMarketExample example);

    int updateByExample(@Param("row") TempletMarket row, @Param("example") TempletMarketExample example);

    int updateByPrimaryKeySelective(TempletMarket row);

    int updateByPrimaryKeyWithBLOBs(TempletMarket row);

    int updateByPrimaryKey(TempletMarket row);
}