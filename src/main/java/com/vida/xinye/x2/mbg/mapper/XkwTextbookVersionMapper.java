package com.vida.xinye.x2.mbg.mapper;

import com.vida.xinye.x2.mbg.model.XkwTextbookVersion;
import com.vida.xinye.x2.mbg.model.XkwTextbookVersionExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface XkwTextbookVersionMapper {
    long countByExample(XkwTextbookVersionExample example);

    int deleteByExample(XkwTextbookVersionExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(XkwTextbookVersion row);

    int insertSelective(XkwTextbookVersion row);

    List<XkwTextbookVersion> selectByExample(XkwTextbookVersionExample example);

    XkwTextbookVersion selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("row") XkwTextbookVersion row, @Param("example") XkwTextbookVersionExample example);

    int updateByExample(@Param("row") XkwTextbookVersion row, @Param("example") XkwTextbookVersionExample example);

    int updateByPrimaryKeySelective(XkwTextbookVersion row);

    int updateByPrimaryKey(XkwTextbookVersion row);
}