package com.vida.xinye.x2.mbg.model;

import java.io.Serializable;
import java.util.Date;

public class PrintHistory implements Serializable {
    private Long id;

    private Long userId;

    private String templetName;

    private String templetCover;

    private Date printTime;

    private Byte sourceType;

    private String templetData;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getTempletName() {
        return templetName;
    }

    public void setTempletName(String templetName) {
        this.templetName = templetName;
    }

    public String getTempletCover() {
        return templetCover;
    }

    public void setTempletCover(String templetCover) {
        this.templetCover = templetCover;
    }

    public Date getPrintTime() {
        return printTime;
    }

    public void setPrintTime(Date printTime) {
        this.printTime = printTime;
    }

    public Byte getSourceType() {
        return sourceType;
    }

    public void setSourceType(Byte sourceType) {
        this.sourceType = sourceType;
    }

    public String getTempletData() {
        return templetData;
    }

    public void setTempletData(String templetData) {
        this.templetData = templetData;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", userId=").append(userId);
        sb.append(", templetName=").append(templetName);
        sb.append(", templetCover=").append(templetCover);
        sb.append(", printTime=").append(printTime);
        sb.append(", sourceType=").append(sourceType);
        sb.append(", templetData=").append(templetData);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}