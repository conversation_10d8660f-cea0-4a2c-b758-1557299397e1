package com.vida.xinye.x2.mbg.model;

import java.io.Serializable;
import java.util.Date;

public class WrongQuestion implements Serializable {
    private Long id;

    private Long userId;

    private String sourceType;

    private String note;

    private String thumbnail;

    private Date createTime;

    private Date updateTime;

    private String data;

    private String xkwQuestionId;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getSourceType() {
        return sourceType;
    }

    public void setSourceType(String sourceType) {
        this.sourceType = sourceType;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public String getThumbnail() {
        return thumbnail;
    }

    public void setThumbnail(String thumbnail) {
        this.thumbnail = thumbnail;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getXkwQuestionId() {
        return xkwQuestionId;
    }

    public void setXkwQuestionId(String xkwQuestionId) {
        this.xkwQuestionId = xkwQuestionId;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", userId=").append(userId);
        sb.append(", sourceType=").append(sourceType);
        sb.append(", note=").append(note);
        sb.append(", thumbnail=").append(thumbnail);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", data=").append(data);
        sb.append(", xkwQuestionId=").append(xkwQuestionId);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}