package com.vida.xinye.x2.mbg.mapper;

import com.vida.xinye.x2.mbg.model.TrainingQuestion;
import com.vida.xinye.x2.mbg.model.TrainingQuestionExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TrainingQuestionMapper {
    long countByExample(TrainingQuestionExample example);

    int deleteByExample(TrainingQuestionExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TrainingQuestion row);

    int insertSelective(TrainingQuestion row);

    List<TrainingQuestion> selectByExampleWithBLOBs(TrainingQuestionExample example);

    List<TrainingQuestion> selectByExample(TrainingQuestionExample example);

    TrainingQuestion selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("row") TrainingQuestion row, @Param("example") TrainingQuestionExample example);

    int updateByExampleWithBLOBs(@Param("row") TrainingQuestion row, @Param("example") TrainingQuestionExample example);

    int updateByExample(@Param("row") TrainingQuestion row, @Param("example") TrainingQuestionExample example);

    int updateByPrimaryKeySelective(TrainingQuestion row);

    int updateByPrimaryKeyWithBLOBs(TrainingQuestion row);

    int updateByPrimaryKey(TrainingQuestion row);
}