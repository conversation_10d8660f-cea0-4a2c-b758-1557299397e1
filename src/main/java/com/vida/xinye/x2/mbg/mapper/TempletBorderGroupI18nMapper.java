package com.vida.xinye.x2.mbg.mapper;

import com.vida.xinye.x2.mbg.model.TempletBorderGroupI18n;
import com.vida.xinye.x2.mbg.model.TempletBorderGroupI18nExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TempletBorderGroupI18nMapper {
    long countByExample(TempletBorderGroupI18nExample example);

    int deleteByExample(TempletBorderGroupI18nExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TempletBorderGroupI18n row);

    int insertSelective(TempletBorderGroupI18n row);

    List<TempletBorderGroupI18n> selectByExample(TempletBorderGroupI18nExample example);

    TempletBorderGroupI18n selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("row") TempletBorderGroupI18n row, @Param("example") TempletBorderGroupI18nExample example);

    int updateByExample(@Param("row") TempletBorderGroupI18n row, @Param("example") TempletBorderGroupI18nExample example);

    int updateByPrimaryKeySelective(TempletBorderGroupI18n row);

    int updateByPrimaryKey(TempletBorderGroupI18n row);
}