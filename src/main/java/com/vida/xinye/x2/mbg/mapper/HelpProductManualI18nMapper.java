package com.vida.xinye.x2.mbg.mapper;

import com.vida.xinye.x2.mbg.model.HelpProductManualI18n;
import com.vida.xinye.x2.mbg.model.HelpProductManualI18nExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface HelpProductManualI18nMapper {
    long countByExample(HelpProductManualI18nExample example);

    int deleteByExample(HelpProductManualI18nExample example);

    int deleteByPrimaryKey(Long id);

    int insert(HelpProductManualI18n row);

    int insertSelective(HelpProductManualI18n row);

    List<HelpProductManualI18n> selectByExample(HelpProductManualI18nExample example);

    HelpProductManualI18n selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("row") HelpProductManualI18n row, @Param("example") HelpProductManualI18nExample example);

    int updateByExample(@Param("row") HelpProductManualI18n row, @Param("example") HelpProductManualI18nExample example);

    int updateByPrimaryKeySelective(HelpProductManualI18n row);

    int updateByPrimaryKey(HelpProductManualI18n row);
}