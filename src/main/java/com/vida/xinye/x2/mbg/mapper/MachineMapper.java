package com.vida.xinye.x2.mbg.mapper;

import com.vida.xinye.x2.mbg.model.Machine;
import com.vida.xinye.x2.mbg.model.MachineExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MachineMapper {
    long countByExample(MachineExample example);

    int deleteByExample(MachineExample example);

    int deleteByPrimaryKey(Long id);

    int insert(Machine row);

    int insertSelective(Machine row);

    List<Machine> selectByExample(MachineExample example);

    Machine selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("row") Machine row, @Param("example") MachineExample example);

    int updateByExample(@Param("row") Machine row, @Param("example") MachineExample example);

    int updateByPrimaryKeySelective(Machine row);

    int updateByPrimaryKey(Machine row);
}