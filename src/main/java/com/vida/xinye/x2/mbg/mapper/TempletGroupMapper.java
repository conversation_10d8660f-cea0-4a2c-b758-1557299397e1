package com.vida.xinye.x2.mbg.mapper;

import com.vida.xinye.x2.mbg.model.TempletGroup;
import com.vida.xinye.x2.mbg.model.TempletGroupExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TempletGroupMapper {
    long countByExample(TempletGroupExample example);

    int deleteByExample(TempletGroupExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TempletGroup row);

    int insertSelective(TempletGroup row);

    List<TempletGroup> selectByExample(TempletGroupExample example);

    TempletGroup selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("row") TempletGroup row, @Param("example") TempletGroupExample example);

    int updateByExample(@Param("row") TempletGroup row, @Param("example") TempletGroupExample example);

    int updateByPrimaryKeySelective(TempletGroup row);

    int updateByPrimaryKey(TempletGroup row);
}