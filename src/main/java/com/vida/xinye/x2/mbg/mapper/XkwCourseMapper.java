package com.vida.xinye.x2.mbg.mapper;

import com.vida.xinye.x2.mbg.model.XkwCourse;
import com.vida.xinye.x2.mbg.model.XkwCourseExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface XkwCourseMapper {
    long countByExample(XkwCourseExample example);

    int deleteByExample(XkwCourseExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(XkwCourse row);

    int insertSelective(XkwCourse row);

    List<XkwCourse> selectByExample(XkwCourseExample example);

    XkwCourse selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("row") XkwCourse row, @Param("example") XkwCourseExample example);

    int updateByExample(@Param("row") XkwCourse row, @Param("example") XkwCourseExample example);

    int updateByPrimaryKeySelective(XkwCourse row);

    int updateByPrimaryKey(XkwCourse row);
}