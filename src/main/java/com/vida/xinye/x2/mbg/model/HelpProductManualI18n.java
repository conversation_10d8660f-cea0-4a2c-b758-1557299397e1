package com.vida.xinye.x2.mbg.model;

import java.io.Serializable;

public class HelpProductManualI18n implements Serializable {
    private Long id;

    private Long productManualId;

    private String locale;

    private String title;

    private String url;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getProductManualId() {
        return productManualId;
    }

    public void setProductManualId(Long productManualId) {
        this.productManualId = productManualId;
    }

    public String getLocale() {
        return locale;
    }

    public void setLocale(String locale) {
        this.locale = locale;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", productManualId=").append(productManualId);
        sb.append(", locale=").append(locale);
        sb.append(", title=").append(title);
        sb.append(", url=").append(url);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}