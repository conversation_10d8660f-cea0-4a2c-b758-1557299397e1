package com.vida.xinye.x2.mbg.mapper;

import com.vida.xinye.x2.mbg.model.HelpTutorialsI18n;
import com.vida.xinye.x2.mbg.model.HelpTutorialsI18nExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface HelpTutorialsI18nMapper {
    long countByExample(HelpTutorialsI18nExample example);

    int deleteByExample(HelpTutorialsI18nExample example);

    int deleteByPrimaryKey(Long id);

    int insert(HelpTutorialsI18n row);

    int insertSelective(HelpTutorialsI18n row);

    List<HelpTutorialsI18n> selectByExample(HelpTutorialsI18nExample example);

    HelpTutorialsI18n selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("row") HelpTutorialsI18n row, @Param("example") HelpTutorialsI18nExample example);

    int updateByExample(@Param("row") HelpTutorialsI18n row, @Param("example") HelpTutorialsI18nExample example);

    int updateByPrimaryKeySelective(HelpTutorialsI18n row);

    int updateByPrimaryKey(HelpTutorialsI18n row);
}