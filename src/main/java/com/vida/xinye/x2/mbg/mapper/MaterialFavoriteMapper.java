package com.vida.xinye.x2.mbg.mapper;

import com.vida.xinye.x2.mbg.model.MaterialFavorite;
import com.vida.xinye.x2.mbg.model.MaterialFavoriteExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MaterialFavoriteMapper {
    long countByExample(MaterialFavoriteExample example);

    int deleteByExample(MaterialFavoriteExample example);

    int deleteByPrimaryKey(Long id);

    int insert(MaterialFavorite row);

    int insertSelective(MaterialFavorite row);

    List<MaterialFavorite> selectByExample(MaterialFavoriteExample example);

    MaterialFavorite selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("row") MaterialFavorite row, @Param("example") MaterialFavoriteExample example);

    int updateByExample(@Param("row") MaterialFavorite row, @Param("example") MaterialFavoriteExample example);

    int updateByPrimaryKeySelective(MaterialFavorite row);

    int updateByPrimaryKey(MaterialFavorite row);
}