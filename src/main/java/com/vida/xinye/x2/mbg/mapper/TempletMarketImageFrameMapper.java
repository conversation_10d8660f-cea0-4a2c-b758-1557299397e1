package com.vida.xinye.x2.mbg.mapper;

import com.vida.xinye.x2.mbg.model.TempletMarketImageFrame;
import com.vida.xinye.x2.mbg.model.TempletMarketImageFrameExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TempletMarketImageFrameMapper {
    long countByExample(TempletMarketImageFrameExample example);

    int deleteByExample(TempletMarketImageFrameExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TempletMarketImageFrame row);

    int insertSelective(TempletMarketImageFrame row);

    List<TempletMarketImageFrame> selectByExample(TempletMarketImageFrameExample example);

    TempletMarketImageFrame selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("row") TempletMarketImageFrame row, @Param("example") TempletMarketImageFrameExample example);

    int updateByExample(@Param("row") TempletMarketImageFrame row, @Param("example") TempletMarketImageFrameExample example);

    int updateByPrimaryKeySelective(TempletMarketImageFrame row);

    int updateByPrimaryKey(TempletMarketImageFrame row);
}