package com.vida.xinye.x2.mbg.mapper;

import com.vida.xinye.x2.mbg.model.SysTemplet;
import com.vida.xinye.x2.mbg.model.SysTempletExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SysTempletMapper {
    long countByExample(SysTempletExample example);

    int deleteByExample(SysTempletExample example);

    int deleteByPrimaryKey(Long id);

    int insert(SysTemplet row);

    int insertSelective(SysTemplet row);

    List<SysTemplet> selectByExampleWithBLOBs(SysTempletExample example);

    List<SysTemplet> selectByExample(SysTempletExample example);

    SysTemplet selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("row") SysTemplet row, @Param("example") SysTempletExample example);

    int updateByExampleWithBLOBs(@Param("row") SysTemplet row, @Param("example") SysTempletExample example);

    int updateByExample(@Param("row") SysTemplet row, @Param("example") SysTempletExample example);

    int updateByPrimaryKeySelective(SysTemplet row);

    int updateByPrimaryKeyWithBLOBs(SysTemplet row);

    int updateByPrimaryKey(SysTemplet row);
}