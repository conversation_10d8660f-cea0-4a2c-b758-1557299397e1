package com.vida.xinye.x2.mbg.mapper;

import com.vida.xinye.x2.mbg.model.XkwStage;
import com.vida.xinye.x2.mbg.model.XkwStageExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface XkwStageMapper {
    long countByExample(XkwStageExample example);

    int deleteByExample(XkwStageExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(XkwStage row);

    int insertSelective(XkwStage row);

    List<XkwStage> selectByExample(XkwStageExample example);

    XkwStage selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("row") XkwStage row, @Param("example") XkwStageExample example);

    int updateByExample(@Param("row") XkwStage row, @Param("example") XkwStageExample example);

    int updateByPrimaryKeySelective(XkwStage row);

    int updateByPrimaryKey(XkwStage row);
}