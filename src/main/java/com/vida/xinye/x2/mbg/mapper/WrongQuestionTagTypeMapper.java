package com.vida.xinye.x2.mbg.mapper;

import com.vida.xinye.x2.mbg.model.WrongQuestionTagType;
import com.vida.xinye.x2.mbg.model.WrongQuestionTagTypeExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface WrongQuestionTagTypeMapper {
    long countByExample(WrongQuestionTagTypeExample example);

    int deleteByExample(WrongQuestionTagTypeExample example);

    int deleteByPrimaryKey(Long id);

    int insert(WrongQuestionTagType row);

    int insertSelective(WrongQuestionTagType row);

    List<WrongQuestionTagType> selectByExample(WrongQuestionTagTypeExample example);

    WrongQuestionTagType selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("row") WrongQuestionTagType row, @Param("example") WrongQuestionTagTypeExample example);

    int updateByExample(@Param("row") WrongQuestionTagType row, @Param("example") WrongQuestionTagTypeExample example);

    int updateByPrimaryKeySelective(WrongQuestionTagType row);

    int updateByPrimaryKey(WrongQuestionTagType row);
}