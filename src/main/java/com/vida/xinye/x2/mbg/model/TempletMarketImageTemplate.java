package com.vida.xinye.x2.mbg.model;

import java.io.Serializable;
import java.util.Date;

public class TempletMarketImageTemplate implements Serializable {
    private Long id;

    private Long groupId;

    private String thumbPic;

    private Integer cellWidth;

    private Integer cellHeight;

    private String editPic;

    private Boolean fullLine;

    private Integer bgviewHeight;

    private Integer rows;

    private Integer columns;

    private Integer lineWidth;

    private Integer sort;

    private Date createTime;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getGroupId() {
        return groupId;
    }

    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

    public String getThumbPic() {
        return thumbPic;
    }

    public void setThumbPic(String thumbPic) {
        this.thumbPic = thumbPic;
    }

    public Integer getCellWidth() {
        return cellWidth;
    }

    public void setCellWidth(Integer cellWidth) {
        this.cellWidth = cellWidth;
    }

    public Integer getCellHeight() {
        return cellHeight;
    }

    public void setCellHeight(Integer cellHeight) {
        this.cellHeight = cellHeight;
    }

    public String getEditPic() {
        return editPic;
    }

    public void setEditPic(String editPic) {
        this.editPic = editPic;
    }

    public Boolean getFullLine() {
        return fullLine;
    }

    public void setFullLine(Boolean fullLine) {
        this.fullLine = fullLine;
    }

    public Integer getBgviewHeight() {
        return bgviewHeight;
    }

    public void setBgviewHeight(Integer bgviewHeight) {
        this.bgviewHeight = bgviewHeight;
    }

    public Integer getRows() {
        return rows;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getColumns() {
        return columns;
    }

    public void setColumns(Integer columns) {
        this.columns = columns;
    }

    public Integer getLineWidth() {
        return lineWidth;
    }

    public void setLineWidth(Integer lineWidth) {
        this.lineWidth = lineWidth;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", groupId=").append(groupId);
        sb.append(", thumbPic=").append(thumbPic);
        sb.append(", cellWidth=").append(cellWidth);
        sb.append(", cellHeight=").append(cellHeight);
        sb.append(", editPic=").append(editPic);
        sb.append(", fullLine=").append(fullLine);
        sb.append(", bgviewHeight=").append(bgviewHeight);
        sb.append(", rows=").append(rows);
        sb.append(", columns=").append(columns);
        sb.append(", lineWidth=").append(lineWidth);
        sb.append(", sort=").append(sort);
        sb.append(", createTime=").append(createTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}