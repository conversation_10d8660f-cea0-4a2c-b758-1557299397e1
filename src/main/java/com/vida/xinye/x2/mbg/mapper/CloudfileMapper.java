package com.vida.xinye.x2.mbg.mapper;

import com.vida.xinye.x2.mbg.model.Cloudfile;
import com.vida.xinye.x2.mbg.model.CloudfileExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Author：Chenjy
 * Date:2025/7/17
 * Description:
 */
public interface CloudfileMapper {
    long countByExample(CloudfileExample example);

    int deleteByExample(CloudfileExample example);

    int deleteByPrimaryKey(Long id);

    int insert(Cloudfile row);

    int insertSelective(Cloudfile row);

    List<Cloudfile> selectByExample(CloudfileExample example);

    Cloudfile selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("row") Cloudfile row, @Param("example") CloudfileExample example);

    int updateByExample(@Param("row") Cloudfile row, @Param("example") CloudfileExample example);

    int updateByPrimaryKeySelective(Cloudfile row);

    int updateByPrimaryKey(Cloudfile row);
}
