package com.vida.xinye.x2.mbg.mapper;

import com.vida.xinye.x2.mbg.model.HelpTutorial;
import com.vida.xinye.x2.mbg.model.HelpTutorialExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface HelpTutorialMapper {
    long countByExample(HelpTutorialExample example);

    int deleteByExample(HelpTutorialExample example);

    int deleteByPrimaryKey(Long id);

    int insert(HelpTutorial row);

    int insertSelective(HelpTutorial row);

    List<HelpTutorial> selectByExample(HelpTutorialExample example);

    HelpTutorial selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("row") HelpTutorial row, @Param("example") HelpTutorialExample example);

    int updateByExample(@Param("row") HelpTutorial row, @Param("example") HelpTutorialExample example);

    int updateByPrimaryKeySelective(HelpTutorial row);

    int updateByPrimaryKey(HelpTutorial row);
}