package com.vida.xinye.x2.mbg.mapper;

import com.vida.xinye.x2.mbg.model.HelpProductManual;
import com.vida.xinye.x2.mbg.model.HelpProductManualExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface HelpProductManualMapper {
    long countByExample(HelpProductManualExample example);

    int deleteByExample(HelpProductManualExample example);

    int deleteByPrimaryKey(Long id);

    int insert(HelpProductManual row);

    int insertSelective(HelpProductManual row);

    List<HelpProductManual> selectByExample(HelpProductManualExample example);

    HelpProductManual selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("row") HelpProductManual row, @Param("example") HelpProductManualExample example);

    int updateByExample(@Param("row") HelpProductManual row, @Param("example") HelpProductManualExample example);

    int updateByPrimaryKeySelective(HelpProductManual row);

    int updateByPrimaryKey(HelpProductManual row);
}