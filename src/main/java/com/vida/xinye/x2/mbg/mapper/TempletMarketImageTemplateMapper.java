package com.vida.xinye.x2.mbg.mapper;

import com.vida.xinye.x2.mbg.model.TempletMarketImageTemplate;
import com.vida.xinye.x2.mbg.model.TempletMarketImageTemplateExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TempletMarketImageTemplateMapper {
    long countByExample(TempletMarketImageTemplateExample example);

    int deleteByExample(TempletMarketImageTemplateExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TempletMarketImageTemplate row);

    int insertSelective(TempletMarketImageTemplate row);

    List<TempletMarketImageTemplate> selectByExample(TempletMarketImageTemplateExample example);

    TempletMarketImageTemplate selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("row") TempletMarketImageTemplate row, @Param("example") TempletMarketImageTemplateExample example);

    int updateByExample(@Param("row") TempletMarketImageTemplate row, @Param("example") TempletMarketImageTemplateExample example);

    int updateByPrimaryKeySelective(TempletMarketImageTemplate row);

    int updateByPrimaryKey(TempletMarketImageTemplate row);
}