package com.vida.xinye.x2.mbg.mapper;

import com.vida.xinye.x2.mbg.model.PrintHistory;
import com.vida.xinye.x2.mbg.model.PrintHistoryExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface PrintHistoryMapper {
    long countByExample(PrintHistoryExample example);

    int deleteByExample(PrintHistoryExample example);

    int deleteByPrimaryKey(Long id);

    int insert(PrintHistory row);

    int insertSelective(PrintHistory row);

    List<PrintHistory> selectByExampleWithBLOBs(PrintHistoryExample example);

    List<PrintHistory> selectByExample(PrintHistoryExample example);

    PrintHistory selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("row") PrintHistory row, @Param("example") PrintHistoryExample example);

    int updateByExampleWithBLOBs(@Param("row") PrintHistory row, @Param("example") PrintHistoryExample example);

    int updateByExample(@Param("row") PrintHistory row, @Param("example") PrintHistoryExample example);

    int updateByPrimaryKeySelective(PrintHistory row);

    int updateByPrimaryKeyWithBLOBs(PrintHistory row);

    int updateByPrimaryKey(PrintHistory row);
}