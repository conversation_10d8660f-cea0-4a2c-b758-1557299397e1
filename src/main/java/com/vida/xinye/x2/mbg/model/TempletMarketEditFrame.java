package com.vida.xinye.x2.mbg.model;

import java.io.Serializable;

public class TempletMarketEditFrame implements Serializable {
    private Long id;

    private Long templateId;

    private Integer frameX;

    private Integer frameY;

    private Integer frameWidth;

    private Integer frameHeight;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Long templateId) {
        this.templateId = templateId;
    }

    public Integer getFrameX() {
        return frameX;
    }

    public void setFrameX(Integer frameX) {
        this.frameX = frameX;
    }

    public Integer getFrameY() {
        return frameY;
    }

    public void setFrameY(Integer frameY) {
        this.frameY = frameY;
    }

    public Integer getFrameWidth() {
        return frameWidth;
    }

    public void setFrameWidth(Integer frameWidth) {
        this.frameWidth = frameWidth;
    }

    public Integer getFrameHeight() {
        return frameHeight;
    }

    public void setFrameHeight(Integer frameHeight) {
        this.frameHeight = frameHeight;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", templateId=").append(templateId);
        sb.append(", frameX=").append(frameX);
        sb.append(", frameY=").append(frameY);
        sb.append(", frameWidth=").append(frameWidth);
        sb.append(", frameHeight=").append(frameHeight);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}