package com.vida.xinye.x2.validator;

import org.springframework.stereotype.Component;

import java.util.regex.Pattern;

/**
 * 密码强度验证器
 *
 * <AUTHOR>
 * @date 2025/08/12
 */
@Component
public class PasswordStrengthValidator {

    // 密码强度正则表达式：至少包含数字和字母，长度6-20位
    private static final Pattern STRONG_PASSWORD_PATTERN = 
        Pattern.compile("^(?=.*[0-9])(?=.*[a-zA-Z]).{6,20}$");

    // 弱密码模式：纯数字、纯字母、连续字符等
    private static final Pattern[] WEAK_PATTERNS = {
        Pattern.compile("^\\d+$"), // 纯数字
        Pattern.compile("^[a-zA-Z]+$"), // 纯字母
        Pattern.compile("^(.)\\1{5,}$"), // 相同字符重复6次以上
        Pattern.compile("^(012|123|234|345|456|567|678|789|890|abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz)"), // 连续字符
    };

    /**
     * 验证密码强度
     *
     * @param password 密码
     * @return 验证结果
     */
    public PasswordValidationResult validatePassword(String password) {
        if (password == null || password.trim().isEmpty()) {
            return new PasswordValidationResult(false, "密码不能为空");
        }

        // 长度检查
        if (password.length() < 6) {
            return new PasswordValidationResult(false, "密码长度不能少于6位");
        }

        if (password.length() > 20) {
            return new PasswordValidationResult(false, "密码长度不能超过20位");
        }

        // 强度检查
        if (!STRONG_PASSWORD_PATTERN.matcher(password).matches()) {
            return new PasswordValidationResult(false, "密码必须包含数字和字母");
        }

        // 弱密码检查
        for (Pattern weakPattern : WEAK_PATTERNS) {
            if (weakPattern.matcher(password.toLowerCase()).find()) {
                return new PasswordValidationResult(false, "密码过于简单，请使用更复杂的密码");
            }
        }

        return new PasswordValidationResult(true, "密码强度符合要求");
    }

    /**
     * 密码验证结果
     */
    public static class PasswordValidationResult {
        private final boolean valid;
        private final String message;

        public PasswordValidationResult(boolean valid, String message) {
            this.valid = valid;
            this.message = message;
        }

        public boolean isValid() {
            return valid;
        }

        public String getMessage() {
            return message;
        }
    }
}
