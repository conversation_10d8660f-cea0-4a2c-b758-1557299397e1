package com.vida.xinye.x2.validator;

import com.vida.xinye.x2.annotation.ValidImageBase64;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class ImageBase64Validator implements ConstraintValidator<ValidImageBase64, String> {
    private long maxSize;

    @Override
    public void initialize(ValidImageBase64 constraintAnnotation) {
        this.maxSize = constraintAnnotation.maxSize();
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (value == null) return true;

        // 检查大小
        int padding = value.endsWith("==") ? 2 : (value.endsWith("=") ? 1 : 0);
        long size = (long)(value.length() * 3L / 4) - padding;
        if (size > maxSize) {
            context.buildConstraintViolationWithTemplate("图片大小不能超过2MB")
                    .addConstraintViolation();
            return false;
        }

        return true;
    }
}