package com.vida.xinye.x2.validator;

import com.vida.xinye.x2.constant.AccountTypeEnum;
import org.springframework.stereotype.Component;

import java.util.regex.Pattern;

/**
 * 账号安全验证器
 *
 * <AUTHOR>
 * @date 2025/08/12
 */
@Component
public class AccountSecurityValidator {

    // 邮箱格式验证
    private static final Pattern EMAIL_PATTERN = 
        Pattern.compile("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$");

    // 手机号格式验证（中国大陆）
    private static final Pattern PHONE_PATTERN = 
        Pattern.compile("^1[3-9]\\d{9}$");

    /**
     * 验证账号格式
     *
     * @param accountType 账号类型
     * @param accountIdentifier 账号标识符
     * @return 是否有效
     */
    public boolean validateAccountFormat(AccountTypeEnum accountType, String accountIdentifier) {
        if (accountIdentifier == null || accountIdentifier.trim().isEmpty()) {
            return false;
        }

        switch (accountType) {
            case EMAIL:
                return EMAIL_PATTERN.matcher(accountIdentifier).matches();
            case PHONE:
                return PHONE_PATTERN.matcher(accountIdentifier).matches();
            case WECHAT:
            case QQ:
                // 第三方账号由第三方平台保证格式正确性
                return accountIdentifier.length() > 0;
            default:
                return false;
        }
    }

    /**
     * 验证邮箱格式
     *
     * @param email 邮箱
     * @return 是否有效
     */
    public boolean isValidEmail(String email) {
        return email != null && EMAIL_PATTERN.matcher(email).matches();
    }

    /**
     * 验证手机号格式
     *
     * @param phone 手机号
     * @return 是否有效
     */
    public boolean isValidPhone(String phone) {
        return phone != null && PHONE_PATTERN.matcher(phone).matches();
    }

    /**
     * 检查账号是否为敏感账号（需要额外验证）
     *
     * @param accountIdentifier 账号标识符
     * @return 是否为敏感账号
     */
    public boolean isSensitiveAccount(String accountIdentifier) {
        if (accountIdentifier == null) {
            return false;
        }

        String lowerAccount = accountIdentifier.toLowerCase();
        
        // 检查是否包含敏感词汇
        String[] sensitiveWords = {"admin", "root", "test", "system", "service"};
        for (String word : sensitiveWords) {
            if (lowerAccount.contains(word)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 验证验证码格式
     *
     * @param captcha 验证码
     * @return 是否有效
     */
    public boolean isValidCaptcha(String captcha) {
        return captcha != null && 
               captcha.matches("^\\d{4,6}$") && // 4-6位数字
               captcha.length() >= 4 && 
               captcha.length() <= 6;
    }
}
