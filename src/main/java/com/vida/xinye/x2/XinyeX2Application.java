package com.vida.xinye.x2;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

@EnableFeignClients
@EnableDiscoveryClient
@SpringBootApplication(scanBasePackages = "com.vida.xinye.x2")
@MapperScan("com.vida.xinye.x2.mbg.mapper")
public class XinyeX2Application {

    public static void main(String[] args) {
        SpringApplication.run(XinyeX2Application.class, args);
    }

}
