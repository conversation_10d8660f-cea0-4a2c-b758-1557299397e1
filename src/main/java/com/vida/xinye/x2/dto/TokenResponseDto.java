package com.vida.xinye.x2.dto;

import lombok.Data;

/**
 * Token响应DTO
 *
 * <AUTHOR>
 * @date 2025/08/13
 */
@Data
public class TokenResponseDto {
    
    /**
     * 访问令牌
     */
    private String accessToken;
    
    /**
     * 刷新令牌
     */
    private String refreshToken;
    
    /**
     * 访问令牌过期时间（时间戳）
     */
    private Long expiration;
    
    /**
     * 刷新令牌过期时间（时间戳）
     */
    private Long refreshTokenExpiration;
    
    /**
     * 令牌类型
     */
    private String tokenType = "Bearer";
}
