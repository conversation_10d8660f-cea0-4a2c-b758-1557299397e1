package com.vida.xinye.x2.dto.param;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Description: 家长模式切换参数
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/06/04 10:00
 */
@Data
public class ParentModeToggleParam {
  /**
   * 操作类型 enable/disable
   */
  @NotBlank(message = "{parent.mode.action.notnull}")
  private String action;

  /**
   * 青少年密码（仅开启时需要）
   */
  private String teenagerPassword;
}