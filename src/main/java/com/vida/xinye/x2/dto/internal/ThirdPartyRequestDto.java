package com.vida.xinye.x2.dto.internal;

import lombok.Data;

import java.util.Map;

/**
 * 第三方接口请求DTO
 *
 * <AUTHOR>
 * @date 2025/08/14
 */
@Data
public class ThirdPartyRequestDto {

    /**
     * 请求ID
     */
    private String requestId;

    /**
     * 图片数据（Base64编码）
     */
    private String imageBase64;

    /**
     * 图片URL
     */
    private String imageUrl;

    /**
     * 图片格式
     */
    private String imageFormat;

    /**
     * 学科类型
     */
    private String subject;

    /**
     * 年级
     */
    private String grade;

    /**
     * 超时时间（秒）
     */
    private Integer timeoutSeconds;

    /**
     * 扩展参数
     */
    private Map<String, Object> extraParams;

    /**
     * 天壤接口特有参数（根据官方文档）
     */
    @Data
    public static class TianrangRequest extends ThirdPartyRequestDto {
        /**
         * API密钥（APPCODE）
         */
        private String apiKey;

        // 根据官方文档，天壤接口只需要 media_id 参数
        // 其他参数都是多余的，已移除
    }

    /**
     * 有道接口特有参数（根据官方文档）
     */
    @Data
    public static class YoudaoRequest extends ThirdPartyRequestDto {
        /**
         * 应用ID
         */
        private String appKey;

        /**
         * 应用密钥
         */
        private String appSecret;

        /**
         * 签名
         */
        private String sign;

        /**
         * UUID，和curtime一起防请求重放
         */
        private String salt;

        /**
         * 当前UTC时间戳（秒）
         */
        private String curtime;

        /**
         * 图片类型，目前只支持Base64，固定为1
         */
        private String imageType = "1";

        /**
         * 服务器响应类型，目前只支持json，固定为json
         */
        private String docType = "json";

        /**
         * 签名类型，固定为v3
         */
        private String signType = "v3";
    }

    /**
     * 阿里云接口特有参数
     */
    @Data
    public static class AliyunRequest extends ThirdPartyRequestDto {
        /**
         * 访问密钥ID
         */
        private String accessKeyId;

        /**
         * 访问密钥Secret
         */
        private String accessKeySecret;

        /**
         * 接口端点
         */
        private String endpoint;

        /**
         * 区域ID
         */
        private String regionId = "cn-hangzhou";

        /**
         * 产品代码
         */
        private String product = "ocr-api";

        /**
         * 版本
         */
        private String version = "2021-07-07";

        /**
         * 动作
         */
        private String action = "RecognizeEduPaperStructed";

        /**
         * 是否需要旋转（根据官方文档）
         */
        private Boolean needRotate = false;

        /**
         * 是否输出原始坐标（根据官方文档）
         */
        private Boolean outputOricoord = false;
    }
}
