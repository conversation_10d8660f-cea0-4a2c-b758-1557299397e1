package com.vida.xinye.x2.dto.internal;

import com.vida.xinye.x2.api.IErrorCode;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 第三方接口响应DTO
 *
 * <AUTHOR>
 * @date 2025/08/14
 */
@Data
public class ThirdPartyResponseDto {

    /**
     * 请求ID
     */
    private String requestId;

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 错误码
     */
    private IErrorCode errorCode;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 响应时间（毫秒）
     */
    private Long responseTime;

    /**
     * 原始响应数据
     */
    private Object rawData;

    /**
     * 天壤接口响应
     */
    @Data
    public static class TianrangResponse extends ThirdPartyResponseDto {
        /**
         * 状态码
         */
        private Integer code;

        /**
         * 消息
         */
        private String message;

        /**
         * 数据
         */
        private TianrangData data;

        @Data
        public static class TianrangData {
            /**
             * 检测结果
             */
            private List<TianrangQuestion> questions;

            /**
             * 图片高度
             */
            private Integer height;

            /**
             * 图片宽度
             */
            private Integer width;

            /**
             * 处理时间（秒）
             */
            private Double elapsedTime;

            /**
             * 总体置信度（计算得出）
             */
            private Double confidence;
        }

        @Data
        public static class TianrangQuestion {
            /**
             * 题目ID
             */
            private String questionId;

            /**
             * 题目类型
             */
            private String type;

            /**
             * 题目内容
             */
            private String content;

            /**
             * 选项
             */
            private List<String> options;

            /**
             * 答案
             */
            private String answer;

            /**
             * 位置信息
             */
            private BoundingBox bbox;

            /**
             * 置信度
             */
            private Double confidence;

            @Data
            public static class BoundingBox {
                private Integer x;
                private Integer y;
                private Integer width;
                private Integer height;

                /**
                 * 原始坐标数组 [x1,y1,x2,y2,x3,y3,x4,y4]
                 */
                private List<Integer> coordinates;
            }
        }
    }

    /**
     * 有道接口响应
     */
    @Data
    public static class YoudaoResponse extends ThirdPartyResponseDto {
        /**
         * 有道接口错误码（字符串格式）
         */
        private String youdaoErrorCode;

        /**
         * 结果
         */
        private List<YoudaoResult> Result;

        /**
         * 方向
         */
        private String orientation;

        /**
         * 语言类型
         */
        private String lanType;

        @Data
        public static class YoudaoResult {
            /**
             * 行信息
             */
            private List<YoudaoLine> lines;

            /**
             * 区域信息
             */
            private YoudaoRegion region;
        }

        @Data
        public static class YoudaoLine {
            /**
             * 边界框
             */
            private List<List<Integer>> boundingBox;

            /**
             * 边界框字符串（真实响应格式）
             */
            private String boundingBoxStr;

            /**
             * 文本内容
             */
            private String text;

            /**
             * 置信度
             */
            private Double confidence;

            /**
             * 分割信息（真实响应字段）
             */
            private String segment;

            /**
             * 题目类型（扩展字段）
             */
            private String questionType;

            /**
             * 选项（扩展字段）
             */
            private List<String> options;
        }

        @Data
        public static class YoudaoRegion {
            /**
             * 边界框
             */
            private List<List<Integer>> boundingBox;

            /**
             * 区域类型
             */
            private String type;
        }
    }

    /**
     * 阿里云接口响应
     */
    @Data
    public static class AliyunResponse extends ThirdPartyResponseDto {
        /**
         * 请求ID
         */
        private String RequestId;

        /**
         * 数据
         */
        private AliyunData Data;

        @Data
        public static class AliyunData {
            /**
             * 角度
             */
            private Integer angle;

            /**
             * 高度
             */
            private Integer height;

            /**
             * 宽度
             */
            private Integer width;

            /**
             * 结构化结果
             */
            private List<AliyunStructuredResult> structuredResult;
        }

        @Data
        public static class AliyunStructuredResult {
            /**
             * 类型
             */
            private String type;

            /**
             * 值
             */
            private String value;

            /**
             * 位置
             */
            private AliyunPosition pos;

            /**
             * 子项
             */
            private List<AliyunStructuredResult> subResults;

            /**
             * 置信度
             */
            private Double confidence;
        }

        @Data
        public static class AliyunPosition {
            /**
             * 顶点坐标
             */
            private List<AliyunPoint> points;

            /**
             * 左上角X
             */
            private Integer x;

            /**
             * 左上角Y
             */
            private Integer y;

            /**
             * 宽度
             */
            private Integer w;

            /**
             * 高度
             */
            private Integer h;
        }

        @Data
        public static class AliyunPoint {
            private Integer x;
            private Integer y;
        }
    }
}
