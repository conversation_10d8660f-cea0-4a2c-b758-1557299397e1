package com.vida.xinye.x2.dto;

import com.vida.xinye.x2.domain.TempletMarketImageInfo;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 模板集市DTO（数据传输对象）
 * 用于在不同层之间传递模板集市相关的数据
 *
 * <AUTHOR>
 * @date 2025/06/27
 */
@Data
public class TempletMarketDto  {
    private Long id;
    @NotNull
    private TempletMarketImageInfo resUrl;
    @NotNull
    private TempletMarketImageInfo listUrl;
    private TempletMarketImageInfo downUrl;
    private TempletMarketImageInfo rightUrl;
    private TempletMarketImageInfo leftUrl;
    private TempletMarketImageInfo topUrl;
    private TempletMarketImageInfo addLeftUrl;
    @NotNull
    private Long groupId;
    private Integer sort;
    private Date createTime;
    // 额外的字段-模板数据
    private String data;
}