package com.vida.xinye.x2.dto.param;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 手机号密码登录参数
 *
 * <AUTHOR>
 * @date 2025/08/12
 */
@Data
public class PhonePasswordLoginDto {
    
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;
    
    @NotBlank(message = "密码不能为空")
    private String password;
}
