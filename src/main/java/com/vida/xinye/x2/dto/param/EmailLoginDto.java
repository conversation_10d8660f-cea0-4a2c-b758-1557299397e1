package com.vida.xinye.x2.dto.param;

import lombok.Data;
import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;

/**
 * 邮箱登录参数
 *
 * <AUTHOR>
 * @date 2025/08/12
 */
@Data
public class EmailLoginDto {
    
    @NotBlank(message = "邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    private String email;
    
    private String password; // 密码登录时使用
    
    private String captcha; // 验证码登录时使用
    
    @NotBlank(message = "登录方式不能为空")
    private String loginMethod; // password 或 captcha
}
