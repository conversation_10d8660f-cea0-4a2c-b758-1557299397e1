package com.vida.xinye.x2.dto.param;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 第三方登录参数（参考snapTag项目设计）
 *
 * <AUTHOR>
 * @date 2025/08/12
 */
@Data
public class ThirdPartyLoginDto {

    /**
     * 用户授权时生成的access_token
     * 新浪微博、微信、QQ都需要
     */
//    @NotBlank(message = "访问令牌不能为空")
    private String accessToken;

    /**
     * 用户的唯一标识（微信、QQ需要）
     */
    private String openId;

    /**
     * 应用的唯一ID（QQ需要）
     */
    private String appId;

    /**
     * 来源标识
     * 微信：app, miniprogram, web
     * QQ：app
     */
    private String from;

    /**
     * 登录方式：byweixin, byqq, bysina, byappleid
     */
    @NotBlank(message = "登录方式不能为空")
    private String loginWay;

    /**
     * 用户昵称（可选）
     */
    private String nickname;

    /**
     * 用户头像（可选）
     */
    private String headPic;

    /**
     * 性别（可选）
     */
    private String sex;

    /**
     * unionId（微信、QQ可能有）
     */
    private String unionId;

    /**
     * Apple ID登录时的JWT token
     */
    private String jwtToken;

    /**
     * Apple ID登录时的用户标识
     */
    private String appleAccount;

    /**
     * 刷新令牌（可选）
     */
    private String refreshToken;

    /**
     * 令牌过期时间（秒）
     */
    private Long expiresIn;
}
