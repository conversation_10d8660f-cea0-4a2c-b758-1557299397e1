package com.vida.xinye.x2.dto.param;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Description: 身份证验证请求参数
 * @Author: wangpf
 * @date 2025/06/03
 */
@Data
public class IdCardVerifyDto {

  /**
   * 姓名
   */
  @NotBlank(message = "{idcard.name.notnull}")
  private String name;

  /**
   * 身份证号
   */
  @NotBlank(message = "{idcard.number.notnull}")
  private String idcard;
}