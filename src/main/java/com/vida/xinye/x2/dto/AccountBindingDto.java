package com.vida.xinye.x2.dto;

import lombok.Data;
import java.util.Date;

/**
 * 账号绑定信息DTO
 *
 * <AUTHOR>
 * @date 2025/08/12
 */
@Data
public class AccountBindingDto {
    
    private Long id;
    
    private Long userId;
    
    private Integer accountType; // 账号类型
    
    private String accountTypeDesc; // 账号类型描述
    
    private String accountIdentifier; // 账号标识符
    
    private Boolean isPrimary; // 是否为主账号
    
    private Boolean isVerified; // 是否已验证
    
    private Date bindTime; // 绑定时间
}
