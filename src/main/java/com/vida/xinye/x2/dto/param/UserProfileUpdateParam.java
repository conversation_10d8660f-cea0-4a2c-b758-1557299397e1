package com.vida.xinye.x2.dto.param;

import com.vida.xinye.x2.domain.enumtype.UserRoleType;
import com.vida.xinye.x2.domain.enumtype.GradeType;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Description: 用户身份和年级选择参数
 * @Author: z<PERSON><PERSON><PERSON>
 * @Date: 2025/06/03 17:45
 */
@Data
public class UserProfileUpdateParam {
  @NotNull(message = "{user.role.notnull}")
  private UserRoleType roleType;

  @NotNull(message = "{user.grade.notnull}")
  private GradeType gradeType;
}