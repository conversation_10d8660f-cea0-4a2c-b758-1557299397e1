package com.vida.xinye.x2.dto;

import lombok.Getter;
import lombok.Setter;

/**
 * token对象
 *
 * <AUTHOR>
 * @date 2023/11/6
 */
@Getter
@Setter
public class TokenDto {
    private Long userId;
    /**
     * 账号
     */
    private String username;
    /**
     * token
     */
    private String accessToken;
    /**
     * 过期时间
     */
    private Long expiration;
    private Integer parentMode;
    private String roleType;
    private String gradeType;
    private String refreshToken; // 刷新token
    private Long refreshTokenExpiration; // 刷新token过期时间（毫秒）

    private String nickname;

    private String avatar;

    /**
     * 是否需要绑定手机号（第三方登录后可能需要）
     */
    private Boolean requirePhoneBinding;

    /**
     * 登录方式
     */
    private String loginWay;
}
