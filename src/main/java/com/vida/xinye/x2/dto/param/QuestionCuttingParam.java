package com.vida.xinye.x2.dto.param;

import com.vida.xinye.x2.constant.QuestionCuttingConstant;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 智能切题请求参数
 *
 * <AUTHOR>
 * @date 2025/08/14
 */
@Data
public class QuestionCuttingParam {

    /**
     * 图片文件（文件上传方式）
     */
    private MultipartFile imageFile;

    /**
     * 图片URL（URL方式）
     */
    private String imageUrl;

    /**
     * 图片Base64编码（Base64方式）
     */
    private String imageBase64;

    /**
     * 切题策略
     * 可选值：BEST_QUALITY, FASTEST, AGGREGATED, SPECIFIED, ROUND_ROBIN
     */
    private String strategy = QuestionCuttingConstant.Default.DEFAULT_STRATEGY;

    /**
     * 指定的接口提供商（当strategy为SPECIFIED时使用）
     * 可选值：tianrang, youdao, aliyun
     * 默认值将从配置文件中读取
     */
    private String provider;

    /**
     * 学科类型
     * 可选值：math, chinese, english, physics, chemistry, biology, history, geography, politics
     */
    private String subject;

    /**
     * 年级
     * 可选值：primary_1~6, junior_1~3, senior_1~3
     */
    private String grade;

    /**
     * 是否需要详细信息
     */
    private Boolean needDetail = false;

    /**
     * 是否启用缓存
     */
    private Boolean enableCache = true;

    /**
     * 超时时间（秒）
     */
    private Integer timeoutSeconds;

    /**
     * 扩展参数
     */
    private java.util.Map<String, Object> extraParams;

    /**
     * 请求ID（用于追踪）
     */
    private String requestId;
}

/**
 * 批量切题请求参数
 */
@Data
class BatchQuestionCuttingParam {

    /**
     * 图片列表
     */
    @NotNull(message = "图片列表不能为空")
    @Size(min = 1, max = 10, message = "图片数量必须在1-10之间")
    private List<ImageInfo> images;

    /**
     * 切题策略
     */
    private String strategy = QuestionCuttingConstant.Default.DEFAULT_STRATEGY;

    /**
     * 指定的接口提供商
     */
    private String provider;

    /**
     * 学科类型
     */
    private String subject;

    /**
     * 年级
     */
    private String grade;

    /**
     * 是否需要详细信息
     */
    private Boolean needDetail = false;

    /**
     * 是否启用缓存
     */
    private Boolean enableCache = true;

    /**
     * 超时时间（秒）
     */
    private Integer timeoutSeconds;

    /**
     * 是否并行处理
     */
    private Boolean parallel = true;

    /**
     * 扩展参数
     */
    private java.util.Map<String, Object> extraParams;

    /**
     * 请求ID（用于追踪）
     */
    private String requestId;

    @Data
    public static class ImageInfo {
        /**
         * 图片ID（用户自定义）
         */
        private String imageId;

        /**
         * 图片文件
         */
        private MultipartFile imageFile;

        /**
         * 图片URL
         */
        private String imageUrl;

        /**
         * 图片Base64编码
         */
        private String imageBase64;

        /**
         * 图片描述
         */
        private String description;
    }
}
