package com.vida.xinye.x2.dto.param;

import com.vida.xinye.x2.dto.LanguageNameDto;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
public class TempletGroupParam {
    @NotBlank(message = "分组名称不能为空")
    private String name;

    @Min(value = 0, message = "排序值不能小于0")
    private Integer sort;

    //多语言字段
    private List<LanguageNameDto> i18nNames;
}
