package com.vida.xinye.x2.dto.edu;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class XopTextbookCatalog  implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer id;
    private String name;
    @JsonProperty("textbook_id")
    private Integer textbookId;
    @JsonProperty("parent_id")
    private Integer parentId;
    private Integer ordinal;
    private String type;
    private Date createTime;
    private Date updateTime;
}
