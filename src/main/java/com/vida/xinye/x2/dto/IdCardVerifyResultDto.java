/**
 * @Description: 身份证验证结果响应DTO
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/01/16 15:30
 */
package com.vida.xinye.x2.dto;

import lombok.Data;
import java.io.Serializable;

@Data
public class IdCardVerifyResultDto implements Serializable {
  private static final long serialVersionUID = 1L;

  /**
   * 请求是否成功
   */
  private Boolean success;

  /**
   * 响应消息
   */
  private String msg;

  /**
   * 响应状态码
   */
  private Integer code;

  /**
   * 响应数据
   */
  private IdCardVerifyData data;

  @Data
  public static class IdCardVerifyData implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 生日
     */
    private String birthday;

    /**
     * 验证结果：1-不一致，0-一致
     */
    private Integer result;

    /**
     * 地址
     */
    private String address;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 性别
     */
    private String sex;

    /**
     * 描述
     */
    private String desc;
  }
}