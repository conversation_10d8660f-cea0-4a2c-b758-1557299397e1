package com.vida.xinye.x2.dto.edu;

import lombok.Data;

import java.io.Serializable;

/**
 * 学科网接口返回
 *
 * <AUTHOR>
 * @date 2025/2/13
 */
@Data
public class XopQuestionPushVO {
    private String id;  // 试题id
    private String courseId; // 课程id
    private Integer difficultyLevel;// 试题难度等级（17 容易，18 较易，19 一般，20 较难，21 困难）
    private IdNamePair type;// 试题类型
    private XopQuestion question;
    private String title;
    private String collectId;
    // 是否已加入错题本
    private Boolean isInWrongBook;
    // 错题本ID（搜索解析错题、提分训练错题不同表）
    private Long wrongBookId;
}
