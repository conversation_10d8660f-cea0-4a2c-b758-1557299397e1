package com.vida.xinye.x2.dto.param;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 设置密码参数
 *
 * <AUTHOR>
 * @date 2025/08/12
 */
@Data
public class SetPasswordDto {
    
    @NotBlank(message = "新密码不能为空")
    @Size(min = 6, max = 20, message = "密码长度必须在6-20位之间")
    private String newPassword;
    
    @NotBlank(message = "确认密码不能为空")
    private String confirmPassword;
    
    private String oldPassword; // 修改密码时需要提供旧密码
}
