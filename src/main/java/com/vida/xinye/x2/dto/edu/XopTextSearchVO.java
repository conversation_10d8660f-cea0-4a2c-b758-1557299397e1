package com.vida.xinye.x2.dto.edu;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 学科网接口返回
 *
 * <AUTHOR>
 * @date 2025/1/14
 */
@Data
public class XopTextSearchVO implements Serializable {
    private static final long serialVersionUID = 1L;

    private String id;                  // 试题ID
    private List<XopTextSearchContentVO> contentVOS;  // 内容集合
    private String knowledge;            // 知识点
    private boolean hadGotAnswer;         // 是否已获取答案
}
