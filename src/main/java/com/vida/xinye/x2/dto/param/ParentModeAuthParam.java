package com.vida.xinye.x2.dto.param;

import lombok.Data;
import javax.validation.constraints.NotBlank;

/**
 * @Description: 家长模式认证参数
 * @Author: z<PERSON><PERSON><PERSON>
 * @Date: 2025/06/03 18:10
 */
@Data
public class ParentModeAuthParam {
  @NotBlank(message = "{parent.name.notnull}")
  private String parentName;

  @NotBlank(message = "{parent.idcard.notnull}")
  private String parentIdcard;

  @NotBlank(message = "{teenager.password.notnull}")
  private String teenagerPassword;
}