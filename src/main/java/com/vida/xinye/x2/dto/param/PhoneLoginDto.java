package com.vida.xinye.x2.dto.param;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 手机号登录参数（统一接口）
 * 支持密码登录和验证码登录两种方式
 *
 * <AUTHOR>
 * @date 2025/08/13
 */
@Data
public class PhoneLoginDto {
    
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;
    
    private String password; // 密码登录时使用
    
    private String captcha; // 验证码登录时使用（对应原来的smsCode）
    
    @NotBlank(message = "登录方式不能为空")
    private String loginMethod; // password 或 captcha
}
