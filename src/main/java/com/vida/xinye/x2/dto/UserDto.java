package com.vida.xinye.x2.dto;

import lombok.Data;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.Collection;
import java.util.Date;

/**
 * 用户实例
 *
 * <AUTHOR>
 * @date 2024/11/5
 */
@Data
public class UserDto implements UserDetails {
    Long id;
    String nickname;
    Date lastLoginTime;
    Date createTime;
    /**
     * 用户登录权限参数
     */
    String username;
    String password;
    boolean accountNonExpired;
    boolean accountNonLocked;
    boolean credentialsNonExpired;
    boolean enabled;
    Collection<? extends GrantedAuthority> authorities;

    String roleType;
    String gradeType;

    Integer parentMode;
    String parentName;
    String parentIdcard;
    String teenagerPassword;
}
