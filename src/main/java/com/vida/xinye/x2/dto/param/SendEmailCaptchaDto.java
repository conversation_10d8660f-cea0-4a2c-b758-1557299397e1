package com.vida.xinye.x2.dto.param;

import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;

/**
 * 发送邮箱验证码请求参数
 */
@Data
public class SendEmailCaptchaDto {

    /**
     * 邮箱地址
     */
    @NotBlank(message = "邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    private String email;

    /**
     * 验证码用途：login-登录, register-注册, reset_password-重置密码, bind-绑定账号
     */
    private String purpose = "login";
}
