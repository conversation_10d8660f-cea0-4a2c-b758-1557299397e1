package com.vida.xinye.x2.dto;

import com.vida.xinye.x2.api.IErrorCode;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 批量智能切题结果DTO
 *
 * <AUTHOR>
 * @date 2025/08/14
 */
@Data
public class BatchQuestionCuttingResultDto {

    /**
     * 请求ID
     */
    private String requestId;

    /**
     * 批次ID
     */
    private String batchId;

    /**
     * 总体是否成功
     */
    private Boolean success;

    /**
     * 总处理时间（毫秒）
     */
    private Long totalProcessingTime;

    /**
     * 成功处理的图片数量
     */
    private Integer successCount;

    /**
     * 失败处理的图片数量
     */
    private Integer failureCount;

    /**
     * 总图片数量
     */
    private Integer totalCount;

    /**
     * 每张图片的处理结果
     */
    private List<ImageResult> imageResults;

    /**
     * 批量处理统计信息
     */
    private BatchStats batchStats;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 完成时间
     */
    private Date completeTime;

    /**
     * 单张图片处理结果
     */
    @Data
    public static class ImageResult {
        /**
         * 图片ID（用户自定义）
         */
        private String imageId;

        /**
         * 处理顺序
         */
        private Integer index;

        /**
         * 是否成功
         */
        private Boolean success;

        /**
         * 错误码
         */
        private IErrorCode errorCode;

        /**
         * 错误信息
         */
        private String errorMessage;

        /**
         * 切题结果
         */
        private QuestionCuttingResultDto result;

        /**
         * 处理开始时间
         */
        private Date startTime;

        /**
         * 处理结束时间
         */
        private Date endTime;

        /**
         * 处理时间（毫秒）
         */
        private Long processingTime;
    }

    /**
     * 批量处理统计信息
     */
    @Data
    public static class BatchStats {
        /**
         * 平均处理时间（毫秒）
         */
        private Double averageProcessingTime;

        /**
         * 最快处理时间（毫秒）
         */
        private Long fastestProcessingTime;

        /**
         * 最慢处理时间（毫秒）
         */
        private Long slowestProcessingTime;

        /**
         * 成功率
         */
        private Double successRate;

        /**
         * 总识别题目数量
         */
        private Integer totalQuestionCount;

        /**
         * 平均质量评分
         */
        private Double averageQualityScore;

        /**
         * 平均置信度
         */
        private Double averageConfidence;

        /**
         * 使用的接口提供商统计
         */
        private java.util.Map<String, Integer> providerUsageStats;

        /**
         * 错误统计
         */
        private java.util.Map<String, Integer> errorStats;

        /**
         * 题目类型统计
         */
        private java.util.Map<String, Integer> questionTypeStats;

        /**
         * 学科统计
         */
        private java.util.Map<String, Integer> subjectStats;
    }
}
