package com.vida.xinye.x2.dto;

import lombok.Data;

/**
 * 第三方用户信息DTO
 *
 * <AUTHOR>
 * @date 2025/08/12
 */
@Data
public class ThirdPartyUserInfoDto {
    
    private String openId; // 第三方平台用户唯一标识
    
    private String unionId; // 第三方平台统一标识
    
    private String nickname; // 昵称
    
    private String avatarUrl; // 头像URL
    
    private String accessToken; // 访问令牌
    
    private String refreshToken; // 刷新令牌
    
    private Long expiresIn; // 令牌过期时间（秒）
    
    private String provider; // 第三方平台标识
}
