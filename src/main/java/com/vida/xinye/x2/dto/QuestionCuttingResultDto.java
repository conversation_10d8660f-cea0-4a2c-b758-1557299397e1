package com.vida.xinye.x2.dto;

import com.vida.xinye.x2.api.IErrorCode;
import com.vida.xinye.x2.enums.QuestionCuttingEnum;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 智能切题结果DTO
 *
 * <AUTHOR>
 * @date 2025/08/14
 */
@Data
public class QuestionCuttingResultDto {

    /**
     * 请求ID
     */
    private String requestId;

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 错误码
     */
    private IErrorCode errorCode;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 使用的接口提供商
     */
    private String provider;

    /**
     * 使用的策略
     */
    private String strategy;

    /**
     * 处理时间（毫秒）
     */
    private Long processingTime;

    /**
     * 识别到的题目列表
     */
    private List<QuestionInfo> questions;

    /**
     * 质量评分（1-5分）
     */
    private Integer qualityScore;

    /**
     * 置信度（0-1）
     */
    private Double confidence;

    /**
     * 原始图片信息
     */
    private ImageInfo originalImage;

    /**
     * 详细信息（当needDetail=true时返回）
     */
    private DetailInfo detail;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 题目信息
     */
    @Data
    public static class QuestionInfo {
        /**
         * 题目ID
         */
        private String questionId;

        /**
         * 题目序号
         */
        private Integer questionNumber;

        /**
         * 题目类型
         */
        private String questionType;

        /**
         * 题目内容（文本）
         */
        private String content;

        /**
         * 题目内容（HTML格式）
         */
        private String htmlContent;

        /**
         * 选项列表（选择题）
         */
        private List<OptionInfo> options;

        /**
         * 答案
         */
        private String answer;

        /**
         * 解析
         */
        private String analysis;

        /**
         * 学科
         */
        private String subject;

        /**
         * 年级
         */
        private String grade;

        /**
         * 难度等级（1-5）
         */
        private Integer difficulty;

        /**
         * 知识点
         */
        private List<String> knowledgePoints;

        /**
         * 题目在原图中的位置
         */
        private BoundingBox boundingBox;

        /**
         * 切题后的图片URL
         */
        private String croppedImageUrl;

        /**
         * 置信度
         */
        private Double confidence;

        /**
         * 扩展属性
         */
        private Map<String, Object> extraProperties;
    }

    /**
     * 选项信息
     */
    @Data
    public static class OptionInfo {
        /**
         * 选项标识（A、B、C、D等）
         */
        private String label;

        /**
         * 选项内容
         */
        private String content;

        /**
         * 选项内容（HTML格式）
         */
        private String htmlContent;

        /**
         * 是否为正确答案
         */
        private Boolean isCorrect;
    }

    /**
     * 边界框信息
     */
    @Data
    public static class BoundingBox {
        /**
         * 宽度
         */
        private Integer width;

        /**
         * 高度
         */
        private Integer height;

        /**
         * 四个顶点坐标（精确的区域定位）
         */
        private List<Point> points;
    }

    /**
     * 坐标点
     */
    @Data
    public static class Point {
        private Integer x;
        private Integer y;
    }

    /**
     * 图片信息
     */
    @Data
    public static class ImageInfo {
        /**
         * 图片URL
         */
        private String url;

        /**
         * 图片宽度
         */
        private Integer width;

        /**
         * 图片高度
         */
        private Integer height;

        /**
         * 图片格式
         */
        private String format;

        /**
         * 图片大小（字节）
         */
        private Long size;
    }

    /**
     * 详细信息
     */
    @Data
    public static class DetailInfo {
        /**
         * 所有接口的调用结果
         */
        private List<ProviderResult> providerResults;

        /**
         * 聚合过程信息
         */
        private AggregationInfo aggregationInfo;

        /**
         * 性能统计
         */
        private PerformanceStats performanceStats;
    }

    /**
     * 接口提供商结果
     */
    @Data
    public static class ProviderResult {
        /**
         * 提供商名称
         */
        private String provider;

        /**
         * 调用状态
         */
        private String status;

        /**
         * 响应时间（毫秒）
         */
        private Long responseTime;

        /**
         * 质量评分
         */
        private Integer qualityScore;

        /**
         * 置信度
         */
        private Double confidence;

        /**
         * 识别结果
         */
        private List<QuestionInfo> questions;

        /**
         * 错误信息
         */
        private String errorMessage;

        /**
         * 原始响应数据
         */
        private Object rawResponse;
    }

    /**
     * 聚合信息
     */
    @Data
    public static class AggregationInfo {
        /**
         * 聚合策略
         */
        private String strategy;

        /**
         * 参与聚合的提供商数量
         */
        private Integer providerCount;

        /**
         * 聚合权重
         */
        private Map<String, Double> weights;

        /**
         * 聚合规则
         */
        private String rules;
    }

    /**
     * 性能统计
     */
    @Data
    public static class PerformanceStats {
        /**
         * 总处理时间
         */
        private Long totalTime;

        /**
         * 图片预处理时间
         */
        private Long preprocessTime;

        /**
         * 接口调用时间
         */
        private Long apiCallTime;

        /**
         * 结果处理时间
         */
        private Long postprocessTime;

        /**
         * 内存使用量（字节）
         */
        private Long memoryUsage;
    }
}
