package com.vida.xinye.x2.dto;

import com.vida.xinye.x2.domain.TempletMarketImageInfo;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
public class TempletBorderDto {

    private Long id;
    @NotNull
    private TempletMarketImageInfo resUrl;
    @NotNull
    private TempletMarketImageInfo listUrl;
    private TempletMarketImageInfo leftUrl;
    private TempletMarketImageInfo rightUrl;
    private TempletMarketImageInfo topUrl;
    private TempletMarketImageInfo downUrl;
    private TempletMarketImageInfo leftTopUrl;
    private TempletMarketImageInfo rightTopUrl;
    private TempletMarketImageInfo leftDownUrl;
    private TempletMarketImageInfo rightDownUrl;
    @NotNull
    private Long groupId;
    private Date createTime;
}
