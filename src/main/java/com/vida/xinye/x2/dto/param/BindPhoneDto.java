package com.vida.xinye.x2.dto.param;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 绑定手机号参数
 *
 * <AUTHOR>
 * @date 2025/08/12
 */
@Data
public class BindPhoneDto {
    
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;
    
    @NotBlank(message = "验证码不能为空")
    private String captcha;
}
