package com.vida.xinye.x2.dto.param;

import com.vida.xinye.x2.dto.LanguageNameDto;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class TempletMarketGroupParam {
    @NotBlank(message = "分组名称不能为空")
    private String name;

    @NotNull(message = "分组类型不能为空")
    private Integer type;

    @Min(value = 0, message = "排序值不能小于0")
    private Integer sort;

    //多语言字段
    private List<LanguageNameDto> i18nNames;
}
