package com.vida.xinye.x2.dto.param;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * @Description: 阿里云身份证配置属性
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/12/19 21:45
 */
@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "aliyun.idcard")
public class AliyunIdCardProperties {
  /**
   * 阿里云AppKey
   */
  private String appKey;
  /**
   * 阿里云AppSecret
   */
  private String appSecret;
  /**
   * 身份证认证接口URL
   */
  private String url;
  /**
   * 请求方法
   */
  private String method;
}