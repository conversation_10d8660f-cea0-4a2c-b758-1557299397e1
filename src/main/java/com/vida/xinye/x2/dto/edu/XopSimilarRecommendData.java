package com.vida.xinye.x2.dto.edu;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 学科网接口返回
 * 举一反三推题结果
 *
 * <AUTHOR>
 * @date 2025/2/13
 */
@Data
public class XopSimilarRecommendData implements Serializable {
    /***
     * 知识点ID列表
     */
    private List<String> kpoint_ids;
    /***
     * 试题是否包含媒体内容（0 没有，1 有音频 2 有解题视频 3 音频和解题视频都有；解题视频不在试题中输出，请通过"获取解题视频"接口获取）
     */
    private Integer media;
    /**
     * 解题视频封面（一个解题视频对应一个封面，如果没有则为空；一般只有一个解题视频）
     */
    private List<String> exp_video_posters;
    /**
     * 试题解析（HTML格式），请参考《试题结构和HTML渲染说明文档》
     */
    private String explanation;

    /**
     * 试题类型
     */
    private IdNamePair type;

    /**
     * 试题出现在试卷中的年份，可能多个
     */
    private List<Integer> years;

    /**
     * 试题相似度，范围为0~100，值越大越相似，保留两位小数
     */
    private Double similarity;

    /**
     * 课程
     */
    private List<IdNamePair> course;

    /**
     * 在线作答（0 不支持，1 支持）；选择题或者打标了机阅的试题
     */
    private Integer answer_scoreable;

    /**
     * 试题ID
     */
    private String id;

    /**
     * 试题入库日期
     */
    private String create_date;

    /**
     * 课程ID
     */
    private Integer course_id;

    /**
     * 试题难度等级（17 容易，18 较易，19 一般，20 较难，21 困难）
     */
    private Integer difficulty_level;

    /**
     *
     */
    private List<String> tag_ids;

    /**
     * 试题类型ID
     */
    private String type_id;

    /**
     * 知识点列表
     */
    private List<IdNamePair> kpoints;

    /**
     * 试卷类型ID列表
     */
    private List<String> paper_type_ids;

    /**
     * 教材目录ID列表
     */
    private List<String> catalog_ids;

    /**
     * 标签列表
     */
    private List<IdNamePair> tags;
    /**
     *试题难度，0~1之间的数字，值越小难度越大（(0.9,1] 容易，(0.8,0.9] 较易，(0.5,0.8] 一般，(0.3,0.5] 较难，[0, 0.3] 困难）
     */
    private Double difficulty;

    /**
     * 单词列表，可通过"单词查询"接口获取单词的详细信息
     */
    private List<IdNamePair> en_words;

    /**
     *  试题答案（HTML格式），请参考《试题结构和HTML渲染说明文档》
     */
    private String answer;

    /**
     * 教材目录列表
     */
    private List<IdNamePair> catalogs;
    /**
     *
     */
    private List<String> en_word_ids;

    /**
     * 试题题干（HTML格式），请参考《试题结构和HTML渲染说明文档》
     */
    private String stem;

}
