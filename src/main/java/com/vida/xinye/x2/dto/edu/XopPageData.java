package com.vida.xinye.x2.dto.edu;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class XopPageData<T> {
    @JsonProperty("page_index")
    private Integer pageIndex;

    @JsonProperty("page_size")
    private Integer pageSize;

    @JsonProperty("total_size")
    private Integer totalSize;

    @JsonProperty("total_page")
    private Integer totalPage;

    private List<T> items;
}
