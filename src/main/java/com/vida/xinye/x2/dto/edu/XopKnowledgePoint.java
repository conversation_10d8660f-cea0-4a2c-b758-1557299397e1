package com.vida.xinye.x2.dto.edu;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class XopKnowledgePoint implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer id;
    private String name;

    @JsonProperty("course_id")
    private Integer courseId;

    private String type;

    @JsonProperty("parent_id")
    private Integer parentId;

    private Integer ordinal;

    @JsonProperty("for_lite")
    private Boolean forLite;

    private String tag;

    @JsonProperty("create_time")
    private LocalDateTime createTime;

    @JsonProperty("update_time")
    private LocalDateTime updateTime;

    private Integer depth;

    @JsonProperty("root_id")
    private Integer rootId;
}
