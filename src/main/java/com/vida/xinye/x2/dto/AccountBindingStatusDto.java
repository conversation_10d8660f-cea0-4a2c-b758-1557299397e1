package com.vida.xinye.x2.dto;

import lombok.Data;
import java.util.List;

/**
 * 账号绑定状态DTO
 *
 * <AUTHOR>
 * @date 2025/08/13
 */
@Data
public class AccountBindingStatusDto {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 手机号绑定状态
     */
    private PhoneBindingInfo phoneBinding;
    
    /**
     * 邮箱绑定状态
     */
    private EmailBindingInfo emailBinding;
    
    /**
     * 第三方绑定状态列表
     */
    private List<ThirdPartyBindingInfo> thirdPartyBindings;
    
    @Data
    public static class PhoneBindingInfo {
        private boolean bound;
        private String phone; // 脱敏显示
        private boolean verified;
    }
    
    @Data
    public static class EmailBindingInfo {
        private boolean bound;
        private String email; // 脱敏显示
        private boolean verified;
    }
    
    @Data
    public static class ThirdPartyBindingInfo {
        private String provider; // wechat, qq, weibo, apple
        private String providerName; // 微信, QQ, 微博, Apple ID
        private boolean bound;
        private String nickname;
        private String avatar;
        private java.util.Date bindTime;
        private java.util.Date lastLoginTime;
    }
}
