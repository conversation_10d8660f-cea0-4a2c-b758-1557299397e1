/**
 * @Description: 用户身份类型
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/06/03 17:40
 */
package com.vida.xinye.x2.domain.enumtype;

public enum UserRoleType {
  STUDENT, PARENT, TEACHER, HOBBYIST;

  public boolean isValid() {
    //是否是enum里面的值
    for (UserRoleType type : UserRoleType.values()) {
      if (type.name().equals(this.name())) {
        return true;
      }
    }
    return false;
  }
}