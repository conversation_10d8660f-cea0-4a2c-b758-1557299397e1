package com.vida.xinye.x2.domain.param.xkw;

import lombok.Data;

import java.util.List;

/**
 * 提分训练推题请求参数
 *
 * <AUTHOR>
 * @date 2025/1/14
 */
@Data
public class ScoreImprovementRecommendParam {
    private Integer course_id;
    private Integer year;
    private List<String> kpoint_ids;
    private List<String> type_ids;
    private Integer count;
    private List<String> paper_type_ids;
    private Integer version_id;
    private String session_id;
    private Integer kpoint_match_type;
    private List<String> catalog_ids;
    private List<String> difficulty_levels;
    private List<String> area_ids;
    private String formula_pic_format;
    private List<String> en_words_ids;
    private Integer textbook_id;
}