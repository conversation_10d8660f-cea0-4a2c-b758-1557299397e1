package com.vida.xinye.x2.domain.param.xkw;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 关键词搜题请求参数
 *
 * <AUTHOR>
 * @date 2025/xx/xx
 */
@Data
public class KeywordSearchParam {
    @NotNull(message = "课程ID是必填项")
    private Integer course_id;
    private Boolean highlight;
    private List<Integer> area_ids;
    private String formula_pic_format;
    @NotEmpty(message = "搜索词是必填项")
    @Size(max = 200, message = "搜索词长度不能超过200")
    private String keywords;
    private Integer year;
    private List<Integer> type_ids;
    private Integer page_index;
    private List<Integer> difficulty_levels;
    private Integer page_size;
}