package com.vida.xinye.x2.domain;

import com.aliyun.openservices.aliyun.log.producer.Result;
import com.aliyun.openservices.aliyun.log.producer.errors.ResultFailedException;
import com.aliyun.openservices.log.common.LogItem;
import com.google.common.util.concurrent.FutureCallback;
import org.checkerframework.checker.nullness.qual.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.atomic.AtomicLong;

/**
 * 阿里云日志 回调处理
 *
 * <AUTHOR>
 * @date 2024/1/3
 */
public class AliyunLogFutureCallback implements FutureCallback<Result> {

    private static final Logger LOGGER = LoggerFactory.getLogger(AliyunLogFutureCallback.class);

    private final String project;
    private final String logStore;
    private final LogItem logItem;
    private final AtomicLong completed;

    public AliyunLogFutureCallback(
            String project, String logStore, LogItem logItem, AtomicLong completed) {
        this.project = project;
        this.logStore = logStore;
        this.logItem = logItem;
        this.completed = completed;
    }

    @Override
    public void onSuccess(@Nullable Result result) {
        LOGGER.info("Send aliYun logs successfully.");
        completed.getAndIncrement();
    }

    @Override
    public void onFailure(Throwable t) {
        if (t instanceof ResultFailedException) {
            Result result = ((ResultFailedException) t).getResult();
            LOGGER.error(
                    "Failed to send log, project={}, logStore={}, logItem={}, result={}",
                    project,
                    logStore,
                    logItem.ToJsonString(),
                    result);
        } else {
            LOGGER.error("Failed to send log, e=", t);
        }
        completed.getAndIncrement();
    }
}
