package com.vida.xinye.x2.domain.param;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;


/**
 * 意见反馈请求参数
 *
 * <AUTHOR>
 * @date 2024/11/15
 */
@Data
public class FeedbackParam {
    private Long userId;

    @NotBlank(message = "反馈内容不能为空")
    private String content;

    private String contact;

    private List<String> images;
}
