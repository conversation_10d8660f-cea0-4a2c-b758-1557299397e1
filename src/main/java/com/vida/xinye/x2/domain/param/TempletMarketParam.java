package com.vida.xinye.x2.domain.param;

import com.vida.xinye.x2.mbg.model.TempletMarketEditFrame;
import com.vida.xinye.x2.mbg.model.TempletMarketImageFrame;
import lombok.Data;


/**
 * 标签请求参数
 *
 * <AUTHOR>
 * @date 2025/06/27
 */
@Data
public class TempletMarketParam {
    private Long groupId;
    private String templateThumbPic;
    private Integer cellWidth;
    private Integer cellHeight;
    private TempletMarketImageFrame imageFrame;
    private String templateEditPic;
    private TempletMarketEditFrame editFrame;
    private Boolean templateFullLine;
    private Integer templateBgviewHeight;
    private Integer templateRows;
    private Integer templateColumns;
    private Integer templateLineWidth;
    private Integer templateSort;
}
