package com.vida.xinye.x2.domain;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * bean比较类
 *
 * <AUTHOR>
 * @date 2024/1/3
 */
@Data
public class BeanDiff {

    private List<FieldDiff> fieldDiffs;

    public void addFieldDiff(FieldDiff fieldDiff) {
        if (fieldDiffs == null) {
            fieldDiffs = new ArrayList<>();
        }
        fieldDiffs.add(fieldDiff);
    }
}
