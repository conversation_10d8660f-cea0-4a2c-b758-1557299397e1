package com.vida.xinye.x2.domain;

import lombok.Data;

/**
 * 阿里云日志结构
 *
 * <AUTHOR>
 * @date 2024/1/3
 */
@Data
public class OperationLogItem extends BaseLogItem {
    /**
     * 用户ID
     */
    private String userId;
    /**
     * 用户名称
     */
    private String username;

    /**
     * 操作类型 - insert：新增  update：修改  delete：删除
     */
    private String type;
    /**
     * 操作大类 - 例如 应用管理
     */
    private String category;
    /**
     * 操作子类 - 例如 流程应用
     */
    private String subCategory;

}
