package com.vida.xinye.x2.domain.param;

import com.vida.xinye.x2.mbg.model.WrongQuestion;
import com.vida.xinye.x2.mbg.model.WrongQuestionTag;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;


/**
 * 错题标签请求参数
 *
 * <AUTHOR>
 * @date 2025/02/21
 */
@Data
public class WrongQuestionParam extends WrongQuestion {
    @NotBlank
    private String sourceType;
    private String thumbnail;
    @NotBlank
    private String data;

    private String note;
    // 错题标签
    private List<WrongQuestionTag> tags;
    // 课程ID
    private Long courseId;

    private Long userId;
}
