package com.vida.xinye.x2.domain.param;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description: 批量移动错题到指定科目 参数
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/07/26 16:00
 */
@Data
public class BatchMoveWrongQuestionsParam {

  @NotNull(message = "来源科目ID不能为空")
  private Long fromSubjectTagId;

  @NotEmpty(message = "错题ID列表不能为空")
  private List<Long> ids;

  @NotNull(message = "目标科目ID不能为空")
  private Long toSubjectTagId;
}