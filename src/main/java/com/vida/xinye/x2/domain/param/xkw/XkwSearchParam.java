package com.vida.xinye.x2.domain.param.xkw;

import com.vida.xinye.x2.annotation.ValidImageBase64;
import lombok.Data;


/**
 * 学科网拍搜题请求参数
 *
 * <AUTHOR>
 * @date 2025/1/14
 */
@Data
public class XkwSearchParam {
    // name 课程ID（不能为0，详见基础数据API一获取课程列表）
    private String course_id;
    // image_base64 图片的base64字符串（要求png格式，不超过2MB，尺寸不得小于5px或大于8192px)
    @ValidImageBase64(message = "图片不能超过2MB")
    private String image_base64;
    // count 返回的最大试题数
    private Integer count;
    // text
    private String text;


}
