package com.vida.xinye.x2.domain.param.edu;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 添加训练题到错题本请求参数
 *
 * <AUTHOR>
 * @date 2025/03/17
 */
@Data
public class AddTrainingQuestionParam {
  private Long userId;

  /**
   * 课程ID
   */
  @NotNull(message = "课程ID不能为空")
  private Long courseId;

  /**
   * 缩略图
   */
  private String thumbnail;

  /**
   * 训练题数据（JSON格式）
   */
  @NotNull(message = "训练题数据不能为空")
  private String data;

  /**
   * 试题类型ID
   * 单选、多选、判断、填空、简答、论述、计算题、计算题等
   */
  @NotNull(message = "训练题类型不能为空")
  private String typeId;

  /**
   * 试题难度等级（17 容易，18 较易，19 一般，20 较难，21 困难）
   */
  @NotNull(message = "训练题难度等级不能为空")
  private Integer difficultyLevel;

  /**
   * 年份
   */
  @NotNull(message = "年份不能为空")
  private Integer year;

  /**
   * 学科网试题ID
   */
  private String xkwQuestionId;

}