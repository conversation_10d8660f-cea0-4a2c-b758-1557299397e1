/**
 * @Description: 年级类型
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/06/03 17:40
 */
package com.vida.xinye.x2.domain.enumtype;

public enum GradeType {
  PRIMARY_1, PRIMARY_2, PRIMARY_3, PRIMARY_4, PRIMARY_5, PRIMARY_6,
  JUNIOR_1, JUNIOR_2, JUNIOR_3,
  SENIOR_1, SENIOR_2, SENIOR_3;

  public boolean isValid() {
    // 是否是enum里面的值
    for (GradeType type : GradeType.values()) {
      if (type.name().equals(this.name())) {
        return true;
      }
    }
    return false;
  }
}