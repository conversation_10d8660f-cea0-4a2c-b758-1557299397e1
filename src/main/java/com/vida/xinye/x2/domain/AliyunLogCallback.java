package com.vida.xinye.x2.domain;

import com.aliyun.openservices.aliyun.log.producer.Callback;
import com.aliyun.openservices.aliyun.log.producer.Result;
import com.aliyun.openservices.log.common.LogItem;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.atomic.AtomicLong;

/**
 * 阿里云日志 回调处理
 *
 * <AUTHOR>
 * @date 2024/1/3
 */
@Slf4j
public class AliyunLogCallback implements Callback {
    private final String project;
    private final String logStore;
    private final LogItem logItem;
    private final AtomicLong completed;

    public AliyunLogCallback(
            String project, String logStore, LogItem logItem, AtomicLong completed) {
        this.project = project;
        this.logStore = logStore;
        this.logItem = logItem;
        this.completed = completed;
    }

    @Override
    public void onCompletion(Result result) {
        try {
            if (result.isSuccessful()) {
                log.debug("Send log successfully.");
            } else {
                log.error(
                        "Failed to send log, project={}, logStore={}, logItem={}, result={}",
                        project,
                        logStore,
                        logItem.ToJsonString(),
                        result);
            }
        } finally {
            completed.getAndIncrement();
        }
    }
}
