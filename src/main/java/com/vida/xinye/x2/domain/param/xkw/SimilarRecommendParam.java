package com.vida.xinye.x2.domain.param.xkw;

import lombok.Data;

import javax.validation.constraints.NotBlank;


/**
 * 学科网拍搜题请求参数
 *
 * <AUTHOR>
 * @date 2025/1/14
 */
@Data
public class SimilarRecommendParam {
    // formula_pic_format 公式图片格式，支持两种：png或svg，默认是svg
    private String formula_pic_format;
    // count 返回的最大试题数，默认为5道题，最大10题
    private Integer count;
    @NotBlank(message = "试题ID不能为空!")
    // 试题ID
    private String question_id;

    private Integer typeId;


}
