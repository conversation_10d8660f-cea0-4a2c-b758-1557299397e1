package com.vida.xinye.x2.domain;

import cn.hutool.core.date.DateTime;
import lombok.Data;

/**
 * 阿里云日志结构
 *
 * <AUTHOR>
 * @date 2024/1/3
 */
@Data
public class BaseLogItem {
    /**
     * 调用的后台方法
     */
    private String method;
    /**
     * 操作参数
     */
    private String param;
    /**
     * 操作结果
     */
    private String result;
    /**
     * 操作描述
     */
    private String desc;
    /**
     * 操作耗时
     */
    private Long requestTime;
    /**
     * 请求路径
     */
    private String uri;
    /**
     * 请求ip
     */
    private String ip;
    /**
     * 请求扩展字段
     */
    private String extend;
    /**
     * 创建时间
     */
    private DateTime createTime;
}
