package com.vida.xinye.x2.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * 线程池的配置
 */
@EnableAsync
@Configuration
public class AsyncConfig {

    @Value("${thread.pool.max.size:60}")
    private int maxPoolSize;

    @Value("${thread.pool.core.size:30}")
    private int corePoolSize;

    @Value("${thread.pool.queue.capacity:120}")
    private int queueCapacity;

    @Bean("asyncLogExecutor")
    public AsyncTaskExecutor asyncLogExecutor() {
        ThreadPoolTaskExecutor asyncTaskExecutor = new ThreadPoolTaskExecutor();
        asyncTaskExecutor.setMaxPoolSize(maxPoolSize);
        asyncTaskExecutor.setCorePoolSize(corePoolSize);
        asyncTaskExecutor.setQueueCapacity(queueCapacity);
        asyncTaskExecutor.setThreadNamePrefix("async-log-exec-");
        asyncTaskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy()); // 设置拒绝策略
        asyncTaskExecutor.initialize();
        return asyncTaskExecutor;
    }
}
