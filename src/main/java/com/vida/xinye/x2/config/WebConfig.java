package com.vida.xinye.x2.config;

import com.vida.xinye.x2.annotation.AdminRestController;
import com.vida.xinye.x2.annotation.ApiRestController;
import com.vida.xinye.x2.component.UserAuthInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.PathMatchConfigurer;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 配置拦截器
 *
 * <AUTHOR>
 * @date 2024/11/4
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {
    // 以这种方式将拦截器注入为一个bean，可以防止拦截器中无法注入bean的问题出现
//    @Bean
//    public UserAuthInterceptor authInterceptor() {
//        return new UserAuthInterceptor();
//    }

    /**
     * 配置拦截器
     *
     * @param registry 相当于拦截器的注册中心
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
//        /** 用户端接口拦截器 */
////       下面这句代码相当于添加一个拦截器   添加的拦截器就是我们刚刚创建的
//        registry.addInterceptor(authInterceptor())
////       addPathPatterns()配置我们要拦截哪些路径 addPathPatterns("/**")表示拦截所有请求，包括我们的静态资源
//                .addPathPatterns("/**")
////       excludePathPatterns()表示我们要放行哪些（表示不用经过拦截器）
////       如果有静态资源的时候可以在这个地方放行
//                .excludePathPatterns("/", "/swagger/**", "/authen/**", "/captcha/**", "/cache/**");


    }

    @Override
    public void configurePathMatch(PathMatchConfigurer configurer) {
        configurer.addPathPrefix("/api", c -> c.isAnnotationPresent(ApiRestController.class));
        configurer.addPathPrefix("/admin", c -> c.isAnnotationPresent(AdminRestController.class));
    }

}
