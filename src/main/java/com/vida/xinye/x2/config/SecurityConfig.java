package com.vida.xinye.x2.config;

import com.vida.xinye.x2.component.CustomAuthenticationProvider;
import com.vida.xinye.x2.filter.ApiJwtRequestFilter;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.annotation.web.configurers.ExpressionUrlAuthorizationConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.security.web.util.matcher.RequestMatcher;

import java.util.Arrays;
import java.util.List;

/**
 * Security配置类
 *
 * <AUTHOR>
 * @date 2024/11/5
 */
@Configuration
@EnableWebSecurity
public class SecurityConfig extends WebSecurityConfigurerAdapter {
    // 提供一个方法来获取排除的匹配器列表
    @Getter
    private static final List<String> ignoredUrls = Arrays.asList(
            "/**/authen/**",
            "/api/authen/third-party/**",
            "/**/captcha/**",
            "/api/materialCategory/list",
            "/api/material/list",
            "/api/font/**",
            "/api/help/**",
            "/api/machine/list",
            // 学科网api，调试中放开权限
            "/api/xkw/**",
            "/api/wrongQuestionTag/**",
            "/api/wrongQuestion/**",
            "/api/training/**",
            "/api/oss/**",
            // 模板集市
            "/api/templet-market-group",
            "/api/templet-market",
            // 模板管理
            "/api/system/templet-group",
            "/api/system/templet",
            //标签边框
            "/api/templet-border",
            "/api/templet-border-group",
            // 可以继续添加更多的排除规则
            "/api/authen/third-party/login",
            "/question/cutting/diagnostic/youdao/config"

    );

    @Autowired
    private ApiJwtRequestFilter apiJwtRequestFilter;

    @Autowired
    @Lazy
    private CustomAuthenticationProvider customAuthenticationProvider;

    @Override
    protected void configure(AuthenticationManagerBuilder auth) {
        auth.authenticationProvider(customAuthenticationProvider);
    }

    @Override
    protected void configure(HttpSecurity http) throws Exception {
        ExpressionUrlAuthorizationConfigurer<HttpSecurity>.ExpressionInterceptUrlRegistry expressionInterceptUrlRegistry = http.csrf().disable() // 禁用CSRF保护
                .authorizeRequests();
        //遍历excludedUrls列表，为每个排除路径调用.antMatchers()方法
        for (String matchUrl : ignoredUrls) {
            expressionInterceptUrlRegistry.antMatchers(matchUrl).permitAll();
        }
        expressionInterceptUrlRegistry.anyRequest().authenticated() // 其他所有请求都需要认证
                .and()
                .sessionManagement()
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS); // 无状态会话

        // 对所有请求应用 apiJwtRequestFilter，除了上面配置的 permitAll 路径
        http.addFilterBefore(apiJwtRequestFilter, UsernamePasswordAuthenticationFilter.class);
    }

    @Override
    @Bean
    public AuthenticationManager authenticationManagerBean() throws Exception {
        return super.authenticationManagerBean();
    }

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
}
