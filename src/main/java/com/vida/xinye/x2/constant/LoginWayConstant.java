package com.vida.xinye.x2.constant;

/**
 * 登录方式常量（参考snapTag项目）
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
public class LoginWayConstant {
    
    /**
     * 手机号登录（验证码）
     */
    public static final String LOGIN_BY_MOBILE = "bymobile";
    
    /**
     * 手机号密码登录
     */
    public static final String LOGIN_BY_MOBILE_PASSWORD = "bymobile_password";

    /**
     * 短信验证码登录
     */
    public static final String LOGIN_BY_SMS = "bysms";
    
    /**
     * 邮箱登录（验证码）
     */
    public static final String LOGIN_BY_EMAIL = "byemail";
    
    /**
     * 邮箱密码登录
     */
    public static final String LOGIN_BY_EMAIL_PASSWORD = "byemail_password";
    
    /**
     * 微信登录
     */
    public static final String LOGIN_BY_WEIXIN = "byweixin";
    
    /**
     * QQ登录
     */
    public static final String LOGIN_BY_QQ = "byqq";
    
    /**
     * 新浪微博登录
     */
    public static final String LOGIN_BY_SINA = "bysina";
    
    /**
     * Apple ID登录
     */
    public static final String LOGIN_BY_APPLE_ID = "byappleid";
    
    /**
     * 友盟一键登录
     */
    public static final String LOGIN_BY_YM_ONEKEY = "byyumeng";
    
    // 第三方登录验证URL
    /**
     * 新浪微博第三方登录验证接口地址
     */
    public static final String SINA_THIRD_LOGIN_CHECK_URL = "https://api.weibo.com/oauth2/get_token_info";
    
    /**
     * QQ第三方登录验证接口地址
     */
    public static final String QQ_THIRD_LOGIN_CHECK_URL = "https://openmobile.qq.com/user/get_simple_userinfo";
    
    /**
     * 微信第三方登录验证接口地址
     */
    public static final String WEIXIN_THIRD_LOGIN_CHECK_URL = "https://api.weixin.qq.com/sns/auth";
    
    /**
     * 微信获取用户信息接口地址
     */
    public static final String WEIXIN_THIRD_USER_INFO_URL = "https://api.weixin.qq.com/cgi-bin/user/info";
    
    /**
     * 微信网页授权获取用户信息接口
     */
    public static final String WEIXIN_WEB_USER_INFO_URL = "https://api.weixin.qq.com/sns/userinfo";
    
    // 登录状态
    /**
     * 登录成功
     */
    public static final String LOGIN_SUCCESS = "success";
    
    /**
     * 登录失败
     */
    public static final String LOGIN_FAILED = "failed";
    
    /**
     * 登录错误
     */
    public static final String LOGIN_ERROR = "error";
    
    // 微信openId类型
    /**
     * APP微信登录
     */
    public static final String WECHAT_FROM_APP = "app";
    
    /**
     * 小程序登录
     */
    public static final String WECHAT_FROM_MINIPROGRAM = "miniprogram";
    
    /**
     * 网页登录
     */
    public static final String WECHAT_FROM_WEB = "web";
    
    // 验证码用途
    /**
     * 登录验证码
     */
    public static final String CAPTCHA_PURPOSE_LOGIN = "login";
    
    /**
     * 注册验证码
     */
    public static final String CAPTCHA_PURPOSE_REGISTER = "register";
    
    /**
     * 重置密码验证码
     */
    public static final String CAPTCHA_PURPOSE_RESET_PASSWORD = "reset_password";
    
    /**
     * 绑定账号验证码
     */
    public static final String CAPTCHA_PURPOSE_BIND = "bind";
    
    // 安全限制
    /**
     * 登录失败最大次数
     */
    public static final int MAX_LOGIN_ATTEMPTS = 5;
    
    /**
     * 账号锁定时间（分钟）
     */
    public static final int ACCOUNT_LOCK_MINUTES = 30;
    
    /**
     * 验证码有效期（分钟）
     */
    public static final int CAPTCHA_EXPIRE_MINUTES = 5;
    
    /**
     * 单日短信验证码上限
     */
    public static final int DAY_SMS_MAX_TOTAL_NUM = 50;
    
    /**
     * 单日邮箱验证码上限
     */
    public static final int DAY_EMAIL_MAX_TOTAL_NUM = 20;
}
