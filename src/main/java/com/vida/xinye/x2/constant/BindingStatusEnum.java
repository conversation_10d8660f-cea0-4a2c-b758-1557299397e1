package com.vida.xinye.x2.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 账号绑定状态枚举
 *
 * <AUTHOR>
 * @date 2025/08/12
 */
@Getter
@AllArgsConstructor
public enum BindingStatusEnum {
    // 未绑定
    UNBOUND(0, "未绑定"),
    // 已绑定
    BOUND(1, "已绑定"),
    // 绑定失败
    BIND_FAILED(2, "绑定失败");

    private final int code;
    private final String description;

    public static BindingStatusEnum getByCode(int code) {
        for (BindingStatusEnum status : values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return null;
    }
}
