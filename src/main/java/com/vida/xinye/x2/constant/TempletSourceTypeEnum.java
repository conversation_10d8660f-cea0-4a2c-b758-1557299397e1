package com.vida.xinye.x2.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 枚举类型，表示标签来源。
 *
 * <AUTHOR>
 * @date 2025/06/27
 */
@Getter
@AllArgsConstructor
public enum TempletSourceTypeEnum {
    // 纸条，用户创建，值为0
    NOTE((byte) 0),
    // SYSTEM表示系统创建，值为1
    SYSTEM((byte) 1),
    // 标签，用户创建，值为2
    LABEL((byte) 2),
    // 模板，用户创建，值为3
    TEMPLET((byte) 3);
    private final byte value;
}

