package com.vida.xinye.x2.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 第三方登录平台枚举
 *
 * <AUTHOR>
 * @date 2025/08/12
 */
@Getter
@AllArgsConstructor
public enum ThirdPartyProviderEnum {
    WECHAT("wechat", "微信"),
    QQ("qq", "QQ"),
    SINA("sina", "微博"),
    APPLE("apple", "Apple ID");

    private final String code;
    private final String name;

    public static ThirdPartyProviderEnum getByCode(String code) {
        for (ThirdPartyProviderEnum provider : values()) {
            if (provider.getCode().equals(code)) {
                return provider;
            }
        }
        return null;
    }
}
