package com.vida.xinye.x2.constant;

/**
 * 智能切题常量定义
 *
 * <AUTHOR>
 * @date 2025/08/14
 */
public class QuestionCuttingConstant {

    /**
     * 第三方接口提供商
     */
    public static class Provider {
        public static final String TIANRANG = "tianrang";
        public static final String YOUDAO = "youdao";
        public static final String ALIYUN = "aliyun";
    }

    /**
     * 切题策略
     */
    public static class Strategy {
        /**
         * 最佳质量 - 选择识别质量最好的结果
         */
        public static final String BEST_QUALITY = "BEST_QUALITY";

        /**
         * 最快速度 - 选择响应最快的接口
         */
        public static final String FASTEST = "FASTEST";

        /**
         * 聚合结果 - 综合多个接口的结果
         */
        public static final String AGGREGATED = "AGGREGATED";

        /**
         * 指定提供商 - 使用指定的接口
         */
        public static final String SPECIFIED = "SPECIFIED";

        /**
         * 轮询 - 轮流使用不同接口
         */
        public static final String ROUND_ROBIN = "ROUND_ROBIN";
    }

    /**
     * 图片格式
     */
    public static class ImageFormat {
        public static final String JPG = "jpg";
        public static final String JPEG = "jpeg";
        public static final String PNG = "png";
        public static final String BMP = "bmp";
        public static final String GIF = "gif";
    }

    /**
     * 题目类型
     */
    public static class QuestionType {
        /**
         * 选择题
         */
        public static final String CHOICE = "choice";

        /**
         * 填空题
         */
        public static final String FILL_BLANK = "fill_blank";

        /**
         * 解答题
         */
        public static final String ANSWER = "answer";

        /**
         * 判断题
         */
        public static final String JUDGE = "judge";

        /**
         * 计算题
         */
        public static final String CALCULATION = "calculation";

        /**
         * 其他
         */
        public static final String OTHER = "other";
    }

    /**
     * 学科分类
     */
    public static class Subject {
        public static final String MATH = "math";
        public static final String CHINESE = "chinese";
        public static final String ENGLISH = "english";
        public static final String PHYSICS = "physics";
        public static final String CHEMISTRY = "chemistry";
        public static final String BIOLOGY = "biology";
        public static final String HISTORY = "history";
        public static final String GEOGRAPHY = "geography";
        public static final String POLITICS = "politics";
    }

    /**
     * 年级分类
     */
    public static class Grade {
        public static final String PRIMARY_1 = "primary_1";
        public static final String PRIMARY_2 = "primary_2";
        public static final String PRIMARY_3 = "primary_3";
        public static final String PRIMARY_4 = "primary_4";
        public static final String PRIMARY_5 = "primary_5";
        public static final String PRIMARY_6 = "primary_6";
        public static final String JUNIOR_1 = "junior_1";
        public static final String JUNIOR_2 = "junior_2";
        public static final String JUNIOR_3 = "junior_3";
        public static final String SENIOR_1 = "senior_1";
        public static final String SENIOR_2 = "senior_2";
        public static final String SENIOR_3 = "senior_3";
    }

    /**
     * 错误码 - 使用 ResultCode 枚举
     */
    public static class ErrorCode {
        /**
         * 图片格式不支持
         */
        public static final com.vida.xinye.x2.api.ResultCode UNSUPPORTED_IMAGE_FORMAT =
            com.vida.xinye.x2.api.ResultCode.QUESTION_CUTTING_UNSUPPORTED_IMAGE_FORMAT;

        /**
         * 图片尺寸过大
         */
        public static final com.vida.xinye.x2.api.ResultCode IMAGE_SIZE_TOO_LARGE =
            com.vida.xinye.x2.api.ResultCode.QUESTION_CUTTING_IMAGE_SIZE_TOO_LARGE;

        /**
         * 图片内容为空
         */
        public static final com.vida.xinye.x2.api.ResultCode EMPTY_IMAGE_CONTENT =
            com.vida.xinye.x2.api.ResultCode.QUESTION_CUTTING_EMPTY_IMAGE_CONTENT;

        /**
         * 接口调用失败
         */
        public static final com.vida.xinye.x2.api.ResultCode API_CALL_FAILED =
            com.vida.xinye.x2.api.ResultCode.QUESTION_CUTTING_API_CALL_FAILED;

        /**
         * 接口响应超时
         */
        public static final com.vida.xinye.x2.api.ResultCode API_TIMEOUT =
            com.vida.xinye.x2.api.ResultCode.QUESTION_CUTTING_API_TIMEOUT;

        /**
         * 接口返回格式错误
         */
        public static final com.vida.xinye.x2.api.ResultCode INVALID_API_RESPONSE =
            com.vida.xinye.x2.api.ResultCode.QUESTION_CUTTING_INVALID_API_RESPONSE;

        /**
         * 配置错误
         */
        public static final com.vida.xinye.x2.api.ResultCode CONFIG_ERROR =
            com.vida.xinye.x2.api.ResultCode.QUESTION_CUTTING_CONFIG_ERROR;

        /**
         * 未找到可用的接口
         */
        public static final com.vida.xinye.x2.api.ResultCode NO_AVAILABLE_PROVIDER =
            com.vida.xinye.x2.api.ResultCode.QUESTION_CUTTING_NO_AVAILABLE_PROVIDER;

        /**
         * 切题结果为空
         */
        public static final com.vida.xinye.x2.api.ResultCode EMPTY_CUTTING_RESULT =
            com.vida.xinye.x2.api.ResultCode.QUESTION_CUTTING_EMPTY_CUTTING_RESULT;

        /**
         * 系统内部错误
         */
        public static final com.vida.xinye.x2.api.ResultCode INTERNAL_ERROR =
            com.vida.xinye.x2.api.ResultCode.QUESTION_CUTTING_INTERNAL_ERROR;
    }

    /**
     * 缓存键前缀
     */
    public static class CacheKey {
        public static final String CUTTING_RESULT = "question_cutting:result:";
        public static final String PROVIDER_STATUS = "question_cutting:provider:";
        public static final String USER_QUOTA = "question_cutting:quota:";
    }

    /**
     * HTTP头部
     */
    public static class HttpHeader {
        public static final String CONTENT_TYPE = "Content-Type";
        public static final String AUTHORIZATION = "Authorization";
        public static final String USER_AGENT = "User-Agent";
        public static final String X_API_KEY = "X-API-Key";
        public static final String X_REQUEST_ID = "X-Request-Id";
    }

    /**
     * 默认值（当配置文件中没有配置时的fallback值）
     */
    public static class Default {
        public static final int TIMEOUT_SECONDS = 30;
        public static final int RETRY_COUNT = 2;
        public static final int MAX_CONCURRENT_REQUESTS = 10;
        public static final long MAX_IMAGE_SIZE = 10 * 1024 * 1024; // 10MB
        public static final String DEFAULT_STRATEGY = Strategy.BEST_QUALITY;
        public static final String DEFAULT_PROVIDER = Provider.TIANRANG;
        public static final boolean ENABLE_AGGREGATION = true;
        public static final boolean ENABLE_CACHE = true;
        public static final int CACHE_EXPIRE_SECONDS = 3600;
        public static final String[] SUPPORTED_FORMATS = {"jpg", "jpeg", "png", "bmp", "gif"};
    }
}
