package com.vida.xinye.x2.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 登录方式枚举
 *
 * <AUTHOR>
 * @date 2025/08/12
 */
@Getter
@AllArgsConstructor
public enum LoginMethodEnum {
    // 密码登录
    PASSWORD("password", "密码登录"),
    // 验证码登录
    CAPTCHA("captcha", "验证码登录"),
    // 第三方登录
    THIRD_PARTY("third_party", "第三方登录");

    private final String code;
    private final String description;

    public static LoginMethodEnum getByCode(String code) {
        for (LoginMethodEnum method : values()) {
            if (method.getCode().equals(code)) {
                return method;
            }
        }
        return null;
    }
}
