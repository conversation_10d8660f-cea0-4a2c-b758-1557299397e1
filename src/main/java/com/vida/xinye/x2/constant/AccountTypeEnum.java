package com.vida.xinye.x2.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 枚举类型，表示账号类型。
 *
 * <AUTHOR>
 * @date 2024/11/4
 */
@Getter
@AllArgsConstructor
public enum AccountTypeEnum {
    // EMAIL表示邮箱，值为1
    EMAIL(1, "邮箱"),
    // PHONE表示手机号，值为2
    PHONE(2, "手机号"),
    // WECHAT表示微信，值为3
    WECHAT(3, "微信"),
    // QQ表示QQ，值为4
    QQ(4, "QQ"),
    // WEIBO表示新浪微博，值为5
    WEIBO(5, "新浪微博"),
    // APPLE表示Apple ID，值为6
    APPLE(6, "Apple ID");

    // 枚举类型的值
    private final int value;
    // 枚举类型的描述
    private final String description;

    /**
     * 根据值获取枚举
     */
    public static AccountTypeEnum getByValue(int value) {
        for (AccountTypeEnum type : values()) {
            if (type.getValue() == value) {
                return type;
            }
        }
        return null;
    }

    /**
     * 是否为第三方登录类型
     */
    public boolean isThirdParty() {
        return this == WECHAT || this == QQ || this == WEIBO || this == APPLE;
    }

    /**
     * 是否需要绑定手机号
     */
    public boolean requiresPhoneBinding() {
        return this == EMAIL || this == WECHAT || this == QQ || this == WEIBO || this == APPLE;
    }
}
