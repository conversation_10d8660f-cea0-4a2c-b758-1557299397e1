package com.vida.xinye.x2.adapter.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.json.JSONUtil;
import com.vida.xinye.x2.adapter.QuestionCuttingAdapter;
import com.vida.xinye.x2.config.QuestionCuttingConfig;
import com.vida.xinye.x2.constant.QuestionCuttingConstant;
import com.vida.xinye.x2.dto.QuestionCuttingResultDto;
import com.vida.xinye.x2.dto.internal.ThirdPartyRequestDto;
import com.vida.xinye.x2.dto.internal.ThirdPartyResponseDto;
import com.vida.xinye.x2.enums.QuestionCuttingEnum;
import com.vida.xinye.x2.util.HttpClientUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 有道智云切题适配器
 *
 * <AUTHOR>
 * @date 2025/08/14
 */
@Slf4j
@Component
public class YoudaoAdapter implements QuestionCuttingAdapter {

    @Autowired
    private QuestionCuttingConfig config;

    @Autowired
    private HttpClientUtil httpClientUtil;

    @Override
    public QuestionCuttingEnum.Provider getProvider() {
        return QuestionCuttingEnum.Provider.YOUDAO;
    }

    @Override
    public boolean isAvailable() {
        QuestionCuttingConfig.YoudaoConfig youdaoConfig = config.getYoudao();
        return youdaoConfig != null &&
               youdaoConfig.isEnabled() &&
               StrUtil.isNotBlank(youdaoConfig.getAppKey()) &&
               StrUtil.isNotBlank(youdaoConfig.getAppSecret()) &&
               StrUtil.isNotBlank(youdaoConfig.getUrl());
    }

    @Override
    public int getPriority() {
        return 20; // 中等优先级
    }

    @Override
    public int getQualityScore() {
        return 4; // 高质量评分
    }

    @Override
    public long getAverageResponseTime() {
        return 4000; // 4秒平均响应时间
    }

    @Override
    public ThirdPartyResponseDto callThirdPartyApi(ThirdPartyRequestDto request) {
        long startTime = System.currentTimeMillis();
        ThirdPartyResponseDto.YoudaoResponse response = new ThirdPartyResponseDto.YoudaoResponse();
        response.setRequestId(request.getRequestId());

        try {
            // 构建有道请求参数
            ThirdPartyRequestDto.YoudaoRequest youdaoRequest = buildYoudaoRequest(request);

            // 构建请求参数
            MultiValueMap<String, String> params = buildRequestParams(youdaoRequest);

            log.info("调用有道接口 - 请求ID: {}, URL: {}", request.getRequestId(), config.getYoudao().getUrl());

            // 发送HTTP请求（有道使用表单提交）
            String responseBody = httpClientUtil.postForm(
                config.getYoudao().getUrl(),
                params
            );

            long responseTime = System.currentTimeMillis() - startTime;
            response.setResponseTime(responseTime);

            log.info("有道接口响应 - 请求ID: {}, 响应时间: {}ms", request.getRequestId(), responseTime);

            // 解析响应
            parseYoudaoResponse(responseBody, response);

            response.setSuccess(true);

        } catch (Exception e) {
            long responseTime = System.currentTimeMillis() - startTime;
            response.setResponseTime(responseTime);
            response.setSuccess(false);
            response.setErrorCode(QuestionCuttingConstant.ErrorCode.API_CALL_FAILED);
            response.setErrorMessage("有道接口调用失败: " + e.getMessage());

            log.error("有道接口调用失败 - 请求ID: {}, 错误: {}", request.getRequestId(), e.getMessage(), e);
        }

        return response;
    }

    @Override
    public QuestionCuttingResultDto convertToStandardResult(ThirdPartyResponseDto thirdPartyResponse, String requestId) {
        QuestionCuttingResultDto result = new QuestionCuttingResultDto();
        result.setRequestId(requestId);
        result.setProvider(getProvider().getCode());
        result.setCreateTime(new Date());

        if (!thirdPartyResponse.getSuccess()) {
            result.setSuccess(false);
            result.setErrorCode(thirdPartyResponse.getErrorCode());
            result.setErrorMessage(thirdPartyResponse.getErrorMessage());
            return result;
        }

        try {
            ThirdPartyResponseDto.YoudaoResponse youdaoResponse =
                (ThirdPartyResponseDto.YoudaoResponse) thirdPartyResponse;

            // 检查有道错误码
            if (!"0".equals(youdaoResponse.getYoudaoErrorCode())) {
                result.setSuccess(false);
                result.setErrorCode(QuestionCuttingConstant.ErrorCode.API_CALL_FAILED);
                String errorCode = youdaoResponse.getYoudaoErrorCode();
                String errorMessage = getYoudaoErrorMessage(errorCode);
                result.setErrorMessage(String.format("有道接口返回错误码: %s - %s", errorCode, errorMessage));

                log.error("有道接口调用失败 - 请求ID: {}, 错误码: {}, 错误信息: {}",
                         requestId, errorCode, errorMessage);
                return result;
            }

            result.setSuccess(true);
            result.setProcessingTime(youdaoResponse.getResponseTime());

            // 转换题目信息
            List<QuestionCuttingResultDto.QuestionInfo> questions =
                convertYoudaoResults(youdaoResponse.getResult());
            result.setQuestions(questions);

            // 计算整体置信度和质量评分
            double avgConfidence = questions.stream()
                .filter(q -> q.getConfidence() != null)
                .mapToDouble(QuestionCuttingResultDto.QuestionInfo::getConfidence)
                .average()
                .orElse(0.8);

            result.setConfidence(avgConfidence);
            result.setQualityScore(calculateQualityScore(avgConfidence));

        } catch (Exception e) {
            log.error("转换有道响应失败 - 请求ID: {}, 错误: {}", requestId, e.getMessage(), e);
            result.setSuccess(false);
            result.setErrorCode(QuestionCuttingConstant.ErrorCode.INVALID_API_RESPONSE);
            result.setErrorMessage("响应格式转换失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 构建有道请求参数（简化版本）
     */
    private ThirdPartyRequestDto.YoudaoRequest buildYoudaoRequest(ThirdPartyRequestDto request) {
        ThirdPartyRequestDto.YoudaoRequest youdaoRequest = new ThirdPartyRequestDto.YoudaoRequest();

        // 基本信息
        youdaoRequest.setRequestId(request.getRequestId());
        youdaoRequest.setImageBase64(request.getImageBase64());
        youdaoRequest.setImageUrl(request.getImageUrl());
        youdaoRequest.setAppKey(config.getYoudao().getAppKey());
        youdaoRequest.setAppSecret(config.getYoudao().getAppSecret());

        // 有道接口固定参数（根据官方文档）
        youdaoRequest.setImageType("1");      // 图片类型，固定为1
        youdaoRequest.setDocType("json");     // 响应类型，固定为json
        youdaoRequest.setSignType("v3");      // 签名类型，固定为v3

        // 生成时间戳和随机数
        String salt = String.valueOf(System.currentTimeMillis());
        String curtime = String.valueOf(System.currentTimeMillis() / 1000);
        youdaoRequest.setSalt(salt);
        youdaoRequest.setCurtime(curtime);

        // 生成签名
        String sign = generateSign(youdaoRequest);
        youdaoRequest.setSign(sign);

        log.debug("有道请求构建完成 - appKey: {}***, salt: {}, curtime: {}",
                 youdaoRequest.getAppKey().substring(0, Math.min(6, youdaoRequest.getAppKey().length())),
                 salt, curtime);

        return youdaoRequest;
    }

    /**
     * 构建请求参数（根据官方文档）
     */
    private MultiValueMap<String, String> buildRequestParams(ThirdPartyRequestDto.YoudaoRequest request) {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();

        // 根据官方文档添加必填参数
        params.add("appKey", request.getAppKey());
        params.add("salt", request.getSalt());
        params.add("curtime", request.getCurtime());
        params.add("sign", request.getSign());
        params.add("imageType", request.getImageType());  // 固定为1
        params.add("docType", request.getDocType());      // 固定为json
        params.add("signType", request.getSignType());    // 固定为v3

        // 添加图片数据（参数名为q，不是img）
        if (StrUtil.isNotBlank(request.getImageBase64())) {
            params.add("q", request.getImageBase64());
        } else {
            throw new IllegalArgumentException("必须提供Base64编码的图片数据");
        }

        log.debug("有道请求参数构建完成 - appKey: {}***, salt: {}, signType: {}",
                 request.getAppKey().substring(0, Math.min(6, request.getAppKey().length())),
                 request.getSalt(), request.getSignType());

        return params;
    }

    /**
     * 生成有道签名（根据官方文档）
     */
    private String generateSign(ThirdPartyRequestDto.YoudaoRequest request) {
        String appKey = request.getAppKey();
        String appSecret = request.getAppSecret();
        String salt = request.getSalt();
        String curtime = request.getCurtime();
        String q = request.getImageBase64(); // 图片Base64数据

        // 计算input（根据官方文档的规则）
        String input = calculateInput(q);

        // 根据官方文档：sha256(应用ID+input+salt+curtime+应用密钥)
        String signStr = appKey + input + salt + curtime + appSecret;

        log.debug("有道签名计算 - q长度: {}, input: {}, signStr长度: {}",
                 q != null ? q.length() : 0,
                 input.length() > 50 ? input.substring(0, 50) + "..." : input,
                 signStr.length());

        return DigestUtil.sha256Hex(signStr);
    }

    /**
     * 计算input值（根据官方文档规则）
     */
    private String calculateInput(String q) {
        if (StrUtil.isBlank(q)) {
            return "";
        }

        int qLength = q.length();

        if (qLength <= 20) {
            // 当q长度小于等于20时，input=q字符串
            return q;
        } else {
            // 当q长度大于20时，input=q前10个字符 + q长度 + q后10个字符
            String prefix = q.substring(0, 10);
            String suffix = q.substring(qLength - 10);
            return prefix + qLength + suffix;
        }
    }

    /**
     * 获取有道错误码对应的错误信息
     */
    private String getYoudaoErrorMessage(String errorCode) {
        switch (errorCode) {
            case "101":
                return "缺少必填的参数，请检查appKey、salt、curtime、sign、type、detectType、imageType等参数";
            case "102":
                return "不支持的语言类型，请检查detectType参数";
            case "103":
                return "翻译文本过长，请使用较小的图片";
            case "104":
                return "不支持的API类型，请检查type参数";
            case "105":
                return "不支持的签名类型，请检查签名算法";
            case "108":
                return "appKey无效，请检查应用ID配置";
            case "111":
                return "开发者账号无效，请联系有道客服";
            case "113":
                return "图片内容为空，请确保图片包含文字";
            case "201":
                return "解密失败，请检查签名算法";
            case "202":
                return "签名检验失败，请重新生成签名";
            case "203":
                return "访问IP地址不在可访问IP列表，请添加服务器IP到白名单";
            case "401":
                return "账户已经欠费，请充值";
            case "411":
                return "访问频率受限，请降低请求频率";
            case "412":
                return "长请求过于频繁，请控制请求频率";
            default:
                return "未知错误，请查看有道接口文档或联系客服";
        }
    }

    /**
     * 解析有道响应
     */
    private void parseYoudaoResponse(String responseBody, ThirdPartyResponseDto.YoudaoResponse response) {
        try {
            Map<String, Object> responseMap = JSONUtil.toBean(responseBody, Map.class);
            response.setRawData(responseMap);

            String errorCode = (String) responseMap.get("errorCode");
            response.setYoudaoErrorCode(errorCode);

            if ("0".equals(errorCode)) {
                // 解析结果 - Result是一个对象，不是数组
                Map<String, Object> resultMap = (Map<String, Object>) responseMap.get("Result");
                if (resultMap != null) {
                    ThirdPartyResponseDto.YoudaoResponse.YoudaoResult result = parseYoudaoResult(resultMap);
                    response.setResult(Collections.singletonList(result));
                }

                // 解析其他字段（这些字段可能在Result对象内部）
                if (resultMap != null) {
                    response.setOrientation((String) resultMap.get("orientation"));
                    response.setLanType((String) resultMap.get("lanType"));
                }
            }

        } catch (Exception e) {
            log.error("解析有道响应失败: {}", e.getMessage(), e);
            throw new RuntimeException("解析有道响应失败", e);
        }
    }

    /**
     * 解析有道结果（根据真实响应结构）
     */
    private ThirdPartyResponseDto.YoudaoResponse.YoudaoResult parseYoudaoResult(Map<String, Object> resultMap) {
        ThirdPartyResponseDto.YoudaoResponse.YoudaoResult result =
            new ThirdPartyResponseDto.YoudaoResponse.YoudaoResult();

        List<ThirdPartyResponseDto.YoudaoResponse.YoudaoLine> allLines = new ArrayList<>();

        // 解析regions（文本区域）
        List<Map<String, Object>> regionMaps = (List<Map<String, Object>>) resultMap.get("regions");
        if (regionMaps != null) {
            for (Map<String, Object> regionMap : regionMaps) {
                ThirdPartyResponseDto.YoudaoResponse.YoudaoLine line = parseYoudaoRegion(regionMap);
                allLines.add(line);
            }
        }

        // 解析qRegions（题目区域）- 这些是实际的题目区域
        List<Map<String, Object>> qRegionMaps = (List<Map<String, Object>>) resultMap.get("qRegions");
        if (qRegionMaps != null) {
            log.debug("解析到 {} 个题目区域", qRegionMaps.size());
            for (Map<String, Object> qRegionMap : qRegionMaps) {
                ThirdPartyResponseDto.YoudaoResponse.YoudaoLine questionLine = parseYoudaoQuestionRegion(qRegionMap);
                questionLine.setQuestionType("question"); // 标记为题目类型
                allLines.add(questionLine);
            }
        }

        // 解析figures（图形区域）
        List<Map<String, Object>> figureMaps = (List<Map<String, Object>>) resultMap.get("figures");
        if (figureMaps != null) {
            log.debug("解析到 {} 个图形区域", figureMaps.size());
            for (Map<String, Object> figureMap : figureMaps) {
                ThirdPartyResponseDto.YoudaoResponse.YoudaoLine figureLine = parseYoudaoFigure(figureMap);
                figureLine.setQuestionType("figure"); // 标记为图形类型
                allLines.add(figureLine);
            }
        }

        // 解析answers（答案区域）
        List<Map<String, Object>> answerMaps = (List<Map<String, Object>>) resultMap.get("answers");
        if (answerMaps != null) {
            log.debug("解析到 {} 个答案区域", answerMaps.size());
            for (Map<String, Object> answerMap : answerMaps) {
                ThirdPartyResponseDto.YoudaoResponse.YoudaoLine answerLine = parseYoudaoAnswer(answerMap);
                answerLine.setQuestionType("answer"); // 标记为答案类型
                allLines.add(answerLine);
            }
        }

        result.setLines(allLines);
        return result;
    }

    /**
     * 解析有道区域信息（根据真实响应结构）
     */
    private ThirdPartyResponseDto.YoudaoResponse.YoudaoLine parseYoudaoRegion(Map<String, Object> regionMap) {
        ThirdPartyResponseDto.YoudaoResponse.YoudaoLine line =
            new ThirdPartyResponseDto.YoudaoResponse.YoudaoLine();

        // 解析boundingBox字符串为坐标数组
        String boundingBoxStr = (String) regionMap.get("boundingBox");
        if (boundingBoxStr != null) {
            line.setBoundingBoxStr(boundingBoxStr);
            // 转换为坐标数组格式
            List<List<Integer>> boundingBox = parseBoundingBoxString(boundingBoxStr);
            line.setBoundingBox(boundingBox);
        }

        // 置信度
        Object scoreObj = regionMap.get("score");
        if (scoreObj instanceof Number) {
            line.setConfidence(((Number) scoreObj).doubleValue());
        }

        // segment字段包含详细的分割信息
        String segment = (String) regionMap.get("segment");
        if (segment != null) {
            line.setSegment(segment);
            // 从segment中提取更详细的文本信息
            line.setText("文本区域 - 坐标: " + boundingBoxStr + ", 分割信息: " + segment.substring(0, Math.min(100, segment.length())) + "...");
        } else {
            line.setText("文本区域 - 坐标: " + boundingBoxStr);
        }

        return line;
    }

    /**
     * 解析有道题目区域信息
     */
    private ThirdPartyResponseDto.YoudaoResponse.YoudaoLine parseYoudaoQuestionRegion(Map<String, Object> qRegionMap) {
        ThirdPartyResponseDto.YoudaoResponse.YoudaoLine line =
            new ThirdPartyResponseDto.YoudaoResponse.YoudaoLine();

        // 解析boundingBox
        String boundingBoxStr = (String) qRegionMap.get("boundingBox");
        if (boundingBoxStr != null) {
            line.setBoundingBoxStr(boundingBoxStr);
            List<List<Integer>> boundingBox = parseBoundingBoxString(boundingBoxStr);
            line.setBoundingBox(boundingBox);
        }

        // 解析answerRegions
        List<Map<String, Object>> answerRegions = (List<Map<String, Object>>) qRegionMap.get("answerRegions");
        if (answerRegions != null && !answerRegions.isEmpty()) {
            log.debug("题目区域包含 {} 个答案区域", answerRegions.size());
        }

        // 处理segment字段（qRegions可能也有segment信息）
        String segment = (String) qRegionMap.get("segment");
        if (segment != null) {
            line.setSegment(segment);
            line.setText("题目区域 - 坐标: " + boundingBoxStr + ", 分割信息: " + segment.substring(0, Math.min(100, segment.length())) + "...");
        } else {
            line.setText("题目区域 - 坐标: " + boundingBoxStr);
        }

        line.setQuestionType("question");

        return line;
    }

    /**
     * 解析有道图形区域信息
     */
    private ThirdPartyResponseDto.YoudaoResponse.YoudaoLine parseYoudaoFigure(Map<String, Object> figureMap) {
        ThirdPartyResponseDto.YoudaoResponse.YoudaoLine line =
            new ThirdPartyResponseDto.YoudaoResponse.YoudaoLine();

        // 解析boundingBox
        String boundingBoxStr = (String) figureMap.get("boundingBox");
        if (boundingBoxStr != null) {
            line.setBoundingBoxStr(boundingBoxStr);
            List<List<Integer>> boundingBox = parseBoundingBoxString(boundingBoxStr);
            line.setBoundingBox(boundingBox);
        }

        // 置信度
        Object scoreObj = figureMap.get("score");
        if (scoreObj instanceof Number) {
            line.setConfidence(((Number) scoreObj).doubleValue());
        }

        // 处理segment字段
        String segment = (String) figureMap.get("segment");
        if (segment != null) {
            line.setSegment(segment);
            line.setText("图形区域 - 坐标: " + boundingBoxStr + ", 分割信息: " + segment.substring(0, Math.min(100, segment.length())) + "...");
        } else {
            line.setText("图形区域 - 坐标: " + boundingBoxStr);
        }

        line.setQuestionType("figure");

        return line;
    }

    /**
     * 解析有道答案区域信息
     */
    private ThirdPartyResponseDto.YoudaoResponse.YoudaoLine parseYoudaoAnswer(Map<String, Object> answerMap) {
        ThirdPartyResponseDto.YoudaoResponse.YoudaoLine line =
            new ThirdPartyResponseDto.YoudaoResponse.YoudaoLine();

        // 解析boundingBox
        String boundingBoxStr = (String) answerMap.get("boundingBox");
        if (boundingBoxStr != null) {
            line.setBoundingBoxStr(boundingBoxStr);
            List<List<Integer>> boundingBox = parseBoundingBoxString(boundingBoxStr);
            line.setBoundingBox(boundingBox);
        }

        // 置信度
        Object scoreObj = answerMap.get("score");
        if (scoreObj instanceof Number) {
            line.setConfidence(((Number) scoreObj).doubleValue());
        }

        // 处理segment字段
        String segment = (String) answerMap.get("segment");
        if (segment != null) {
            line.setSegment(segment);
            line.setText("答案区域 - 坐标: " + boundingBoxStr + ", 分割信息: " + segment.substring(0, Math.min(100, segment.length())) + "...");
        } else {
            line.setText("答案区域 - 坐标: " + boundingBoxStr);
        }

        line.setQuestionType("answer");

        return line;
    }

    /**
     * 解析boundingBox字符串为坐标数组
     * 输入格式："99,946,1556,946,1556,1386,99,1386"
     * 输出格式：[[99,946],[1556,946],[1556,1386],[99,1386]]
     */
    private List<List<Integer>> parseBoundingBoxString(String boundingBoxStr) {
        List<List<Integer>> boundingBox = new ArrayList<>();
        if (StrUtil.isNotBlank(boundingBoxStr)) {
            try {
                String[] coords = boundingBoxStr.split(",");
                for (int i = 0; i < coords.length; i += 2) {
                    if (i + 1 < coords.length) {
                        List<Integer> point = new ArrayList<>();
                        point.add(Integer.parseInt(coords[i].trim()));
                        point.add(Integer.parseInt(coords[i + 1].trim()));
                        boundingBox.add(point);
                    }
                }
            } catch (NumberFormatException e) {
                log.warn("解析boundingBox失败: {}", boundingBoxStr, e);
            }
        }
        return boundingBox;
    }

    /**
     * 转换有道结果为标准格式
     */
    private List<QuestionCuttingResultDto.QuestionInfo> convertYoudaoResults(
            List<ThirdPartyResponseDto.YoudaoResponse.YoudaoResult> youdaoResults) {

        if (youdaoResults == null) {
            return new ArrayList<>();
        }

        List<QuestionCuttingResultDto.QuestionInfo> questions = new ArrayList<>();
        int questionNumber = 1;

        for (ThirdPartyResponseDto.YoudaoResponse.YoudaoResult result : youdaoResults) {
            if (result.getLines() != null) {
                // 分别处理题目区域和文本区域
                List<ThirdPartyResponseDto.YoudaoResponse.YoudaoLine> questionLines = result.getLines().stream()
                    .filter(line -> "question".equals(line.getQuestionType()))
                    .collect(Collectors.toList());

                List<ThirdPartyResponseDto.YoudaoResponse.YoudaoLine> textLines = result.getLines().stream()
                    .filter(line -> !"question".equals(line.getQuestionType()))
                    .collect(Collectors.toList());

                // 如果有题目区域，为每个题目区域创建一个题目
                if (!questionLines.isEmpty()) {
                    for (ThirdPartyResponseDto.YoudaoResponse.YoudaoLine questionLine : questionLines) {
                        QuestionCuttingResultDto.QuestionInfo question = createQuestionFromLine(questionLine, questionNumber++);
                        questions.add(question);
                    }
                } else if (!textLines.isEmpty()) {
                    // 如果没有明确的题目区域，将所有文本区域合并为一个题目
                    String content = textLines.stream()
                        .map(ThirdPartyResponseDto.YoudaoResponse.YoudaoLine::getText)
                        .collect(Collectors.joining("\n"));

                    if (StrUtil.isNotBlank(content)) {
                        QuestionCuttingResultDto.QuestionInfo question = new QuestionCuttingResultDto.QuestionInfo();
                        question.setQuestionId("youdao_" + questionNumber);
                        question.setQuestionNumber(questionNumber++);
                        question.setContent(content);
                        question.setQuestionType(detectQuestionType(content));

                        // 计算平均置信度
                        double avgConfidence = textLines.stream()
                            .filter(line -> line.getConfidence() != null)
                            .mapToDouble(ThirdPartyResponseDto.YoudaoResponse.YoudaoLine::getConfidence)
                            .average()
                            .orElse(0.8);
                        question.setConfidence(avgConfidence);

                        // 设置边界框（使用第一行的边界框）
                        if (!textLines.isEmpty() && textLines.get(0).getBoundingBox() != null) {
                            QuestionCuttingResultDto.BoundingBox boundingBox =
                                convertBoundingBox(textLines.get(0).getBoundingBox());
                            question.setBoundingBox(boundingBox);
                        }

                        questions.add(question);
                    }
                }
            }
        }

        return questions;
    }

    /**
     * 从单个line创建题目信息
     */
    private QuestionCuttingResultDto.QuestionInfo createQuestionFromLine(
            ThirdPartyResponseDto.YoudaoResponse.YoudaoLine line, int questionNumber) {

        QuestionCuttingResultDto.QuestionInfo question = new QuestionCuttingResultDto.QuestionInfo();
        question.setQuestionId("youdao_" + questionNumber);
        question.setQuestionNumber(questionNumber);
        question.setContent(line.getText());

        // 使用line中已经设置的questionType，如果没有则检测
        String questionType = line.getQuestionType();
        if (StrUtil.isBlank(questionType)) {
            questionType = detectQuestionType(line.getText());
        }
        question.setQuestionType(questionType);

        // 设置置信度
        if (line.getConfidence() != null) {
            question.setConfidence(line.getConfidence());
        } else {
            question.setConfidence(0.8);
        }

        // 设置边界框
        if (line.getBoundingBox() != null) {
            QuestionCuttingResultDto.BoundingBox boundingBox = convertBoundingBox(line.getBoundingBox());
            question.setBoundingBox(boundingBox);
        }

        return question;
    }

    /**
     * 转换边界框格式
     */
    private QuestionCuttingResultDto.BoundingBox convertBoundingBox(List<List<Integer>> youdaoBoundingBox) {
        if (youdaoBoundingBox == null || youdaoBoundingBox.size() < 4) {
            return null;
        }

        QuestionCuttingResultDto.BoundingBox boundingBox = new QuestionCuttingResultDto.BoundingBox();

        // 计算宽度和高度（保留有用信息）
        int minX = youdaoBoundingBox.stream().mapToInt(point -> point.get(0)).min().orElse(0);
        int minY = youdaoBoundingBox.stream().mapToInt(point -> point.get(1)).min().orElse(0);
        int maxX = youdaoBoundingBox.stream().mapToInt(point -> point.get(0)).max().orElse(0);
        int maxY = youdaoBoundingBox.stream().mapToInt(point -> point.get(1)).max().orElse(0);

        boundingBox.setWidth(maxX - minX);
        boundingBox.setHeight(maxY - minY);

        // 保存原始顶点坐标
        List<QuestionCuttingResultDto.Point> points = youdaoBoundingBox.stream()
            .map(point -> {
                QuestionCuttingResultDto.Point p = new QuestionCuttingResultDto.Point();
                p.setX(point.get(0));
                p.setY(point.get(1));
                return p;
            })
            .collect(Collectors.toList());
        boundingBox.setPoints(points);

        return boundingBox;
    }

    /**
     * 检测题目类型
     */
    private String detectQuestionType(String content) {
        if (StrUtil.isBlank(content)) {
            return QuestionCuttingConstant.QuestionType.OTHER;
        }

        String lowerContent = content.toLowerCase();

        if (lowerContent.contains("a.") || lowerContent.contains("b.") ||
            lowerContent.contains("a、") || lowerContent.contains("b、")) {
            return QuestionCuttingConstant.QuestionType.CHOICE;
        } else if (lowerContent.contains("填空") || lowerContent.contains("____")) {
            return QuestionCuttingConstant.QuestionType.FILL_BLANK;
        } else if (lowerContent.contains("判断") || lowerContent.contains("对错")) {
            return QuestionCuttingConstant.QuestionType.JUDGE;
        } else if (lowerContent.contains("计算") || lowerContent.contains("求")) {
            return QuestionCuttingConstant.QuestionType.CALCULATION;
        } else if (lowerContent.contains("解答") || lowerContent.contains("分析")) {
            return QuestionCuttingConstant.QuestionType.ANSWER;
        }

        return QuestionCuttingConstant.QuestionType.OTHER;
    }

    /**
     * 根据置信度计算质量评分
     */
    private Integer calculateQualityScore(Double confidence) {
        if (confidence == null) {
            return 3;
        }

        if (confidence >= 0.9) {
            return 5;
        } else if (confidence >= 0.8) {
            return 4;
        } else if (confidence >= 0.6) {
            return 3;
        } else if (confidence >= 0.4) {
            return 2;
        } else {
            return 1;
        }
    }
}
