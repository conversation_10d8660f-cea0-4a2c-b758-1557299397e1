package com.vida.xinye.x2.adapter.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.vida.xinye.x2.adapter.QuestionCuttingAdapter;
import com.vida.xinye.x2.config.QuestionCuttingConfig;
import com.vida.xinye.x2.constant.QuestionCuttingConstant;
import com.vida.xinye.x2.dto.QuestionCuttingResultDto;
import com.vida.xinye.x2.dto.internal.ThirdPartyRequestDto;
import com.vida.xinye.x2.dto.internal.ThirdPartyResponseDto;
import com.vida.xinye.x2.enums.QuestionCuttingEnum;
import com.vida.xinye.x2.util.HttpClientUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 阿里云OCR切题适配器
 *
 * <AUTHOR>
 * @date 2025/08/14
 */
@Slf4j
@Component
public class AliyunAdapter implements QuestionCuttingAdapter {

    @Autowired
    private QuestionCuttingConfig config;

    @Autowired
    private HttpClientUtil httpClientUtil;

    @Override
    public QuestionCuttingEnum.Provider getProvider() {
        return QuestionCuttingEnum.Provider.ALIYUN;
    }

    @Override
    public boolean isAvailable() {
        QuestionCuttingConfig.AliyunConfig aliyunConfig = config.getAliyun();
        return aliyunConfig != null &&
               aliyunConfig.isEnabled() &&
               StrUtil.isNotBlank(aliyunConfig.getAccessKeyId()) &&
               StrUtil.isNotBlank(aliyunConfig.getAccessKeySecret()) &&
               StrUtil.isNotBlank(aliyunConfig.getEndpoint());
    }

    @Override
    public int getPriority() {
        return 30; // 较低优先级
    }

    @Override
    public int getQualityScore() {
        return 4; // 高质量评分
    }

    @Override
    public long getAverageResponseTime() {
        return 5000; // 5秒平均响应时间
    }

    @Override
    public ThirdPartyResponseDto callThirdPartyApi(ThirdPartyRequestDto request) {
        long startTime = System.currentTimeMillis();
        ThirdPartyResponseDto.AliyunResponse response = new ThirdPartyResponseDto.AliyunResponse();
        response.setRequestId(request.getRequestId());

        try {
            // 构建阿里云请求参数
            ThirdPartyRequestDto.AliyunRequest aliyunRequest = buildAliyunRequest(request);
            
            // 构建请求参数
            Map<String, Object> params = buildRequestParams(aliyunRequest);
            
            // 构建请求头
            Map<String, String> headers = buildHeaders(aliyunRequest);
            
            log.info("调用阿里云接口 - 请求ID: {}, Endpoint: {}", request.getRequestId(), config.getAliyun().getEndpoint());
            
            // 发送HTTP请求
            String url = "https://" + config.getAliyun().getEndpoint();
            String responseBody = httpClientUtil.postJson(
                url,
                JSONUtil.toJsonStr(params),
                headers
            );
            
            long responseTime = System.currentTimeMillis() - startTime;
            response.setResponseTime(responseTime);
            
            log.info("阿里云接口响应 - 请求ID: {}, 响应时间: {}ms", request.getRequestId(), responseTime);
            
            // 解析响应
            parseAliyunResponse(responseBody, response);
            
            response.setSuccess(true);
            
        } catch (Exception e) {
            long responseTime = System.currentTimeMillis() - startTime;
            response.setResponseTime(responseTime);
            response.setSuccess(false);
            response.setErrorCode(QuestionCuttingConstant.ErrorCode.API_CALL_FAILED);
            response.setErrorMessage("阿里云接口调用失败: " + e.getMessage());
            
            log.error("阿里云接口调用失败 - 请求ID: {}, 错误: {}", request.getRequestId(), e.getMessage(), e);
        }

        return response;
    }

    @Override
    public QuestionCuttingResultDto convertToStandardResult(ThirdPartyResponseDto thirdPartyResponse, String requestId) {
        QuestionCuttingResultDto result = new QuestionCuttingResultDto();
        result.setRequestId(requestId);
        result.setProvider(getProvider().getCode());
        result.setCreateTime(new Date());

        if (!thirdPartyResponse.getSuccess()) {
            result.setSuccess(false);
            result.setErrorCode(thirdPartyResponse.getErrorCode());
            result.setErrorMessage(thirdPartyResponse.getErrorMessage());
            return result;
        }

        try {
            ThirdPartyResponseDto.AliyunResponse aliyunResponse = 
                (ThirdPartyResponseDto.AliyunResponse) thirdPartyResponse;
            
            result.setSuccess(true);
            result.setProcessingTime(aliyunResponse.getResponseTime());
            
            if (aliyunResponse.getData() != null) {
                // 转换题目信息
                List<QuestionCuttingResultDto.QuestionInfo> questions = 
                    convertAliyunResults(aliyunResponse.getData().getStructuredResult());
                result.setQuestions(questions);
                
                // 计算整体置信度和质量评分
                double avgConfidence = questions.stream()
                    .filter(q -> q.getConfidence() != null)
                    .mapToDouble(QuestionCuttingResultDto.QuestionInfo::getConfidence)
                    .average()
                    .orElse(0.85);
                
                result.setConfidence(avgConfidence);
                result.setQualityScore(calculateQualityScore(avgConfidence));
                
                // 设置原始图片信息
                QuestionCuttingResultDto.ImageInfo imageInfo = new QuestionCuttingResultDto.ImageInfo();
                imageInfo.setWidth(aliyunResponse.getData().getWidth());
                imageInfo.setHeight(aliyunResponse.getData().getHeight());
                result.setOriginalImage(imageInfo);
            }
            
        } catch (Exception e) {
            log.error("转换阿里云响应失败 - 请求ID: {}, 错误: {}", requestId, e.getMessage(), e);
            result.setSuccess(false);
            result.setErrorCode(QuestionCuttingConstant.ErrorCode.INVALID_API_RESPONSE);
            result.setErrorMessage("响应格式转换失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 构建阿里云请求参数
     */
    private ThirdPartyRequestDto.AliyunRequest buildAliyunRequest(ThirdPartyRequestDto request) {
        ThirdPartyRequestDto.AliyunRequest aliyunRequest = new ThirdPartyRequestDto.AliyunRequest();
        aliyunRequest.setRequestId(request.getRequestId());
        aliyunRequest.setImageBase64(request.getImageBase64());
        aliyunRequest.setImageUrl(request.getImageUrl());
        aliyunRequest.setSubject(request.getSubject());
        aliyunRequest.setGrade(request.getGrade());
        aliyunRequest.setAccessKeyId(config.getAliyun().getAccessKeyId());
        aliyunRequest.setAccessKeySecret(config.getAliyun().getAccessKeySecret());
        aliyunRequest.setEndpoint(config.getAliyun().getEndpoint());
        aliyunRequest.setTimeoutSeconds(request.getTimeoutSeconds());
        
        return aliyunRequest;
    }

    /**
     * 构建请求参数（根据官方文档）
     */
    private Map<String, Object> buildRequestParams(ThirdPartyRequestDto.AliyunRequest request) {
        Map<String, Object> params = new HashMap<>();

        // 根据官方文档，参数名应该是大写
        if (StrUtil.isNotBlank(request.getImageBase64())) {
            params.put("body", request.getImageBase64());  // 图片二进制文件
        } else if (StrUtil.isNotBlank(request.getImageUrl())) {
            params.put("Url", request.getImageUrl());      // 注意大写
        }

        // 可选参数（根据官方文档）
        if (StrUtil.isNotBlank(request.getSubject())) {
            params.put("Subject", request.getSubject());   // 学科
        }

        params.put("NeedRotate", request.getNeedRotate());           // 是否需要旋转
        params.put("OutputOricoord", request.getOutputOricoord());   // 是否输出原始坐标

        log.debug("阿里云请求参数构建完成 - 参数数量: {}", params.size());

        return params;
    }

    /**
     * 构建请求头
     */
    private Map<String, String> buildHeaders(ThirdPartyRequestDto.AliyunRequest request) {
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Accept", "application/json");
        
        // 阿里云签名认证（简化版本，实际应该使用阿里云SDK）
        headers.put("Authorization", "Bearer " + generateAccessToken(request));
        
        return headers;
    }

    /**
     * 生成访问令牌（简化版本）
     */
    private String generateAccessToken(ThirdPartyRequestDto.AliyunRequest request) {
        // 实际应该使用阿里云SDK生成签名
        // 这里简化处理，返回AccessKeyId
        return request.getAccessKeyId();
    }

    /**
     * 解析阿里云响应
     */
    private void parseAliyunResponse(String responseBody, ThirdPartyResponseDto.AliyunResponse response) {
        try {
            Map<String, Object> responseMap = JSONUtil.toBean(responseBody, Map.class);
            response.setRawData(responseMap);
            
            String requestId = (String) responseMap.get("RequestId");
            response.setRequestId(requestId);
            
            // 解析数据部分
            Map<String, Object> dataMap = (Map<String, Object>) responseMap.get("Data");
            if (dataMap != null) {
                ThirdPartyResponseDto.AliyunResponse.AliyunData data = 
                    new ThirdPartyResponseDto.AliyunResponse.AliyunData();
                
                data.setAngle((Integer) dataMap.get("angle"));
                data.setHeight((Integer) dataMap.get("height"));
                data.setWidth((Integer) dataMap.get("width"));
                
                // 解析结构化结果
                List<Map<String, Object>> structuredMaps = 
                    (List<Map<String, Object>>) dataMap.get("structuredResult");
                if (structuredMaps != null) {
                    List<ThirdPartyResponseDto.AliyunResponse.AliyunStructuredResult> structuredResults = 
                        structuredMaps.stream()
                            .map(this::parseStructuredResult)
                            .collect(Collectors.toList());
                    data.setStructuredResult(structuredResults);
                }
                
                response.setData(data);
            }
            
        } catch (Exception e) {
            log.error("解析阿里云响应失败: {}", e.getMessage(), e);
            throw new RuntimeException("解析阿里云响应失败", e);
        }
    }

    /**
     * 解析结构化结果
     */
    private ThirdPartyResponseDto.AliyunResponse.AliyunStructuredResult parseStructuredResult(
            Map<String, Object> resultMap) {
        
        ThirdPartyResponseDto.AliyunResponse.AliyunStructuredResult result = 
            new ThirdPartyResponseDto.AliyunResponse.AliyunStructuredResult();
        
        result.setType((String) resultMap.get("type"));
        result.setValue((String) resultMap.get("value"));
        result.setConfidence((Double) resultMap.get("confidence"));
        
        // 解析位置信息
        Map<String, Object> posMap = (Map<String, Object>) resultMap.get("pos");
        if (posMap != null) {
            ThirdPartyResponseDto.AliyunResponse.AliyunPosition position = parsePosition(posMap);
            result.setPos(position);
        }
        
        // 解析子结果
        List<Map<String, Object>> subResultMaps = (List<Map<String, Object>>) resultMap.get("subResults");
        if (subResultMaps != null) {
            List<ThirdPartyResponseDto.AliyunResponse.AliyunStructuredResult> subResults = 
                subResultMaps.stream()
                    .map(this::parseStructuredResult)
                    .collect(Collectors.toList());
            result.setSubResults(subResults);
        }
        
        return result;
    }

    /**
     * 解析位置信息
     */
    private ThirdPartyResponseDto.AliyunResponse.AliyunPosition parsePosition(Map<String, Object> posMap) {
        ThirdPartyResponseDto.AliyunResponse.AliyunPosition position = 
            new ThirdPartyResponseDto.AliyunResponse.AliyunPosition();
        
        position.setX((Integer) posMap.get("x"));
        position.setY((Integer) posMap.get("y"));
        position.setW((Integer) posMap.get("w"));
        position.setH((Integer) posMap.get("h"));
        
        // 解析顶点坐标
        List<Map<String, Object>> pointMaps = (List<Map<String, Object>>) posMap.get("points");
        if (pointMaps != null) {
            List<ThirdPartyResponseDto.AliyunResponse.AliyunPoint> points = 
                pointMaps.stream()
                    .map(pointMap -> {
                        ThirdPartyResponseDto.AliyunResponse.AliyunPoint point = 
                            new ThirdPartyResponseDto.AliyunResponse.AliyunPoint();
                        point.setX((Integer) pointMap.get("x"));
                        point.setY((Integer) pointMap.get("y"));
                        return point;
                    })
                    .collect(Collectors.toList());
            position.setPoints(points);
        }
        
        return position;
    }

    /**
     * 转换阿里云结果为标准格式
     */
    private List<QuestionCuttingResultDto.QuestionInfo> convertAliyunResults(
            List<ThirdPartyResponseDto.AliyunResponse.AliyunStructuredResult> aliyunResults) {

        if (aliyunResults == null) {
            return new ArrayList<>();
        }

        List<QuestionCuttingResultDto.QuestionInfo> questions = new ArrayList<>();
        int questionNumber = 1;

        for (ThirdPartyResponseDto.AliyunResponse.AliyunStructuredResult result : aliyunResults) {
            if ("question".equals(result.getType()) || "text".equals(result.getType())) {
                QuestionCuttingResultDto.QuestionInfo question = convertAliyunQuestion(result, questionNumber++);
                if (question != null) {
                    questions.add(question);
                }
            }
        }

        return questions;
    }

    /**
     * 转换单个阿里云题目
     */
    private QuestionCuttingResultDto.QuestionInfo convertAliyunQuestion(
            ThirdPartyResponseDto.AliyunResponse.AliyunStructuredResult result, int questionNumber) {

        if (StrUtil.isBlank(result.getValue())) {
            return null;
        }

        QuestionCuttingResultDto.QuestionInfo question = new QuestionCuttingResultDto.QuestionInfo();
        question.setQuestionId("aliyun_" + questionNumber);
        question.setQuestionNumber(questionNumber);
        question.setContent(result.getValue());
        question.setConfidence(result.getConfidence());
        question.setQuestionType(detectQuestionType(result.getValue()));

        // 转换位置信息
        if (result.getPos() != null) {
            QuestionCuttingResultDto.BoundingBox boundingBox = convertAliyunBoundingBox(result.getPos());
            question.setBoundingBox(boundingBox);
        }

        // 处理子结果（选项等）
        if (result.getSubResults() != null && !result.getSubResults().isEmpty()) {
            List<QuestionCuttingResultDto.OptionInfo> options = new ArrayList<>();
            for (ThirdPartyResponseDto.AliyunResponse.AliyunStructuredResult subResult : result.getSubResults()) {
                if ("option".equals(subResult.getType()) && StrUtil.isNotBlank(subResult.getValue())) {
                    QuestionCuttingResultDto.OptionInfo option = new QuestionCuttingResultDto.OptionInfo();
                    option.setContent(subResult.getValue());
                    // 从内容中提取选项标识
                    String label = extractOptionLabel(subResult.getValue());
                    option.setLabel(label);
                    options.add(option);
                }
            }
            if (!options.isEmpty()) {
                question.setOptions(options);
            }
        }

        return question;
    }

    /**
     * 转换阿里云边界框
     */
    private QuestionCuttingResultDto.BoundingBox convertAliyunBoundingBox(
            ThirdPartyResponseDto.AliyunResponse.AliyunPosition aliyunPos) {

        QuestionCuttingResultDto.BoundingBox boundingBox = new QuestionCuttingResultDto.BoundingBox();

        if (aliyunPos.getW() != null && aliyunPos.getH() != null) {
            boundingBox.setWidth(aliyunPos.getW());
            boundingBox.setHeight(aliyunPos.getH());
        }

        // 转换顶点坐标
        if (aliyunPos.getPoints() != null) {
            List<QuestionCuttingResultDto.Point> points = aliyunPos.getPoints().stream()
                .map(aliyunPoint -> {
                    QuestionCuttingResultDto.Point point = new QuestionCuttingResultDto.Point();
                    point.setX(aliyunPoint.getX());
                    point.setY(aliyunPoint.getY());
                    return point;
                })
                .collect(Collectors.toList());
            boundingBox.setPoints(points);
        }

        return boundingBox;
    }

    /**
     * 检测题目类型
     */
    private String detectQuestionType(String content) {
        if (StrUtil.isBlank(content)) {
            return QuestionCuttingConstant.QuestionType.OTHER;
        }

        String lowerContent = content.toLowerCase();

        if (lowerContent.contains("a.") || lowerContent.contains("b.") ||
            lowerContent.contains("a、") || lowerContent.contains("b、") ||
            lowerContent.contains("选择")) {
            return QuestionCuttingConstant.QuestionType.CHOICE;
        } else if (lowerContent.contains("填空") || lowerContent.contains("____") ||
                   lowerContent.contains("（）") || lowerContent.contains("()")) {
            return QuestionCuttingConstant.QuestionType.FILL_BLANK;
        } else if (lowerContent.contains("判断") || lowerContent.contains("对错") ||
                   lowerContent.contains("正确") || lowerContent.contains("错误")) {
            return QuestionCuttingConstant.QuestionType.JUDGE;
        } else if (lowerContent.contains("计算") || lowerContent.contains("求") ||
                   lowerContent.contains("=") || lowerContent.contains("解：")) {
            return QuestionCuttingConstant.QuestionType.CALCULATION;
        } else if (lowerContent.contains("解答") || lowerContent.contains("分析") ||
                   lowerContent.contains("证明") || lowerContent.contains("说明")) {
            return QuestionCuttingConstant.QuestionType.ANSWER;
        }

        return QuestionCuttingConstant.QuestionType.OTHER;
    }

    /**
     * 提取选项标识
     */
    private String extractOptionLabel(String optionContent) {
        if (StrUtil.isBlank(optionContent)) {
            return "";
        }

        // 匹配 A. B. C. D. 格式
        if (optionContent.matches("^[A-Z]\\..*")) {
            return optionContent.substring(0, 1);
        }

        // 匹配 A、B、C、D、格式
        if (optionContent.matches("^[A-Z]、.*")) {
            return optionContent.substring(0, 1);
        }

        // 匹配 (A) (B) (C) (D) 格式
        if (optionContent.matches("^\\([A-Z]\\).*")) {
            return optionContent.substring(1, 2);
        }

        // 默认返回空字符串
        return "";
    }

    /**
     * 根据置信度计算质量评分
     */
    private Integer calculateQualityScore(Double confidence) {
        if (confidence == null) {
            return 3;
        }

        if (confidence >= 0.95) {
            return 5;
        } else if (confidence >= 0.85) {
            return 4;
        } else if (confidence >= 0.7) {
            return 3;
        } else if (confidence >= 0.5) {
            return 2;
        } else {
            return 1;
        }
    }
}
