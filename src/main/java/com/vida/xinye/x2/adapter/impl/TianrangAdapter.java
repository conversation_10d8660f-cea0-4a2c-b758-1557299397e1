package com.vida.xinye.x2.adapter.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.vida.xinye.x2.adapter.QuestionCuttingAdapter;
import com.vida.xinye.x2.config.QuestionCuttingConfig;
import com.vida.xinye.x2.constant.QuestionCuttingConstant;
import com.vida.xinye.x2.dto.QuestionCuttingResultDto;
import com.vida.xinye.x2.dto.internal.ThirdPartyRequestDto;
import com.vida.xinye.x2.dto.internal.ThirdPartyResponseDto;
import com.vida.xinye.x2.enums.QuestionCuttingEnum;
import com.vida.xinye.x2.exception.Asserts;
import com.vida.xinye.x2.util.HttpClientUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 天壤智能切题适配器
 *
 * <AUTHOR>
 * @date 2025/08/14
 */
@Slf4j
@Component
public class TianrangAdapter implements QuestionCuttingAdapter {

    @Autowired
    private QuestionCuttingConfig config;

    @Autowired
    private HttpClientUtil httpClientUtil;

    @Override
    public QuestionCuttingEnum.Provider getProvider() {
        return QuestionCuttingEnum.Provider.TIANRANG;
    }

    @Override
    public boolean isAvailable() {
        QuestionCuttingConfig.TianrangConfig tianrangConfig = config.getTianrang();
        return tianrangConfig != null &&
               tianrangConfig.isEnabled() &&
               StrUtil.isNotBlank(tianrangConfig.getApiKey()) &&
               StrUtil.isNotBlank(tianrangConfig.getUrl());
    }

    @Override
    public int getPriority() {
        return 10; // 高优先级
    }

    @Override
    public int getQualityScore() {
        return 5; // 最高质量评分
    }

    @Override
    public long getAverageResponseTime() {
        return 3000; // 3秒平均响应时间
    }

    @Override
    public ThirdPartyResponseDto callThirdPartyApi(ThirdPartyRequestDto request) {
        long startTime = System.currentTimeMillis();
        ThirdPartyResponseDto.TianrangResponse response = new ThirdPartyResponseDto.TianrangResponse();
        response.setRequestId(request.getRequestId());

        try {
            // 构建请求参数
            ThirdPartyRequestDto.TianrangRequest tianrangRequest = buildTianrangRequest(request);
            
            // 构建请求头
            Map<String, String> headers = buildHeaders();
            
            // 构建请求体
            Map<String, Object> requestBody = buildRequestBody(tianrangRequest);
            
            log.info("调用天壤接口 - 请求ID: {}, URL: {}", request.getRequestId(), config.getTianrang().getUrl());
            
            // 发送HTTP请求
            String responseBody = httpClientUtil.postJson(
                config.getTianrang().getUrl(),
                JSONUtil.toJsonStr(requestBody),
                headers
            );
            
            long responseTime = System.currentTimeMillis() - startTime;
            response.setResponseTime(responseTime);
            
            log.info("天壤接口响应 - 请求ID: {}, 响应时间: {}ms", request.getRequestId(), responseTime);
            
            // 解析响应
            parseTianrangResponse(responseBody, response);
            
            response.setSuccess(true);
            
        } catch (Exception e) {
            long responseTime = System.currentTimeMillis() - startTime;
            response.setResponseTime(responseTime);
            response.setSuccess(false);
            response.setErrorCode(QuestionCuttingConstant.ErrorCode.API_CALL_FAILED);
            response.setErrorMessage("天壤接口调用失败: " + e.getMessage());
            
            log.error("天壤接口调用失败 - 请求ID: {}, 错误: {}", request.getRequestId(), e.getMessage(), e);
        }

        return response;
    }

    @Override
    public QuestionCuttingResultDto convertToStandardResult(ThirdPartyResponseDto thirdPartyResponse, String requestId) {
        QuestionCuttingResultDto result = new QuestionCuttingResultDto();
        result.setRequestId(requestId);
        result.setProvider(getProvider().getCode());
        result.setCreateTime(new Date());

        if (!thirdPartyResponse.getSuccess()) {
            result.setSuccess(false);
            result.setErrorCode(thirdPartyResponse.getErrorCode());
            result.setErrorMessage(thirdPartyResponse.getErrorMessage());
            return result;
        }

        try {
            ThirdPartyResponseDto.TianrangResponse tianrangResponse = 
                (ThirdPartyResponseDto.TianrangResponse) thirdPartyResponse;
            
            result.setSuccess(true);
            result.setProcessingTime(tianrangResponse.getResponseTime());
            
            if (tianrangResponse.getData() != null) {
                // 转换题目信息
                List<QuestionCuttingResultDto.QuestionInfo> questions = 
                    convertTianrangQuestions(tianrangResponse.getData().getQuestions());
                result.setQuestions(questions);
                
                // 设置质量评分和置信度
                result.setConfidence(tianrangResponse.getData().getConfidence());
                result.setQualityScore(calculateQualityScore(tianrangResponse.getData().getConfidence()));
            }
            
        } catch (Exception e) {
            log.error("转换天壤响应失败 - 请求ID: {}, 错误: {}", requestId, e.getMessage(), e);
            result.setSuccess(false);
            result.setErrorCode(QuestionCuttingConstant.ErrorCode.INVALID_API_RESPONSE);
            result.setErrorMessage("响应格式转换失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 构建天壤请求参数（根据官方文档）
     */
    private ThirdPartyRequestDto.TianrangRequest buildTianrangRequest(ThirdPartyRequestDto request) {
        ThirdPartyRequestDto.TianrangRequest tianrangRequest = new ThirdPartyRequestDto.TianrangRequest();
        tianrangRequest.setRequestId(request.getRequestId());
        tianrangRequest.setImageBase64(request.getImageBase64());
        tianrangRequest.setApiKey(config.getTianrang().getApiKey());

        return tianrangRequest;
    }

    /**
     * 构建请求头（根据官方文档）
     */
    private Map<String, String> buildHeaders() {
        Map<String, String> headers = new HashMap<>();
        headers.put(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
        // 根据官方文档，使用 Authorization: APPCODE xxx
        headers.put(HttpHeaders.AUTHORIZATION, "APPCODE " + config.getTianrang().getApiKey());
        return headers;
    }

    /**
     * 构建请求体（根据官方文档）
     */
    private Map<String, Object> buildRequestBody(ThirdPartyRequestDto.TianrangRequest request) {
        Map<String, Object> body = new HashMap<>();

        // 根据官方文档，只需要 media_id 参数
        if (StrUtil.isNotBlank(request.getImageBase64())) {
            body.put("media_id", request.getImageBase64());
        } else {
            throw new IllegalArgumentException("天壤接口必须提供Base64编码的图片数据");
        }

        log.debug("天壤请求体构建完成 - media_id长度: {}", request.getImageBase64().length());

        return body;
    }

    /**
     * 解析天壤响应
     */
    private void parseTianrangResponse(String responseBody, ThirdPartyResponseDto.TianrangResponse response) {
        try {
            Map<String, Object> responseMap = JSONUtil.toBean(responseBody, Map.class);
            response.setRawData(responseMap);
            
            Integer code = (Integer) responseMap.get("code");
            String message = (String) responseMap.get("message");
            
            response.setCode(code);
            response.setMessage(message);
            
            if (code != null && code == 0) {  // 天壤成功码是0，不是200
                // 解析数据部分（根据真实响应结构）
                Map<String, Object> outerDataMap = (Map<String, Object>) responseMap.get("data");
                if (outerDataMap != null) {
                    Map<String, Object> innerDataMap = (Map<String, Object>) outerDataMap.get("data");
                    if (innerDataMap != null) {
                        ThirdPartyResponseDto.TianrangResponse.TianrangData data =
                            new ThirdPartyResponseDto.TianrangResponse.TianrangData();

                        // 解析structure_result（题目结构）
                        List<Map<String, Object>> structureResults = (List<Map<String, Object>>) innerDataMap.get("structure_result");
                        if (structureResults != null) {
                            List<ThirdPartyResponseDto.TianrangResponse.TianrangQuestion> questions =
                                structureResults.stream()
                                    .map(this::parseStructureResult)
                                    .collect(Collectors.toList());
                            data.setQuestions(questions);
                        }

                        // 解析图片尺寸（安全类型转换）
                        Object heightObj = innerDataMap.get("height");
                        Object widthObj = innerDataMap.get("width");
                        if (heightObj instanceof Number) {
                            data.setHeight(((Number) heightObj).intValue());
                        }
                        if (widthObj instanceof Number) {
                            data.setWidth(((Number) widthObj).intValue());
                        }

                        // 解析处理时间（安全类型转换）
                        Object elapsedTimeObj = innerDataMap.get("elapsed_time");
                        if (elapsedTimeObj instanceof Number) {
                            data.setElapsedTime(((Number) elapsedTimeObj).doubleValue());
                        }

                        response.setData(data);
                    }
                }
            } else {
                response.setSuccess(false);
                response.setErrorCode(QuestionCuttingConstant.ErrorCode.API_CALL_FAILED);
                response.setErrorMessage(message != null ? message : "天壤接口返回错误");
            }
            
        } catch (Exception e) {
            log.error("解析天壤响应失败: {}", e.getMessage(), e);
            throw new RuntimeException("解析天壤响应失败", e);
        }
    }

    /**
     * 解析单个结构结果（根据真实响应格式）
     */
    private ThirdPartyResponseDto.TianrangResponse.TianrangQuestion parseStructureResult(Map<String, Object> structureMap) {
        ThirdPartyResponseDto.TianrangResponse.TianrangQuestion question =
            new ThirdPartyResponseDto.TianrangResponse.TianrangQuestion();

        // 解析block_type
        String blockType = (String) structureMap.get("block_type");
        question.setType(blockType);

        // 解析location（坐标数组）- 安全类型转换
        List<?> locationRaw = (List<?>) structureMap.get("location");
        List<Integer> location = null;

        if (locationRaw != null && locationRaw.size() >= 8) {
            // 转换为Integer列表，处理BigDecimal等类型
            location = new ArrayList<>();
            for (Object coord : locationRaw) {
                if (coord instanceof Number) {
                    location.add(((Number) coord).intValue());
                }
            }

            if (location.size() >= 8) {
                // location格式：[x1,y1,x2,y2,x3,y3,x4,y4] 表示四个角的坐标
                ThirdPartyResponseDto.TianrangResponse.TianrangQuestion.BoundingBox bbox =
                    new ThirdPartyResponseDto.TianrangResponse.TianrangQuestion.BoundingBox();

                // 计算边界框
                int minX = Math.min(Math.min(location.get(0), location.get(2)), Math.min(location.get(4), location.get(6)));
                int minY = Math.min(Math.min(location.get(1), location.get(3)), Math.min(location.get(5), location.get(7)));
                int maxX = Math.max(Math.max(location.get(0), location.get(2)), Math.max(location.get(4), location.get(6)));
                int maxY = Math.max(Math.max(location.get(1), location.get(3)), Math.max(location.get(5), location.get(7)));

                bbox.setX(minX);
                bbox.setY(minY);
                bbox.setWidth(maxX - minX);
                bbox.setHeight(maxY - minY);
                bbox.setCoordinates(location);  // 保存原始坐标

                question.setBbox(bbox);
            }
        }

        // 设置基本信息
        question.setQuestionId("tianrang_" + System.currentTimeMillis());
        question.setContent(blockType + "区域 - 坐标: " + (location != null ? location.toString() : "未知"));
        question.setConfidence(0.9); // 天壤没有提供置信度，设置默认值

        return question;
    }

    /**
     * 转换天壤题目为标准格式
     */
    private List<QuestionCuttingResultDto.QuestionInfo> convertTianrangQuestions(
            List<ThirdPartyResponseDto.TianrangResponse.TianrangQuestion> tianrangQuestions) {
        
        if (tianrangQuestions == null) {
            return new ArrayList<>();
        }
        
        return tianrangQuestions.stream()
            .map(this::convertTianrangQuestion)
            .collect(Collectors.toList());
    }

    /**
     * 转换单个天壤题目
     */
    private QuestionCuttingResultDto.QuestionInfo convertTianrangQuestion(
            ThirdPartyResponseDto.TianrangResponse.TianrangQuestion tianrangQuestion) {
        
        QuestionCuttingResultDto.QuestionInfo question = new QuestionCuttingResultDto.QuestionInfo();
        
        question.setQuestionId(tianrangQuestion.getQuestionId());
        question.setQuestionType(tianrangQuestion.getType());
        question.setContent(tianrangQuestion.getContent());
        question.setAnswer(tianrangQuestion.getAnswer());
        question.setConfidence(tianrangQuestion.getConfidence());
        
        // 转换选项
        if (tianrangQuestion.getOptions() != null) {
            List<QuestionCuttingResultDto.OptionInfo> options = new ArrayList<>();
            for (int i = 0; i < tianrangQuestion.getOptions().size(); i++) {
                QuestionCuttingResultDto.OptionInfo option = new QuestionCuttingResultDto.OptionInfo();
                option.setLabel(String.valueOf((char) ('A' + i)));
                option.setContent(tianrangQuestion.getOptions().get(i));
                options.add(option);
            }
            question.setOptions(options);
        }
        
        // 转换位置信息（只使用points）
        if (tianrangQuestion.getBbox() != null &&
            tianrangQuestion.getBbox().getCoordinates() != null &&
            tianrangQuestion.getBbox().getCoordinates().size() >= 8) {

            QuestionCuttingResultDto.BoundingBox boundingBox = new QuestionCuttingResultDto.BoundingBox();

            List<Integer> coords = tianrangQuestion.getBbox().getCoordinates();
            List<QuestionCuttingResultDto.Point> points = new ArrayList<>();

            // 将8个数字转换为4个Point对象：[x1,y1,x2,y2,x3,y3,x4,y4]
            for (int i = 0; i < coords.size() - 1; i += 2) {
                QuestionCuttingResultDto.Point point = new QuestionCuttingResultDto.Point();
                point.setX(coords.get(i));
                point.setY(coords.get(i + 1));
                points.add(point);
            }

            // 计算width和height
            int minX = coords.get(0), maxX = coords.get(0);
            int minY = coords.get(1), maxY = coords.get(1);
            for (int i = 0; i < coords.size() - 1; i += 2) {
                minX = Math.min(minX, coords.get(i));
                maxX = Math.max(maxX, coords.get(i));
                minY = Math.min(minY, coords.get(i + 1));
                maxY = Math.max(maxY, coords.get(i + 1));
            }

            boundingBox.setWidth(maxX - minX);
            boundingBox.setHeight(maxY - minY);
            boundingBox.setPoints(points);
            question.setBoundingBox(boundingBox);
        }
        
        return question;
    }

    /**
     * 根据置信度计算质量评分
     */
    private Integer calculateQualityScore(Double confidence) {
        if (confidence == null) {
            return 3;
        }
        
        if (confidence >= 0.9) {
            return 5;
        } else if (confidence >= 0.8) {
            return 4;
        } else if (confidence >= 0.6) {
            return 3;
        } else if (confidence >= 0.4) {
            return 2;
        } else {
            return 1;
        }
    }
}
