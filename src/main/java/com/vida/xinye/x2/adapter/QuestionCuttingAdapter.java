package com.vida.xinye.x2.adapter;

import com.vida.xinye.x2.dto.internal.ThirdPartyRequestDto;
import com.vida.xinye.x2.dto.internal.ThirdPartyResponseDto;
import com.vida.xinye.x2.dto.QuestionCuttingResultDto;
import com.vida.xinye.x2.enums.QuestionCuttingEnum;

/**
 * 智能切题适配器接口
 *
 * <AUTHOR>
 * @date 2025/08/14
 */
public interface QuestionCuttingAdapter {

    /**
     * 获取适配器支持的提供商
     *
     * @return 提供商枚举
     */
    QuestionCuttingEnum.Provider getProvider();

    /**
     * 检查适配器是否可用
     *
     * @return 是否可用
     */
    boolean isAvailable();

    /**
     * 调用第三方接口进行切题
     *
     * @param request 请求参数
     * @return 第三方接口响应
     */
    ThirdPartyResponseDto callThirdPartyApi(ThirdPartyRequestDto request);

    /**
     * 将第三方响应转换为统一格式
     *
     * @param thirdPartyResponse 第三方响应
     * @param requestId 请求ID
     * @return 统一格式的切题结果
     */
    QuestionCuttingResultDto convertToStandardResult(ThirdPartyResponseDto thirdPartyResponse, String requestId);

    /**
     * 获取适配器的优先级（数值越小优先级越高）
     *
     * @return 优先级
     */
    default int getPriority() {
        return 100;
    }

    /**
     * 获取适配器的质量评分（1-5分）
     *
     * @return 质量评分
     */
    default int getQualityScore() {
        return 3;
    }

    /**
     * 获取适配器的平均响应时间（毫秒）
     *
     * @return 平均响应时间
     */
    default long getAverageResponseTime() {
        return 5000;
    }

    /**
     * 验证请求参数
     *
     * @param request 请求参数
     * @return 验证结果
     */
    default boolean validateRequest(ThirdPartyRequestDto request) {
        return request != null && 
               (request.getImageBase64() != null || request.getImageUrl() != null);
    }

    /**
     * 预处理请求参数
     *
     * @param request 原始请求参数
     * @return 预处理后的请求参数
     */
    default ThirdPartyRequestDto preprocessRequest(ThirdPartyRequestDto request) {
        return request;
    }

    /**
     * 后处理响应结果
     *
     * @param response 原始响应结果
     * @return 后处理后的响应结果
     */
    default ThirdPartyResponseDto postprocessResponse(ThirdPartyResponseDto response) {
        return response;
    }
}
