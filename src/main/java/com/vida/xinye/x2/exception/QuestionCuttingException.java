package com.vida.xinye.x2.exception;

import com.vida.xinye.x2.api.IErrorCode;
import com.vida.xinye.x2.constant.QuestionCuttingConstant;

/**
 * 智能切题异常类
 *
 * <AUTHOR>
 * @date 2025/08/14
 */
public class QuestionCuttingException extends RuntimeException {

    /**
     * 错误码
     */
    private IErrorCode errorCode;

    /**
     * 请求ID
     */
    private String requestId;

    /**
     * 提供商
     */
    private String provider;

    public QuestionCuttingException(String message) {
        super(message);
        this.errorCode = QuestionCuttingConstant.ErrorCode.INTERNAL_ERROR;
    }

    public QuestionCuttingException(IErrorCode errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    public QuestionCuttingException(IErrorCode errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }

    public QuestionCuttingException(IErrorCode errorCode, String message, String requestId) {
        super(message);
        this.errorCode = errorCode;
        this.requestId = requestId;
    }

    public QuestionCuttingException(IErrorCode errorCode, String message, String requestId, String provider) {
        super(message);
        this.errorCode = errorCode;
        this.requestId = requestId;
        this.provider = provider;
    }

    public QuestionCuttingException(IErrorCode errorCode, String message, String requestId, String provider, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.requestId = requestId;
        this.provider = provider;
    }

    // Getters and Setters
    public IErrorCode getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(IErrorCode errorCode) {
        this.errorCode = errorCode;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getProvider() {
        return provider;
    }

    public void setProvider(String provider) {
        this.provider = provider;
    }

    /**
     * 创建图片格式不支持异常
     */
    public static QuestionCuttingException unsupportedImageFormat(String format) {
        return new QuestionCuttingException(
            QuestionCuttingConstant.ErrorCode.UNSUPPORTED_IMAGE_FORMAT,
            "不支持的图片格式: " + format
        );
    }

    /**
     * 创建图片尺寸过大异常
     */
    public static QuestionCuttingException imageSizeTooLarge(long size, long maxSize) {
        return new QuestionCuttingException(
            QuestionCuttingConstant.ErrorCode.IMAGE_SIZE_TOO_LARGE,
            String.format("图片尺寸过大: %d bytes, 最大支持: %d bytes", size, maxSize)
        );
    }

    /**
     * 创建图片内容为空异常
     */
    public static QuestionCuttingException emptyImageContent() {
        return new QuestionCuttingException(
            QuestionCuttingConstant.ErrorCode.EMPTY_IMAGE_CONTENT,
            "图片内容为空"
        );
    }

    /**
     * 创建接口调用失败异常
     */
    public static QuestionCuttingException apiCallFailed(String provider, String message) {
        return new QuestionCuttingException(
            QuestionCuttingConstant.ErrorCode.API_CALL_FAILED,
            "接口调用失败: " + message,
            null,
            provider
        );
    }

    /**
     * 创建接口调用失败异常（带请求ID）
     */
    public static QuestionCuttingException apiCallFailed(String provider, String message, String requestId) {
        return new QuestionCuttingException(
            QuestionCuttingConstant.ErrorCode.API_CALL_FAILED,
            "接口调用失败: " + message,
            requestId,
            provider
        );
    }

    /**
     * 创建接口响应超时异常
     */
    public static QuestionCuttingException apiTimeout(String provider, String requestId) {
        return new QuestionCuttingException(
            QuestionCuttingConstant.ErrorCode.API_TIMEOUT,
            "接口响应超时",
            requestId,
            provider
        );
    }

    /**
     * 创建接口返回格式错误异常
     */
    public static QuestionCuttingException invalidApiResponse(String provider, String requestId, String message) {
        return new QuestionCuttingException(
            QuestionCuttingConstant.ErrorCode.INVALID_API_RESPONSE,
            "接口返回格式错误: " + message,
            requestId,
            provider
        );
    }

    /**
     * 创建配置错误异常
     */
    public static QuestionCuttingException configError(String message) {
        return new QuestionCuttingException(
            QuestionCuttingConstant.ErrorCode.CONFIG_ERROR,
            "配置错误: " + message
        );
    }

    /**
     * 创建无可用提供商异常
     */
    public static QuestionCuttingException noAvailableProvider() {
        return new QuestionCuttingException(
            QuestionCuttingConstant.ErrorCode.NO_AVAILABLE_PROVIDER,
            "没有可用的切题接口提供商"
        );
    }

    /**
     * 创建切题结果为空异常
     */
    public static QuestionCuttingException emptyCuttingResult(String requestId) {
        return new QuestionCuttingException(
            QuestionCuttingConstant.ErrorCode.EMPTY_CUTTING_RESULT,
            "切题结果为空",
            requestId
        );
    }

    /**
     * 创建系统内部错误异常
     */
    public static QuestionCuttingException internalError(String message) {
        return new QuestionCuttingException(
            QuestionCuttingConstant.ErrorCode.INTERNAL_ERROR,
            "系统内部错误: " + message
        );
    }

    /**
     * 创建系统内部错误异常（带原因）
     */
    public static QuestionCuttingException internalError(String message, Throwable cause) {
        return new QuestionCuttingException(
            QuestionCuttingConstant.ErrorCode.INTERNAL_ERROR,
            "系统内部错误: " + message,
            cause
        );
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("QuestionCuttingException{");
        sb.append("errorCode='").append(errorCode).append('\'');
        if (requestId != null) {
            sb.append(", requestId='").append(requestId).append('\'');
        }
        if (provider != null) {
            sb.append(", provider='").append(provider).append('\'');
        }
        sb.append(", message='").append(getMessage()).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
