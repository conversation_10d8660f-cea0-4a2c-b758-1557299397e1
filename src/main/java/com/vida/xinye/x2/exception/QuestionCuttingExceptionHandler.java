package com.vida.xinye.x2.exception;

import com.vida.xinye.x2.api.CommonResult;
import com.vida.xinye.x2.constant.QuestionCuttingConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.stream.Collectors;

/**
 * 智能切题异常处理器
 *
 * <AUTHOR>
 * @date 2025/08/14
 */
@Slf4j
@RestControllerAdvice(basePackages = "com.vida.xinye.x2.controller.api")
public class QuestionCuttingExceptionHandler {

    /**
     * 处理智能切题异常
     */
    @ExceptionHandler(QuestionCuttingException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public CommonResult<Void> handleQuestionCuttingException(QuestionCuttingException e) {
        log.error("智能切题异常 - 错误码: {}, 请求ID: {}, 提供商: {}, 消息: {}", 
                 e.getErrorCode(), e.getRequestId(), e.getProvider(), e.getMessage(), e);
        
        return CommonResult.failed(e.getErrorCode());
    }

    /**
     * 处理参数验证异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public CommonResult<Void> handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        String errorMessage = e.getBindingResult().getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining(", "));
        
        log.warn("参数验证失败: {}", errorMessage);
        return CommonResult.failed(QuestionCuttingConstant.ErrorCode.CONFIG_ERROR);
    }

    /**
     * 处理绑定异常
     */
    @ExceptionHandler(BindException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public CommonResult<Void> handleBindException(BindException e) {
        String errorMessage = e.getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining(", "));
        
        log.warn("参数绑定失败: {}", errorMessage);
        return CommonResult.failed(QuestionCuttingConstant.ErrorCode.CONFIG_ERROR);
    }

    /**
     * 处理约束违反异常
     */
    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public CommonResult<Void> handleConstraintViolationException(ConstraintViolationException e) {
        String errorMessage = e.getConstraintViolations().stream()
                .map(ConstraintViolation::getMessage)
                .collect(Collectors.joining(", "));
        
        log.warn("约束验证失败: {}", errorMessage);
        return CommonResult.failed(QuestionCuttingConstant.ErrorCode.CONFIG_ERROR);
    }

    /**
     * 处理文件上传大小超限异常
     */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public CommonResult<Void> handleMaxUploadSizeExceededException(MaxUploadSizeExceededException e) {
        log.warn("文件上传大小超限: {}", e.getMessage());
        return CommonResult.failed(QuestionCuttingConstant.ErrorCode.IMAGE_SIZE_TOO_LARGE);
    }

    /**
     * 处理非法参数异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public CommonResult<Void> handleIllegalArgumentException(IllegalArgumentException e) {
        log.warn("非法参数异常: {}", e.getMessage());
        return CommonResult.failed(QuestionCuttingConstant.ErrorCode.CONFIG_ERROR);
    }

    /**
     * 处理空指针异常
     */
    @ExceptionHandler(NullPointerException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public CommonResult<Void> handleNullPointerException(NullPointerException e) {
        log.error("空指针异常", e);
        return CommonResult.failed(QuestionCuttingConstant.ErrorCode.INTERNAL_ERROR);
    }

    /**
     * 处理运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public CommonResult<Void> handleRuntimeException(RuntimeException e) {
        log.error("运行时异常", e);
        return CommonResult.failed(QuestionCuttingConstant.ErrorCode.INTERNAL_ERROR);
    }

    /**
     * 处理通用异常
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public CommonResult<Void> handleException(Exception e) {
        log.error("未知异常", e);
        return CommonResult.failed(QuestionCuttingConstant.ErrorCode.INTERNAL_ERROR);
    }
}
