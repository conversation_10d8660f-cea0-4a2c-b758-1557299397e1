package com.vida.xinye.x2.controller.api;

import com.vida.xinye.x2.annotation.ApiRestController;
import com.vida.xinye.x2.annotation.OperationLog;
import com.vida.xinye.x2.annotation.RateLimit;
import com.vida.xinye.x2.api.CommonResult;
import com.vida.xinye.x2.constant.OperationLogTypeEnum;
import com.vida.xinye.x2.constant.ParentModeStatus;
import com.vida.xinye.x2.dto.UserDto;
import com.vida.xinye.x2.dto.param.*;
import com.vida.xinye.x2.dto.IdCardVerifyResultDto;
import com.vida.xinye.x2.mbg.mapper.UserMapper;
import com.vida.xinye.x2.mbg.model.User;
import com.vida.xinye.x2.service.UserService;
import com.vida.xinye.x2.service.api.AuthService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * @Description: 用户信息相关接口
 * @Author: zhangwenbin
 * @Date: 2025/06/03 17:55
 */
@ApiRestController
@RequestMapping("/user")
public class UserController {
  private static final Logger logger = LoggerFactory.getLogger(UserController.class);

  @Autowired
  private UserService userService;

  @Autowired
  private AuthService authService;

  @Autowired
  private MessageSource messageSource;

  @Resource
  private UserMapper userMapper;

  /**
   * 修改用户昵称或头像
   * @param param
   * @param userDto
   * @return
   */
  @OperationLog(category = "用户管理", subcategory = "用户信息", desc = "修改用户昵称或头像", type = OperationLogTypeEnum.UPDATE)
  @PostMapping("/info")
  public CommonResult updateUserInfo(@Validated @RequestBody UserInfoUpdateParam param,
                                     @AuthenticationPrincipal UserDto userDto) {
    User user = userMapper.selectByPrimaryKey(userDto.getId());
    if (user == null) {
      return CommonResult.failed("用户不存在");
    }

    // 昵称和头像不能同时为空
    if (param.getNickname() == null && param.getAvatar() == null) {
      return CommonResult.failed("昵称和头像不能同时为空");
    }

    // 更新用户信息
    User updateUser = new User();
    updateUser.setId(userDto.getId());
    if (param.getNickname() != null) {
      updateUser.setNickname(param.getNickname());
    }
    if (param.getAvatar() != null) {
      updateUser.setAvatar(param.getAvatar());
    }
    userMapper.updateByPrimaryKeySelective(updateUser);
    return CommonResult.success(null);
  }

  /**
   * 保存用户身份和年级
   *
   * @param param 用户身份和年级参数
   * @param user  当前登录用户
   * @return CommonResult
   */
  @OperationLog(category = "用户管理", subcategory = "用户档案", desc = "更新用户身份和年级信息", type = OperationLogTypeEnum.UPDATE)
  @PostMapping("/profile")
  public CommonResult<Void> updateProfile(@Validated @RequestBody UserProfileUpdateParam param,
      @AuthenticationPrincipal UserDto user) {
    userService.updateProfile(user.getId(), param);
    return CommonResult.success(null, "保存成功");
  }

  /**
   * 查询家长模式是否开启
   */
  @GetMapping("/parent-mode")
  public CommonResult<Boolean> isParentMode(@AuthenticationPrincipal UserDto user) {
    return CommonResult.success(user.getParentMode() != null && user.getParentMode().equals(ParentModeStatus.ENABLED.getCode()));
  }

  /**
   * 家长模式认证
   */
  @OperationLog(category = "用户管理", subcategory = "家长模式", desc = "家长模式身份认证", type = OperationLogTypeEnum.CREATE)
  @PostMapping("/parent-mode/auth")
  @ResponseBody
  @RateLimit(key = "parent_mode_auth", time = 1, timeUnit = TimeUnit.MINUTES, count = 3, limitType = RateLimit.LimitType.USER, message = "rate.limit.parent.mode.auth.frequent")
  public CommonResult<Void> parentModeAuth(@Validated @RequestBody ParentModeAuthParam param,
      @AuthenticationPrincipal UserDto user) {
    // 1. 实名认证（调用已有身份证认证服务）
    IdCardVerifyDto verifyDto = new IdCardVerifyDto();
    verifyDto.setName(param.getParentName());
    verifyDto.setIdcard(param.getParentIdcard());
    IdCardVerifyResultDto result = authService.verifyIdCard(verifyDto);
    logger.info("家长模式认证::身份证认证结果: {}", result);
    if (result == null || !Boolean.TRUE.equals(result.getSuccess()) || result.getData() == null
        || result.getData().getResult() == null || result.getData().getResult() != 0) {
      return CommonResult.failed(result != null ? result.getMsg() : "实名认证失败");
    }
    // 2. 密码加密存储
    String encodedPwd = new BCryptPasswordEncoder().encode(param.getTeenagerPassword());
    userService.enableParentMode(user.getId(), param.getParentName(), param.getParentIdcard(), encodedPwd);
    return CommonResult.success(null, "家长模式认证成功");
  }

  /**
   * 校验青少年密码
   */
  @PostMapping("/teenager-password/check")
  public CommonResult<Boolean> checkTeenagerPassword(@Validated @RequestBody TeenagerPasswordCheckParam param,
      @AuthenticationPrincipal UserDto user) {
    boolean valid = userService.checkTeenagerPassword(user.getId(), param.getTeenagerPassword());
    return valid ? CommonResult.success(null, "青少年密码正确") : CommonResult.failed("青少年密码错误");
  }

  /**
   * 切换家长模式（开启/关闭）
   *
   * @param param 切换参数
   * @param user  当前登录用户
   * @return CommonResult
   */
  @OperationLog(category = "用户管理", subcategory = "家长模式", desc = "切换家长模式状态", type = OperationLogTypeEnum.UPDATE)
  @PostMapping("/parent-mode/toggle")
  public CommonResult<Void> toggleParentMode(@Validated @RequestBody ParentModeToggleParam param,
      @AuthenticationPrincipal UserDto user) {
    String action = param.getAction();
    Integer parentMode = user.getParentMode();
    String teenagerPassword = param.getTeenagerPassword();
    // 未认证（parentName或parentIdcard为空）
    if (user.getParentName() == null || user.getParentIdcard() == null) {
      String msg = messageSource.getMessage("parent.mode.not.auth", null, "请先完成家长模式认证",
          LocaleContextHolder.getLocale());
      return CommonResult.failed(msg);
    }
    if ("enable".equalsIgnoreCase(action)) {
      if (parentMode.equals(ParentModeStatus.ENABLED.getCode())) {
        String msg = messageSource.getMessage("parent.mode.already.enabled", null, "家长模式已开启",
            LocaleContextHolder.getLocale());
        return CommonResult.success(null, msg);
      }
      // 已认证未开启，需校验青少年密码
      if (teenagerPassword == null || teenagerPassword.isEmpty()) {
        String msg = messageSource.getMessage("teenager.password.notnull", null, "请输入青少年密码",
            LocaleContextHolder.getLocale());
        return CommonResult.failed(msg);
      }
      boolean valid = userService.checkTeenagerPassword(user.getId(), teenagerPassword);
      if (!valid) {
        String msg = messageSource.getMessage("teenager.password.invalid", null, "青少年密码错误",
            LocaleContextHolder.getLocale());
        return CommonResult.failed(msg);
      }
      // 开启家长模式
      String encodedPwd = new BCryptPasswordEncoder().encode(teenagerPassword);
      userService.enableParentMode(user.getId(), user.getParentName(), user.getParentIdcard(), encodedPwd);
      return CommonResult.success(null,
          messageSource.getMessage("parent.mode.enabled", null, "家长模式已开启", LocaleContextHolder.getLocale()));
    } else if ("disable".equalsIgnoreCase(action)) {
      if (parentMode.equals(ParentModeStatus.DISABLED.getCode())) {
        String msg = messageSource.getMessage("parent.mode.not.enabled", null, "家长模式已关闭",
            LocaleContextHolder.getLocale());
        return CommonResult.success(null, msg);
      }
      userService.disableParentMode(user.getId());
      return CommonResult.success(null,
          messageSource.getMessage("parent.mode.disabled", null, "家长模式已关闭", LocaleContextHolder.getLocale()));
    } else {
      String msg = messageSource.getMessage("parent.mode.action.invalid", null, "无效的操作类型",
          LocaleContextHolder.getLocale());
      return CommonResult.failed(msg);
    }
  }
}