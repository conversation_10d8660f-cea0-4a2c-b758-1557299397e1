package com.vida.xinye.x2.controller.admin;

import com.vida.xinye.x2.annotation.AdminRestController;
import com.vida.xinye.x2.annotation.OperationLog;
import com.vida.xinye.x2.api.CommonPage;
import com.vida.xinye.x2.api.CommonResult;
import com.vida.xinye.x2.constant.OperationLogTypeEnum;
import com.vida.xinye.x2.domain.param.MaterialParam;
import com.vida.xinye.x2.dto.MaterialDto;
import com.vida.xinye.x2.service.MaterialService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 素材控制器
 *
 * <AUTHOR>
 * @date 2024/11/11
 */
@AdminRestController("adminMaterialController")
@RequestMapping("/material")
public class MaterialController {
    @Autowired
    private MaterialService materialService;

    /**
     * 获取素材库列表（分页查询）
     */
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    @ResponseBody
    public CommonResult<CommonPage<MaterialDto>> list(@RequestParam(value = "pageSize", defaultValue = "8") Integer pageSize,
                                                      @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                      @RequestParam(value = "categoryId", required = false) Long categoryId) {
        if (categoryId == null) {
            return CommonResult.success(null);
        }
        CommonPage<MaterialDto> result = materialService.list(pageSize, pageNum, 0L, categoryId);
        return CommonResult.success(result);
    }

    /**
     * 新增素材
     */
    @PostMapping
    @ResponseBody
    @OperationLog(category = "素材管理", desc = "新增素材", type = OperationLogTypeEnum.CREATE)
    public CommonResult create(@Validated @RequestBody MaterialParam param) {
        Long id = materialService.create(param);
        if (id > 0) {
            return CommonResult.success(id);
        }
        return CommonResult.failed();
    }

    /**
     * 修改素材
     */
    @RequestMapping(value = "/{id}", method = RequestMethod.POST)
    @ResponseBody
    @OperationLog(category = "素材管理", desc = "修改素材", type = OperationLogTypeEnum.UPDATE)
    public CommonResult update(@PathVariable Long id, @RequestBody MaterialParam param) {
        int count = materialService.update(id, param);
        if (count > 0) {
            return CommonResult.success(null);
        }
        return CommonResult.failed();
    }

    /**
     * 删除素材
     *
     * @param id
     * @return
     */
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @ResponseBody
    @OperationLog(category = "素材管理", desc = "删除素材", type = OperationLogTypeEnum.DELETE)
    public CommonResult delete(@PathVariable Long id) {
        int count = materialService.delete(id);
        if (count > 0) {
            return CommonResult.success(null);
        }
        return CommonResult.failed("数据不存在或已被删除！");
    }

}
