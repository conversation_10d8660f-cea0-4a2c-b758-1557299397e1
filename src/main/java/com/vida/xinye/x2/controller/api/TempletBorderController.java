package com.vida.xinye.x2.controller.api;

import com.vida.xinye.x2.annotation.ApiRestController;
import com.vida.xinye.x2.api.CommonResult;
import com.vida.xinye.x2.dto.TempletBorderDto;
import com.vida.xinye.x2.service.TempletBorderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@ApiRestController
@RequestMapping("/templet-border")
public class TempletBorderController {

    @Autowired
    private TempletBorderService templetBorderService;

    /**
     * 标签边框分页列表
     */
    @GetMapping
    public CommonResult<List<TempletBorderDto>> list(
            @RequestParam(value = "groupId", required = false) Long groupId) {
        return CommonResult.success(templetBorderService.list(groupId));
    }

}
