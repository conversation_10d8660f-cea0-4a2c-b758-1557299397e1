package com.vida.xinye.x2.controller.api.edu;

import cn.hutool.core.util.ObjectUtil;
import com.vida.xinye.x2.annotation.ApiRestController;
import com.vida.xinye.x2.annotation.OperationLog;
import com.vida.xinye.x2.api.CommonResult;
import com.vida.xinye.x2.constant.OperationLogTypeEnum;
import com.vida.xinye.x2.domain.param.xkw.ScoreImprovementRecommendParam;
import com.vida.xinye.x2.domain.param.xkw.SimilarRecommendParam;
import com.vida.xinye.x2.domain.param.xkw.XkwSearchParam;
import com.vida.xinye.x2.dto.UserDto;
import com.vida.xinye.x2.dto.edu.*;
import com.vida.xinye.x2.exception.Asserts;
import com.vida.xinye.x2.service.XkwService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * 学科网接口
 *
 * <AUTHOR>
 * @date 2024/11/4
 */
@ApiRestController
@RequestMapping("/xkw")
public class XkwController {

    @Autowired
    private XkwService xkwService;

    /**
     * 拍搜题-精品
     *
     * @return CommonResult
     */
    @RequestMapping(value = "/search-jp", method = RequestMethod.POST)
    @ResponseBody
    @OperationLog(category = "学科网", desc = "拍搜题-精品", type = OperationLogTypeEnum.GET)
    public CommonResult<XopTextSearchRespVO> searchJP(@Validated @RequestBody XkwSearchParam param) {
        XopTextSearchRespVO result = xkwService.searchJP(param);
        return CommonResult.success(result);
    }

    /**
     * 拍搜题-海量
     *
     * @return CommonResult
     */
    @RequestMapping(value = "/search-deep", method = RequestMethod.POST)
    @ResponseBody
    @OperationLog(category = "学科网", desc = "拍搜题-海量", type = OperationLogTypeEnum.GET)
    public CommonResult<XopTextSearchRespVO> searchDeep(@Validated @RequestBody XkwSearchParam param) {
        XopTextSearchRespVO result = xkwService.searchDeep(param);
        return CommonResult.success(result);
    }

    /**
     * 试题推送-举一反三
     *
     * @param param typeId 1:错题本 2 训练记录
     * @return CommonResult
     */
    @RequestMapping(value = "/similar-recommend", method = RequestMethod.POST)
    @ResponseBody
    @OperationLog(category = "学科网", desc = "试题推送-举一反三", type = OperationLogTypeEnum.CREATE)
    public CommonResult<XopSimilarRecommendRespVO> similarRecommend(
            @Validated @RequestBody SimilarRecommendParam param,
            @AuthenticationPrincipal UserDto userDto) {
        if (ObjectUtil.isNull(userDto)) {
            Asserts.fail("用户未登录或登录失效！");
        }
        Integer typeId = ObjectUtil.isNotNull(param.getTypeId()) ? param.getTypeId() : 1;
        XopSimilarRecommendRespVO result = xkwService.similarRecommend(userDto.getId(), param, typeId);
        return CommonResult.success(result);
    }

    /**
     * 获取题型列表
     *
     * @param courseId 课程ID，可选
     * @return CommonResult
     */
    @RequestMapping(value = "/question-types", method = RequestMethod.GET)
    @ResponseBody
    @OperationLog(category = "学科网", desc = "获取题型列表", type = OperationLogTypeEnum.GET)
    public CommonResult<List<XopQuestionType>> getQuestionTypes(@RequestParam(required = true) Integer courseId) {
        List<XopQuestionType> result = xkwService.getQuestionTypes(courseId);
        return CommonResult.success(result);
    }

    /**
     * 获取难度等级列表
     *
     * @return CommonResult
     */
    @RequestMapping(value = "/question-difficulties", method = RequestMethod.GET)
    @ResponseBody
    @OperationLog(category = "学科网", desc = "获取难度等级列表", type = OperationLogTypeEnum.GET)
    public CommonResult<List<XopQuestionDifficulty>> getQuestionDifficulties() {
        List<XopQuestionDifficulty> result = xkwService.getQuestionDifficulties();
        return CommonResult.success(result);
    }

    /**
     * 获取行政区列表
     *
     * @return CommonResult
     */
    @RequestMapping(value = "/areas/all", method = RequestMethod.GET)
    @ResponseBody
    @OperationLog(category = "学科网", desc = "获取行政区列表", type = OperationLogTypeEnum.GET)
    public CommonResult<List<XopArea>> getAllAreas() {
        List<XopArea> result = xkwService.getAllAreas();
        return CommonResult.success(result);
    }

    /**
     * 获取省级和直辖市级别的行政区列表
     *
     * @return CommonResult
     */
    @RequestMapping(value = "/areas/provinces", method = RequestMethod.GET)
    @ResponseBody
    @OperationLog(category = "学科网", desc = "获取省级和直辖市级别的行政区列表", type = OperationLogTypeEnum.GET)
    public CommonResult<List<XopArea>> getProvinceAreas() {
        List<XopArea> result = xkwService.getProvinceAreas();
        return CommonResult.success(result);
    }

}
