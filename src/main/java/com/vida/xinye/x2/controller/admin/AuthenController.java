package com.vida.xinye.x2.controller.admin;

import com.vida.xinye.x2.annotation.AdminRestController;
import com.vida.xinye.x2.api.CommonResult;
import com.vida.xinye.x2.dto.TokenDto;
import com.vida.xinye.x2.dto.param.UserAuthenDto;
import com.vida.xinye.x2.service.admin.AuthService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 身份认证相关接口
 *
 * <AUTHOR>
 * @date 2024/11/4
 */
@AdminRestController("adminAuthenController")
@RequestMapping("/authen")
public class AuthenController {
    @Autowired
    private AuthService authService;

    /**
     * 用户登录
     * 登录以后返回token
     */
    @RequestMapping(value = "/login", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult login(@RequestBody UserAuthenDto loginDto) {
        TokenDto tokenDto = authService.login(loginDto);
        if (tokenDto == null) {
            return CommonResult.failed("登录失败！");
        }
        return CommonResult.success(tokenDto, "登录成功");
    }

    /**
     * 用户退出登录
     * 登录以后返回token
     */
    @RequestMapping(value = "/logout", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult logout() {

        return CommonResult.success(null);
    }


}
