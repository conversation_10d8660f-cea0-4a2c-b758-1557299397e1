package com.vida.xinye.x2.controller.api;

import com.vida.xinye.x2.annotation.ApiRestController;
import com.vida.xinye.x2.annotation.OperationLog;
import com.vida.xinye.x2.annotation.RateLimit;
import com.vida.xinye.x2.api.CommonResult;
import com.vida.xinye.x2.constant.OperationLogTypeEnum;
import com.vida.xinye.x2.dto.BatchQuestionCuttingResultDto;
import com.vida.xinye.x2.dto.QuestionCuttingResultDto;
import com.vida.xinye.x2.dto.UserDto;
import com.vida.xinye.x2.dto.param.QuestionCuttingParam;
import com.vida.xinye.x2.service.QuestionCuttingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 智能切题API控制器
 *
 * <AUTHOR>
 * @date 2025/08/14
 */
@Slf4j
@ApiRestController
@RequestMapping("/question/cutting")
@Validated
public class QuestionCuttingController {

    @Autowired
    private QuestionCuttingService questionCuttingService;

    /**
     * 单张图片切题（文件上传方式）
     */
    @OperationLog(category = "智能切题", subcategory = "单图切题", desc = "上传图片进行智能切题", type = OperationLogTypeEnum.CREATE)
    @RateLimit(key = "cut_question_file", time = 1, timeUnit = TimeUnit.MINUTES, count = 10, limitType = RateLimit.LimitType.USER)
    @PostMapping("/file")
    public CommonResult<QuestionCuttingResultDto> cutQuestionByFile(
            @RequestParam("image") @NotNull(message = "图片文件不能为空") MultipartFile image,
            @RequestParam(value = "strategy", required = false) String strategy,
            @RequestParam(value = "provider", required = false) String provider,
            @RequestParam(value = "subject", required = false) String subject,
            @RequestParam(value = "grade", required = false) String grade,
            @RequestParam(value = "needDetail", defaultValue = "false") Boolean needDetail,
            @RequestParam(value = "enableCache", defaultValue = "true") Boolean enableCache,
            @AuthenticationPrincipal UserDto user) {

        try {
            log.info("用户上传图片切题 - 用户ID: {}, 策略: {}, 提供商: {}", user.getId(), strategy, provider);

            QuestionCuttingParam param = new QuestionCuttingParam();
            param.setImageFile(image);
            param.setStrategy(strategy);
            param.setProvider(provider);
            param.setSubject(subject);
            param.setGrade(grade);
            param.setNeedDetail(needDetail);
            param.setEnableCache(enableCache);

            QuestionCuttingResultDto result = questionCuttingService.cutQuestion(param);

            if (result.getSuccess()) {
                log.info("图片切题成功 - 用户ID: {}, 请求ID: {}, 题目数量: {}", 
                        user.getId(), result.getRequestId(), 
                        result.getQuestions() != null ? result.getQuestions().size() : 0);
                return CommonResult.success(result, "切题成功");
            } else {
                log.warn("图片切题失败 - 用户ID: {}, 请求ID: {}, 错误: {}", 
                        user.getId(), result.getRequestId(), result.getErrorMessage());
                return CommonResult.failed(result.getErrorMessage());
            }

        } catch (Exception e) {
            log.error("图片切题异常 - 用户ID: {}, 错误: {}", user.getId(), e.getMessage(), e);
            return CommonResult.failed("切题处理失败: " + e.getMessage());
        }
    }

    /**
     * 单张图片切题（URL方式）
     */
    @OperationLog(category = "智能切题", subcategory = "单图切题", desc = "通过URL进行智能切题", type = OperationLogTypeEnum.CREATE)
    @RateLimit(key = "cut_question_url", time = 1, timeUnit = TimeUnit.MINUTES, count = 15, limitType = RateLimit.LimitType.USER)
    @PostMapping("/url")
    public CommonResult<QuestionCuttingResultDto> cutQuestionByUrl(
            @RequestParam("imageUrl") @NotBlank(message = "图片URL不能为空") String imageUrl,
            @RequestParam(value = "strategy", required = false) String strategy,
            @RequestParam(value = "provider", required = false) String provider,
            @RequestParam(value = "subject", required = false) String subject,
            @RequestParam(value = "grade", required = false) String grade,
            @RequestParam(value = "needDetail", defaultValue = "false") Boolean needDetail,
            @RequestParam(value = "enableCache", defaultValue = "true") Boolean enableCache,
            @AuthenticationPrincipal UserDto user) {

        try {
            log.info("用户URL切题 - 用户ID: {}, URL: {}, 策略: {}", user.getId(), imageUrl, strategy);

            QuestionCuttingResultDto result = questionCuttingService.cutQuestionByUrl(
                    imageUrl, strategy, provider, subject, grade, needDetail, enableCache);

            if (result.getSuccess()) {
                log.info("URL切题成功 - 用户ID: {}, 请求ID: {}, 题目数量: {}", 
                        user.getId(), result.getRequestId(), 
                        result.getQuestions() != null ? result.getQuestions().size() : 0);
                return CommonResult.success(result, "切题成功");
            } else {
                log.warn("URL切题失败 - 用户ID: {}, 请求ID: {}, 错误: {}", 
                        user.getId(), result.getRequestId(), result.getErrorMessage());
                return CommonResult.failed(result.getErrorMessage());
            }

        } catch (Exception e) {
            log.error("URL切题异常 - 用户ID: {}, 错误: {}", user.getId(), e.getMessage(), e);
            return CommonResult.failed("切题处理失败: " + e.getMessage());
        }
    }

    /**
     * 单张图片切题（Base64方式）
     */
    @OperationLog(category = "智能切题", subcategory = "单图切题", desc = "通过Base64进行智能切题", type = OperationLogTypeEnum.CREATE)
    @RateLimit(key = "cut_question_base64", time = 1, timeUnit = TimeUnit.MINUTES, count = 10, limitType = RateLimit.LimitType.USER)
    @PostMapping("/base64")
    public CommonResult<QuestionCuttingResultDto> cutQuestionByBase64(
            @RequestBody @Validated QuestionCuttingParam param,
            @AuthenticationPrincipal UserDto user) {

        try {
            log.info("用户Base64切题 - 用户ID: {}, 策略: {}", user.getId(), param.getStrategy());

            QuestionCuttingResultDto result = questionCuttingService.cutQuestion(param);

            if (result.getSuccess()) {
                log.info("Base64切题成功 - 用户ID: {}, 请求ID: {}, 题目数量: {}", 
                        user.getId(), result.getRequestId(), 
                        result.getQuestions() != null ? result.getQuestions().size() : 0);
                return CommonResult.success(result, "切题成功");
            } else {
                log.warn("Base64切题失败 - 用户ID: {}, 请求ID: {}, 错误: {}", 
                        user.getId(), result.getRequestId(), result.getErrorMessage());
                return CommonResult.failed(result.getErrorMessage());
            }

        } catch (Exception e) {
            log.error("Base64切题异常 - 用户ID: {}, 错误: {}", user.getId(), e.getMessage(), e);
            return CommonResult.failed("切题处理失败: " + e.getMessage());
        }
    }

    /**
     * 批量图片切题
     */
    @OperationLog(category = "智能切题", subcategory = "批量切题", desc = "批量图片智能切题", type = OperationLogTypeEnum.CREATE)
    @RateLimit(key = "batch_cut_question", time = 5, timeUnit = TimeUnit.MINUTES, count = 3, limitType = RateLimit.LimitType.USER)
    @PostMapping("/batch")
    public CommonResult<BatchQuestionCuttingResultDto> batchCutQuestion(
            @RequestParam("images") @NotNull(message = "图片列表不能为空") List<MultipartFile> images,
            @RequestParam(value = "strategy", required = false) String strategy,
            @RequestParam(value = "provider", required = false) String provider,
            @RequestParam(value = "subject", required = false) String subject,
            @RequestParam(value = "grade", required = false) String grade,
            @RequestParam(value = "needDetail", defaultValue = "false") Boolean needDetail,
            @RequestParam(value = "enableCache", defaultValue = "true") Boolean enableCache,
            @RequestParam(value = "parallel", defaultValue = "true") Boolean parallel,
            @AuthenticationPrincipal UserDto user) {

        try {
            log.info("用户批量切题 - 用户ID: {}, 图片数量: {}, 策略: {}, 并行: {}", 
                    user.getId(), images.size(), strategy, parallel);

            BatchQuestionCuttingResultDto result = questionCuttingService.batchCutQuestion(
                    images, strategy, provider, subject, grade, needDetail, enableCache, parallel);

            if (result.getSuccess()) {
                log.info("批量切题成功 - 用户ID: {}, 批次ID: {}, 成功: {}/{}", 
                        user.getId(), result.getBatchId(), result.getSuccessCount(), result.getTotalCount());
                return CommonResult.success(result, "批量切题完成");
            } else {
                log.warn("批量切题失败 - 用户ID: {}, 批次ID: {}", user.getId(), result.getBatchId());
                return CommonResult.failed("批量切题处理失败");
            }

        } catch (Exception e) {
            log.error("批量切题异常 - 用户ID: {}, 错误: {}", user.getId(), e.getMessage(), e);
            return CommonResult.failed("批量切题处理失败: " + e.getMessage());
        }
    }

    /**
     * 获取支持的提供商列表
     */
    @OperationLog(category = "智能切题", subcategory = "配置查询", desc = "获取支持的提供商列表", type = OperationLogTypeEnum.GET)
    @GetMapping("/providers")
    public CommonResult<List<String>> getSupportedProviders() {
        try {
            List<String> providers = questionCuttingService.getSupportedProviders();
            return CommonResult.success(providers, "获取提供商列表成功");
        } catch (Exception e) {
            log.error("获取提供商列表失败: {}", e.getMessage(), e);
            return CommonResult.failed("获取提供商列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取支持的策略列表
     */
    @OperationLog(category = "智能切题", subcategory = "配置查询", desc = "获取支持的策略列表", type = OperationLogTypeEnum.GET)
    @GetMapping("/strategies")
    public CommonResult<List<String>> getSupportedStrategies() {
        try {
            List<String> strategies = questionCuttingService.getSupportedStrategies();
            return CommonResult.success(strategies, "获取策略列表成功");
        } catch (Exception e) {
            log.error("获取策略列表失败: {}", e.getMessage(), e);
            return CommonResult.failed("获取策略列表失败: " + e.getMessage());
        }
    }

    /**
     * 检查提供商状态
     */
    @OperationLog(category = "智能切题", subcategory = "状态检查", desc = "检查提供商状态", type = OperationLogTypeEnum.GET)
    @GetMapping("/provider/{provider}/status")
    public CommonResult<Boolean> checkProviderStatus(
            @PathVariable("provider") @NotBlank(message = "提供商不能为空") String provider) {
        try {
            boolean available = questionCuttingService.checkProviderStatus(provider);
            return CommonResult.success(available, "提供商状态检查完成");
        } catch (Exception e) {
            log.error("检查提供商状态失败 - 提供商: {}, 错误: {}", provider, e.getMessage(), e);
            return CommonResult.failed("检查提供商状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取提供商统计信息
     */
    @OperationLog(category = "智能切题", subcategory = "统计查询", desc = "获取提供商统计信息", type = OperationLogTypeEnum.GET)
    @GetMapping("/stats")
    public CommonResult<Map<String, Object>> getProviderStats(@AuthenticationPrincipal UserDto user) {
        try {
            log.info("用户查询提供商统计 - 用户ID: {}", user.getId());
            Map<String, Object> stats = questionCuttingService.getProviderStats();
            return CommonResult.success(stats, "获取统计信息成功");
        } catch (Exception e) {
            log.error("获取提供商统计失败 - 用户ID: {}, 错误: {}", user.getId(), e.getMessage(), e);
            return CommonResult.failed("获取统计信息失败: " + e.getMessage());
        }
    }
}
