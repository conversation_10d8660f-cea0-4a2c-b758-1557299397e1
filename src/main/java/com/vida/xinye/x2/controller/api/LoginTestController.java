package com.vida.xinye.x2.controller.api;

import com.vida.xinye.x2.annotation.ApiRestController;
import com.vida.xinye.x2.api.CommonResult;
import com.vida.xinye.x2.util.AppleIdUtil;
import com.vida.xinye.x2.util.QQApiUtil;
import com.vida.xinye.x2.util.WechatApiUtil;
import com.vida.xinye.x2.util.WeiboApiUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.HashMap;
import java.util.Map;

/**
 * 登录功能测试控制器
 * 用于测试第三方登录配置是否正确
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@ApiRestController
@RequestMapping("/test/login")
@Slf4j
public class LoginTestController {

    @Autowired
    private WechatApiUtil wechatApiUtil;

    @Autowired
    private QQApiUtil qqApiUtil;

    @Autowired
    private WeiboApiUtil weiboApiUtil;

    @Autowired
    private AppleIdUtil appleIdUtil;

    /**
     * 检查第三方登录配置状态
     */
    @GetMapping("/config/status")
    public CommonResult checkConfigStatus() {
        Map<String, Object> status = new HashMap<>();
        
        // 检查微信配置
        Map<String, Object> wechatStatus = new HashMap<>();
        wechatStatus.put("configured", wechatApiUtil.isConfigured());
        wechatStatus.put("description", wechatApiUtil.isConfigured() ? "微信登录配置正常" : "微信登录未配置");
        status.put("wechat", wechatStatus);
        
        // 检查QQ配置
        Map<String, Object> qqStatus = new HashMap<>();
        qqStatus.put("configured", qqApiUtil.isConfigured());
        qqStatus.put("description", qqApiUtil.isConfigured() ? "QQ登录配置正常" : "QQ登录未配置");
        status.put("qq", qqStatus);
        
        // 检查微博配置
        Map<String, Object> weiboStatus = new HashMap<>();
        weiboStatus.put("configured", weiboApiUtil.isConfigured());
        weiboStatus.put("description", weiboApiUtil.isConfigured() ? "微博登录配置正常" : "微博登录未配置");
        status.put("weibo", weiboStatus);
        
        // 检查Apple ID配置
        Map<String, Object> appleStatus = new HashMap<>();
        appleStatus.put("configured", appleIdUtil.isConfigured());
        appleStatus.put("description", appleIdUtil.isConfigured() ? "Apple ID登录配置正常" : "Apple ID登录未配置");
        status.put("apple", appleStatus);
        
        return CommonResult.success(status, "配置状态检查完成");
    }

    /**
     * 生成微信授权URL（测试用）
     */
    @GetMapping("/wechat/auth-url")
    public CommonResult getWechatAuthUrl() {
        try {
            if (!wechatApiUtil.isConfigured()) {
                return CommonResult.failed("微信登录未配置");
            }
            
            String redirectUri = "http://localhost:8080/callback/wechat";
            String state = "test_" + System.currentTimeMillis();
            String authUrl = wechatApiUtil.buildAuthUrl(redirectUri, state, "snsapi_userinfo");
            
            Map<String, Object> result = new HashMap<>();
            result.put("authUrl", authUrl);
            result.put("state", state);
            result.put("redirectUri", redirectUri);
            
            return CommonResult.success(result, "微信授权URL生成成功");
            
        } catch (Exception e) {
            log.error("生成微信授权URL失败", e);
            return CommonResult.failed("生成微信授权URL失败: " + e.getMessage());
        }
    }

    /**
     * 生成QQ授权URL（测试用）
     */
    @GetMapping("/qq/auth-url")
    public CommonResult getQQAuthUrl() {
        try {
            if (!qqApiUtil.isConfigured()) {
                return CommonResult.failed("QQ登录未配置");
            }
            
            String state = "test_" + System.currentTimeMillis();
            String authUrl = qqApiUtil.buildAuthUrl(state, "web");
            
            Map<String, Object> result = new HashMap<>();
            result.put("authUrl", authUrl);
            result.put("state", state);
            result.put("platform", "web");
            
            return CommonResult.success(result, "QQ授权URL生成成功");
            
        } catch (Exception e) {
            log.error("生成QQ授权URL失败", e);
            return CommonResult.failed("生成QQ授权URL失败: " + e.getMessage());
        }
    }

    /**
     * 生成微博授权URL（测试用）
     */
    @GetMapping("/weibo/auth-url")
    public CommonResult getWeiboAuthUrl() {
        try {
            if (!weiboApiUtil.isConfigured()) {
                return CommonResult.failed("微博登录未配置");
            }
            
            String state = "test_" + System.currentTimeMillis();
            String authUrl = weiboApiUtil.buildAuthUrl(state);
            
            Map<String, Object> result = new HashMap<>();
            result.put("authUrl", authUrl);
            result.put("state", state);
            
            return CommonResult.success(result, "微博授权URL生成成功");
            
        } catch (Exception e) {
            log.error("生成微博授权URL失败", e);
            return CommonResult.failed("生成微博授权URL失败: " + e.getMessage());
        }
    }

    /**
     * 测试邮件发送功能
     */
    @GetMapping("/email/test")
    public CommonResult testEmail() {
        // 这个接口需要谨慎使用，避免发送垃圾邮件
        return CommonResult.success(null, "邮件测试接口已禁用，请通过验证码发送接口测试");
    }

    /**
     * 获取支持的登录方式
     */
    @GetMapping("/supported-methods")
    public CommonResult getSupportedMethods() {
        Map<String, Object> methods = new HashMap<>();
        
        // 基础登录方式（总是支持）
        methods.put("phone_password", true);
        methods.put("email_password", true);
        methods.put("email_captcha", true);
        
        // 第三方登录方式（根据配置决定）
        methods.put("wechat", wechatApiUtil.isConfigured());
        methods.put("qq", qqApiUtil.isConfigured());
        methods.put("weibo", weiboApiUtil.isConfigured());
        methods.put("apple", appleIdUtil.isConfigured());
        
        return CommonResult.success(methods, "支持的登录方式查询完成");
    }
}
