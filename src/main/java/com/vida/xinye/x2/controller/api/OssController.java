
package com.vida.xinye.x2.controller.api;

import cn.hutool.json.JSONObject;
import com.vida.xinye.x2.annotation.ApiRestController;
import com.vida.xinye.x2.api.CommonResult;
import com.vida.xinye.x2.domain.param.ImageReqParam;
import com.vida.xinye.x2.service.impl.OssServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Base64;

/**
 * Oss相关操作接口
 */
@ApiRestController
@RequestMapping("/oss")
public class OssController {
	@Autowired
	private OssServiceImpl ossService;

	@RequestMapping(value = "/assumeRole", method = RequestMethod.GET)
	@ResponseBody
	public CommonResult getAssumeRole() {
		JSONObject assumeRole = ossService.getAssumeRole();
		return CommonResult.success(assumeRole);
	}

	@RequestMapping(value = "/upload", method = RequestMethod.POST)
	@ResponseBody
	public CommonResult uploadImage(@RequestBody ImageReqParam imageReqParam) {
		String ossUrl = ossService.uploadImage(imageReqParam.getImage(), imageReqParam.getFileName());
		return CommonResult.success(ossUrl);
	}

}
