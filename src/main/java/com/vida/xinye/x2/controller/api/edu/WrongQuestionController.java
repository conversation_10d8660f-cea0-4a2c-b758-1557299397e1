package com.vida.xinye.x2.controller.api.edu;

import cn.hutool.core.util.ObjectUtil;
import com.vida.xinye.x2.annotation.ApiRestController;
import com.vida.xinye.x2.api.CommonPage;
import com.vida.xinye.x2.api.CommonResult;
import com.vida.xinye.x2.domain.param.BatchMoveWrongQuestionsParam;
import com.vida.xinye.x2.domain.param.WrongQuestionParam;
import com.vida.xinye.x2.domain.param.WrongQuestionSubjectParam;
import com.vida.xinye.x2.domain.param.WrongQuestionTagParam;
import com.vida.xinye.x2.dto.UserDto;
import com.vida.xinye.x2.dto.WrongQuestionDto;
import com.vida.xinye.x2.dto.WrongQuestionSubject;
import com.vida.xinye.x2.exception.Asserts;
import com.vida.xinye.x2.mbg.model.WrongQuestion;
import com.vida.xinye.x2.mbg.model.WrongQuestionTag;
import com.vida.xinye.x2.service.api.WrongQuestionService;
import com.vida.xinye.x2.service.api.WrongQuestionTagService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 错题控制器
 *
 * <AUTHOR>
 * @date 2025/02/25
 */
@ApiRestController
@RequestMapping("/wrongQuestion")
public class WrongQuestionController {
    @Autowired
    private WrongQuestionService questionService;

    // 根据错题标签，返回标签对应的错题
    // 分页查询
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public CommonResult<CommonPage<WrongQuestionDto>> list(
            @RequestParam(value = "tagId", required = true) List<Long> tagIds,
            @RequestParam(value = "sourceType", required = false) String sourceType,
            @RequestParam(value = "saveTimeRange", required = false) String saveTimeRange,
            @RequestParam(value = "pageSize", defaultValue = "8") Integer pageSize,
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @AuthenticationPrincipal UserDto userDto) {
        if (ObjectUtil.isNull(userDto)) {
            Asserts.fail("用户未登录或登录失效！");
        }
        CommonPage<WrongQuestionDto> result = questionService.list(tagIds, sourceType, saveTimeRange, userDto.getId(),
                pageNum, pageSize);
        return CommonResult.success(result);
    }

    @GetMapping(value = "/subject")
    public CommonResult<List<WrongQuestionSubject>> findSubjects(@AuthenticationPrincipal UserDto userDto) {
        if (ObjectUtil.isNull(userDto)) {
            Asserts.fail("用户未登录或登录失效！");
        }
        List<WrongQuestionSubject> result = questionService.findSubjects(userDto.getId());
        return CommonResult.success(result);
    }

    @PostMapping(value = "/subject")
    public CommonResult createSubject(@Validated @RequestBody WrongQuestionSubjectParam param,
            @AuthenticationPrincipal UserDto userDto) {
        if (ObjectUtil.isNull(userDto)) {
            Asserts.fail("用户未登录或登录失效！");
        }
        param.setUserId(userDto.getId());
        Long id = questionService.createSubject(param);
        if (id > 0) {
            return CommonResult.success(id);
        }
        return CommonResult.failed();
    }

    @DeleteMapping(value = "/subject/{id}")
    public CommonResult deleteSubject(@PathVariable Long id,
            @AuthenticationPrincipal UserDto userDto) {
        if (ObjectUtil.isNull(userDto)) {
            Asserts.fail("用户未登录或登录失效！");
        }
        int count = questionService.deleteSubject(id, userDto.getId());
        if (count > 0) {
            return CommonResult.success(null);
        }
        return CommonResult.failed();
    }

    /**
     * 新增错题
     */
    @PostMapping
    public CommonResult create(@Validated @RequestBody WrongQuestionParam param,
            @AuthenticationPrincipal UserDto userDto) {
        if (ObjectUtil.isNull(userDto)) {
            Asserts.fail("用户未登录或登录失效！");
        }
        param.setUserId(userDto.getId());
        Long id = questionService.create(param);
        if (id > 0) {
            return CommonResult.success(id);
        }
        return CommonResult.failed();
    }

    /**
     * 修改错题
     */
    @PutMapping("/{id}")
    public CommonResult update(@PathVariable Long id,
            @Validated @RequestBody WrongQuestionParam param,
            @AuthenticationPrincipal UserDto userDto) {
        if (ObjectUtil.isNull(userDto)) {
            Asserts.fail("用户未登录或登录失效！");
        }
        param.setUserId(userDto.getId());
        int count = questionService.update(id, param);
        if (count > 0) {
            return CommonResult.success(count);
        }
        return CommonResult.failed();
    }

    // 新增批量删除接口
    @PostMapping("/batchDelete")
    public CommonResult batchDelete(@RequestParam List<Long> ids,
            @AuthenticationPrincipal UserDto userDto) {
        if (ObjectUtil.isNull(userDto)) {
            Asserts.fail("用户未登录或登录失效！");
        }
        int count = questionService.deleteBatch(ids, userDto.getId());
        if (count > 0) {
            return CommonResult.success(count);
        }
        return CommonResult.failed();
    }

    /**
     * 批量移动错题到指定科目
     */
    @PostMapping("/batchMove")
    public CommonResult batchMove(@Validated @RequestBody BatchMoveWrongQuestionsParam param,
            @AuthenticationPrincipal UserDto userDto) {
        if (ObjectUtil.isNull(userDto)) {
            Asserts.fail("用户未登录或登录失效！");
        }
        int count = questionService.batchMove(param, userDto.getId());
        if (count > 0) {
            return CommonResult.success(count);
        }
        return CommonResult.failed("没有错题被移动，或操作失败！");
    }

}
