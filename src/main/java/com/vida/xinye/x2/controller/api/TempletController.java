package com.vida.xinye.x2.controller.api;

import com.vida.xinye.x2.annotation.ApiRestController;
import com.vida.xinye.x2.annotation.OperationLog;
import com.vida.xinye.x2.api.CommonPage;
import com.vida.xinye.x2.api.CommonResult;
import com.vida.xinye.x2.constant.OperationLogTypeEnum;
import com.vida.xinye.x2.constant.PrintHistorySourceTypeEnum;
import com.vida.xinye.x2.constant.TempletSourceTypeEnum;
import com.vida.xinye.x2.domain.param.TempletParam;
import com.vida.xinye.x2.domain.param.TempletPrintParam;
import com.vida.xinye.x2.dto.TempletDto;
import com.vida.xinye.x2.dto.UserDto;
import com.vida.xinye.x2.service.TempletService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 标签控制器
 *
 * <AUTHOR>
 * @date 2024/11/11
 */
@ApiRestController
@RequestMapping("/templet")
public class TempletController {
    @Autowired
    private TempletService templetService;

    /**
     * 获取标签库列表（分页查询）
     */
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    @ResponseBody
    public CommonResult<CommonPage<TempletDto>> list(@RequestParam(value = "pageSize", defaultValue = "8") Integer pageSize,
                                                     @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                     @RequestParam(value = "categoryId", required = false) Long categoryId,
                                                     @AuthenticationPrincipal UserDto userDto) {
        CommonPage<TempletDto> result = templetService.list(pageSize, pageNum, userDto.getId(), TempletSourceTypeEnum.NOTE.getValue());
        return CommonResult.success(result);
    }

    /**
     * 新增标签
     */
    @PostMapping
    @ResponseBody
    @OperationLog(category = "标签管理", desc = "新增标签", type = OperationLogTypeEnum.CREATE)
    public CommonResult create(@Validated @RequestBody TempletParam param, @AuthenticationPrincipal UserDto userDto) {
        param.setUserId(userDto.getId());
        //sourceType是纸条类型
        param.setSourceType(TempletSourceTypeEnum.NOTE.getValue());

        Long id = templetService.create(param);
        if (id > 0) {
            return CommonResult.success(id);
        }
        return CommonResult.failed();
    }

    /**
     * 修改标签
     */
    @RequestMapping(value = "/{id}", method = RequestMethod.POST)
    @ResponseBody
    @OperationLog(category = "标签管理", desc = "修改标签", type = OperationLogTypeEnum.UPDATE)
    public CommonResult update(@PathVariable Long id, @RequestBody TempletParam param) {
        int count = templetService.update(id, param);
        //sourceType是纸条类型
        param.setSourceType(TempletSourceTypeEnum.NOTE.getValue());

        if (count > 0) {
            return CommonResult.success(null);
        }
        return CommonResult.failed();
    }

    /**
     * 删除标签
     *
     * @param id
     * @return
     */
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @ResponseBody
    @OperationLog(category = "标签管理", desc = "删除标签", type = OperationLogTypeEnum.DELETE)
    public CommonResult delete(@PathVariable Long id) {
        int count = templetService.delete(id);
        if (count > 0) {
            return CommonResult.success(null);
        }
        return CommonResult.failed("数据不存在或已被删除！");
    }

    /**
     * 批量删除标签
     *
     * @param ids
     * @return
     */
    @DeleteMapping
    @ResponseBody
    @OperationLog(category = "标签管理", desc = "删除标签-批量", type = OperationLogTypeEnum.DELETE)
    public CommonResult batchDelete(@RequestParam List<Long> ids, @AuthenticationPrincipal UserDto userDto) {
        int count = templetService.batchDelete(ids, userDto.getId());
        if (count > 0) {
            return CommonResult.success(null);
        }
        return CommonResult.failed("数据不存在或已被删除！");
    }

    /**
     * 获取打印历史记录
     */
    @RequestMapping(value = "/print", method = RequestMethod.GET)
    @ResponseBody
    public CommonResult printHistory(@RequestParam(value = "pageSize", defaultValue = "8") Integer pageSize,
                                     @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                     @AuthenticationPrincipal UserDto userDto) {

        CommonPage<TempletDto> result = templetService.printHistory(pageSize, pageNum, userDto.getId(), PrintHistorySourceTypeEnum.NOTE.getValue());
        return CommonResult.success(result);
    }


    /**
     * 记录打印
     */
    @RequestMapping(value = "/print", method = RequestMethod.POST)
    @ResponseBody
    @OperationLog(category = "标签管理", desc = "打印历史", type = OperationLogTypeEnum.CREATE)
    public CommonResult print(@RequestBody TempletPrintParam param, @AuthenticationPrincipal UserDto userDto) {
        param.setUserId(userDto.getId());
        //sourceType是纸条类型
        param.setSourceType(PrintHistorySourceTypeEnum.NOTE.getValue());

        int count = templetService.print(param);
        if (count > 0) {
            return CommonResult.success(null);
        }
        return CommonResult.failed("打印历史记录失败！");
    }

    /**
     * 删除打印历史（批量）
     */
    @RequestMapping(value = "/print", method = RequestMethod.DELETE)
    @ResponseBody
    @OperationLog(category = "标签管理", desc = "打印历史", type = OperationLogTypeEnum.DELETE)
    public CommonResult deletePrintHistory(@RequestParam List<Long> ids, @AuthenticationPrincipal UserDto userDto) {
        int count = templetService.deletePrintHistory(ids, userDto.getId());
        if (count > 0) {
            return CommonResult.success(null);
        }
        return CommonResult.failed("打印历史删除失败！");
    }

}
