package com.vida.xinye.x2.controller.admin;

import com.vida.xinye.x2.annotation.AdminRestController;
import com.vida.xinye.x2.api.CommonResult;
import com.vida.xinye.x2.dto.TempletBorderGroupDto;
import com.vida.xinye.x2.dto.param.TempletBorderGroupParam;
import com.vida.xinye.x2.service.TempletBorderGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@AdminRestController("adminTempletBorderGroupController")
@RequestMapping("/templet-border-group")
public class TempletBorderGroupController {
    @Autowired
    private TempletBorderGroupService borderGroupService;

    @PostMapping
    public CommonResult createGroup(@RequestBody @Valid TempletBorderGroupParam param) {
        Long id = borderGroupService.createGroup(param);
        return CommonResult.success(id);
    }

    @PutMapping("/{id}")
    public CommonResult<Void> updateGroup(
            @PathVariable Long id,
            @RequestBody @Valid TempletBorderGroupParam param) {
        borderGroupService.updateGroup(id, param);
        return CommonResult.success(null);
    }

    @DeleteMapping("/{id}")
    public CommonResult<Void> deleteGroup(@PathVariable Long id) {
        borderGroupService.deleteGroup(id);
        return CommonResult.success(null);
    }

    @GetMapping
    public CommonResult<List<TempletBorderGroupDto>> listAllGroups(@RequestParam(required = false) String locale) {
        return CommonResult.success(borderGroupService.listWithLocale(locale));
    }

    @PostMapping("/sort")
    public CommonResult<Void> sortGroups(@RequestBody List<Long> groupIds) {
        borderGroupService.sortGroups(groupIds);
        return CommonResult.success(null);
    }


}

