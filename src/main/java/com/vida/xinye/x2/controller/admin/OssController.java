package com.vida.xinye.x2.controller.admin;

import cn.hutool.core.util.StrUtil;
import com.vida.xinye.x2.annotation.AdminRestController;
import com.vida.xinye.x2.api.CommonResult;
import com.vida.xinye.x2.service.impl.OssServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Objects;


/**
 * Oss相关操作接口
 */
@AdminRestController("adminOssController")
@RequestMapping("/oss")
public class OssController {
    @Autowired
    private OssServiceImpl ossService;

    @RequestMapping(value = "/upload", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult upload(@RequestPart(value = "files") MultipartFile[] files,
                               @RequestPart(value = "prefix", required = false) String objectPrefix,
                               @RequestPart(value = "autoName", required = false) Boolean autoName) {
        if (Objects.isNull(autoName)) {
            autoName = false;
        }
        if (StrUtil.isEmpty(objectPrefix)) {
            objectPrefix = "";
        }

        List<String> uploadFileNames = ossService.upload(objectPrefix, files, autoName);
        if (uploadFileNames.size() == files.length) {
            return CommonResult.success(uploadFileNames);
        }
        return CommonResult.failed();
    }

}
