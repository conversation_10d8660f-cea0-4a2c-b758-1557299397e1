package com.vida.xinye.x2.controller.api;

import cn.hutool.core.util.StrUtil;
import com.vida.xinye.x2.api.CommonResult;
import com.vida.xinye.x2.config.QuestionCuttingConfig;
import com.vida.xinye.x2.util.YoudaoConfigValidator;
import com.vida.xinye.x2.util.YoudaoQuickTest;
import com.vida.xinye.x2.util.SimpleYoudaoTest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 智能切题诊断控制器
 *
 * <AUTHOR>
 * @date 2025/08/14
 */
@Slf4j
@RestController
@RequestMapping("/question/cutting/diagnostic")
public class QuestionCuttingDiagnosticController {

    @Autowired
    private QuestionCuttingConfig config;

    @Autowired
    private YoudaoConfigValidator youdaoConfigValidator;

    @Autowired
    private YoudaoQuickTest youdaoQuickTest;

    /**
     * 检查有道配置
     */
    @GetMapping("/youdao/config")
    public CommonResult<Map<String, Object>> checkYoudaoConfig() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            QuestionCuttingConfig.YoudaoConfig youdaoConfig = config.getYoudao();
            
            if (youdaoConfig == null) {
                result.put("status", "ERROR");
                result.put("message", "有道配置对象为空");
                result.put("solution", "请检查application.yml中的question.cutting.youdao配置");
                return CommonResult.success(result);
            }

            // 基本配置检查
            Map<String, Object> configCheck = new HashMap<>();
            configCheck.put("url", youdaoConfig.getUrl());
            configCheck.put("urlValid", StrUtil.isNotBlank(youdaoConfig.getUrl()) && youdaoConfig.getUrl().contains("openapi.youdao.com"));
            
            String appKey = youdaoConfig.getAppKey();
            configCheck.put("appKeyConfigured", StrUtil.isNotBlank(appKey));
            configCheck.put("appKeyLength", appKey != null ? appKey.length() : 0);
            configCheck.put("appKeyPreview", appKey != null && appKey.length() > 6 ? appKey.substring(0, 6) + "***" : "未配置");
            configCheck.put("appKeyIsExample", appKey != null && (appKey.contains("your_") || appKey.contains("test") || appKey.equals("your_youdao_app_key")));
            
            String appSecret = youdaoConfig.getAppSecret();
            configCheck.put("appSecretConfigured", StrUtil.isNotBlank(appSecret));
            configCheck.put("appSecretLength", appSecret != null ? appSecret.length() : 0);
            configCheck.put("appSecretPreview", appSecret != null && appSecret.length() > 6 ? appSecret.substring(0, 6) + "***" : "未配置");
            configCheck.put("appSecretIsExample", appSecret != null && (appSecret.contains("your_") || appSecret.contains("test") || appSecret.equals("your_youdao_app_secret")));
            
            configCheck.put("enabled", youdaoConfig.isEnabled());
            configCheck.put("timeout", youdaoConfig.getTimeout());
            configCheck.put("retryCount", youdaoConfig.getRetryCount());
            
            result.put("configCheck", configCheck);

            // 环境变量检查
            Map<String, Object> envCheck = new HashMap<>();
            String envAppKey = System.getenv("YOUDAO_APP_KEY");
            String envAppSecret = System.getenv("YOUDAO_APP_SECRET");
            
            envCheck.put("YOUDAO_APP_KEY_exists", envAppKey != null);
            envCheck.put("YOUDAO_APP_KEY_preview", envAppKey != null && envAppKey.length() > 6 ? envAppKey.substring(0, 6) + "***" : "未设置");
            envCheck.put("YOUDAO_APP_SECRET_exists", envAppSecret != null);
            envCheck.put("YOUDAO_APP_SECRET_preview", envAppSecret != null && envAppSecret.length() > 6 ? envAppSecret.substring(0, 6) + "***" : "未设置");
            
            result.put("environmentCheck", envCheck);

            // 配置验证
            boolean isValid = youdaoConfigValidator.validateYoudaoConfig(youdaoConfig);
            result.put("configValid", isValid);

            // 问题诊断
            Map<String, String> issues = new HashMap<>();
            if (!configCheck.get("urlValid").equals(true)) {
                issues.put("url", "URL配置不正确，应该是: https://openapi.youdao.com/ocrapi");
            }
            if (!configCheck.get("appKeyConfigured").equals(true)) {
                issues.put("appKey", "appKey未配置");
            } else if (configCheck.get("appKeyIsExample").equals(true)) {
                issues.put("appKey", "appKey使用的是示例值，请使用真实的应用ID");
            }
            if (!configCheck.get("appSecretConfigured").equals(true)) {
                issues.put("appSecret", "appSecret未配置");
            } else if (configCheck.get("appSecretIsExample").equals(true)) {
                issues.put("appSecret", "appSecret使用的是示例值，请使用真实的应用密钥");
            }
            if (!configCheck.get("enabled").equals(true)) {
                issues.put("enabled", "有道接口已禁用");
            }

            // 注意：如果配置文件中有默认值，环境变量未设置不算问题
            boolean hasDefaultValues = appKey != null && appSecret != null &&
                                     !appKey.contains("your_") && !appSecret.contains("your_");

            if (envAppKey == null && !hasDefaultValues) {
                issues.put("env_appkey", "环境变量YOUDAO_APP_KEY未设置且配置文件无默认值");
            }
            if (envAppSecret == null && !hasDefaultValues) {
                issues.put("env_appsecret", "环境变量YOUDAO_APP_SECRET未设置且配置文件无默认值");
            }

            // 添加配置来源说明
            Map<String, String> configSource = new HashMap<>();
            configSource.put("appKeySource", envAppKey != null ? "环境变量" : "配置文件默认值");
            configSource.put("appSecretSource", envAppSecret != null ? "环境变量" : "配置文件默认值");
            result.put("configSource", configSource);

            result.put("issues", issues);

            // 解决方案
            if (!issues.isEmpty()) {
                result.put("status", "ISSUES_FOUND");
                result.put("message", "发现配置问题，请查看issues字段");
                result.put("solutions", getSolutions());
            } else {
                result.put("status", "OK");
                result.put("message", "配置检查通过");
            }

            return CommonResult.success(result);

        } catch (Exception e) {
            log.error("配置检查失败", e);
            result.put("status", "ERROR");
            result.put("message", "配置检查失败: " + e.getMessage());
            return CommonResult.success(result);
        }
    }

    /**
     * 生成测试签名
     */
    @GetMapping("/youdao/test-sign")
    public CommonResult<Map<String, Object>> generateTestSign() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            QuestionCuttingConfig.YoudaoConfig youdaoConfig = config.getYoudao();
            
            if (youdaoConfig == null || StrUtil.isBlank(youdaoConfig.getAppKey()) || StrUtil.isBlank(youdaoConfig.getAppSecret())) {
                result.put("status", "ERROR");
                result.put("message", "有道配置不完整，无法生成测试签名");
                return CommonResult.success(result);
            }

            String testSign = youdaoConfigValidator.generateTestSign(youdaoConfig.getAppKey(), youdaoConfig.getAppSecret());
            
            result.put("status", "OK");
            result.put("appKey", youdaoConfig.getAppKey().substring(0, Math.min(6, youdaoConfig.getAppKey().length())) + "***");
            result.put("testSign", testSign);
            result.put("message", "测试签名生成成功");

            return CommonResult.success(result);

        } catch (Exception e) {
            log.error("生成测试签名失败", e);
            result.put("status", "ERROR");
            result.put("message", "生成测试签名失败: " + e.getMessage());
            return CommonResult.success(result);
        }
    }

    /**
     * 获取解决方案
     */
    private Map<String, String> getSolutions() {
        Map<String, String> solutions = new HashMap<>();
        solutions.put("step1", "设置环境变量: export YOUDAO_APP_KEY=\"你的应用ID\"");
        solutions.put("step2", "设置环境变量: export YOUDAO_APP_SECRET=\"你的应用密钥\"");
        solutions.put("step3", "重启应用使环境变量生效");
        solutions.put("step4", "登录有道智云控制台检查应用状态和余额");
        solutions.put("step5", "确认OCR服务已开通且IP在白名单中");
        return solutions;
    }

    /**
     * 打印调试信息
     */
    @GetMapping("/youdao/debug")
    public CommonResult<String> printDebugInfo() {
        try {
            QuestionCuttingConfig.YoudaoConfig youdaoConfig = config.getYoudao();
            youdaoConfigValidator.printDebugInfo(youdaoConfig);
            youdaoConfigValidator.checkCommonIssues(youdaoConfig);
            youdaoConfigValidator.provideSolutions();

            return CommonResult.success("调试信息已输出到日志，请查看应用日志");
        } catch (Exception e) {
            log.error("打印调试信息失败", e);
            return CommonResult.failed("打印调试信息失败: " + e.getMessage());
        }
    }

    /**
     * 测试有道接口连通性
     */
    @GetMapping("/youdao/test-connection")
    public CommonResult<Map<String, Object>> testYoudaoConnection() {
        Map<String, Object> result = new HashMap<>();

        try {
            QuestionCuttingConfig.YoudaoConfig youdaoConfig = config.getYoudao();

            if (youdaoConfig == null || !youdaoConfig.isEnabled()) {
                result.put("status", "ERROR");
                result.put("message", "有道配置未启用");
                return CommonResult.success(result);
            }

            // 检查基本配置
            if (StrUtil.isBlank(youdaoConfig.getAppKey()) || StrUtil.isBlank(youdaoConfig.getAppSecret())) {
                result.put("status", "ERROR");
                result.put("message", "有道密钥配置不完整");
                return CommonResult.success(result);
            }

            // 生成测试参数
            String appKey = youdaoConfig.getAppKey();
            String appSecret = youdaoConfig.getAppSecret();
            String salt = String.valueOf(System.currentTimeMillis());
            String curtime = String.valueOf(System.currentTimeMillis() / 1000);
            String input = ""; // OCR接口input为空

            // 生成签名
            String signStr = appKey + input + salt + curtime + appSecret;
            String sign = cn.hutool.crypto.digest.DigestUtil.md5Hex(signStr);

            result.put("status", "OK");
            result.put("message", "测试参数生成成功");

            // 创建测试参数Map（兼容Java 8）
            Map<String, Object> testParams = new HashMap<>();
            testParams.put("appKey", appKey.substring(0, Math.min(8, appKey.length())) + "***");
            testParams.put("salt", salt);
            testParams.put("curtime", curtime);
            testParams.put("sign", sign);
            testParams.put("type", "1");
            testParams.put("detectType", "10012");
            testParams.put("imageType", "1");
            result.put("testParams", testParams);

            result.put("curlCommand", String.format(
                "curl -X POST \"%s\" " +
                "-H \"Content-Type: application/x-www-form-urlencoded\" " +
                "-d \"appKey=%s\" " +
                "-d \"salt=%s\" " +
                "-d \"curtime=%s\" " +
                "-d \"sign=%s\" " +
                "-d \"type=1\" " +
                "-d \"detectType=10012\" " +
                "-d \"imageType=1\" " +
                "-d \"img=iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==\"",
                youdaoConfig.getUrl(), appKey, salt, curtime, sign
            ));

            return CommonResult.success(result);

        } catch (Exception e) {
            log.error("测试有道连接失败", e);
            result.put("status", "ERROR");
            result.put("message", "测试失败: " + e.getMessage());
            return CommonResult.success(result);
        }
    }

    /**
     * 快速测试有道接口
     */
    @GetMapping("/youdao/quick-test")
    public CommonResult<Map<String, Object>> quickTestYoudao() {
        try {
            Map<String, Object> result = youdaoQuickTest.quickTest();
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("快速测试失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("error", "快速测试失败: " + e.getMessage());
            return CommonResult.success(errorResult);
        }
    }

    /**
     * 生成curl测试命令
     */
    @GetMapping("/youdao/curl-command")
    public CommonResult<String> generateCurlCommand() {
        try {
            String curlCommand = youdaoQuickTest.generateCurlCommand();
            return CommonResult.success(curlCommand);
        } catch (Exception e) {
            log.error("生成curl命令失败", e);
            return CommonResult.failed("生成curl命令失败: " + e.getMessage());
        }
    }

    /**
     * 检查网络连通性
     */
    @GetMapping("/youdao/network-check")
    public CommonResult<Map<String, Object>> checkNetwork() {
        try {
            Map<String, Object> result = youdaoQuickTest.checkNetworkConnectivity();
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("网络检查失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("error", "网络检查失败: " + e.getMessage());
            return CommonResult.success(errorResult);
        }
    }

    /**
     * 最简化的有道接口测试（参考官方demo）
     */
    @GetMapping("/youdao/simple-test")
    public CommonResult<String> simpleTestYoudao() {
        try {
            QuestionCuttingConfig.YoudaoConfig youdaoConfig = config.getYoudao();

            if (youdaoConfig == null || !youdaoConfig.isEnabled()) {
                return CommonResult.failed("有道配置无效或已禁用");
            }

            String appKey = youdaoConfig.getAppKey();
            String appSecret = youdaoConfig.getAppSecret();

            if (StrUtil.isBlank(appKey) || StrUtil.isBlank(appSecret)) {
                return CommonResult.failed("有道密钥配置不完整");
            }

            // 执行简化测试
            SimpleYoudaoTest.testYoudaoOCR(appKey, appSecret);

            return CommonResult.success("简化测试已执行，请查看应用日志获取详细结果");
        } catch (Exception e) {
            log.error("简化测试失败", e);
            return CommonResult.failed("简化测试失败: " + e.getMessage());
        }
    }

    /**
     * 生成最简化的curl命令
     */
    @GetMapping("/youdao/simple-curl")
    public CommonResult<String> generateSimpleCurl() {
        try {
            QuestionCuttingConfig.YoudaoConfig youdaoConfig = config.getYoudao();

            if (youdaoConfig == null || !youdaoConfig.isEnabled()) {
                return CommonResult.failed("有道配置无效或已禁用");
            }

            String appKey = youdaoConfig.getAppKey();
            String appSecret = youdaoConfig.getAppSecret();

            if (StrUtil.isBlank(appKey) || StrUtil.isBlank(appSecret)) {
                return CommonResult.failed("有道密钥配置不完整");
            }

            String curlCommand = SimpleYoudaoTest.generateCurlCommand(appKey, appSecret);
            return CommonResult.success(curlCommand);
        } catch (Exception e) {
            log.error("生成简化curl命令失败", e);
            return CommonResult.failed("生成简化curl命令失败: " + e.getMessage());
        }
    }
}
