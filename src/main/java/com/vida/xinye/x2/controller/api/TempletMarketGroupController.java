package com.vida.xinye.x2.controller.api;

import com.vida.xinye.x2.annotation.ApiRestController;
import com.vida.xinye.x2.annotation.OperationLog;
import com.vida.xinye.x2.api.CommonResult;
import com.vida.xinye.x2.constant.OperationLogTypeEnum;
import com.vida.xinye.x2.dao.TempletGroupDao;
import com.vida.xinye.x2.dto.TempletMarketGroupDto;
import com.vida.xinye.x2.dto.param.TempletMarketGroupParam;
import com.vida.xinye.x2.service.TempletMarketGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@ApiRestController
@RequestMapping("/templet-market-group")
public class TempletMarketGroupController {

    @Autowired
    private TempletMarketGroupService groupService;
    @Resource
    private TempletGroupDao templetGroupDao;

    @GetMapping
    public CommonResult<List<TempletMarketGroupDto>> list(@RequestParam(required = false) String locale) {
        return CommonResult.success(groupService.listWithLocale(locale));
    }

    @DeleteMapping
    @OperationLog(category = "模板集市分组管理", desc = "删除模板集市分组", type = OperationLogTypeEnum.DELETE)
    public CommonResult<Integer> deleteGroup(@RequestParam Long groupId) {
        return CommonResult.success(groupService.deleteGroup(groupId));
    }


}

