package com.vida.xinye.x2.controller.api;

import com.vida.xinye.x2.annotation.ApiRestController;
import com.vida.xinye.x2.annotation.OperationLog;
import com.vida.xinye.x2.api.CommonPage;
import com.vida.xinye.x2.api.CommonResult;
import com.vida.xinye.x2.constant.OperationLogTypeEnum;
import com.vida.xinye.x2.dto.UserDto;
import com.vida.xinye.x2.dto.param.CloudfileParam;
import com.vida.xinye.x2.mbg.model.Cloudfile;
import com.vida.xinye.x2.service.CloudfileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Author：Chenjy
 * Date:2025/7/17
 * Description:
 */
@ApiRestController
@RequestMapping("/cloudfile")
public class CloudfileController {
    @Autowired
    private CloudfileService cloudfileService;

    // 文件列表查询
    @GetMapping
    @ResponseBody
    public CommonResult<CommonPage<Cloudfile>> pageList(
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "20") int pageSize,
            @AuthenticationPrincipal UserDto userDto) {
        // 调用service层获取分页列表

        CommonPage<Cloudfile> files = cloudfileService.list(userDto.getId(), pageSize, pageNum);
        return CommonResult.success(files);
    }

    // 文件上传（新增/修改）
    @PostMapping
    @ResponseBody
    @OperationLog(category = "云文件管理", desc = "上传文件", type = OperationLogTypeEnum.CREATE)
    public CommonResult uploadFile(
            @Validated @RequestBody CloudfileParam param,
            @AuthenticationPrincipal UserDto userDto) {

        if (userDto.getId() != null) {
            param.setUserId(userDto.getId());
        }
        int count = cloudfileService.upload(param);
        if (count > 0) {
            return CommonResult.success(count);
        }
        return CommonResult.failed("上传失败！");
    }

    // 检查文件名是否重名
    @GetMapping("/checkExists")
    @ResponseBody
    public CommonResult<Boolean> checkFilename(
            @RequestParam("filename") String filename,
            @AuthenticationPrincipal UserDto userDto) {
        if (userDto.getId() == null) {
            return CommonResult.failed("用户ID为空！");
        }
        boolean exists = cloudfileService.checkFilenameExists(filename, userDto.getId());
        return CommonResult.success(exists);
    }

    // 文件删除
    @DeleteMapping("/{id}")
    @ResponseBody
    @OperationLog(category = "云文件管理", desc = "删除文件", type = OperationLogTypeEnum.DELETE)
    public CommonResult deleteFile(@PathVariable Long id,
                                   @AuthenticationPrincipal UserDto userDto) {
        // 调用service层删除文件
        int count = cloudfileService.delete(id, userDto.getId());
        if (count > 0) {
            return CommonResult.success(count);
        }
        return CommonResult.failed("删除失败！");
    }

    @DeleteMapping
    @ResponseBody
    @OperationLog(category = "云文件管理", desc = "批量删除文件", type = OperationLogTypeEnum.DELETE)
    public CommonResult batchDelete(@RequestParam(value = "id") List<Long> id,
                                    @AuthenticationPrincipal UserDto userDto) {
        // 调用service层删除文件
        int count = cloudfileService.delete(id, userDto.getId());
        if (count > 0) {
            return CommonResult.success(count);
        }
        return CommonResult.failed("删除失败！");
    }
}
