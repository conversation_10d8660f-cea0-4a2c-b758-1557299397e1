package com.vida.xinye.x2.controller.api;

import com.vida.xinye.x2.annotation.ApiRestController;
import com.vida.xinye.x2.api.CommonResult;
import com.vida.xinye.x2.dto.HelpFaqDto;
import com.vida.xinye.x2.dto.HelpProductManualDto;
import com.vida.xinye.x2.dto.HelpTutorialDto;
import com.vida.xinye.x2.service.HelpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * 机器设备控制器
 *
 * <AUTHOR>
 * @date 2024/11/11
 */
@ApiRestController
@RequestMapping("/help")
public class HelpController {
    @Autowired
    private HelpService helpService;

    /**
     * 获取使用教程
     */
    @RequestMapping(value = "/tutorial", method = RequestMethod.GET)
    @ResponseBody
    public CommonResult<List<HelpTutorialDto>> list(@RequestParam(value = "locale", required = false) String locale) {
        List<HelpTutorialDto> result = helpService.tutorials(locale);
        return CommonResult.success(result);
    }

    /**
     * 获取产品说明书
     */
    @RequestMapping(value = "/productManual", method = RequestMethod.GET)
    @ResponseBody
    public CommonResult<List<HelpProductManualDto>> productManual(@RequestParam(value = "locale", required = false) String locale) {
        List<HelpProductManualDto> result = helpService.productManuals(locale);
        return CommonResult.success(result);
    }

    /**
     * 获取常见问题
     */
    @RequestMapping(value = "/faq", method = RequestMethod.GET)
    @ResponseBody
    public CommonResult<List<HelpFaqDto>> faq(@RequestParam(value = "locale", required = false) String locale) {
        List<HelpFaqDto> result = helpService.faqs(locale);
        return CommonResult.success(result);
    }


}
