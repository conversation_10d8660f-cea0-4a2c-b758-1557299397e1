package com.vida.xinye.x2.controller.api;

import com.vida.xinye.x2.annotation.ApiRestController;
import com.vida.xinye.x2.api.CommonResult;
import com.vida.xinye.x2.dto.TempletBorderGroupDto;
import com.vida.xinye.x2.service.TempletBorderGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@ApiRestController
@RequestMapping("/templet-border-group")
public class TempletBorderGroupController {
    @Autowired
    private TempletBorderGroupService borderGroupService;

    @GetMapping
    public CommonResult<List<TempletBorderGroupDto>> listAllGroups(@RequestParam(required = false) String locale) {
        return CommonResult.success(borderGroupService.listWithLocale(locale));
    }

}
