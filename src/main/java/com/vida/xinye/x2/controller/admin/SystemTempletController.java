package com.vida.xinye.x2.controller.admin;

import com.vida.xinye.x2.annotation.AdminRestController;
import com.vida.xinye.x2.api.CommonPage;
import com.vida.xinye.x2.api.CommonResult;
import com.vida.xinye.x2.domain.param.SysTempletParam;
import com.vida.xinye.x2.dto.SystemTempletDto;
import com.vida.xinye.x2.service.SysTempletService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@AdminRestController("adminSystemTempletController")
@RequestMapping("/system/templet")
public class SystemTempletController {

    @Autowired
    private SysTempletService sysTempletService;

    /**
     * 系统标签分页列表
     */
    @GetMapping
    public CommonResult<CommonPage<SystemTempletDto>> list(
            @RequestParam(value = "groupId", required = false) Long groupId,
            @RequestParam(defaultValue = "8") Integer pageSize,
            @RequestParam(defaultValue = "1") Integer pageNum) {
        return CommonResult.success(sysTempletService.listSystemTemples(groupId, pageSize, pageNum));
    }

    /**
     * 新增系统标签（需要管理员权限）
     */
    @PostMapping
    public CommonResult create(@Validated @RequestBody SysTempletParam param) {
        return CommonResult.success(sysTempletService.createSystemTemplet(param));
    }

    /**
     * 更新系统标签（需要管理员权限）
     */
    @PutMapping("/{id}")
    public CommonResult update(@PathVariable Long id, @RequestBody SysTempletParam param) {
        return sysTempletService.updateSystemTemplet(id, param) > 0 ?
                CommonResult.success(null, "更新成功") : CommonResult.failed();
    }

    /**
     * 删除系统标签（需要管理员权限）
     */
    @DeleteMapping("/{id}")
    public CommonResult delete(@PathVariable Long id) {
        return sysTempletService.deleteSystemTemplet(id) > 0 ?
                CommonResult.success(null, "删除成功") : CommonResult.failed();
    }

}
