package com.vida.xinye.x2.controller.api;

import com.vida.xinye.x2.annotation.AdminRestController;
import com.vida.xinye.x2.annotation.ApiRestController;
import com.vida.xinye.x2.api.CommonResult;
import com.vida.xinye.x2.domain.param.MaterialCategoryParam;
import com.vida.xinye.x2.mbg.model.MaterialCategory;
import com.vida.xinye.x2.service.MaterialCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 素材控制器
 *
 * <AUTHOR>
 * @date 2024/11/11
 */
@ApiRestController
@RequestMapping("/materialCategory")
public class MaterialCategoryController {
    @Autowired
    private MaterialCategoryService materialCategoryService;

    /**
     * 获取素材类别（不分页）
     */
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    @ResponseBody
    public CommonResult<List<MaterialCategory>> list() {
        List<MaterialCategory> result = materialCategoryService.list();
        return CommonResult.success(result);
    }

    /**
     * 新增素材类别
     */
    @PostMapping
    @ResponseBody
    public CommonResult create(@Validated @RequestBody MaterialCategoryParam param) {
        Long id = materialCategoryService.create(param);
        if (id > 0) {
            return CommonResult.success(id, "新增类别成功!");
        }
        return CommonResult.failed();
    }

    /**
     * 修改素材类别
     */
    @RequestMapping(value = "/{id}", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult update(@PathVariable Long id, @RequestBody MaterialCategoryParam param) {
        int count = materialCategoryService.update(id, param);
        if (count > 0) {
            return CommonResult.success(null);
        }
        return CommonResult.failed();
    }

    /**
     * 删除素材类别
     *
     * @param id
     * @return
     */
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @ResponseBody
    public CommonResult delete(@PathVariable Long id) {
        int count = materialCategoryService.delete(id);
        if (count > 0) {
            return CommonResult.success(null);
        }
        return CommonResult.failed("数据不存在或已被删除！");
    }

}
