package com.vida.xinye.x2.controller.api;

import com.vida.xinye.x2.annotation.ApiRestController;
import com.vida.xinye.x2.api.CommonPage;
import com.vida.xinye.x2.api.CommonResult;
import com.vida.xinye.x2.dto.MaterialDto;
import com.vida.xinye.x2.dto.UserDto;
import com.vida.xinye.x2.mbg.model.Material;
import com.vida.xinye.x2.service.MaterialService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * 素材控制器
 *
 * <AUTHOR>
 * @date 2024/11/11
 */
@ApiRestController
@RequestMapping("/material")
public class MaterialController {
    @Autowired
    private MaterialService materialService;

    /**
     * 获取素材库列表（分页查询）
     */
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    @ResponseBody
    public CommonResult<CommonPage<MaterialDto>> list(@RequestParam(value = "pageSize", defaultValue = "8") Integer pageSize,
                                                      @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                      @RequestParam(value = "categoryId", required = false) Long categoryId,
                                                      @AuthenticationPrincipal UserDto userDto) {

        Long userId = userDto != null ? userDto.getId() : null;
        CommonPage<MaterialDto> result = materialService.list(pageSize, pageNum, userId, categoryId);
        return CommonResult.success(result);
    }

    /**
     * 获取我的收藏列表
     */
    @RequestMapping(value = "/favorite", method = RequestMethod.GET)
    @ResponseBody
    public CommonResult favorite(@RequestParam(value = "pageSize", defaultValue = "8") Integer pageSize,
                                 @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                 @AuthenticationPrincipal UserDto userDto) {

        CommonPage<Material> result = materialService.favorite(pageSize, pageNum, userDto.getId());
        return CommonResult.success(result);
    }

    /**
     * 收藏素材
     */
    @RequestMapping(value = "/addFavorite", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult addFavorite(@RequestParam Long materialId, @AuthenticationPrincipal UserDto userDto) {

        boolean success = materialService.addFavorite(materialId, userDto.getId());
        if (success) {
            return CommonResult.success("素材收藏成功");
        }
        return CommonResult.failed("素材收藏失败！");
    }

    /**
     * 取消收藏素材
     */
    @RequestMapping(value = "/removeFavorite", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult removeFavorite(@RequestParam List<Long> materialId, @AuthenticationPrincipal UserDto userDto) {

        boolean success = materialService.removeFavorite(materialId, userDto.getId());
        if (success) {
            return CommonResult.success("取消收藏素材成功");
        }
        return CommonResult.failed("取消收藏素材失败！");
    }


}
