package com.vida.xinye.x2.controller.admin;


import com.vida.xinye.x2.annotation.AdminRestController;
import com.vida.xinye.x2.api.CommonResult;
import com.vida.xinye.x2.dto.TempletBorderDto;
import com.vida.xinye.x2.service.TempletBorderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@AdminRestController("adminTempletBorderController")
@RequestMapping("/templet-border")
public class TempletBorderController {

    @Autowired
    private TempletBorderService templetBorderService;

    /**
     * 标签边框分页列表
     */
    @GetMapping
    public CommonResult<List<TempletBorderDto>> list(
            @RequestParam(value = "groupId", required = false) Long groupId) {
        return CommonResult.success(templetBorderService.list(groupId));
    }

    /**
     * 新增标签边框（需要管理员权限）
     */
    @PostMapping
    public CommonResult create(@Validated @RequestBody TempletBorderDto param) {
        return CommonResult.success(templetBorderService.create(param));
    }

    /**
     * 更新标签边框（需要管理员权限）
     */
    @PutMapping("/{id}")
    public CommonResult update(@PathVariable Long id, @RequestBody TempletBorderDto param) {
        return templetBorderService.update(id, param) > 0 ?
                CommonResult.success(null, "更新成功") : CommonResult.failed();
    }

    /**
     * 删除标签边框（需要管理员权限）
     */
    @DeleteMapping("/{id}")
    public CommonResult delete(@PathVariable Long id) {
        return templetBorderService.delete(id) > 0 ?
                CommonResult.success(null, "删除成功") : CommonResult.failed();
    }
}
