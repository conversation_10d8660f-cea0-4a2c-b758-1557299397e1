package com.vida.xinye.x2.controller.api.edu;

import cn.hutool.core.util.ObjectUtil;
import com.vida.xinye.x2.annotation.ApiRestController;
import com.vida.xinye.x2.api.CommonResult;
import com.vida.xinye.x2.domain.param.WrongQuestionTagParam;
import com.vida.xinye.x2.dto.UserDto;
import com.vida.xinye.x2.mbg.model.WrongQuestionTag;
import com.vida.xinye.x2.mbg.model.WrongQuestionTagType;
import com.vida.xinye.x2.service.api.WrongQuestionTagService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 错题标签控制器
 *
 * <AUTHOR>
 * @date 2025/02/20
 */
@ApiRestController
@RequestMapping("/wrongQuestionTag")
public class WrongQuestionTagController {
    @Autowired
    private WrongQuestionTagService tagService;

    // 查询错题标签类型
    @GetMapping("/types")
    public CommonResult<List<WrongQuestionTagType>> findAllTagTypes() {
        List<WrongQuestionTagType> result = tagService.findAllTagTypes();
        return CommonResult.success(result);
    }

    // 根据错题标签类型，返回该类型下的默认标签 + 用户添加的标签
    @GetMapping
    public CommonResult<List<WrongQuestionTag>> findTagsByType(@RequestParam(value = "typeId", required = false) Long typeId,
                                                               @AuthenticationPrincipal UserDto userDto) {
        // 调试用
        if (ObjectUtil.isNull(userDto)) {
            userDto = new UserDto();
            userDto.setId(1L);
        }
        List<WrongQuestionTag> result = tagService.findTagsByTypeId(typeId,  userDto.getId());
        return CommonResult.success(result);
    }

    // 用户可新增标签到指定标签类型下
    @PostMapping
    public CommonResult create(@Validated @RequestBody WrongQuestionTagParam param, @AuthenticationPrincipal UserDto userDto) {
        // 调试用
        if (ObjectUtil.isNull(userDto)) {
            userDto = new UserDto();
            userDto.setId(1L);
        }
        Long id = tagService.create(param.getName(), param.getTypeId(), userDto.getId());
        if (id > 0) {
            return CommonResult.success(id);
        }
        return CommonResult.failed();
    }

    // 用户可删除自己新增的标签
    @DeleteMapping("/{id}")
    public CommonResult delete(@PathVariable Long id, @AuthenticationPrincipal UserDto userDto) {
        // 调试用
        if (ObjectUtil.isNull(userDto)) {
            userDto = new UserDto();
            userDto.setId(1L);
        }
        int count = tagService.delete(id, userDto.getId());
        if (count > 0) {
            return CommonResult.success(null);
        }
        return CommonResult.failed();
    }

}
