package com.vida.xinye.x2.controller.api;

import com.vida.xinye.x2.annotation.ApiRestController;
import com.vida.xinye.x2.annotation.OperationLog;
import com.vida.xinye.x2.api.CommonPage;
import com.vida.xinye.x2.api.CommonResult;
import com.vida.xinye.x2.constant.OperationLogTypeEnum;
import com.vida.xinye.x2.dto.TempletMarketDto;
import com.vida.xinye.x2.service.TempletMarketService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@ApiRestController
@RequestMapping("/templet-market")
public class TempletMarketController {

    @Autowired
    private TempletMarketService templetMarketService;

    /**
     * 系统标签分页列表
     */
    @GetMapping
    public CommonResult<CommonPage<TempletMarketDto>> list(
            @RequestParam(value = "groupId", required = false) Long groupId,
            @RequestParam(defaultValue = "8") Integer pageSize,
            @RequestParam(defaultValue = "1") Integer pageNum) {
        return CommonResult.success(templetMarketService.list(pageSize, pageNum, groupId));
    }

    @DeleteMapping
    @OperationLog(category = "模板集市管理", desc = "删除模板集市", type = OperationLogTypeEnum.DELETE)
    public CommonResult<Integer> delete(@RequestParam("id") Long id) {
        return CommonResult.success(templetMarketService.delete(id));
    }

}
