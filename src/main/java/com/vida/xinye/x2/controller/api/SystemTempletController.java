package com.vida.xinye.x2.controller.api;

import com.vida.xinye.x2.annotation.ApiRestController;
import com.vida.xinye.x2.api.CommonPage;
import com.vida.xinye.x2.api.CommonResult;
import com.vida.xinye.x2.dto.SystemTempletDto;
import com.vida.xinye.x2.service.SysTempletService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@ApiRestController
@RequestMapping("/system/templet")
public class SystemTempletController {

    @Autowired
    private SysTempletService templetService;

    /**
     * 系统标签分页列表
     */
    @GetMapping
    public CommonResult<CommonPage<SystemTempletDto>> list(
            @RequestParam(value = "groupId") Long groupId,
            @RequestParam(defaultValue = "8") Integer pageSize,
            @RequestParam(defaultValue = "1") Integer pageNum) {
        return CommonResult.success(templetService.listSystemTemples(groupId, pageSize, pageNum));
    }

}
