package com.vida.xinye.x2.controller.api;

import com.vida.xinye.x2.annotation.ApiRestController;
import com.vida.xinye.x2.api.CommonPage;
import com.vida.xinye.x2.api.CommonResult;
import com.vida.xinye.x2.dto.MaterialDto;
import com.vida.xinye.x2.dto.UserDto;
import com.vida.xinye.x2.mbg.model.Machine;
import com.vida.xinye.x2.mbg.model.Material;
import com.vida.xinye.x2.service.MachineService;
import com.vida.xinye.x2.service.MaterialService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * 机器设备控制器
 *
 * <AUTHOR>
 * @date 2024/11/11
 */
@ApiRestController
@RequestMapping("/machine")
public class MachineController {
    @Autowired
    private MachineService machineService;

    /**
     * 获取素材库列表（分页查询）
     */
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    @ResponseBody
    public CommonResult<List<Machine>> list(@RequestParam(value = "machineName", required = false) String machineName) {
        List<Machine> result = machineService.list(machineName);
        return CommonResult.success(result);
    }

}
