package com.vida.xinye.x2.controller.api;

import com.vida.xinye.x2.annotation.ApiRestController;
import com.vida.xinye.x2.api.CommonResult;
import com.vida.xinye.x2.mbg.model.Font;
import com.vida.xinye.x2.mbg.model.Machine;
import com.vida.xinye.x2.service.FontService;
import com.vida.xinye.x2.service.MachineService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * 字体控制器
 *
 * <AUTHOR>
 * @date 2025/02/07
 */
@ApiRestController
@RequestMapping("/font")
public class FontController {
    @Autowired
    private FontService fontService;

    /**
     * 获取字体类别
     */
    @RequestMapping(value = "/kind", method = RequestMethod.GET)
    @ResponseBody
    public CommonResult<List<String>> list() {
        List<String> result = fontService.kindList();
        return CommonResult.success(result);
    }

    /**
     * 获取素材库列表（分页查询）
     */
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    @ResponseBody
    public CommonResult<List<Font>> list(@RequestParam(value = "kind", required = false) String kind) {
        List<Font> result = fontService.list(kind);
        return CommonResult.success(result);
    }

}
