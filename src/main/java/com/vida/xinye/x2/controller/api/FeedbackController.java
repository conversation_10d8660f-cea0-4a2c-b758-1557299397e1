package com.vida.xinye.x2.controller.api;

import com.vida.xinye.x2.annotation.ApiRestController;
import com.vida.xinye.x2.annotation.OperationLog;
import com.vida.xinye.x2.api.CommonPage;
import com.vida.xinye.x2.api.CommonResult;
import com.vida.xinye.x2.constant.OperationLogTypeEnum;
import com.vida.xinye.x2.domain.param.FeedbackParam;
import com.vida.xinye.x2.domain.param.TempletParam;
import com.vida.xinye.x2.domain.param.TempletPrintParam;
import com.vida.xinye.x2.dto.TempletDto;
import com.vida.xinye.x2.dto.UserDto;
import com.vida.xinye.x2.service.FeedbackService;
import com.vida.xinye.x2.service.TempletService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 标签控制器
 *
 * <AUTHOR>
 * @date 2024/11/11
 */
@ApiRestController
@RequestMapping("/feedback")
public class FeedbackController {
    @Autowired
    private FeedbackService feedbackService;

    /**
     * 新增标签
     */
    @PostMapping
    @ResponseBody
    @OperationLog(category = "我的", subcategory = "意见反馈", desc = "新增意见反馈", type = OperationLogTypeEnum.CREATE)
    public CommonResult create(@Validated @RequestBody FeedbackParam param, @AuthenticationPrincipal UserDto userDto) {
        param.setUserId(userDto == null ? null : userDto.getId());
        Long id = feedbackService.create(param);
        if (id > 0) {
            return CommonResult.success(id);
        }
        return CommonResult.failed();
    }

}
