package com.vida.xinye.x2.controller.admin;

import com.vida.xinye.x2.annotation.AdminRestController;
import com.vida.xinye.x2.api.CommonResult;
import com.vida.xinye.x2.dao.TempletGroupDao;
import com.vida.xinye.x2.dto.TempletMarketGroupDto;
import com.vida.xinye.x2.dto.param.TempletMarketGroupParam;
import com.vida.xinye.x2.service.TempletMarketGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@AdminRestController("adminTempletMarketGroupController")
@RequestMapping("/templet-market-group")
public class TempletMarketGroupController {

    @Autowired
    private TempletMarketGroupService groupService;
    @Resource
    private TempletGroupDao templetGroupDao;

    @GetMapping
    public CommonResult<List<TempletMarketGroupDto>> list(@RequestParam(required = false) String locale) {
        return CommonResult.success(groupService.listWithLocale(locale));
    }

    @PostMapping
    public CommonResult<Long> create(@Validated @RequestBody TempletMarketGroupParam param) {
        return CommonResult.success(groupService.createGroup(param));
    }

    @PutMapping("/{id}")
    public CommonResult update(@PathVariable Long id, @Validated @RequestBody TempletMarketGroupParam param) {
        return groupService.updateGroup(id, param) > 0 ?
                CommonResult.success(null, "修改成功") : CommonResult.failed();
    }

    @DeleteMapping("/{id}")
    public CommonResult delete(@PathVariable Long id) {
        return groupService.deleteGroup(id) > 0 ?
                CommonResult.success(null, "删除成功") : CommonResult.failed();
    }

    @PostMapping("/sort")
    public CommonResult sort(@RequestBody List<Long> groupIds) {
        return groupService.sortGroups(groupIds) > 0 ?
                CommonResult.success(null, "排序成功") : CommonResult.failed();
    }
}

