package com.vida.xinye.x2.controller.api;

import com.vida.xinye.x2.annotation.ApiRestController;
import com.vida.xinye.x2.annotation.OperationLog;
import com.vida.xinye.x2.annotation.RateLimit;
import com.vida.xinye.x2.api.CommonResult;
import com.vida.xinye.x2.constant.OperationLogTypeEnum;
import com.vida.xinye.x2.dto.AccountBindingDto;
import com.vida.xinye.x2.dto.AccountBindingStatusDto;
import com.vida.xinye.x2.dto.UserDto;
import com.vida.xinye.x2.dto.param.BindEmailDto;
import com.vida.xinye.x2.dto.param.BindPhoneDto;
import com.vida.xinye.x2.dto.param.UnbindAccountDto;
import com.vida.xinye.x2.service.AccountBindingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 账号绑定控制器
 *
 * <AUTHOR>
 * @date 2025/08/14
 */
@Slf4j
@ApiRestController
@RequestMapping("/account/binding")
public class AccountBindingController {

    @Autowired
    private AccountBindingService accountBindingService;

    /**
     * 检查账号绑定状态
     */
    @OperationLog(category = "账号绑定", subcategory = "绑定状态", desc = "检查账号绑定状态", type = OperationLogTypeEnum.GET)
    @GetMapping("/check")
    public CommonResult<AccountBindingStatusDto> checkBindingStatus(@AuthenticationPrincipal UserDto user) {
        try {
            log.info("检查账号绑定状态 - 用户ID: {}", user.getId());
            
            AccountBindingStatusDto status = accountBindingService.checkBindingStatus(user.getId());
            return CommonResult.success(status, "获取绑定状态成功");
            
        } catch (Exception e) {
            log.error("检查账号绑定状态失败 - 用户ID: {}", user.getId(), e);
            return CommonResult.failed(e.getMessage());
        }
    }

    /**
     * 获取用户绑定的账号列表
     */
    @OperationLog(category = "账号绑定", subcategory = "绑定列表", desc = "获取用户绑定账号列表", type = OperationLogTypeEnum.GET)
    @GetMapping("/list")
    public CommonResult<List<AccountBindingDto>> getUserBindings(@AuthenticationPrincipal UserDto user) {
        try {
            log.info("获取用户绑定账号列表 - 用户ID: {}", user.getId());
            
            List<AccountBindingDto> bindings = accountBindingService.getUserBindings(user.getId());
            return CommonResult.success(bindings, "获取绑定列表成功");
            
        } catch (Exception e) {
            log.error("获取用户绑定账号列表失败 - 用户ID: {}", user.getId(), e);
            return CommonResult.failed(e.getMessage());
        }
    }

    /**
     * 绑定手机号
     */
    @OperationLog(category = "账号绑定", subcategory = "手机号绑定", desc = "绑定手机号", type = OperationLogTypeEnum.CREATE)
    @RateLimit(key = "bind_phone", time = 1, timeUnit = TimeUnit.MINUTES, count = 3, limitType = RateLimit.LimitType.USER)
    @PostMapping("/phone")
    public CommonResult<Void> bindPhone(@Validated @RequestBody BindPhoneDto bindPhoneDto,
                                       @AuthenticationPrincipal UserDto user) {
        try {
            log.info("绑定手机号请求 - 用户ID: {}, 手机号: {}", user.getId(), maskPhone(bindPhoneDto.getPhone()));
            
            boolean success = accountBindingService.bindPhone(user.getId(), bindPhoneDto);
            if (success) {
                return CommonResult.success(null, "手机号绑定成功");
            } else {
                return CommonResult.failed("手机号绑定失败");
            }
            
        } catch (Exception e) {
            log.error("绑定手机号失败 - 用户ID: {}, 手机号: {}", user.getId(), maskPhone(bindPhoneDto.getPhone()), e);
            return CommonResult.failed(e.getMessage());
        }
    }

    /**
     * 绑定邮箱
     */
    @OperationLog(category = "账号绑定", subcategory = "邮箱绑定", desc = "绑定邮箱", type = OperationLogTypeEnum.CREATE)
    @RateLimit(key = "bind_email", time = 1, timeUnit = TimeUnit.MINUTES, count = 3, limitType = RateLimit.LimitType.USER)
    @PostMapping("/email")
    public CommonResult<Void> bindEmail(@Validated @RequestBody BindEmailDto bindEmailDto,
                                       @AuthenticationPrincipal UserDto user) {
        try {
            log.info("绑定邮箱请求 - 用户ID: {}, 邮箱: {}", user.getId(), maskEmail(bindEmailDto.getEmail()));
            
            boolean success = accountBindingService.bindEmail(user.getId(), bindEmailDto);
            if (success) {
                return CommonResult.success(null, "邮箱绑定成功");
            } else {
                return CommonResult.failed("邮箱绑定失败");
            }
            
        } catch (Exception e) {
            log.error("绑定邮箱失败 - 用户ID: {}, 邮箱: {}", user.getId(), maskEmail(bindEmailDto.getEmail()), e);
            return CommonResult.failed(e.getMessage());
        }
    }

    /**
     * 解绑账号
     */
    @OperationLog(category = "账号绑定", subcategory = "账号解绑", desc = "解绑账号", type = OperationLogTypeEnum.DELETE)
    @RateLimit(key = "unbind_account", time = 1, timeUnit = TimeUnit.MINUTES, count = 3, limitType = RateLimit.LimitType.USER)
    @PostMapping("/unbind")
    public CommonResult<Void> unbindAccount(@Validated @RequestBody UnbindAccountDto unbindAccountDto,
                                           @AuthenticationPrincipal UserDto user) {
        try {
            log.info("解绑账号请求 - 用户ID: {}, 绑定类型: {}", user.getId(), unbindAccountDto.getBindingType());
            
            boolean success = accountBindingService.unbindAccount(user.getId(), unbindAccountDto);
            if (success) {
                return CommonResult.success(null, "账号解绑成功");
            } else {
                return CommonResult.failed("账号解绑失败");
            }
            
        } catch (Exception e) {
            log.error("解绑账号失败 - 用户ID: {}, 绑定类型: {}", user.getId(), unbindAccountDto.getBindingType(), e);
            return CommonResult.failed(e.getMessage());
        }
    }

    /**
     * 检查用户是否需要绑定手机号
     */
    @OperationLog(category = "账号绑定", subcategory = "绑定检查", desc = "检查是否需要绑定手机号", type = OperationLogTypeEnum.GET)
    @GetMapping("/phone/required")
    public CommonResult<Boolean> checkPhoneBindingRequired(@AuthenticationPrincipal UserDto user) {
        try {
            log.info("检查是否需要绑定手机号 - 用户ID: {}", user.getId());
            
            boolean required = accountBindingService.requiresPhoneBinding(user.getId());
            return CommonResult.success(required, "检查完成");
            
        } catch (Exception e) {
            log.error("检查是否需要绑定手机号失败 - 用户ID: {}", user.getId(), e);
            return CommonResult.failed(e.getMessage());
        }
    }

    /**
     * 检查账号是否已被绑定
     */
    @OperationLog(category = "账号绑定", subcategory = "绑定检查", desc = "检查账号是否已被绑定", type = OperationLogTypeEnum.GET)
    @GetMapping("/check-bound")
    public CommonResult<Boolean> checkAccountBound(@RequestParam Integer accountType,
                                                   @RequestParam String accountIdentifier) {
        try {
            log.info("检查账号是否已被绑定 - 账号类型: {}, 账号标识: {}", accountType, maskAccount(accountIdentifier));
            
            boolean bound = accountBindingService.isAccountBound(accountType, accountIdentifier);
            return CommonResult.success(bound, "检查完成");
            
        } catch (Exception e) {
            log.error("检查账号是否已被绑定失败 - 账号类型: {}, 账号标识: {}", accountType, maskAccount(accountIdentifier), e);
            return CommonResult.failed(e.getMessage());
        }
    }

    /**
     * 设置主账号
     */
    @OperationLog(category = "账号绑定", subcategory = "主账号设置", desc = "设置主账号", type = OperationLogTypeEnum.UPDATE)
    @RateLimit(key = "set_primary", time = 1, timeUnit = TimeUnit.MINUTES, count = 5, limitType = RateLimit.LimitType.USER)
    @PostMapping("/primary")
    public CommonResult<Void> setPrimaryAccount(@RequestParam Integer accountType,
                                               @RequestParam String accountIdentifier,
                                               @AuthenticationPrincipal UserDto user) {
        try {
            log.info("设置主账号 - 用户ID: {}, 账号类型: {}, 账号标识: {}", 
                    user.getId(), accountType, maskAccount(accountIdentifier));
            
            boolean success = accountBindingService.setPrimaryAccount(user.getId(), accountType, accountIdentifier);
            if (success) {
                return CommonResult.success(null, "主账号设置成功");
            } else {
                return CommonResult.failed("主账号设置失败");
            }
            
        } catch (Exception e) {
            log.error("设置主账号失败 - 用户ID: {}, 账号类型: {}, 账号标识: {}", 
                    user.getId(), accountType, maskAccount(accountIdentifier), e);
            return CommonResult.failed(e.getMessage());
        }
    }

    // 私有辅助方法

    /**
     * 手机号脱敏
     */
    private String maskPhone(String phone) {
        if (phone == null || phone.length() < 7) {
            return phone;
        }
        return phone.substring(0, 3) + "****" + phone.substring(7);
    }

    /**
     * 邮箱脱敏
     */
    private String maskEmail(String email) {
        if (email == null || !email.contains("@")) {
            return email;
        }
        int atIndex = email.indexOf('@');
        if (atIndex > 1) {
            return email.charAt(0) + "***" + email.substring(atIndex);
        }
        return email;
    }

    /**
     * 通用账号脱敏
     */
    private String maskAccount(String account) {
        if (account == null || account.length() <= 3) {
            return account;
        }
        return account.substring(0, 2) + "***" + account.substring(account.length() - 1);
    }
}
