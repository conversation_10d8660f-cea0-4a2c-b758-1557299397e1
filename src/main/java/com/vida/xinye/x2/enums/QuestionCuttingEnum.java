package com.vida.xinye.x2.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 智能切题相关枚举
 *
 * <AUTHOR>
 * @date 2025/08/14
 */
public class QuestionCuttingEnum {

    /**
     * 第三方接口提供商枚举
     */
    @Getter
    @AllArgsConstructor
    public enum Provider {
        TIANRANG("tianrang", "天壤", "天壤智能切题接口"),
        YOUDAO("youdao", "有道", "有道智云切题接口"),
        ALIYUN("aliyun", "阿里云", "阿里云OCR切题接口");

        private final String code;
        private final String name;
        private final String description;

        public static Provider fromCode(String code) {
            for (Provider provider : values()) {
                if (provider.getCode().equals(code)) {
                    return provider;
                }
            }
            throw new IllegalArgumentException("Unknown provider code: " + code);
        }
    }

    /**
     * 切题策略枚举
     */
    @Getter
    @AllArgsConstructor
    public enum Strategy {
        BEST_QUALITY("BEST_QUALITY", "最佳质量", "选择识别质量最好的结果"),
        FASTEST("FASTEST", "最快速度", "选择响应最快的接口"),
        AGGREGATED("AGGREGATED", "聚合结果", "综合多个接口的结果"),
        SPECIFIED("SPECIFIED", "指定提供商", "使用指定的接口"),
        ROUND_ROBIN("ROUND_ROBIN", "轮询", "轮流使用不同接口");

        private final String code;
        private final String name;
        private final String description;

        public static Strategy fromCode(String code) {
            for (Strategy strategy : values()) {
                if (strategy.getCode().equals(code)) {
                    return strategy;
                }
            }
            throw new IllegalArgumentException("Unknown strategy code: " + code);
        }
    }

    /**
     * 题目类型枚举
     */
    @Getter
    @AllArgsConstructor
    public enum QuestionType {
        CHOICE("choice", "选择题", "单选题或多选题"),
        FILL_BLANK("fill_blank", "填空题", "填空题"),
        ANSWER("answer", "解答题", "主观题或解答题"),
        JUDGE("judge", "判断题", "判断题"),
        CALCULATION("calculation", "计算题", "计算题"),
        OTHER("other", "其他", "其他类型题目");

        private final String code;
        private final String name;
        private final String description;

        public static QuestionType fromCode(String code) {
            for (QuestionType type : values()) {
                if (type.getCode().equals(code)) {
                    return type;
                }
            }
            return OTHER; // 默认返回其他类型
        }
    }

    /**
     * 学科枚举
     */
    @Getter
    @AllArgsConstructor
    public enum Subject {
        MATH("math", "数学", "数学学科"),
        CHINESE("chinese", "语文", "语文学科"),
        ENGLISH("english", "英语", "英语学科"),
        PHYSICS("physics", "物理", "物理学科"),
        CHEMISTRY("chemistry", "化学", "化学学科"),
        BIOLOGY("biology", "生物", "生物学科"),
        HISTORY("history", "历史", "历史学科"),
        GEOGRAPHY("geography", "地理", "地理学科"),
        POLITICS("politics", "政治", "政治学科");

        private final String code;
        private final String name;
        private final String description;

        public static Subject fromCode(String code) {
            for (Subject subject : values()) {
                if (subject.getCode().equals(code)) {
                    return subject;
                }
            }
            throw new IllegalArgumentException("Unknown subject code: " + code);
        }
    }

    /**
     * 年级枚举
     */
    @Getter
    @AllArgsConstructor
    public enum Grade {
        PRIMARY_1("primary_1", "小学一年级", "小学一年级"),
        PRIMARY_2("primary_2", "小学二年级", "小学二年级"),
        PRIMARY_3("primary_3", "小学三年级", "小学三年级"),
        PRIMARY_4("primary_4", "小学四年级", "小学四年级"),
        PRIMARY_5("primary_5", "小学五年级", "小学五年级"),
        PRIMARY_6("primary_6", "小学六年级", "小学六年级"),
        JUNIOR_1("junior_1", "初中一年级", "初中一年级"),
        JUNIOR_2("junior_2", "初中二年级", "初中二年级"),
        JUNIOR_3("junior_3", "初中三年级", "初中三年级"),
        SENIOR_1("senior_1", "高中一年级", "高中一年级"),
        SENIOR_2("senior_2", "高中二年级", "高中二年级"),
        SENIOR_3("senior_3", "高中三年级", "高中三年级");

        private final String code;
        private final String name;
        private final String description;

        public static Grade fromCode(String code) {
            for (Grade grade : values()) {
                if (grade.getCode().equals(code)) {
                    return grade;
                }
            }
            throw new IllegalArgumentException("Unknown grade code: " + code);
        }
    }

    /**
     * 接口调用状态枚举
     */
    @Getter
    @AllArgsConstructor
    public enum CallStatus {
        SUCCESS("success", "成功", "接口调用成功"),
        FAILED("failed", "失败", "接口调用失败"),
        TIMEOUT("timeout", "超时", "接口调用超时"),
        ERROR("error", "错误", "接口调用出错"),
        DISABLED("disabled", "禁用", "接口已禁用");

        private final String code;
        private final String name;
        private final String description;
    }

    /**
     * 图片格式枚举
     */
    @Getter
    @AllArgsConstructor
    public enum ImageFormat {
        JPG("jpg", "JPEG图片", "image/jpeg"),
        JPEG("jpeg", "JPEG图片", "image/jpeg"),
        PNG("png", "PNG图片", "image/png"),
        BMP("bmp", "BMP图片", "image/bmp"),
        GIF("gif", "GIF图片", "image/gif");

        private final String extension;
        private final String description;
        private final String mimeType;

        public static ImageFormat fromExtension(String extension) {
            for (ImageFormat format : values()) {
                if (format.getExtension().equalsIgnoreCase(extension)) {
                    return format;
                }
            }
            throw new IllegalArgumentException("Unsupported image format: " + extension);
        }

        public static boolean isSupported(String extension) {
            try {
                fromExtension(extension);
                return true;
            } catch (IllegalArgumentException e) {
                return false;
            }
        }
    }

    /**
     * 切题质量评分枚举
     */
    @Getter
    @AllArgsConstructor
    public enum QualityScore {
        EXCELLENT(5, "优秀", "识别质量优秀"),
        GOOD(4, "良好", "识别质量良好"),
        FAIR(3, "一般", "识别质量一般"),
        POOR(2, "较差", "识别质量较差"),
        VERY_POOR(1, "很差", "识别质量很差");

        private final int score;
        private final String name;
        private final String description;

        public static QualityScore fromScore(int score) {
            for (QualityScore quality : values()) {
                if (quality.getScore() == score) {
                    return quality;
                }
            }
            throw new IllegalArgumentException("Invalid quality score: " + score);
        }
    }
}
