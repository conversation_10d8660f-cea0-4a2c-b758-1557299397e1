package com.vida.xinye.x2.strategy;

import cn.hutool.core.util.StrUtil;
import com.vida.xinye.x2.adapter.QuestionCuttingAdapter;
import com.vida.xinye.x2.constant.QuestionCuttingConstant;
import com.vida.xinye.x2.dto.QuestionCuttingResultDto;
import com.vida.xinye.x2.dto.internal.ThirdPartyRequestDto;
import com.vida.xinye.x2.dto.internal.ThirdPartyResponseDto;
import com.vida.xinye.x2.enums.QuestionCuttingEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * 切题策略管理器
 *
 * <AUTHOR>
 * @date 2025/08/14
 */
@Slf4j
@Component
public class CuttingStrategyManager {

    @Autowired
    private List<QuestionCuttingAdapter> adapters;

    private final ExecutorService executorService = Executors.newFixedThreadPool(10);

    /**
     * 根据策略执行切题
     *
     * @param request 请求参数
     * @param strategy 切题策略
     * @param provider 指定的提供商（当strategy为SPECIFIED时使用）
     * @return 切题结果
     */
    public QuestionCuttingResultDto executeCutting(ThirdPartyRequestDto request, String strategy, String provider) {
        log.info("执行切题策略 - 请求ID: {}, 策略: {}, 提供商: {}", request.getRequestId(), strategy, provider);

        try {
            // 如果明确指定了提供商，强制使用SPECIFIED策略
            if (StrUtil.isNotBlank(provider)) {
                log.info("检测到指定提供商: {}, 强制使用SPECIFIED策略", provider);
                return executeSpecifiedStrategy(request, provider);
            }

            switch (strategy) {
                case QuestionCuttingConstant.Strategy.BEST_QUALITY:
                    return executeBestQualityStrategy(request);

                case QuestionCuttingConstant.Strategy.FASTEST:
                    return executeFastestStrategy(request);

                case QuestionCuttingConstant.Strategy.AGGREGATED:
                    return executeAggregatedStrategy(request);

                case QuestionCuttingConstant.Strategy.SPECIFIED:
                    return executeSpecifiedStrategy(request, provider);

                case QuestionCuttingConstant.Strategy.ROUND_ROBIN:
                    return executeRoundRobinStrategy(request);

                default:
                    log.warn("未知的切题策略: {}, 使用默认策略", strategy);
                    return executeBestQualityStrategy(request);
            }
        } catch (Exception e) {
            log.error("执行切题策略失败 - 请求ID: {}, 策略: {}, 错误: {}", 
                     request.getRequestId(), strategy, e.getMessage(), e);
            
            QuestionCuttingResultDto result = new QuestionCuttingResultDto();
            result.setRequestId(request.getRequestId());
            result.setSuccess(false);
            result.setErrorCode(QuestionCuttingConstant.ErrorCode.INTERNAL_ERROR);
            result.setErrorMessage("策略执行失败: " + e.getMessage());
            result.setCreateTime(new Date());
            return result;
        }
    }

    /**
     * 最佳质量策略：选择质量评分最高的可用接口
     */
    private QuestionCuttingResultDto executeBestQualityStrategy(ThirdPartyRequestDto request) {
        List<QuestionCuttingAdapter> availableAdapters = getAvailableAdapters();
        if (availableAdapters.isEmpty()) {
            return createNoProviderResult(request.getRequestId());
        }

        // 按质量评分排序，选择最高的
        QuestionCuttingAdapter bestAdapter = availableAdapters.stream()
            .max(Comparator.comparingInt(QuestionCuttingAdapter::getQualityScore))
            .orElse(availableAdapters.get(0));

        return executeWithAdapter(request, bestAdapter);
    }

    /**
     * 最快速度策略：选择平均响应时间最短的接口
     */
    private QuestionCuttingResultDto executeFastestStrategy(ThirdPartyRequestDto request) {
        List<QuestionCuttingAdapter> availableAdapters = getAvailableAdapters();
        if (availableAdapters.isEmpty()) {
            return createNoProviderResult(request.getRequestId());
        }

        // 按平均响应时间排序，选择最快的
        QuestionCuttingAdapter fastestAdapter = availableAdapters.stream()
            .min(Comparator.comparingLong(QuestionCuttingAdapter::getAverageResponseTime))
            .orElse(availableAdapters.get(0));

        return executeWithAdapter(request, fastestAdapter);
    }

    /**
     * 聚合策略：并行调用多个接口，聚合结果
     */
    private QuestionCuttingResultDto executeAggregatedStrategy(ThirdPartyRequestDto request) {
        List<QuestionCuttingAdapter> availableAdapters = getAvailableAdapters();
        if (availableAdapters.isEmpty()) {
            return createNoProviderResult(request.getRequestId());
        }

        // 并行调用所有可用接口
        List<CompletableFuture<QuestionCuttingResultDto>> futures = availableAdapters.stream()
            .map(adapter -> CompletableFuture.supplyAsync(() -> 
                executeWithAdapter(request, adapter), executorService))
            .collect(Collectors.toList());

        // 等待所有结果
        List<QuestionCuttingResultDto> results = futures.stream()
            .map(CompletableFuture::join)
            .collect(Collectors.toList());

        // 聚合结果
        return aggregateResults(request.getRequestId(), results);
    }

    /**
     * 指定提供商策略：使用指定的接口
     */
    private QuestionCuttingResultDto executeSpecifiedStrategy(ThirdPartyRequestDto request, String provider) {
        if (provider == null) {
            log.warn("指定提供商策略但未提供provider参数，使用默认策略");
            return executeBestQualityStrategy(request);
        }

        QuestionCuttingAdapter specifiedAdapter = adapters.stream()
            .filter(adapter -> adapter.getProvider().getCode().equals(provider))
            .filter(QuestionCuttingAdapter::isAvailable)
            .findFirst()
            .orElse(null);

        if (specifiedAdapter == null) {
            log.warn("指定的提供商不可用: {}, 使用默认策略", provider);
            return executeBestQualityStrategy(request);
        }

        return executeWithAdapter(request, specifiedAdapter);
    }

    /**
     * 轮询策略：轮流使用不同的接口
     */
    private QuestionCuttingResultDto executeRoundRobinStrategy(ThirdPartyRequestDto request) {
        List<QuestionCuttingAdapter> availableAdapters = getAvailableAdapters();
        if (availableAdapters.isEmpty()) {
            return createNoProviderResult(request.getRequestId());
        }

        // 简单轮询：根据请求ID的哈希值选择
        int index = Math.abs(request.getRequestId().hashCode()) % availableAdapters.size();
        QuestionCuttingAdapter selectedAdapter = availableAdapters.get(index);

        return executeWithAdapter(request, selectedAdapter);
    }

    /**
     * 使用指定适配器执行切题
     */
    private QuestionCuttingResultDto executeWithAdapter(ThirdPartyRequestDto request, QuestionCuttingAdapter adapter) {
        long startTime = System.currentTimeMillis();
        
        try {
            log.info("使用适配器执行切题 - 请求ID: {}, 提供商: {}", 
                    request.getRequestId(), adapter.getProvider().getCode());

            // 预处理请求
            ThirdPartyRequestDto processedRequest = adapter.preprocessRequest(request);

            // 验证请求
            if (!adapter.validateRequest(processedRequest)) {
                QuestionCuttingResultDto result = new QuestionCuttingResultDto();
                result.setRequestId(request.getRequestId());
                result.setSuccess(false);
                result.setErrorCode(QuestionCuttingConstant.ErrorCode.INVALID_API_RESPONSE);
                result.setErrorMessage("请求参数验证失败");
                result.setProvider(adapter.getProvider().getCode());
                result.setCreateTime(new Date());
                return result;
            }

            // 调用第三方接口
            ThirdPartyResponseDto thirdPartyResponse = adapter.callThirdPartyApi(processedRequest);

            // 后处理响应
            ThirdPartyResponseDto processedResponse = adapter.postprocessResponse(thirdPartyResponse);

            // 转换为标准格式
            QuestionCuttingResultDto result = adapter.convertToStandardResult(processedResponse, request.getRequestId());
            
            long processingTime = System.currentTimeMillis() - startTime;
            result.setProcessingTime(processingTime);
            result.setStrategy(QuestionCuttingConstant.Strategy.SPECIFIED); // 单个适配器执行

            log.info("适配器执行完成 - 请求ID: {}, 提供商: {}, 处理时间: {}ms, 成功: {}", 
                    request.getRequestId(), adapter.getProvider().getCode(), processingTime, result.getSuccess());

            return result;

        } catch (Exception e) {
            long processingTime = System.currentTimeMillis() - startTime;
            
            log.error("适配器执行失败 - 请求ID: {}, 提供商: {}, 错误: {}", 
                     request.getRequestId(), adapter.getProvider().getCode(), e.getMessage(), e);

            QuestionCuttingResultDto result = new QuestionCuttingResultDto();
            result.setRequestId(request.getRequestId());
            result.setSuccess(false);
            result.setErrorCode(QuestionCuttingConstant.ErrorCode.API_CALL_FAILED);
            result.setErrorMessage("适配器执行失败: " + e.getMessage());
            result.setProvider(adapter.getProvider().getCode());
            result.setProcessingTime(processingTime);
            result.setCreateTime(new Date());
            return result;
        }
    }

    /**
     * 聚合多个结果
     */
    private QuestionCuttingResultDto aggregateResults(String requestId, List<QuestionCuttingResultDto> results) {
        QuestionCuttingResultDto aggregatedResult = new QuestionCuttingResultDto();
        aggregatedResult.setRequestId(requestId);
        aggregatedResult.setStrategy(QuestionCuttingConstant.Strategy.AGGREGATED);
        aggregatedResult.setCreateTime(new Date());

        // 过滤成功的结果
        List<QuestionCuttingResultDto> successResults = results.stream()
            .filter(QuestionCuttingResultDto::getSuccess)
            .collect(Collectors.toList());

        if (successResults.isEmpty()) {
            aggregatedResult.setSuccess(false);
            aggregatedResult.setErrorCode(QuestionCuttingConstant.ErrorCode.NO_AVAILABLE_PROVIDER);
            aggregatedResult.setErrorMessage("所有接口调用都失败了");
            return aggregatedResult;
        }

        // 选择质量评分最高的结果作为主结果
        QuestionCuttingResultDto bestResult = successResults.stream()
            .max(Comparator.comparing(r -> r.getQualityScore() != null ? r.getQualityScore() : 0))
            .orElse(successResults.get(0));

        // 复制主结果的基本信息
        aggregatedResult.setSuccess(true);
        aggregatedResult.setProvider("aggregated");
        aggregatedResult.setQuestions(bestResult.getQuestions());
        aggregatedResult.setQualityScore(bestResult.getQualityScore());
        aggregatedResult.setConfidence(bestResult.getConfidence());
        aggregatedResult.setOriginalImage(bestResult.getOriginalImage());

        // 计算聚合统计信息
        long totalProcessingTime = results.stream()
            .filter(r -> r.getProcessingTime() != null)
            .mapToLong(QuestionCuttingResultDto::getProcessingTime)
            .sum();
        aggregatedResult.setProcessingTime(totalProcessingTime);

        // 构建详细信息
        if (results.size() > 1) {
            QuestionCuttingResultDto.DetailInfo detailInfo = new QuestionCuttingResultDto.DetailInfo();
            
            // 提供商结果列表
            List<QuestionCuttingResultDto.ProviderResult> providerResults = results.stream()
                .map(this::convertToProviderResult)
                .collect(Collectors.toList());
            detailInfo.setProviderResults(providerResults);

            // 聚合信息
            QuestionCuttingResultDto.AggregationInfo aggregationInfo = new QuestionCuttingResultDto.AggregationInfo();
            aggregationInfo.setStrategy(QuestionCuttingConstant.Strategy.AGGREGATED);
            aggregationInfo.setProviderCount(results.size());
            detailInfo.setAggregationInfo(aggregationInfo);

            aggregatedResult.setDetail(detailInfo);
        }

        return aggregatedResult;
    }

    /**
     * 转换为提供商结果
     */
    private QuestionCuttingResultDto.ProviderResult convertToProviderResult(QuestionCuttingResultDto result) {
        QuestionCuttingResultDto.ProviderResult providerResult = new QuestionCuttingResultDto.ProviderResult();
        providerResult.setProvider(result.getProvider());
        providerResult.setStatus(result.getSuccess() ? "success" : "failed");
        providerResult.setResponseTime(result.getProcessingTime());
        providerResult.setQualityScore(result.getQualityScore());
        providerResult.setConfidence(result.getConfidence());
        providerResult.setQuestions(result.getQuestions());
        providerResult.setErrorMessage(result.getErrorMessage());
        return providerResult;
    }

    /**
     * 获取可用的适配器列表
     */
    private List<QuestionCuttingAdapter> getAvailableAdapters() {
        return adapters.stream()
            .filter(QuestionCuttingAdapter::isAvailable)
            .sorted(Comparator.comparingInt(QuestionCuttingAdapter::getPriority))
            .collect(Collectors.toList());
    }

    /**
     * 创建无可用提供商的结果
     */
    private QuestionCuttingResultDto createNoProviderResult(String requestId) {
        QuestionCuttingResultDto result = new QuestionCuttingResultDto();
        result.setRequestId(requestId);
        result.setSuccess(false);
        result.setErrorCode(QuestionCuttingConstant.ErrorCode.NO_AVAILABLE_PROVIDER);
        result.setErrorMessage("没有可用的切题接口");
        result.setCreateTime(new Date());
        return result;
    }
}
