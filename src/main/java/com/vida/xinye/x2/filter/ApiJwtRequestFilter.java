package com.vida.xinye.x2.filter;

import com.vida.xinye.x2.component.JwtTokenProvider;
import com.vida.xinye.x2.config.SecurityConfig;
import com.vida.xinye.x2.constant.AuthConstant;
import com.vida.xinye.x2.service.api.ApiUserDetailsService;
import io.jsonwebtoken.ExpiredJwtException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.security.web.util.matcher.RequestMatcher;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * API请求安全性校验
 *
 * <AUTHOR>
 * @date 2024/11/5
 */
@Component
@Slf4j
public class ApiJwtRequestFilter extends OncePerRequestFilter {
    private final List<String> ignoredUrls;

    public ApiJwtRequestFilter() {
        // 从SecurityConfig中获取公共匹配器
        this.ignoredUrls = SecurityConfig.getIgnoredUrls();
    }

    @Autowired
    private ApiUserDetailsService userDetailsService;

    @Autowired
    private JwtTokenProvider jwtTokenProvider;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain)
            throws ServletException, IOException {

        final String authorizationHeader = request.getHeader(AuthConstant.JWT_TOKEN_HEADER);
        if (authorizationHeader != null && authorizationHeader.startsWith(AuthConstant.JWT_TOKEN_PREFIX)) {
            String jwtToken = extractJwtToken(authorizationHeader);
            try {
                String username = jwtTokenProvider.extractUsername(jwtToken);

                if (username != null && SecurityContextHolder.getContext().getAuthentication() == null) {
                    try {
                        processValidToken(jwtToken, username, request);
                    } catch (ExpiredJwtException e) {
                        handleAuthenticationException(response, "token已过期，请重新登录！");
                        return;
                    } catch (BadCredentialsException e) {
                        handleAuthenticationException(response, "账号不存在或密码错误！");
                        return;
                    } catch (Exception e) {
                        handleAuthenticationException(response, "用户认证失败！");
                        return;
                    }
                }
            } catch (ExpiredJwtException e) {
                handleAuthenticationException(response, "token已过期，请重新登录！");
                return;
            }

        } else {
            // 检查请求是否匹配排除规则
            for (String url : ignoredUrls) {
                if (new AntPathRequestMatcher(url).matches(request)) {
                    // 如果匹配，直接调用下一个过滤器
                    chain.doFilter(request, response);
                    return;
                }
            }
            handleAuthenticationException(response, "token已过期，请重新登录！");
            return;
        }
        chain.doFilter(request, response);
    }

    private String extractJwtToken(String authorizationHeader) {
        return authorizationHeader.substring(AuthConstant.JWT_TOKEN_PREFIX.length());
    }

    private void processValidToken(String jwtToken, String username, HttpServletRequest request) throws BadCredentialsException {
        UserDetails userDetails = userDetailsService.loadUserByUsername(username);
        if (!jwtTokenProvider.validateToken(jwtToken, username)) {
            throw new BadCredentialsException("token is invalid!");
        }
        UsernamePasswordAuthenticationToken usernamePasswordAuthenticationToken =
                new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());
        usernamePasswordAuthenticationToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
        SecurityContextHolder.getContext().setAuthentication(usernamePasswordAuthenticationToken);
    }

    private void handleAuthenticationException(HttpServletResponse response, String err)
            throws IOException {
        response.setContentType("text/html; charset=UTF-8");
        response.setCharacterEncoding("UTF-8");
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.getWriter().write(err);
    }
}
