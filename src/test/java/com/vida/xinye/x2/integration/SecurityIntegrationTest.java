package com.vida.xinye.x2.integration;

import com.vida.xinye.x2.dto.param.EmailLoginDto;
import com.vida.xinye.x2.dto.param.SetPasswordDto;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.test.web.servlet.MvcResult;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 安全机制集成测试
 * 测试登录安全、密码强度、账号锁定等安全功能
 *
 * <AUTHOR>
 * @date 2025/08/12
 */
@DisplayName("安全机制集成测试")
public class SecurityIntegrationTest extends BaseIntegrationTest {

    @Test
    @DisplayName("登录失败次数限制测试")
    void testLoginFailureLimit() throws Exception {
        String email = "<EMAIL>";
        createAnotherTestUser(email, "13900139000");

        EmailLoginDto loginDto = new EmailLoginDto();
        loginDto.setEmail(email);
        loginDto.setLoginMethod("password");
        loginDto.setPassword("wrongpassword");

        // 连续多次错误登录
        for (int i = 0; i < 6; i++) {
            MvcResult result = mockMvc.perform(post("/authen/email/login")
                    .contentType(getJsonContentType())
                    .content(toJson(loginDto)))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andReturn();

            String response = result.getResponse().getContentAsString();
            System.out.println("第" + (i + 1) + "次错误登录响应: " + response);

            // 短暂等待避免过快请求
            Thread.sleep(100);
        }

        // 验证账号是否被锁定
        loginDto.setPassword("123456"); // 使用正确密码
        mockMvc.perform(post("/authen/email/login")
                .contentType(getJsonContentType())
                .content(toJson(loginDto)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.message").value(org.hamcrest.Matchers.containsString("锁定")));
    }

    @Test
    @DisplayName("密码强度验证测试")
    void testPasswordStrengthValidation() throws Exception {
        Long userId = testUser.getId();

        // 测试弱密码
        String[] weakPasswords = {
            "123456",      // 纯数字
            "password",    // 纯字母
            "123",         // 太短
            "111111",      // 重复字符
            "abc123"       // 太简单
        };

        for (String weakPassword : weakPasswords) {
            SetPasswordDto setPasswordDto = new SetPasswordDto();
            setPasswordDto.setOldPassword("123456");
            setPasswordDto.setNewPassword(weakPassword);
            setPasswordDto.setConfirmPassword(weakPassword);

            MvcResult result = mockMvc.perform(post("/user/password/set")
                    .header("Authorization", "Bearer mock_token")
                    .contentType(getJsonContentType())
                    .content(toJson(setPasswordDto)))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andReturn();

            String response = result.getResponse().getContentAsString();
            System.out.println("弱密码 " + weakPassword + " 测试响应: " + response);
        }
    }

    @Test
    @DisplayName("强密码设置成功测试")
    void testStrongPasswordSet() throws Exception {
        SetPasswordDto setPasswordDto = new SetPasswordDto();
        setPasswordDto.setOldPassword("123456");
        setPasswordDto.setNewPassword("StrongPass123!");
        setPasswordDto.setConfirmPassword("StrongPass123!");

        MvcResult result = mockMvc.perform(post("/user/password/set")
                .header("Authorization", "Bearer mock_token")
                .contentType(getJsonContentType())
                .content(toJson(setPasswordDto)))
                .andDo(print())
                .andExpect(status().isOk())
                .andReturn();

        String response = result.getResponse().getContentAsString();
        System.out.println("强密码设置响应: " + response);
    }

    @Test
    @DisplayName("密码不一致验证测试")
    void testPasswordMismatch() throws Exception {
        SetPasswordDto setPasswordDto = new SetPasswordDto();
        setPasswordDto.setOldPassword("123456");
        setPasswordDto.setNewPassword("NewPass123!");
        setPasswordDto.setConfirmPassword("DifferentPass123!");

        mockMvc.perform(post("/user/password/set")
                .header("Authorization", "Bearer mock_token")
                .contentType(getJsonContentType())
                .content(toJson(setPasswordDto)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.message").value(org.hamcrest.Matchers.containsString("不一致")));
    }

    @Test
    @DisplayName("验证码频率限制测试")
    void testCaptchaRateLimit() throws Exception {
        String email = "<EMAIL>";

        // 快速连续发送验证码
        for (int i = 0; i < 3; i++) {
            MvcResult result = mockMvc.perform(post("/authen/email/captcha/send")
                    .param("email", email)
                    .param("purpose", "login"))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andReturn();

            String response = result.getResponse().getContentAsString();
            System.out.println("第" + (i + 1) + "次发送验证码响应: " + response);

            // 短暂等待
            Thread.sleep(500);
        }
    }

    @Test
    @DisplayName("IP频率限制测试")
    void testIPRateLimit() throws Exception {
        String email = "<EMAIL>";

        // 模拟同一IP快速请求
        for (int i = 0; i < 10; i++) {
            MvcResult result = mockMvc.perform(post("/authen/email/captcha/send")
                    .param("email", email + i) // 不同邮箱避免邮箱级别限制
                    .param("purpose", "login")
                    .header("X-Forwarded-For", "*************")) // 模拟相同IP
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andReturn();

            String response = result.getResponse().getContentAsString();
            System.out.println("第" + (i + 1) + "次IP请求响应: " + response);

            Thread.sleep(100);
        }
    }

    @Test
    @DisplayName("SQL注入防护测试")
    void testSQLInjectionProtection() throws Exception {
        // 测试邮箱字段SQL注入
        String[] sqlInjectionAttempts = {
            "<EMAIL>'; DROP TABLE user; --",
            "<EMAIL>' OR '1'='1",
            "<EMAIL>' UNION SELECT * FROM user --"
        };

        for (String maliciousEmail : sqlInjectionAttempts) {
            EmailLoginDto loginDto = new EmailLoginDto();
            loginDto.setEmail(maliciousEmail);
            loginDto.setLoginMethod("password");
            loginDto.setPassword("123456");

            MvcResult result = mockMvc.perform(post("/authen/email/login")
                    .contentType(getJsonContentType())
                    .content(toJson(loginDto)))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andReturn();

            String response = result.getResponse().getContentAsString();
            System.out.println("SQL注入测试 " + maliciousEmail + " 响应: " + response);
        }
    }

    @Test
    @DisplayName("XSS防护测试")
    void testXSSProtection() throws Exception {
        // 测试XSS攻击防护
        String[] xssAttempts = {
            "<script>alert('xss')</script>@example.com",
            "<EMAIL><img src=x onerror=alert('xss')>",
            "javascript:alert('xss')@example.com"
        };

        for (String maliciousEmail : xssAttempts) {
            EmailLoginDto loginDto = new EmailLoginDto();
            loginDto.setEmail(maliciousEmail);
            loginDto.setLoginMethod("password");
            loginDto.setPassword("123456");

            MvcResult result = mockMvc.perform(post("/authen/email/login")
                    .contentType(getJsonContentType())
                    .content(toJson(loginDto)))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andReturn();

            String response = result.getResponse().getContentAsString();
            System.out.println("XSS防护测试 " + maliciousEmail + " 响应: " + response);
            
            // 验证响应中不包含恶意脚本
            assert !response.contains("<script>");
            assert !response.contains("javascript:");
        }
    }

    @Test
    @DisplayName("大量数据输入测试")
    void testLargeDataInput() throws Exception {
        // 测试超长邮箱输入
        StringBuilder longEmail = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            longEmail.append("a");
        }
        longEmail.append("@example.com");

        EmailLoginDto loginDto = new EmailLoginDto();
        loginDto.setEmail(longEmail.toString());
        loginDto.setLoginMethod("password");
        loginDto.setPassword("123456");

        MvcResult result = mockMvc.perform(post("/authen/email/login")
                .contentType(getJsonContentType())
                .content(toJson(loginDto)))
                .andDo(print())
                .andExpect(status().isOk())
                .andReturn();

        String response = result.getResponse().getContentAsString();
        System.out.println("超长邮箱测试响应: " + response);
    }
}
