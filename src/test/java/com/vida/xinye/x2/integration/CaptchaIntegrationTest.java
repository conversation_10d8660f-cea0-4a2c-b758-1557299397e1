package com.vida.xinye.x2.integration;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.test.web.servlet.MvcResult;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 验证码功能集成测试
 * 测试验证码发送和验证的完整流程
 *
 * <AUTHOR>
 * @date 2025/08/12
 */
@DisplayName("验证码功能集成测试")
public class CaptchaIntegrationTest extends BaseIntegrationTest {

    @Test
    @DisplayName("发送邮箱验证码 - 成功")
    void testSendEmailCaptcha_Success() throws Exception {
        // When & Then
        MvcResult result = mockMvc.perform(post("/authen/email/captcha/send")
                .param("email", "<EMAIL>")
                .param("purpose", "login"))
                .andDo(print())
                .andExpect(status().isOk())
                .andReturn();

        String response = result.getResponse().getContentAsString();
        System.out.println("发送邮箱验证码响应: " + response);
        
        // 验证响应结构
        mockMvc.perform(post("/authen/email/captcha/send")
                .param("email", "<EMAIL>")
                .param("purpose", "login"))
                .andExpect(jsonPath("$.code").exists())
                .andExpect(jsonPath("$.message").exists());
    }

    @Test
    @DisplayName("发送邮箱验证码 - 邮箱格式错误")
    void testSendEmailCaptcha_InvalidEmail() throws Exception {
        // When & Then
        mockMvc.perform(post("/authen/email/captcha/send")
                .param("email", "invalid_email")
                .param("purpose", "login"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.message").exists());
    }

    @Test
    @DisplayName("发送邮箱验证码 - 频率限制")
    void testSendEmailCaptcha_RateLimit() throws Exception {
        String email = "<EMAIL>";
        
        // 第一次发送
        mockMvc.perform(post("/authen/email/captcha/send")
                .param("email", email)
                .param("purpose", "login"))
                .andDo(print())
                .andExpect(status().isOk());

        // 立即再次发送，应该被频率限制
        mockMvc.perform(post("/authen/email/captcha/send")
                .param("email", email)
                .param("purpose", "login"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500));
    }

    @Test
    @DisplayName("发送短信验证码 - 成功")
    void testSendSmsCode_Success() throws Exception {
        // When & Then
        MvcResult result = mockMvc.perform(post("/captcha/send")
                .param("phone", "***********"))
                .andDo(print())
                .andExpect(status().isOk())
                .andReturn();

        String response = result.getResponse().getContentAsString();
        System.out.println("发送短信验证码响应: " + response);
    }

    @Test
    @DisplayName("发送短信验证码 - 手机号格式错误")
    void testSendSmsCode_InvalidPhone() throws Exception {
        // When & Then
        mockMvc.perform(post("/captcha/send")
                .param("phone", "invalid_phone"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.message").exists());
    }

    @Test
    @DisplayName("验证短信验证码 - 成功")
    void testVerifySmsCode_Success() throws Exception {
        String phone = "***********";
        String code = "123456";
        
        // 模拟验证码存在
        mockCaptchaSuccess(phone, code);

        // When & Then
        mockMvc.perform(post("/captcha/verify")
                .param("account", phone)
                .param("code", code))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @DisplayName("验证短信验证码 - 验证码错误")
    void testVerifySmsCode_WrongCode() throws Exception {
        String phone = "***********";
        
        // 模拟正确验证码
        mockCaptchaSuccess(phone, "123456");

        // 使用错误验证码验证
        mockMvc.perform(post("/captcha/verify")
                .param("account", phone)
                .param("code", "654321"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500));
    }

    @Test
    @DisplayName("验证短信验证码 - 验证码过期")
    void testVerifySmsCode_Expired() throws Exception {
        String phone = "***********";
        String code = "123456";

        // 不设置验证码，模拟过期情况
        mockMvc.perform(post("/captcha/verify")
                .param("account", phone)
                .param("code", code))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500));
    }

    @Test
    @DisplayName("验证邮箱验证码 - 成功")
    void testVerifyEmailCode_Success() throws Exception {
        String email = "<EMAIL>";
        String code = "123456";
        String purpose = "login";
        
        // 模拟邮箱验证码存在
        mockEmailCaptchaSuccess(email, code, purpose);

        // 通过邮箱登录接口验证
        mockMvc.perform(post("/authen/email/login")
                .contentType(getJsonContentType())
                .content("{\"email\":\"" + email + "\",\"loginMethod\":\"captcha\",\"captcha\":\"" + code + "\"}"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @DisplayName("验证码参数缺失")
    void testCaptchaParameterValidation() throws Exception {
        // 缺少email参数
        mockMvc.perform(post("/authen/email/captcha/send")
                .param("purpose", "login"))
                .andDo(print())
                .andExpect(status().isBadRequest());

        // 缺少phone参数
        mockMvc.perform(post("/captcha/send"))
                .andDo(print())
                .andExpect(status().isBadRequest());

        // 缺少验证码参数
        mockMvc.perform(post("/captcha/verify")
                .param("account", "***********"))
                .andDo(print())
                .andExpect(status().isBadRequest());
    }

    @Test
    @DisplayName("不同用途的邮箱验证码")
    void testEmailCaptchaDifferentPurposes() throws Exception {
        String email = "<EMAIL>";
        
        // 测试不同用途的验证码发送
        String[] purposes = {"login", "register", "reset_password", "bind"};
        
        for (String purpose : purposes) {
            MvcResult result = mockMvc.perform(post("/authen/email/captcha/send")
                    .param("email", email)
                    .param("purpose", purpose))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andReturn();
            
            String response = result.getResponse().getContentAsString();
            System.out.println("用途 " + purpose + " 的验证码发送响应: " + response);
            
            // 等待一秒避免频率限制
            Thread.sleep(1000);
        }
    }
}
