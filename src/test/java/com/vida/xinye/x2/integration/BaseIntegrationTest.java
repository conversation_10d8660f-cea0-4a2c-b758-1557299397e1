package com.vida.xinye.x2.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.vida.xinye.x2.mbg.mapper.UserMapper;
import com.vida.xinye.x2.mbg.model.User;
import com.vida.xinye.x2.service.RedisService;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

import java.util.Date;

/**
 * 集成测试基础类
 * 提供通用的测试环境和工具方法
 *
 * <AUTHOR>
 * @date 2025/08/12
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
public abstract class BaseIntegrationTest {

    @Autowired
    protected WebApplicationContext webApplicationContext;

    @Autowired
    protected ObjectMapper objectMapper;

    @Autowired
    protected UserMapper userMapper;

    @Autowired
    protected PasswordEncoder passwordEncoder;

    @Autowired
    protected RedisService redisService;

    protected MockMvc mockMvc;

    protected User testUser;

    @BeforeEach
    void setUp() {
        // 初始化MockMvc
        mockMvc = MockMvcBuilders
                .webAppContextSetup(webApplicationContext)
                .build();

        // 创建测试用户
        createTestUser();

        // 清理Redis缓存
        clearRedisCache();
    }

    /**
     * 创建测试用户
     */
    protected void createTestUser() {
        testUser = new User();
        testUser.setUsername("<EMAIL>");
        testUser.setEmail("<EMAIL>");
        testUser.setPhone("***********");
        testUser.setPassword(passwordEncoder.encode("123456"));
        testUser.setNickname("测试用户");
        testUser.setAccountStatus(1);
        testUser.setEmailVerified(1);
        testUser.setPhoneVerified(1);
        testUser.setRoleType("1");
        testUser.setCreateTime(new Date());
        testUser.setUpdateTime(new Date());

        // 插入测试用户
        userMapper.insertSelective(testUser);
    }

    /**
     * 清理Redis缓存
     */
    protected void clearRedisCache() {
        try {
            // 清理验证码缓存
            redisService.del("captcha:*");
            redisService.del("email_captcha:*");
            
            // 清理登录失败记录
            redisService.del("login_failure:*");
            redisService.del("login_lock:*");
            
            // 清理用户缓存
            redisService.del("user_cache:*");
            redisService.del("user_token:*");
        } catch (Exception e) {
            // 忽略清理错误
        }
    }

    /**
     * 将对象转换为JSON字符串
     */
    protected String toJson(Object object) throws Exception {
        return objectMapper.writeValueAsString(object);
    }

    /**
     * 将JSON字符串转换为对象
     */
    protected <T> T fromJson(String json, Class<T> clazz) throws Exception {
        return objectMapper.readValue(json, clazz);
    }

    /**
     * 获取JSON内容类型
     */
    protected MediaType getJsonContentType() {
        return MediaType.APPLICATION_JSON;
    }

    /**
     * 模拟验证码验证成功
     */
    protected void mockCaptchaSuccess(String account, String captcha) {
        String key = "captcha:" + account;
        redisService.set(key, captcha, 300);
    }

    /**
     * 模拟邮箱验证码验证成功
     */
    protected void mockEmailCaptchaSuccess(String email, String captcha, String purpose) {
        String key = "email_captcha:" + email + ":" + purpose;
        redisService.set(key, captcha, 300);
    }

    /**
     * 获取测试用户
     */
    protected User getTestUser() {
        return testUser;
    }

    /**
     * 创建另一个测试用户
     */
    protected User createAnotherTestUser(String email, String phone) {
        User user = new User();
        user.setUsername(email);
        user.setEmail(email);
        user.setPhone(phone);
        user.setPassword(passwordEncoder.encode("123456"));
        user.setNickname("测试用户2");
        user.setAccountStatus(1);
        user.setEmailVerified(1);
        user.setPhoneVerified(1);
        user.setRoleType("1");
        user.setCreateTime(new Date());
        user.setUpdateTime(new Date());

        userMapper.insertSelective(user);
        return user;
    }
}
