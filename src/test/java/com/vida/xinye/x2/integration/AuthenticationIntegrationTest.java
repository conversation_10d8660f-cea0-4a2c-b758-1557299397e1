package com.vida.xinye.x2.integration;

import com.vida.xinye.x2.dto.param.EmailLoginDto;
import com.vida.xinye.x2.dto.param.PhonePasswordLoginDto;
import com.vida.xinye.x2.dto.param.ThirdPartyLoginDto;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.test.web.servlet.MvcResult;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 登录功能集成测试
 * 测试各种登录方式的完整流程
 *
 * <AUTHOR>
 * @date 2025/08/12
 */
@DisplayName("登录功能集成测试")
public class AuthenticationIntegrationTest extends BaseIntegrationTest {

    @Test
    @DisplayName("邮箱密码登录 - 成功")
    void testEmailPasswordLogin_Success() throws Exception {
        // Given
        EmailLoginDto loginDto = new EmailLoginDto();
        loginDto.setEmail("<EMAIL>");
        loginDto.setLoginMethod("password");
        loginDto.setPassword("123456");

        // When & Then
        mockMvc.perform(post("/authen/email/login")
                .contentType(getJsonContentType())
                .content(toJson(loginDto)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.accessToken").exists())
                .andExpect(jsonPath("$.data.userId").value(testUser.getId()))
                .andExpect(jsonPath("$.data.username").value("<EMAIL>"));
    }

    @Test
    @DisplayName("邮箱密码登录 - 密码错误")
    void testEmailPasswordLogin_WrongPassword() throws Exception {
        // Given
        EmailLoginDto loginDto = new EmailLoginDto();
        loginDto.setEmail("<EMAIL>");
        loginDto.setLoginMethod("password");
        loginDto.setPassword("wrongpassword");

        // When & Then
        mockMvc.perform(post("/authen/email/login")
                .contentType(getJsonContentType())
                .content(toJson(loginDto)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.message").exists());
    }

    @Test
    @DisplayName("邮箱验证码登录 - 成功")
    void testEmailCaptchaLogin_Success() throws Exception {
        // Given
        String captcha = "123456";
        mockEmailCaptchaSuccess("<EMAIL>", captcha, "login");

        EmailLoginDto loginDto = new EmailLoginDto();
        loginDto.setEmail("<EMAIL>");
        loginDto.setLoginMethod("captcha");
        loginDto.setCaptcha(captcha);

        // When & Then
        mockMvc.perform(post("/authen/email/login")
                .contentType(getJsonContentType())
                .content(toJson(loginDto)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.accessToken").exists())
                .andExpect(jsonPath("$.data.userId").value(testUser.getId()));
    }

    @Test
    @DisplayName("邮箱验证码登录 - 验证码错误")
    void testEmailCaptchaLogin_WrongCaptcha() throws Exception {
        // Given
        mockEmailCaptchaSuccess("<EMAIL>", "123456", "login");

        EmailLoginDto loginDto = new EmailLoginDto();
        loginDto.setEmail("<EMAIL>");
        loginDto.setLoginMethod("captcha");
        loginDto.setCaptcha("654321"); // 错误的验证码

        // When & Then
        mockMvc.perform(post("/authen/email/login")
                .contentType(getJsonContentType())
                .content(toJson(loginDto)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.message").exists());
    }

    @Test
    @DisplayName("手机号密码登录 - 成功")
    void testPhonePasswordLogin_Success() throws Exception {
        // Given
        PhonePasswordLoginDto loginDto = new PhonePasswordLoginDto();
        loginDto.setPhone("13800138000");
        loginDto.setPassword("123456");

        // When & Then
        mockMvc.perform(post("/authen/phone/password/login")
                .contentType(getJsonContentType())
                .content(toJson(loginDto)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.accessToken").exists())
                .andExpect(jsonPath("$.data.userId").value(testUser.getId()));
    }

    @Test
    @DisplayName("手机号密码登录 - 手机号不存在")
    void testPhonePasswordLogin_PhoneNotFound() throws Exception {
        // Given
        PhonePasswordLoginDto loginDto = new PhonePasswordLoginDto();
        loginDto.setPhone("13900139000"); // 不存在的手机号
        loginDto.setPassword("123456");

        // When & Then
        mockMvc.perform(post("/authen/phone/password/login")
                .contentType(getJsonContentType())
                .content(toJson(loginDto)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.message").exists());
    }

    @Test
    @DisplayName("第三方登录 - 微信登录成功")
    void testThirdPartyLogin_WeChatSuccess() throws Exception {
        // Given
        ThirdPartyLoginDto loginDto = new ThirdPartyLoginDto();
        loginDto.setLoginWay("byweixin");
        loginDto.setAccessToken("mock_access_token");
        loginDto.setOpenId("wx_openid_123");
        loginDto.setUnionId("wx_unionid_123");
        loginDto.setNickname("微信用户");
        loginDto.setFrom("app");

        // When & Then
        MvcResult result = mockMvc.perform(post("/authen/third-party/login")
                .contentType(getJsonContentType())
                .content(toJson(loginDto)))
                .andDo(print())
                .andExpect(status().isOk())
                .andReturn();

        // 注意：第三方登录可能会因为配置问题失败，这里主要测试接口可达性
        String response = result.getResponse().getContentAsString();
        System.out.println("第三方登录响应: " + response);
    }

    @Test
    @DisplayName("邮箱格式验证")
    void testEmailFormatValidation() throws Exception {
        // Given
        EmailLoginDto loginDto = new EmailLoginDto();
        loginDto.setEmail("invalid_email"); // 无效邮箱格式
        loginDto.setLoginMethod("password");
        loginDto.setPassword("123456");

        // When & Then
        mockMvc.perform(post("/authen/email/login")
                .contentType(getJsonContentType())
                .content(toJson(loginDto)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.message").exists());
    }

    @Test
    @DisplayName("手机号格式验证")
    void testPhoneFormatValidation() throws Exception {
        // Given
        PhonePasswordLoginDto loginDto = new PhonePasswordLoginDto();
        loginDto.setPhone("invalid_phone"); // 无效手机号格式
        loginDto.setPassword("123456");

        // When & Then
        mockMvc.perform(post("/authen/phone/password/login")
                .contentType(getJsonContentType())
                .content(toJson(loginDto)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.message").exists());
    }

    @Test
    @DisplayName("登录参数缺失验证")
    void testLoginParameterValidation() throws Exception {
        // Given - 缺少必要参数的请求
        EmailLoginDto loginDto = new EmailLoginDto();
        // 不设置email和password

        // When & Then
        mockMvc.perform(post("/authen/email/login")
                .contentType(getJsonContentType())
                .content(toJson(loginDto)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500));
    }
}
