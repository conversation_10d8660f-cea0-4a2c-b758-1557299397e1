package com.vida.xinye.x2.adapter;

import com.vida.xinye.x2.adapter.impl.TianrangAdapter;
import com.vida.xinye.x2.config.QuestionCuttingConfig;
import com.vida.xinye.x2.dto.QuestionCuttingResultDto;
import com.vida.xinye.x2.dto.internal.ThirdPartyRequestDto;
import com.vida.xinye.x2.dto.internal.ThirdPartyResponseDto;
import com.vida.xinye.x2.enums.QuestionCuttingEnum;
import com.vida.xinye.x2.util.HttpClientUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 天壤适配器测试
 *
 * <AUTHOR>
 * @date 2025/08/14
 */
@ExtendWith(MockitoExtension.class)
class TianrangAdapterTest {

    @Mock
    private QuestionCuttingConfig config;

    @Mock
    private HttpClientUtil httpClientUtil;

    @InjectMocks
    private TianrangAdapter tianrangAdapter;

    private QuestionCuttingConfig.TianrangConfig tianrangConfig;
    private ThirdPartyRequestDto testRequest;

    @BeforeEach
    void setUp() {
        // 配置天壤配置
        tianrangConfig = new QuestionCuttingConfig.TianrangConfig();
        tianrangConfig.setUrl("https://api.tianrang.com/v1/question/detect");
        tianrangConfig.setApiKey("test-api-key");
        tianrangConfig.setEnabled(true);
        tianrangConfig.setTimeout(30000);
        tianrangConfig.setRetryCount(2);

        when(config.getTianrang()).thenReturn(tianrangConfig);

        // 准备测试请求
        testRequest = new ThirdPartyRequestDto();
        testRequest.setRequestId("test-request-id");
        testRequest.setImageBase64("iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==");
        testRequest.setSubject("math");
        testRequest.setGrade("junior_1");
    }

    @Test
    void testGetProvider() {
        assertEquals(QuestionCuttingEnum.Provider.TIANRANG, tianrangAdapter.getProvider());
    }

    @Test
    void testIsAvailable_True() {
        assertTrue(tianrangAdapter.isAvailable());
    }

    @Test
    void testIsAvailable_False_Disabled() {
        tianrangConfig.setEnabled(false);
        assertFalse(tianrangAdapter.isAvailable());
    }

    @Test
    void testIsAvailable_False_NoApiKey() {
        tianrangConfig.setApiKey("");
        assertFalse(tianrangAdapter.isAvailable());
    }

    @Test
    void testIsAvailable_False_NoUrl() {
        tianrangConfig.setUrl("");
        assertFalse(tianrangAdapter.isAvailable());
    }

    @Test
    void testGetPriority() {
        assertEquals(10, tianrangAdapter.getPriority());
    }

    @Test
    void testGetQualityScore() {
        assertEquals(5, tianrangAdapter.getQualityScore());
    }

    @Test
    void testGetAverageResponseTime() {
        assertEquals(3000, tianrangAdapter.getAverageResponseTime());
    }

    @Test
    void testValidateRequest_Valid() {
        assertTrue(tianrangAdapter.validateRequest(testRequest));
    }

    @Test
    void testValidateRequest_Invalid_NoImage() {
        testRequest.setImageBase64(null);
        testRequest.setImageUrl(null);
        assertFalse(tianrangAdapter.validateRequest(testRequest));
    }

    @Test
    void testValidateRequest_Invalid_NullRequest() {
        assertFalse(tianrangAdapter.validateRequest(null));
    }

    @Test
    void testCallThirdPartyApi_Success() throws Exception {
        // 准备模拟响应
        String mockResponse = "{\n" +
            "    \"code\": 200,\n" +
            "    \"message\": \"success\",\n" +
            "    \"data\": {\n" +
            "        \"questions\": [\n" +
            "            {\n" +
            "                \"questionId\": \"q1\",\n" +
            "                \"type\": \"choice\",\n" +
            "                \"content\": \"1+1=?\",\n" +
            "                \"options\": [\"1\", \"2\", \"3\", \"4\"],\n" +
            "                \"answer\": \"2\",\n" +
            "                \"confidence\": 0.95,\n" +
            "                \"bbox\": {\n" +
            "                    \"x\": 10,\n" +
            "                    \"y\": 20,\n" +
            "                    \"width\": 100,\n" +
            "                    \"height\": 50\n" +
            "                }\n" +
            "            }\n" +
            "        ],\n" +
            "        \"confidence\": 0.95,\n" +
            "        \"processTime\": 1500\n" +
            "    }\n" +
            "}";

        when(httpClientUtil.postJson(anyString(), anyString(), any(Map.class)))
            .thenReturn(mockResponse);

        // 执行测试
        ThirdPartyResponseDto response = tianrangAdapter.callThirdPartyApi(testRequest);

        // 验证结果
        assertNotNull(response);
        assertTrue(response.getSuccess());
        assertNotNull(response.getResponseTime());
        
        // 验证HTTP调用
        verify(httpClientUtil, times(1)).postJson(anyString(), anyString(), any(Map.class));
    }

    @Test
    void testCallThirdPartyApi_HttpException() throws Exception {
        // 配置HTTP异常
        when(httpClientUtil.postJson(anyString(), anyString(), any(Map.class)))
            .thenThrow(new RuntimeException("Network error"));

        // 执行测试
        ThirdPartyResponseDto response = tianrangAdapter.callThirdPartyApi(testRequest);

        // 验证结果
        assertNotNull(response);
        assertFalse(response.getSuccess());
        assertNotNull(response.getErrorMessage());
        assertTrue(response.getErrorMessage().contains("天壤接口调用失败"));
    }

    @Test
    void testConvertToStandardResult_Success() {
        // 准备天壤响应
        ThirdPartyResponseDto.TianrangResponse tianrangResponse = new ThirdPartyResponseDto.TianrangResponse();
        tianrangResponse.setSuccess(true);
        tianrangResponse.setResponseTime(1500L);
        tianrangResponse.setRequestId("test-request-id");

        ThirdPartyResponseDto.TianrangResponse.TianrangData data = new ThirdPartyResponseDto.TianrangResponse.TianrangData();
        data.setConfidence(0.95);

        ThirdPartyResponseDto.TianrangResponse.TianrangQuestion question = new ThirdPartyResponseDto.TianrangResponse.TianrangQuestion();
        question.setQuestionId("q1");
        question.setType("choice");
        question.setContent("1+1=?");
        question.setOptions(Arrays.asList("1", "2", "3", "4"));
        question.setAnswer("2");
        question.setConfidence(0.95);

        ThirdPartyResponseDto.TianrangResponse.TianrangQuestion.BoundingBox bbox = 
            new ThirdPartyResponseDto.TianrangResponse.TianrangQuestion.BoundingBox();
        bbox.setX(10);
        bbox.setY(20);
        bbox.setWidth(100);
        bbox.setHeight(50);
        question.setBbox(bbox);

        data.setQuestions(Arrays.asList(question));
        tianrangResponse.setData(data);

        // 执行转换
        QuestionCuttingResultDto result = tianrangAdapter.convertToStandardResult(tianrangResponse, "test-request-id");

        // 验证结果
        assertNotNull(result);
        assertTrue(result.getSuccess());
        assertEquals("test-request-id", result.getRequestId());
        assertEquals("tianrang", result.getProvider());
        assertEquals(1500L, result.getProcessingTime());
        assertEquals(0.95, result.getConfidence());
        assertEquals(5, result.getQualityScore());

        // 验证题目信息
        assertNotNull(result.getQuestions());
        assertEquals(1, result.getQuestions().size());

        QuestionCuttingResultDto.QuestionInfo questionInfo = result.getQuestions().get(0);
        assertEquals("q1", questionInfo.getQuestionId());
        assertEquals("choice", questionInfo.getQuestionType());
        assertEquals("1+1=?", questionInfo.getContent());
        assertEquals("2", questionInfo.getAnswer());
        assertEquals(0.95, questionInfo.getConfidence());

        // 验证选项
        assertNotNull(questionInfo.getOptions());
        assertEquals(4, questionInfo.getOptions().size());
        assertEquals("A", questionInfo.getOptions().get(0).getLabel());
        assertEquals("1", questionInfo.getOptions().get(0).getContent());

        // 验证边界框
        assertNotNull(questionInfo.getBoundingBox());
        assertEquals(10, questionInfo.getBoundingBox().getX());
        assertEquals(20, questionInfo.getBoundingBox().getY());
        assertEquals(100, questionInfo.getBoundingBox().getWidth());
        assertEquals(50, questionInfo.getBoundingBox().getHeight());
    }

    @Test
    void testConvertToStandardResult_Failed() {
        // 准备失败的响应
        ThirdPartyResponseDto.TianrangResponse tianrangResponse = new ThirdPartyResponseDto.TianrangResponse();
        tianrangResponse.setSuccess(false);
        tianrangResponse.setErrorCode("API_ERROR");
        tianrangResponse.setErrorMessage("API调用失败");
        tianrangResponse.setRequestId("test-request-id");

        // 执行转换
        QuestionCuttingResultDto result = tianrangAdapter.convertToStandardResult(tianrangResponse, "test-request-id");

        // 验证结果
        assertNotNull(result);
        assertFalse(result.getSuccess());
        assertEquals("test-request-id", result.getRequestId());
        assertEquals("tianrang", result.getProvider());
        assertEquals("API_ERROR", result.getErrorCode());
        assertEquals("API调用失败", result.getErrorMessage());
    }

    @Test
    void testPreprocessRequest() {
        ThirdPartyRequestDto processedRequest = tianrangAdapter.preprocessRequest(testRequest);
        
        assertNotNull(processedRequest);
        assertEquals(testRequest.getRequestId(), processedRequest.getRequestId());
        assertEquals(testRequest.getImageBase64(), processedRequest.getImageBase64());
    }

    @Test
    void testPostprocessResponse() {
        ThirdPartyResponseDto.TianrangResponse response = new ThirdPartyResponseDto.TianrangResponse();
        response.setSuccess(true);
        
        ThirdPartyResponseDto processedResponse = tianrangAdapter.postprocessResponse(response);
        
        assertNotNull(processedResponse);
        assertTrue(processedResponse.getSuccess());
    }
}
