package com.vida.xinye.x2.service;

import com.vida.xinye.x2.validator.AccountSecurityValidator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * CaptchaService单元测试
 * 测试验证码发送和验证功能
 *
 * <AUTHOR>
 * @date 2025/08/12
 */
@ExtendWith(MockitoExtension.class)
class CaptchaServiceTest {

    @Mock
    private SmsService smsService;

    @Mock
    private RedisService redisService;

    @Mock
    private EmailService emailService;

    @Mock
    private AccountSecurityValidator accountSecurityValidator;

    @InjectMocks
    private CaptchaService captchaService;

    @BeforeEach
    void setUp() {
        // 设置私有字段值
        ReflectionTestUtils.setField(captchaService, "captchaRedisPrefix", "captcha:");
        ReflectionTestUtils.setField(captchaService, "captchaExpirationSeconds", 300);
    }

    @Test
    void testSendVerificationCode_ValidPhone_Success() {
        // Given
        String phone = "***********";
        when(redisService.hasKey(anyString())).thenReturn(false);
        when(smsService.sendVerificationCode(anyString(), anyString())).thenReturn(true);

        // When
        boolean result = captchaService.sendVerificationCode(phone);

        // Then
        assertTrue(result);
        verify(redisService, times(1)).set(anyString(), anyString(), anyInt());
        verify(smsService, times(1)).sendVerificationCode(eq(phone), anyString());
    }

    @Test
    void testSendVerificationCode_InvalidPhone_Failure() {
        // Given
        String invalidPhone = "invalid_phone";

        // When
        boolean result = captchaService.sendVerificationCode(invalidPhone);

        // Then
        assertFalse(result);
        verify(smsService, never()).sendVerificationCode(anyString(), anyString());
        verify(redisService, never()).set(anyString(), anyString(), anyInt());
    }

    @Test
    void testVerifyCode_ValidCode_Success() {
        // Given
        String account = "***********";
        String code = "123456";
        when(redisService.get(anyString())).thenReturn(code);

        // When
        boolean result = captchaService.verifyCode(account, code);

        // Then
        assertTrue(result);
        verify(redisService, times(1)).del(anyString());
    }

    @Test
    void testVerifyCode_InvalidCode_Failure() {
        // Given
        String account = "***********";
        String code = "123456";
        String wrongCode = "654321";
        when(redisService.get(anyString())).thenReturn(code);

        // When
        boolean result = captchaService.verifyCode(account, wrongCode);

        // Then
        assertFalse(result);
        verify(redisService, never()).del(anyString());
    }

    @Test
    void testVerifyCode_ExpiredCode_Failure() {
        // Given
        String account = "***********";
        String code = "123456";
        when(redisService.get(anyString())).thenReturn(null);

        // When
        boolean result = captchaService.verifyCode(account, code);

        // Then
        assertFalse(result);
        verify(redisService, never()).del(anyString());
    }

    @Test
    void testSendEmailCode_ValidEmail_Success() {
        // Given
        String email = "<EMAIL>";
        String purpose = "login";
        when(accountSecurityValidator.isValidEmail(anyString())).thenReturn(true);
        when(redisService.hasKey(anyString())).thenReturn(false);
        when(redisService.get(anyString())).thenReturn(null);
        when(emailService.sendVerificationCode(anyString(), anyString(), anyString())).thenReturn(true);

        // When
        boolean result = captchaService.sendEmailCode(email, purpose);

        // Then
        assertTrue(result);
        verify(emailService, times(1)).sendVerificationCode(eq(email), anyString(), eq(purpose));
    }

    @Test
    void testSendEmailCode_InvalidEmail_Failure() {
        // Given
        String invalidEmail = "invalid_email";
        String purpose = "login";
        when(accountSecurityValidator.isValidEmail(anyString())).thenReturn(false);

        // When
        boolean result = captchaService.sendEmailCode(invalidEmail, purpose);

        // Then
        assertFalse(result);
        verify(emailService, never()).sendVerificationCode(anyString(), anyString(), anyString());
    }

    @Test
    void testVerifyEmailCode_ValidCode_Success() {
        // Given
        String email = "<EMAIL>";
        String code = "123456";
        String purpose = "login";
        when(redisService.get(anyString())).thenReturn(code);

        // When
        boolean result = captchaService.verifyEmailCode(email, code, purpose);

        // Then
        assertTrue(result);
        verify(redisService, times(1)).del(anyString());
    }

    @Test
    void testVerifyEmailCode_InvalidCode_Failure() {
        // Given
        String email = "<EMAIL>";
        String code = "123456";
        String wrongCode = "654321";
        String purpose = "login";
        when(redisService.get(anyString())).thenReturn(code);

        // When
        boolean result = captchaService.verifyEmailCode(email, wrongCode, purpose);

        // Then
        assertFalse(result);
        verify(redisService, never()).del(anyString());
    }

    @Test
    void testGenerateVerificationCode_ReturnsValidCode() {
        // When
        String code = captchaService.generateVerificationCode(6);

        // Then
        assertNotNull(code);
        assertEquals(6, code.length());
        assertTrue(code.matches("\\d{6}"));
    }
}
