package com.vida.xinye.x2.service;

import com.vida.xinye.x2.dto.TokenDto;
import com.vida.xinye.x2.dto.param.ThirdPartyLoginDto;
import com.vida.xinye.x2.mbg.mapper.UserMapper;
import com.vida.xinye.x2.mbg.mapper.UserThirdPartyAuthMapper;
import com.vida.xinye.x2.mbg.model.User;
import com.vida.xinye.x2.mbg.model.UserThirdPartyAuth;
import com.vida.xinye.x2.service.impl.ExtendedAuthServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 优化后的第三方登录测试
 * 测试APP端直接传递SDK信息的登录流程
 *
 * <AUTHOR>
 * @date 2025/08/12
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("第三方登录优化测试")
class ThirdPartyLoginOptimizedTest {

    @Mock
    private UserMapper userMapper;

    @Mock
    private UserThirdPartyAuthMapper userThirdPartyAuthMapper;

    @InjectMocks
    private ExtendedAuthServiceImpl extendedAuthService;

    private ThirdPartyLoginDto wechatLoginDto;
    private ThirdPartyLoginDto qqLoginDto;
    private ThirdPartyLoginDto appleLoginDto;

    @BeforeEach
    void setUp() {
        // 微信登录参数
        wechatLoginDto = new ThirdPartyLoginDto();
        wechatLoginDto.setLoginWay("byweixin");
        wechatLoginDto.setOpenId("wx_openid_123456");
        wechatLoginDto.setUnionId("wx_unionid_789012");
        wechatLoginDto.setNickname("微信用户");
        wechatLoginDto.setHeadPic("https://wx.qlogo.cn/avatar.jpg");
        wechatLoginDto.setSex("1");
        wechatLoginDto.setFrom("app");

        // QQ登录参数
        qqLoginDto = new ThirdPartyLoginDto();
        qqLoginDto.setLoginWay("byqq");
        qqLoginDto.setOpenId("qq_openid_123456");
        qqLoginDto.setUnionId("qq_unionid_789012");
        qqLoginDto.setNickname("QQ用户");
        qqLoginDto.setHeadPic("https://q.qlogo.cn/avatar.jpg");
        qqLoginDto.setSex("2");
        qqLoginDto.setFrom("app");

        // Apple登录参数
        appleLoginDto = new ThirdPartyLoginDto();
        appleLoginDto.setLoginWay("byappleid");
        appleLoginDto.setAppleAccount("apple_user_123456");
        appleLoginDto.setJwtToken("apple_jwt_token");
        appleLoginDto.setFrom("app");
    }

    @Test
    @DisplayName("微信登录 - 新用户自动注册")
    void testWeChatLogin_NewUserAutoRegister() {
        // Given - 新用户，没有找到现有记录
        when(userThirdPartyAuthMapper.selectByWechatUnionId(anyString())).thenReturn(null);
        when(userThirdPartyAuthMapper.selectByWechatOpenId(anyString())).thenReturn(null);
        when(userMapper.insertSelective(any(User.class))).thenReturn(1);
        when(userThirdPartyAuthMapper.insertSelective(any(UserThirdPartyAuth.class))).thenReturn(1);

        // When
        TokenDto result = extendedAuthService.thirdPartyLogin(wechatLoginDto);

        // Then
        assertNotNull(result);
        assertNotNull(result.getAccessToken());
        assertEquals("byweixin", result.getLoginWay());
        assertTrue(result.getRequirePhoneBinding()); // 新用户需要绑定手机号

        // 验证创建了新用户
        verify(userMapper, times(1)).insertSelective(any(User.class));
        verify(userThirdPartyAuthMapper, times(1)).insertSelective(any(UserThirdPartyAuth.class));
    }

    @Test
    @DisplayName("微信登录 - 已有用户直接登录")
    void testWeChatLogin_ExistingUserDirectLogin() {
        // Given - 已有用户
        User existingUser = createTestUser();
        UserThirdPartyAuth existingAuth = createTestThirdPartyAuth(existingUser.getId());

        when(userThirdPartyAuthMapper.selectByWechatUnionId(anyString())).thenReturn(existingAuth);
        when(userMapper.selectByPrimaryKey(existingUser.getId())).thenReturn(existingUser);
        when(userThirdPartyAuthMapper.updateByPrimaryKeySelective(any(UserThirdPartyAuth.class))).thenReturn(1);

        // When
        TokenDto result = extendedAuthService.thirdPartyLogin(wechatLoginDto);

        // Then
        assertNotNull(result);
        assertEquals(existingUser.getId(), result.getUserId());
        assertEquals(existingUser.getUsername(), result.getUsername());
        assertEquals("byweixin", result.getLoginWay());

        // 验证没有创建新用户，只更新了第三方信息
        verify(userMapper, never()).insertSelective(any(User.class));
        verify(userThirdPartyAuthMapper, times(1)).updateByPrimaryKeySelective(any(UserThirdPartyAuth.class));
    }

    @Test
    @DisplayName("QQ登录 - 新用户自动注册")
    void testQQLogin_NewUserAutoRegister() {
        // Given
        when(userThirdPartyAuthMapper.selectByQqUnionId(anyString())).thenReturn(null);
        when(userThirdPartyAuthMapper.selectByQqOpenId(anyString())).thenReturn(null);
        when(userMapper.insertSelective(any(User.class))).thenReturn(1);
        when(userThirdPartyAuthMapper.insertSelective(any(UserThirdPartyAuth.class))).thenReturn(1);

        // When
        TokenDto result = extendedAuthService.thirdPartyLogin(qqLoginDto);

        // Then
        assertNotNull(result);
        assertNotNull(result.getAccessToken());
        assertEquals("byqq", result.getLoginWay());
        assertTrue(result.getRequirePhoneBinding());

        verify(userMapper, times(1)).insertSelective(any(User.class));
        verify(userThirdPartyAuthMapper, times(1)).insertSelective(any(UserThirdPartyAuth.class));
    }

    @Test
    @DisplayName("Apple登录 - 新用户自动注册")
    void testAppleLogin_NewUserAutoRegister() {
        // Given
        when(userThirdPartyAuthMapper.selectByAppleUserId(anyString())).thenReturn(null);
        when(userMapper.insertSelective(any(User.class))).thenReturn(1);
        when(userThirdPartyAuthMapper.insertSelective(any(UserThirdPartyAuth.class))).thenReturn(1);

        // When
        TokenDto result = extendedAuthService.thirdPartyLogin(appleLoginDto);

        // Then
        assertNotNull(result);
        assertNotNull(result.getAccessToken());
        assertEquals("byappleid", result.getLoginWay());
        assertTrue(result.getRequirePhoneBinding());

        verify(userMapper, times(1)).insertSelective(any(User.class));
        verify(userThirdPartyAuthMapper, times(1)).insertSelective(any(UserThirdPartyAuth.class));
    }

    @Test
    @DisplayName("第三方登录 - 用户名生成规则测试")
    void testUsernameGeneration() {
        // Given
        when(userThirdPartyAuthMapper.selectByWechatUnionId(anyString())).thenReturn(null);
        when(userThirdPartyAuthMapper.selectByWechatOpenId(anyString())).thenReturn(null);
        when(userMapper.insertSelective(any(User.class))).thenAnswer(invocation -> {
            User user = invocation.getArgument(0);
            // 验证用户名生成规则
            assertTrue(user.getUsername().startsWith("wx_"));
            assertTrue(user.getUsername().contains(wechatLoginDto.getUnionId()) || 
                      user.getUsername().contains(wechatLoginDto.getOpenId()));
            return 1;
        });
        when(userThirdPartyAuthMapper.insertSelective(any(UserThirdPartyAuth.class))).thenReturn(1);

        // When
        extendedAuthService.thirdPartyLogin(wechatLoginDto);

        // Then
        verify(userMapper, times(1)).insertSelective(any(User.class));
    }

    @Test
    @DisplayName("第三方登录 - 参数验证")
    void testParameterValidation() {
        // Given - 缺少必要参数
        ThirdPartyLoginDto invalidDto = new ThirdPartyLoginDto();
        invalidDto.setLoginWay("byweixin");
        // 缺少openId

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            extendedAuthService.thirdPartyLogin(invalidDto);
        });
    }

    @Test
    @DisplayName("第三方登录 - 不支持的登录方式")
    void testUnsupportedLoginWay() {
        // Given
        ThirdPartyLoginDto invalidDto = new ThirdPartyLoginDto();
        invalidDto.setLoginWay("unsupported");
        invalidDto.setOpenId("test_openid");

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            extendedAuthService.thirdPartyLogin(invalidDto);
        });
    }

    @Test
    @DisplayName("第三方登录 - 用户状态检查")
    void testUserStatusCheck() {
        // Given - 已有用户但状态为禁用
        User disabledUser = createTestUser();
        disabledUser.setStatus(0); // 禁用状态
        UserThirdPartyAuth existingAuth = createTestThirdPartyAuth(disabledUser.getId());

        when(userThirdPartyAuthMapper.selectByWechatUnionId(anyString())).thenReturn(existingAuth);
        when(userMapper.selectByPrimaryKey(disabledUser.getId())).thenReturn(disabledUser);

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            extendedAuthService.thirdPartyLogin(wechatLoginDto);
        });
    }

    @Test
    @DisplayName("第三方登录 - 优先使用UnionID查找用户")
    void testUnionIdPriority() {
        // Given - 通过UnionID能找到用户
        User existingUser = createTestUser();
        UserThirdPartyAuth existingAuth = createTestThirdPartyAuth(existingUser.getId());

        when(userThirdPartyAuthMapper.selectByWechatUnionId(wechatLoginDto.getUnionId())).thenReturn(existingAuth);
        when(userMapper.selectByPrimaryKey(existingUser.getId())).thenReturn(existingUser);
        when(userThirdPartyAuthMapper.updateByPrimaryKeySelective(any(UserThirdPartyAuth.class))).thenReturn(1);

        // When
        TokenDto result = extendedAuthService.thirdPartyLogin(wechatLoginDto);

        // Then
        assertNotNull(result);
        assertEquals(existingUser.getId(), result.getUserId());

        // 验证优先使用UnionID查找，不会调用OpenID查找
        verify(userThirdPartyAuthMapper, times(1)).selectByWechatUnionId(wechatLoginDto.getUnionId());
        verify(userThirdPartyAuthMapper, never()).selectByWechatOpenId(anyString());
    }

    @Test
    @DisplayName("第三方登录 - OpenID降级查找")
    void testOpenIdFallback() {
        // Given - UnionID找不到，但OpenID能找到
        User existingUser = createTestUser();
        UserThirdPartyAuth existingAuth = createTestThirdPartyAuth(existingUser.getId());

        when(userThirdPartyAuthMapper.selectByWechatUnionId(wechatLoginDto.getUnionId())).thenReturn(null);
        when(userThirdPartyAuthMapper.selectByWechatOpenId(wechatLoginDto.getOpenId())).thenReturn(existingAuth);
        when(userMapper.selectByPrimaryKey(existingUser.getId())).thenReturn(existingUser);
        when(userThirdPartyAuthMapper.updateByPrimaryKeySelective(any(UserThirdPartyAuth.class))).thenReturn(1);

        // When
        TokenDto result = extendedAuthService.thirdPartyLogin(wechatLoginDto);

        // Then
        assertNotNull(result);
        assertEquals(existingUser.getId(), result.getUserId());

        // 验证先尝试UnionID，再尝试OpenID
        verify(userThirdPartyAuthMapper, times(1)).selectByOpenId(wechatLoginDto.getUnionId());
        verify(userThirdPartyAuthMapper, times(1)).selectByWechatOpenId(wechatLoginDto.getOpenId());
    }

    // 辅助方法
    private User createTestUser() {
        User user = new User();
        user.setId(12345L);
        user.setUsername("wx_test_user");
        user.setNickname("测试用户");
        user.setAccountStatus(1); // 正常状态
        user.setRequirePhoneBinding(0);
        return user;
    }

    private UserThirdPartyAuth createTestThirdPartyAuth(Long userId) {
        UserThirdPartyAuth auth = new UserThirdPartyAuth();
        auth.setId(1L);
        auth.setUserId(userId);
        auth.setOpenId("wx_unionid_789012");
        return auth;
    }
}
