package com.vida.xinye.x2.service;

import com.vida.xinye.x2.component.JwtTokenProvider;
import com.vida.xinye.x2.dto.TokenDto;
import com.vida.xinye.x2.dto.param.EmailLoginDto;
import com.vida.xinye.x2.dto.param.PhonePasswordLoginDto;
import com.vida.xinye.x2.dto.param.SetPasswordDto;
import com.vida.xinye.x2.dto.param.ThirdPartyLoginDto;
import com.vida.xinye.x2.mbg.mapper.UserLoginHistoryMapper;
import com.vida.xinye.x2.mbg.mapper.UserMapper;
import com.vida.xinye.x2.mbg.model.User;
import com.vida.xinye.x2.service.CaptchaService;
import com.vida.xinye.x2.service.RedisService;
import com.vida.xinye.x2.service.impl.ExtendedAuthServiceImpl;
import com.vida.xinye.x2.service.impl.LoginSecurityServiceImpl;
import com.vida.xinye.x2.service.impl.ThirdPartyLoginService;
import com.vida.xinye.x2.service.impl.UserCacheServiceImpl;
import com.vida.xinye.x2.validator.AccountSecurityValidator;
import com.vida.xinye.x2.validator.PasswordStrengthValidator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ExtendedAuthService单元测试
 * 测试核心登录功能的正确性和稳定性
 *
 * <AUTHOR>
 * @date 2025/08/12
 */
@ExtendWith(MockitoExtension.class)
class ExtendedAuthServiceTest {

    @Mock
    private UserMapper userMapper;


    @Mock
    private UserLoginHistoryMapper userLoginHistoryMapper;

    @Mock
    private PasswordEncoder passwordEncoder;

    @Mock
    private JwtTokenProvider jwtTokenProvider;

    @Mock
    private CaptchaService captchaService;

    @Mock
    private ThirdPartyLoginService thirdPartyLoginService;

    @Mock
    private AccountSecurityValidator accountSecurityValidator;

    @Mock
    private PasswordStrengthValidator passwordStrengthValidator;

    @Mock
    private RedisService redisService;

    @Mock
    private LoginSecurityServiceImpl loginSecurityService;

    @Mock
    private UserCacheServiceImpl userCacheService;

    @InjectMocks
    private ExtendedAuthServiceImpl extendedAuthService;

    private User testUser;
    private EmailLoginDto emailLoginDto;
    private PhonePasswordLoginDto phonePasswordLoginDto;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        testUser = new User();
        testUser.setId(1L);
        testUser.setUsername("<EMAIL>");
        testUser.setEmail("<EMAIL>");
        testUser.setPhone("***********");
        testUser.setPassword("$2a$10$encodedPassword");
        testUser.setNickname("测试用户");
        testUser.setAccountStatus(1);
        testUser.setCreateTime(new Date());
        testUser.setUpdateTime(new Date());

        // 邮箱登录DTO
        emailLoginDto = new EmailLoginDto();
        emailLoginDto.setEmail("<EMAIL>");
        emailLoginDto.setLoginMethod("password");
        emailLoginDto.setPassword("123456");

        // 手机号密码登录DTO
        phonePasswordLoginDto = new PhonePasswordLoginDto();
        phonePasswordLoginDto.setPhone("***********");
        phonePasswordLoginDto.setPassword("123456");
    }

    @Test
    void testEmailPasswordLogin_Success() {
        // Given
        when(accountSecurityValidator.isValidEmail(anyString())).thenReturn(true);
        when(loginSecurityService.canAttemptLogin(anyString(), anyString())).thenReturn(true);
        when(userMapper.selectByExample(any())).thenReturn(java.util.Arrays.asList(testUser));
        when(passwordEncoder.matches(anyString(), anyString())).thenReturn(true);
//        when(jwtTokenProvider.generateToken(anyString(), anyLong())).thenReturn("mock_access_token");
//        when(jwtTokenProvider.generateRefreshToken(anyString(), anyLong())).thenReturn("mock_refresh_token");

        // When
        try {
            TokenDto result = extendedAuthService.emailLogin(emailLoginDto);

            // Then
            assertNotNull(result);
            assertEquals(testUser.getId(), result.getUserId());
            assertEquals(testUser.getUsername(), result.getUsername());
            assertNotNull(result.getAccessToken());
            verify(userMapper, times(1)).selectByExample(any());
            verify(passwordEncoder, times(1)).matches(anyString(), anyString());
            verify(loginSecurityService, times(1)).clearLoginFailures(anyString(), anyString());
        } catch (Exception e) {
            // 如果方法抛出异常，我们需要处理
            fail("邮箱密码登录应该成功，但抛出了异常: " + e.getMessage());
        }
    }

    @Test
    void testEmailPasswordLogin_InvalidEmail() {
        // Given
        when(accountSecurityValidator.isValidEmail(anyString())).thenReturn(false);

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            extendedAuthService.emailLogin(emailLoginDto);
        });

        verify(userMapper, never()).selectByExample(any());
        verify(loginSecurityService, never()).canAttemptLogin(anyString(), anyString());
    }

    @Test
    void testEmailPasswordLogin_UserNotFound() {
        // Given
        when(accountSecurityValidator.isValidEmail(anyString())).thenReturn(true);
        when(loginSecurityService.canAttemptLogin(anyString(), anyString())).thenReturn(true);
        when(userMapper.selectByExample(any())).thenReturn(java.util.Collections.emptyList());

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            extendedAuthService.emailLogin(emailLoginDto);
        });

        verify(loginSecurityService, times(1)).recordLoginFailure(anyString(), anyString(), anyString());
    }

    @Test
    void testEmailPasswordLogin_WrongPassword() {
        // Given
        when(accountSecurityValidator.isValidEmail(anyString())).thenReturn(true);
        when(loginSecurityService.canAttemptLogin(anyString(), anyString())).thenReturn(true);
        when(userMapper.selectByExample(any())).thenReturn(java.util.Arrays.asList(testUser));
        when(passwordEncoder.matches(anyString(), anyString())).thenReturn(false);

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            extendedAuthService.emailLogin(emailLoginDto);
        });

        verify(loginSecurityService, times(1)).recordLoginFailure(anyString(), anyString(), anyString());
    }

    @Test
    void testEmailCaptchaLogin_Success() {
        // Given
        emailLoginDto.setLoginMethod("captcha");
        emailLoginDto.setPassword(null);
        emailLoginDto.setCaptcha("123456");

        when(accountSecurityValidator.isValidEmail(anyString())).thenReturn(true);
        when(loginSecurityService.canAttemptLogin(anyString(), anyString())).thenReturn(true);
        when(userMapper.selectByExample(any())).thenReturn(java.util.Arrays.asList(testUser));
        when(captchaService.verifyCode(anyString(), anyString())).thenReturn(true);
//        when(jwtTokenProvider.generateToken(anyString(), anyLong())).thenReturn("mock_access_token");
//        when(jwtTokenProvider.generateRefreshToken(anyString(), anyLong())).thenReturn("mock_refresh_token");

        // When
        TokenDto result = extendedAuthService.emailLogin(emailLoginDto);

        // Then
        assertNotNull(result);
        assertEquals(testUser.getId(), result.getUserId());
        verify(captchaService, times(1)).verifyCode(anyString(), anyString());
        verify(loginSecurityService, times(1)).clearLoginFailures(anyString(), anyString());
    }

    @Test
    void testPhonePasswordLogin_Success() {
        // Given
        when(accountSecurityValidator.isValidPhone(anyString())).thenReturn(true);
        when(loginSecurityService.canAttemptLogin(anyString(), anyString())).thenReturn(true);
        when(userMapper.selectByExample(any())).thenReturn(java.util.Arrays.asList(testUser));
        when(passwordEncoder.matches(anyString(), anyString())).thenReturn(true);
//        when(jwtTokenProvider.generateToken(anyString(), anyLong())).thenReturn("mock_access_token");
//        when(jwtTokenProvider.generateRefreshToken(anyString(), anyLong())).thenReturn("mock_refresh_token");

        // When
        TokenDto result = extendedAuthService.phonePasswordLogin(phonePasswordLoginDto);

        // Then
        assertNotNull(result);
        assertEquals(testUser.getId(), result.getUserId());
        assertEquals(testUser.getUsername(), result.getUsername());
        verify(passwordEncoder, times(1)).matches(anyString(), anyString());
        verify(loginSecurityService, times(1)).clearLoginFailures(anyString(), anyString());
    }

    @Test
    void testPhonePasswordLogin_InvalidPhone() {
        // Given
        when(accountSecurityValidator.isValidPhone(anyString())).thenReturn(false);

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            extendedAuthService.phonePasswordLogin(phonePasswordLoginDto);
        });

        verify(loginSecurityService, never()).canAttemptLogin(anyString(), anyString());
    }

    @Test
    void testSetPassword_Success() {
        // Given
        SetPasswordDto setPasswordDto = new SetPasswordDto();
        setPasswordDto.setNewPassword("newPassword123");
        setPasswordDto.setConfirmPassword("newPassword123");
        setPasswordDto.setOldPassword("123456");

        when(userMapper.selectByPrimaryKey(anyLong())).thenReturn(testUser);
        when(passwordStrengthValidator.validatePassword(anyString()))
            .thenReturn(new PasswordStrengthValidator.PasswordValidationResult(true, "密码强度符合要求"));
        when(passwordEncoder.matches(anyString(), anyString())).thenReturn(true);
        when(passwordEncoder.encode(anyString())).thenReturn("$2a$10$newEncodedPassword");
        when(userMapper.updateByPrimaryKeySelective(any())).thenReturn(1);

        // When
        boolean result = extendedAuthService.setPassword(1L, setPasswordDto);

        // Then
        assertTrue(result);
        verify(passwordStrengthValidator, times(1)).validatePassword("newPassword123");
        verify(passwordEncoder, times(1)).encode("newPassword123");
        verify(userMapper, times(1)).updateByPrimaryKeySelective(any());
    }

    @Test
    void testSetPassword_PasswordMismatch() {
        // Given
        SetPasswordDto setPasswordDto = new SetPasswordDto();
        setPasswordDto.setNewPassword("newPassword123");
        setPasswordDto.setConfirmPassword("differentPassword");

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            extendedAuthService.setPassword(1L, setPasswordDto);
        });
        
        verify(userMapper, never()).updateByPrimaryKeySelective(any());
    }

    @Test
    void testThirdPartyLogin_NewUser() {
        // Given
        ThirdPartyLoginDto thirdPartyDto = new ThirdPartyLoginDto();
        thirdPartyDto.setLoginWay("wechat");
        thirdPartyDto.setOpenId("wx_openid_123");
        thirdPartyDto.setUnionId("wx_unionid_123");
        thirdPartyDto.setNickname("微信用户");
        thirdPartyDto.setHeadPic("http://avatar.url");

        doNothing().when(thirdPartyLoginService).checkThirdLoginStatus(any(ThirdPartyLoginDto.class));
        when(userMapper.selectByExample(any())).thenReturn(java.util.Collections.emptyList());
        when(userMapper.insertSelective(any())).thenReturn(1);
        when(userMapper.selectByPrimaryKey(anyLong())).thenReturn(testUser);
//        when(jwtTokenProvider.generateToken(anyString(), anyLong())).thenReturn("mock_access_token");
//        when(jwtTokenProvider.generateRefreshToken(anyString(), anyLong())).thenReturn("mock_refresh_token");

        // When
        TokenDto result = extendedAuthService.thirdPartyLogin(thirdPartyDto);

        // Then
        assertNotNull(result);
        verify(thirdPartyLoginService, times(1)).checkThirdLoginStatus(any());
        verify(userMapper, times(1)).insertSelective(any());
    }
}
