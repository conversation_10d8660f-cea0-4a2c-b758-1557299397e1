package com.vida.xinye.x2.service;

import com.vida.xinye.x2.adapter.QuestionCuttingAdapter;
import com.vida.xinye.x2.config.QuestionCuttingConfig;
import com.vida.xinye.x2.dto.QuestionCuttingResultDto;
import com.vida.xinye.x2.dto.param.QuestionCuttingParam;
import com.vida.xinye.x2.enums.QuestionCuttingEnum;
import com.vida.xinye.x2.service.impl.QuestionCuttingServiceImpl;
import com.vida.xinye.x2.strategy.CuttingStrategyManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockMultipartFile;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * 智能切题服务测试
 *
 * <AUTHOR>
 * @date 2025/08/14
 */
@ExtendWith(MockitoExtension.class)
class QuestionCuttingServiceTest {

    @Mock
    private QuestionCuttingConfig config;

    @Mock
    private CuttingStrategyManager strategyManager;

    @Mock
    private List<QuestionCuttingAdapter> adapters;

    @Mock
    private QuestionCuttingAdapter mockAdapter;

    @InjectMocks
    private QuestionCuttingServiceImpl questionCuttingService;

    private QuestionCuttingParam testParam;
    private MockMultipartFile testImageFile;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        testImageFile = new MockMultipartFile(
            "test.jpg", 
            "test.jpg", 
            "image/jpeg", 
            "test image content".getBytes()
        );

        testParam = new QuestionCuttingParam();
        testParam.setImageFile(testImageFile);
        testParam.setStrategy("BEST_QUALITY");
        testParam.setSubject("math");
        testParam.setGrade("junior_1");

        // 配置Mock
        QuestionCuttingConfig.CommonConfig commonConfig = new QuestionCuttingConfig.CommonConfig();
        commonConfig.setMaxImageSize(10 * 1024 * 1024); // 10MB
        commonConfig.setSupportedFormats(new String[]{"jpg", "jpeg", "png", "bmp", "gif"});
        
        when(config.getCommon()).thenReturn(commonConfig);
        when(mockAdapter.getProvider()).thenReturn(QuestionCuttingEnum.Provider.TIANRANG);
        when(mockAdapter.isAvailable()).thenReturn(true);
    }

    @Test
    void testCutQuestion_Success() {
        // 准备期望结果
        QuestionCuttingResultDto expectedResult = new QuestionCuttingResultDto();
        expectedResult.setSuccess(true);
        expectedResult.setRequestId("test-request-id");
        expectedResult.setProvider("tianrang");
        expectedResult.setCreateTime(new Date());

        // 配置Mock行为
        when(strategyManager.executeCutting(any(), anyString(), anyString()))
            .thenReturn(expectedResult);

        // 执行测试
        QuestionCuttingResultDto result = questionCuttingService.cutQuestion(testParam);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.getSuccess());
        assertEquals("tianrang", result.getProvider());
        
        // 验证Mock调用
        verify(strategyManager, times(1)).executeCutting(any(), eq("BEST_QUALITY"), isNull());
    }

    @Test
    void testCutQuestion_NullParam() {
        // 执行测试并验证异常
        assertThrows(RuntimeException.class, () -> {
            questionCuttingService.cutQuestion(null);
        });
    }

    @Test
    void testCutQuestion_NoImageProvided() {
        // 准备无图片的参数
        QuestionCuttingParam emptyParam = new QuestionCuttingParam();
        emptyParam.setStrategy("BEST_QUALITY");

        // 执行测试并验证异常
        assertThrows(RuntimeException.class, () -> {
            questionCuttingService.cutQuestion(emptyParam);
        });
    }

    @Test
    void testCutQuestion_UnsupportedImageFormat() {
        // 准备不支持格式的图片
        MockMultipartFile unsupportedFile = new MockMultipartFile(
            "test.txt", 
            "test.txt", 
            "text/plain", 
            "test content".getBytes()
        );

        testParam.setImageFile(unsupportedFile);

        // 执行测试并验证异常
        assertThrows(RuntimeException.class, () -> {
            questionCuttingService.cutQuestion(testParam);
        });
    }

    @Test
    void testCutQuestion_ImageTooLarge() {
        // 配置小的最大文件大小
        QuestionCuttingConfig.CommonConfig commonConfig = new QuestionCuttingConfig.CommonConfig();
        commonConfig.setMaxImageSize(10); // 10 bytes
        commonConfig.setSupportedFormats(new String[]{"jpg", "jpeg", "png", "bmp", "gif"});
        
        when(config.getCommon()).thenReturn(commonConfig);

        // 执行测试并验证异常
        assertThrows(RuntimeException.class, () -> {
            questionCuttingService.cutQuestion(testParam);
        });
    }

    @Test
    void testCutQuestionByUrl_Success() {
        // 准备期望结果
        QuestionCuttingResultDto expectedResult = new QuestionCuttingResultDto();
        expectedResult.setSuccess(true);
        expectedResult.setRequestId("test-request-id");

        // 配置Mock行为
        when(strategyManager.executeCutting(any(), anyString(), anyString()))
            .thenReturn(expectedResult);

        // 执行测试
        QuestionCuttingResultDto result = questionCuttingService.cutQuestionByUrl(
            "https://example.com/test.jpg", 
            "BEST_QUALITY", 
            null, 
            "math", 
            "junior_1", 
            false, 
            true
        );

        // 验证结果
        assertNotNull(result);
        assertTrue(result.getSuccess());
    }

    @Test
    void testCutQuestionByBase64_Success() {
        // 准备期望结果
        QuestionCuttingResultDto expectedResult = new QuestionCuttingResultDto();
        expectedResult.setSuccess(true);
        expectedResult.setRequestId("test-request-id");

        // 配置Mock行为
        when(strategyManager.executeCutting(any(), anyString(), anyString()))
            .thenReturn(expectedResult);

        // 执行测试
        QuestionCuttingResultDto result = questionCuttingService.cutQuestionByBase64(
            "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==", 
            "BEST_QUALITY", 
            null, 
            "math", 
            "junior_1", 
            false, 
            true
        );

        // 验证结果
        assertNotNull(result);
        assertTrue(result.getSuccess());
    }

    @Test
    void testGetSupportedProviders() {
        // 配置Mock
        when(adapters.stream()).thenReturn(Arrays.asList(mockAdapter).stream());

        // 执行测试
        List<String> providers = questionCuttingService.getSupportedProviders();

        // 验证结果
        assertNotNull(providers);
        assertTrue(providers.contains("tianrang"));
    }

    @Test
    void testGetSupportedStrategies() {
        // 执行测试
        List<String> strategies = questionCuttingService.getSupportedStrategies();

        // 验证结果
        assertNotNull(strategies);
        assertTrue(strategies.contains("BEST_QUALITY"));
        assertTrue(strategies.contains("FASTEST"));
        assertTrue(strategies.contains("AGGREGATED"));
        assertTrue(strategies.contains("SPECIFIED"));
        assertTrue(strategies.contains("ROUND_ROBIN"));
    }

    @Test
    void testCheckProviderStatus_Available() {
        // 配置Mock
        when(adapters.stream()).thenReturn(Arrays.asList(mockAdapter).stream());

        // 执行测试
        boolean available = questionCuttingService.checkProviderStatus("tianrang");

        // 验证结果
        assertTrue(available);
    }

    @Test
    void testCheckProviderStatus_NotAvailable() {
        // 配置Mock
        when(mockAdapter.isAvailable()).thenReturn(false);
        when(adapters.stream()).thenReturn(Arrays.asList(mockAdapter).stream());

        // 执行测试
        boolean available = questionCuttingService.checkProviderStatus("tianrang");

        // 验证结果
        assertFalse(available);
    }

    @Test
    void testCheckProviderStatus_ProviderNotFound() {
        // 配置Mock
        when(adapters.stream()).thenReturn(Arrays.asList(mockAdapter).stream());

        // 执行测试
        boolean available = questionCuttingService.checkProviderStatus("unknown");

        // 验证结果
        assertFalse(available);
    }

    @Test
    void testGetProviderStats() {
        // 配置Mock
        when(adapters.stream()).thenReturn(Arrays.asList(mockAdapter).stream());

        // 执行测试
        var stats = questionCuttingService.getProviderStats();

        // 验证结果
        assertNotNull(stats);
        assertTrue(stats.containsKey("tianrang"));
        
        var tianrangStats = (java.util.Map<String, Object>) stats.get("tianrang");
        assertTrue(tianrangStats.containsKey("available"));
        assertTrue(tianrangStats.containsKey("priority"));
        assertTrue(tianrangStats.containsKey("qualityScore"));
        assertTrue(tianrangStats.containsKey("averageResponseTime"));
    }
}
