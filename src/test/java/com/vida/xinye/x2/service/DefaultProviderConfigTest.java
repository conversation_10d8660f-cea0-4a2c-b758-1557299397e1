package com.vida.xinye.x2.service;

import com.vida.xinye.x2.config.QuestionCuttingConfig;
import com.vida.xinye.x2.service.impl.QuestionCuttingServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 默认提供商配置测试
 *
 * <AUTHOR>
 * @date 2025/08/14
 */
@ExtendWith(MockitoExtension.class)
class DefaultProviderConfigTest {

    @Mock
    private QuestionCuttingConfig config;

    @Mock
    private QuestionCuttingConfig.CommonConfig commonConfig;

    @InjectMocks
    private QuestionCuttingServiceImpl questionCuttingService;

    @BeforeEach
    void setUp() {
        when(config.getCommon()).thenReturn(commonConfig);
    }

    @Test
    void testApplyDefaultProvider_WithConfiguredProvider() throws Exception {
        // 配置默认提供商为youdao
        when(commonConfig.getDefaultProvider()).thenReturn("youdao");

        // 使用反射调用私有方法
        Method method = QuestionCuttingServiceImpl.class.getDeclaredMethod("applyDefaultProvider", String.class, String.class);
        method.setAccessible(true);

        // 测试：没有指定provider，应该使用配置的默认值
        String result = (String) method.invoke(questionCuttingService, null, "BEST_QUALITY");
        assertEquals("youdao", result);

        // 测试：指定了provider，应该使用指定值
        String result2 = (String) method.invoke(questionCuttingService, "aliyun", "BEST_QUALITY");
        assertEquals("aliyun", result2);
    }

    @Test
    void testApplyDefaultProvider_WithoutConfiguredProvider() throws Exception {
        // 没有配置默认提供商
        when(commonConfig.getDefaultProvider()).thenReturn(null);

        Method method = QuestionCuttingServiceImpl.class.getDeclaredMethod("applyDefaultProvider", String.class, String.class);
        method.setAccessible(true);

        // 测试：没有配置默认提供商，应该使用常量默认值
        String result = (String) method.invoke(questionCuttingService, null, "BEST_QUALITY");
        assertEquals("tianrang", result); // 常量中的默认值
    }

    @Test
    void testApplyDefaultProvider_WithEmptyConfiguredProvider() throws Exception {
        // 配置了空字符串
        when(commonConfig.getDefaultProvider()).thenReturn("");

        Method method = QuestionCuttingServiceImpl.class.getDeclaredMethod("applyDefaultProvider", String.class, String.class);
        method.setAccessible(true);

        // 测试：配置为空字符串，应该使用常量默认值
        String result = (String) method.invoke(questionCuttingService, null, "BEST_QUALITY");
        assertEquals("tianrang", result); // 常量中的默认值
    }

    @Test
    void testApplyDefaultStrategy_WithConfiguredStrategy() throws Exception {
        // 配置默认策略为FASTEST
        when(commonConfig.getDefaultStrategy()).thenReturn("FASTEST");

        Method method = QuestionCuttingServiceImpl.class.getDeclaredMethod("applyDefaultStrategy", String.class);
        method.setAccessible(true);

        // 测试：没有指定strategy，应该使用配置的默认值
        String result = (String) method.invoke(questionCuttingService, null);
        assertEquals("FASTEST", result);

        // 测试：指定了strategy，应该使用指定值
        String result2 = (String) method.invoke(questionCuttingService, "AGGREGATED");
        assertEquals("AGGREGATED", result2);
    }

    @Test
    void testApplyDefaultStrategy_WithoutConfiguredStrategy() throws Exception {
        // 没有配置默认策略
        when(commonConfig.getDefaultStrategy()).thenReturn(null);

        Method method = QuestionCuttingServiceImpl.class.getDeclaredMethod("applyDefaultStrategy", String.class);
        method.setAccessible(true);

        // 测试：没有配置默认策略，应该使用常量默认值
        String result = (String) method.invoke(questionCuttingService, null);
        assertEquals("BEST_QUALITY", result); // 常量中的默认值
    }

    @Test
    void testConfigNull() throws Exception {
        // 测试config.getCommon()返回null的情况
        when(config.getCommon()).thenReturn(null);

        Method providerMethod = QuestionCuttingServiceImpl.class.getDeclaredMethod("applyDefaultProvider", String.class, String.class);
        providerMethod.setAccessible(true);

        // 应该使用常量默认值
        String result = (String) providerMethod.invoke(questionCuttingService, null, "BEST_QUALITY");
        assertEquals("tianrang", result);
    }
}
