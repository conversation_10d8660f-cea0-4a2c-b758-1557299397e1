package com.vida.xinye.x2.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.test.util.ReflectionTestUtils;

import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * EmailService单元测试
 * 测试邮件发送功能
 *
 * <AUTHOR>
 * @date 2025/08/12
 */
@ExtendWith(MockitoExtension.class)
class EmailServiceTest {

    @Mock
    private JavaMailSender mailSender;

    @Mock
    private MimeMessage mimeMessage;

    @InjectMocks
    private EmailService emailService;

    @BeforeEach
    void setUp() {
        // 设置私有字段值
        ReflectionTestUtils.setField(emailService, "fromEmail", "<EMAIL>");
    }

    @Test
    void testSendEmail_Success() throws MessagingException {
        // Given
        String to = "<EMAIL>";
        String subject = "测试邮件";
        String text = "这是一封测试邮件";
        
        when(mailSender.createMimeMessage()).thenReturn(mimeMessage);

        // When
        assertDoesNotThrow(() -> {
            emailService.sendEmail(to, subject, text);
        });

        // Then
        verify(mailSender, times(1)).createMimeMessage();
        verify(mailSender, times(1)).send(mimeMessage);
    }

    @Test
    void testSendSimpleEmail_Success() {
        // Given
        String to = "<EMAIL>";
        String subject = "测试邮件";
        String content = "这是一封测试邮件";
        
        when(mailSender.createMimeMessage()).thenReturn(mimeMessage);

        // When
        boolean result = emailService.sendSimpleEmail(to, subject, content);

        // Then
        assertTrue(result);
        verify(mailSender, times(1)).createMimeMessage();
        verify(mailSender, times(1)).send(mimeMessage);
    }

    @Test
    void testSendSimpleEmail_Failure() {
        // Given
        String to = "<EMAIL>";
        String subject = "测试邮件";
        String content = "这是一封测试邮件";
        
        when(mailSender.createMimeMessage()).thenReturn(mimeMessage);
        doThrow(new RuntimeException("邮件发送失败")).when(mailSender).send(any(MimeMessage.class));

        // When
        boolean result = emailService.sendSimpleEmail(to, subject, content);

        // Then
        assertFalse(result);
        verify(mailSender, times(1)).createMimeMessage();
        verify(mailSender, times(1)).send(mimeMessage);
    }

    @Test
    void testSendVerificationCode_LoginPurpose_Success() {
        // Given
        String email = "<EMAIL>";
        String code = "123456";
        String purpose = "login";
        
        when(mailSender.createMimeMessage()).thenReturn(mimeMessage);

        // When
        boolean result = emailService.sendVerificationCode(email, code, purpose);

        // Then
        assertTrue(result);
        verify(mailSender, times(1)).createMimeMessage();
        verify(mailSender, times(1)).send(mimeMessage);
    }

    @Test
    void testSendVerificationCode_RegisterPurpose_Success() {
        // Given
        String email = "<EMAIL>";
        String code = "123456";
        String purpose = "register";
        
        when(mailSender.createMimeMessage()).thenReturn(mimeMessage);

        // When
        boolean result = emailService.sendVerificationCode(email, code, purpose);

        // Then
        assertTrue(result);
        verify(mailSender, times(1)).createMimeMessage();
        verify(mailSender, times(1)).send(mimeMessage);
    }

    @Test
    void testSendVerificationCode_Failure() {
        // Given
        String email = "<EMAIL>";
        String code = "123456";
        String purpose = "login";
        
        when(mailSender.createMimeMessage()).thenReturn(mimeMessage);
        doThrow(new RuntimeException("邮件发送失败")).when(mailSender).send(any(MimeMessage.class));

        // When
        boolean result = emailService.sendVerificationCode(email, code, purpose);

        // Then
        assertFalse(result);
        verify(mailSender, times(1)).createMimeMessage();
        verify(mailSender, times(1)).send(mimeMessage);
    }
}
