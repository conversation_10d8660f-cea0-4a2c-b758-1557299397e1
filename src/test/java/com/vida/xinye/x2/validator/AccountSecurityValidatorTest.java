package com.vida.xinye.x2.validator;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

/**
 * AccountSecurityValidator单元测试
 * 测试账号安全验证功能
 *
 * <AUTHOR>
 * @date 2025/08/12
 */
@ExtendWith(MockitoExtension.class)
class AccountSecurityValidatorTest {

    @InjectMocks
    private AccountSecurityValidator accountSecurityValidator;

    @BeforeEach
    void setUp() {
        // 初始化验证器
    }

    @Test
    void testIsValidEmail_ValidEmails() {
        // Given & When & Then
        assertTrue(accountSecurityValidator.isValidEmail("<EMAIL>"));
        assertTrue(accountSecurityValidator.isValidEmail("<EMAIL>"));
        assertTrue(accountSecurityValidator.isValidEmail("<EMAIL>"));
        assertTrue(accountSecurityValidator.isValidEmail("<EMAIL>"));
        assertTrue(accountSecurityValidator.isValidEmail("<EMAIL>"));
    }

    @Test
    void testIsValidEmail_InvalidEmails() {
        // Given & When & Then
        assertFalse(accountSecurityValidator.isValidEmail(null));
        assertFalse(accountSecurityValidator.isValidEmail(""));
        assertFalse(accountSecurityValidator.isValidEmail("   "));
        assertFalse(accountSecurityValidator.isValidEmail("invalid_email"));
        assertFalse(accountSecurityValidator.isValidEmail("@example.com"));
        assertFalse(accountSecurityValidator.isValidEmail("test@"));
        assertFalse(accountSecurityValidator.isValidEmail("<EMAIL>"));
        assertFalse(accountSecurityValidator.isValidEmail("test@.com"));
        assertFalse(accountSecurityValidator.isValidEmail("test@com"));
    }

    @Test
    void testIsValidPhone_ValidPhones() {
        // Given & When & Then
        assertTrue(accountSecurityValidator.isValidPhone("***********"));
        assertTrue(accountSecurityValidator.isValidPhone("***********"));
        assertTrue(accountSecurityValidator.isValidPhone("***********"));
        assertTrue(accountSecurityValidator.isValidPhone("***********"));
        assertTrue(accountSecurityValidator.isValidPhone("***********"));
    }

    @Test
    void testIsValidPhone_InvalidPhones() {
        // Given & When & Then
        assertFalse(accountSecurityValidator.isValidPhone(null));
        assertFalse(accountSecurityValidator.isValidPhone(""));
        assertFalse(accountSecurityValidator.isValidPhone("   "));
        assertFalse(accountSecurityValidator.isValidPhone("**********"));
        assertFalse(accountSecurityValidator.isValidPhone("***********"));
        assertFalse(accountSecurityValidator.isValidPhone("1380013800a"));
        assertFalse(accountSecurityValidator.isValidPhone("***********"));
        assertFalse(accountSecurityValidator.isValidPhone("************"));
    }

    @Test
    void testIsValidCaptcha_ValidCaptchas() {
        // Given & When & Then
        assertTrue(accountSecurityValidator.isValidCaptcha("123456"));
        assertTrue(accountSecurityValidator.isValidCaptcha("000000"));
        assertTrue(accountSecurityValidator.isValidCaptcha("999999"));
    }

    @Test
    void testIsValidCaptcha_InvalidCaptchas() {
        // Given & When & Then
        assertFalse(accountSecurityValidator.isValidCaptcha(null));
        assertFalse(accountSecurityValidator.isValidCaptcha(""));
        assertFalse(accountSecurityValidator.isValidCaptcha("   "));
        assertFalse(accountSecurityValidator.isValidCaptcha("12345")); // 太短
        assertFalse(accountSecurityValidator.isValidCaptcha("1234567")); // 太长
        assertFalse(accountSecurityValidator.isValidCaptcha("12 456")); // 包含空格
    }

    @Test
    void testValidateEmailFormat() {
        // 测试邮箱格式验证的边界情况
        assertTrue(accountSecurityValidator.isValidEmail("<EMAIL>"));
        assertFalse(accountSecurityValidator.isValidEmail("a@b"));
        assertFalse(accountSecurityValidator.isValidEmail("a.b"));
    }

    @Test
    void testValidatePhoneFormat() {
        // 测试手机号格式验证的边界情况
        assertTrue(accountSecurityValidator.isValidPhone("***********"));
        assertTrue(accountSecurityValidator.isValidPhone("***********"));
        assertFalse(accountSecurityValidator.isValidPhone("***********"));
        assertFalse(accountSecurityValidator.isValidPhone("***********"));
    }

    @Test
    void testValidateCaptchaFormat() {
        // 测试验证码格式验证的边界情况
        assertTrue(accountSecurityValidator.isValidCaptcha("123456"));
        assertTrue(accountSecurityValidator.isValidCaptcha("000000"));
        assertFalse(accountSecurityValidator.isValidCaptcha("12345"));
        assertFalse(accountSecurityValidator.isValidCaptcha("1234567"));
        assertFalse(accountSecurityValidator.isValidCaptcha("abcdef"));
    }

    @Test
    void testEmailValidation_SpecialCases() {
        // 测试特殊情况的邮箱验证
        assertTrue(accountSecurityValidator.isValidEmail("<EMAIL>"));
        assertTrue(accountSecurityValidator.isValidEmail("<EMAIL>"));
        assertFalse(accountSecurityValidator.isValidEmail("test@example"));
        assertFalse(accountSecurityValidator.isValidEmail("<EMAIL>"));
    }

    @Test
    void testPhoneValidation_SpecialCases() {
        // 测试特殊情况的手机号验证
        assertTrue(accountSecurityValidator.isValidPhone("**********8"));
        assertTrue(accountSecurityValidator.isValidPhone("***********"));
        assertFalse(accountSecurityValidator.isValidPhone("**********"));
        assertFalse(accountSecurityValidator.isValidPhone("************"));
    }
}
