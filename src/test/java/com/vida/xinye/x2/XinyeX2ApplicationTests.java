package com.vida.xinye.x2;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.security.crypto.password.PasswordEncoder;

@SpringBootTest
class XinyeX2ApplicationTests {

    @Value("${aliyun.log.accessKeyId}")
    private String accessKeyId;
    @Value("${aliyun.log.accessKeySecret}")
    private String accessKeySecret;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Test
    public void testAccessKeyId() {
        System.out.println("accessKeyId Value: " + accessKeyId);
        System.out.println("accessKeySecret Value: " + accessKeySecret);
    }

    @Test
    public void testPassword() {
        System.out.println("password: " + passwordEncoder.encode("123456"));
    }

    @Test
    void contextLoads() {
        System.out.println(DateUtil.format(DateTime.now(), "yyyyMMddHHmmss"));
    }

}
