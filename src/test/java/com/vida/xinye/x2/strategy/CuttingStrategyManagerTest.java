package com.vida.xinye.x2.strategy;

import com.vida.xinye.x2.adapter.QuestionCuttingAdapter;
import com.vida.xinye.x2.constant.QuestionCuttingConstant;
import com.vida.xinye.x2.dto.QuestionCuttingResultDto;
import com.vida.xinye.x2.dto.internal.ThirdPartyRequestDto;
import com.vida.xinye.x2.dto.internal.ThirdPartyResponseDto;
import com.vida.xinye.x2.enums.QuestionCuttingEnum;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 切题策略管理器测试
 *
 * <AUTHOR>
 * @date 2025/08/14
 */
@ExtendWith(MockitoExtension.class)
class CuttingStrategyManagerTest {

    @Mock
    private QuestionCuttingAdapter tianrangAdapter;

    @Mock
    private QuestionCuttingAdapter youdaoAdapter;

    @Mock
    private QuestionCuttingAdapter aliyunAdapter;

    @InjectMocks
    private CuttingStrategyManager strategyManager;

    private ThirdPartyRequestDto testRequest;
    private List<QuestionCuttingAdapter> adapters;

    @BeforeEach
    void setUp() {
        // 准备测试请求
        testRequest = new ThirdPartyRequestDto();
        testRequest.setRequestId("test-request-id");
        testRequest.setImageBase64("test-image-base64");

        // 配置适配器列表
        adapters = Arrays.asList(tianrangAdapter, youdaoAdapter, aliyunAdapter);
        
        // 使用反射设置adapters字段
        try {
            java.lang.reflect.Field adaptersField = CuttingStrategyManager.class.getDeclaredField("adapters");
            adaptersField.setAccessible(true);
            adaptersField.set(strategyManager, adapters);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        // 配置Mock适配器
        setupMockAdapters();
    }

    private void setupMockAdapters() {
        // 天壤适配器
        when(tianrangAdapter.getProvider()).thenReturn(QuestionCuttingEnum.Provider.TIANRANG);
        when(tianrangAdapter.isAvailable()).thenReturn(true);
        when(tianrangAdapter.getPriority()).thenReturn(10);
        when(tianrangAdapter.getQualityScore()).thenReturn(5);
        when(tianrangAdapter.getAverageResponseTime()).thenReturn(3000L);
        when(tianrangAdapter.validateRequest(any())).thenReturn(true);
        when(tianrangAdapter.preprocessRequest(any())).thenAnswer(invocation -> invocation.getArgument(0));
        when(tianrangAdapter.postprocessResponse(any())).thenAnswer(invocation -> invocation.getArgument(0));

        // 有道适配器
        when(youdaoAdapter.getProvider()).thenReturn(QuestionCuttingEnum.Provider.YOUDAO);
        when(youdaoAdapter.isAvailable()).thenReturn(true);
        when(youdaoAdapter.getPriority()).thenReturn(20);
        when(youdaoAdapter.getQualityScore()).thenReturn(4);
        when(youdaoAdapter.getAverageResponseTime()).thenReturn(4000L);
        when(youdaoAdapter.validateRequest(any())).thenReturn(true);
        when(youdaoAdapter.preprocessRequest(any())).thenAnswer(invocation -> invocation.getArgument(0));
        when(youdaoAdapter.postprocessResponse(any())).thenAnswer(invocation -> invocation.getArgument(0));

        // 阿里云适配器
        when(aliyunAdapter.getProvider()).thenReturn(QuestionCuttingEnum.Provider.ALIYUN);
        when(aliyunAdapter.isAvailable()).thenReturn(true);
        when(aliyunAdapter.getPriority()).thenReturn(30);
        when(aliyunAdapter.getQualityScore()).thenReturn(4);
        when(aliyunAdapter.getAverageResponseTime()).thenReturn(5000L);
        when(aliyunAdapter.validateRequest(any())).thenReturn(true);
        when(aliyunAdapter.preprocessRequest(any())).thenAnswer(invocation -> invocation.getArgument(0));
        when(aliyunAdapter.postprocessResponse(any())).thenAnswer(invocation -> invocation.getArgument(0));
    }

    @Test
    void testExecuteCutting_BestQualityStrategy() {
        // 准备成功的响应
        QuestionCuttingResultDto expectedResult = createSuccessResult("tianrang");
        
        when(tianrangAdapter.callThirdPartyApi(any())).thenReturn(createSuccessThirdPartyResponse());
        when(tianrangAdapter.convertToStandardResult(any(), anyString())).thenReturn(expectedResult);

        // 执行测试
        QuestionCuttingResultDto result = strategyManager.executeCutting(
            testRequest, 
            QuestionCuttingConstant.Strategy.BEST_QUALITY, 
            null
        );

        // 验证结果
        assertNotNull(result);
        assertTrue(result.getSuccess());
        assertEquals("tianrang", result.getProvider());

        // 验证调用了质量评分最高的适配器（天壤）
        verify(tianrangAdapter, times(1)).callThirdPartyApi(any());
        verify(youdaoAdapter, never()).callThirdPartyApi(any());
        verify(aliyunAdapter, never()).callThirdPartyApi(any());
    }

    @Test
    void testExecuteCutting_FastestStrategy() {
        // 准备成功的响应
        QuestionCuttingResultDto expectedResult = createSuccessResult("tianrang");
        
        when(tianrangAdapter.callThirdPartyApi(any())).thenReturn(createSuccessThirdPartyResponse());
        when(tianrangAdapter.convertToStandardResult(any(), anyString())).thenReturn(expectedResult);

        // 执行测试
        QuestionCuttingResultDto result = strategyManager.executeCutting(
            testRequest, 
            QuestionCuttingConstant.Strategy.FASTEST, 
            null
        );

        // 验证结果
        assertNotNull(result);
        assertTrue(result.getSuccess());
        assertEquals("tianrang", result.getProvider());

        // 验证调用了响应时间最短的适配器（天壤）
        verify(tianrangAdapter, times(1)).callThirdPartyApi(any());
        verify(youdaoAdapter, never()).callThirdPartyApi(any());
        verify(aliyunAdapter, never()).callThirdPartyApi(any());
    }

    @Test
    void testExecuteCutting_SpecifiedStrategy() {
        // 准备成功的响应
        QuestionCuttingResultDto expectedResult = createSuccessResult("youdao");
        
        when(youdaoAdapter.callThirdPartyApi(any())).thenReturn(createSuccessThirdPartyResponse());
        when(youdaoAdapter.convertToStandardResult(any(), anyString())).thenReturn(expectedResult);

        // 执行测试
        QuestionCuttingResultDto result = strategyManager.executeCutting(
            testRequest, 
            QuestionCuttingConstant.Strategy.SPECIFIED, 
            "youdao"
        );

        // 验证结果
        assertNotNull(result);
        assertTrue(result.getSuccess());
        assertEquals("youdao", result.getProvider());

        // 验证只调用了指定的适配器
        verify(tianrangAdapter, never()).callThirdPartyApi(any());
        verify(youdaoAdapter, times(1)).callThirdPartyApi(any());
        verify(aliyunAdapter, never()).callThirdPartyApi(any());
    }

    @Test
    void testExecuteCutting_SpecifiedStrategy_ProviderNotAvailable() {
        // 设置指定的提供商不可用
        when(youdaoAdapter.isAvailable()).thenReturn(false);

        // 准备天壤的成功响应（作为fallback）
        QuestionCuttingResultDto expectedResult = createSuccessResult("tianrang");
        
        when(tianrangAdapter.callThirdPartyApi(any())).thenReturn(createSuccessThirdPartyResponse());
        when(tianrangAdapter.convertToStandardResult(any(), anyString())).thenReturn(expectedResult);

        // 执行测试
        QuestionCuttingResultDto result = strategyManager.executeCutting(
            testRequest, 
            QuestionCuttingConstant.Strategy.SPECIFIED, 
            "youdao"
        );

        // 验证结果 - 应该fallback到默认策略
        assertNotNull(result);
        assertTrue(result.getSuccess());
        assertEquals("tianrang", result.getProvider());
    }

    @Test
    void testExecuteCutting_RoundRobinStrategy() {
        // 准备成功的响应
        QuestionCuttingResultDto expectedResult = createSuccessResult("tianrang");
        
        when(tianrangAdapter.callThirdPartyApi(any())).thenReturn(createSuccessThirdPartyResponse());
        when(tianrangAdapter.convertToStandardResult(any(), anyString())).thenReturn(expectedResult);

        // 执行测试
        QuestionCuttingResultDto result = strategyManager.executeCutting(
            testRequest, 
            QuestionCuttingConstant.Strategy.ROUND_ROBIN, 
            null
        );

        // 验证结果
        assertNotNull(result);
        assertTrue(result.getSuccess());
        
        // 验证调用了某个适配器
        verify(tianrangAdapter, atMost(1)).callThirdPartyApi(any());
        verify(youdaoAdapter, atMost(1)).callThirdPartyApi(any());
        verify(aliyunAdapter, atMost(1)).callThirdPartyApi(any());
    }

    @Test
    void testExecuteCutting_NoAvailableProviders() {
        // 设置所有适配器都不可用
        when(tianrangAdapter.isAvailable()).thenReturn(false);
        when(youdaoAdapter.isAvailable()).thenReturn(false);
        when(aliyunAdapter.isAvailable()).thenReturn(false);

        // 执行测试
        QuestionCuttingResultDto result = strategyManager.executeCutting(
            testRequest, 
            QuestionCuttingConstant.Strategy.BEST_QUALITY, 
            null
        );

        // 验证结果
        assertNotNull(result);
        assertFalse(result.getSuccess());
        assertEquals(QuestionCuttingConstant.ErrorCode.NO_AVAILABLE_PROVIDER, result.getErrorCode());
    }

    @Test
    void testExecuteCutting_UnknownStrategy() {
        // 准备成功的响应（应该fallback到默认策略）
        QuestionCuttingResultDto expectedResult = createSuccessResult("tianrang");
        
        when(tianrangAdapter.callThirdPartyApi(any())).thenReturn(createSuccessThirdPartyResponse());
        when(tianrangAdapter.convertToStandardResult(any(), anyString())).thenReturn(expectedResult);

        // 执行测试
        QuestionCuttingResultDto result = strategyManager.executeCutting(
            testRequest, 
            "UNKNOWN_STRATEGY", 
            null
        );

        // 验证结果 - 应该fallback到默认策略
        assertNotNull(result);
        assertTrue(result.getSuccess());
    }

    @Test
    void testExecuteCutting_AdapterException() {
        // 配置适配器抛出异常
        when(tianrangAdapter.callThirdPartyApi(any())).thenThrow(new RuntimeException("Network error"));

        // 执行测试
        QuestionCuttingResultDto result = strategyManager.executeCutting(
            testRequest, 
            QuestionCuttingConstant.Strategy.SPECIFIED, 
            "tianrang"
        );

        // 验证结果
        assertNotNull(result);
        assertFalse(result.getSuccess());
        assertEquals(QuestionCuttingConstant.ErrorCode.API_CALL_FAILED, result.getErrorCode());
        assertTrue(result.getErrorMessage().contains("适配器执行失败"));
    }

    @Test
    void testExecuteCutting_ValidationFailure() {
        // 配置验证失败
        when(tianrangAdapter.validateRequest(any())).thenReturn(false);

        // 执行测试
        QuestionCuttingResultDto result = strategyManager.executeCutting(
            testRequest, 
            QuestionCuttingConstant.Strategy.SPECIFIED, 
            "tianrang"
        );

        // 验证结果
        assertNotNull(result);
        assertFalse(result.getSuccess());
        assertTrue(result.getErrorMessage().contains("请求参数验证失败"));
    }

    private QuestionCuttingResultDto createSuccessResult(String provider) {
        QuestionCuttingResultDto result = new QuestionCuttingResultDto();
        result.setSuccess(true);
        result.setRequestId("test-request-id");
        result.setProvider(provider);
        result.setProcessingTime(1500L);
        result.setQualityScore(5);
        result.setConfidence(0.95);
        result.setCreateTime(new Date());
        return result;
    }

    private ThirdPartyResponseDto createSuccessThirdPartyResponse() {
        ThirdPartyResponseDto response = new ThirdPartyResponseDto();
        response.setSuccess(true);
        response.setRequestId("test-request-id");
        response.setResponseTime(1500L);
        return response;
    }
}
