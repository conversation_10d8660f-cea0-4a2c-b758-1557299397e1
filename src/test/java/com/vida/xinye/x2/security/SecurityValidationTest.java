package com.vida.xinye.x2.security;

import com.vida.xinye.x2.service.BlacklistService;
import com.vida.xinye.x2.service.CaptchaService;
import com.vida.xinye.x2.service.RedisService;
import com.vida.xinye.x2.service.impl.LoginSecurityServiceImpl;
import com.vida.xinye.x2.validator.AccountSecurityValidator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 安全验证测试
 * 验证登录安全机制的有效性
 *
 * <AUTHOR>
 * @date 2025/08/12
 */
@SpringBootTest
@ActiveProfiles("test")
@DisplayName("安全验证测试")
public class SecurityValidationTest {

    @Autowired
    private SecurityTestUtils securityTestUtils;

    @Autowired
    private LoginSecurityServiceImpl loginSecurityService;

    @Autowired
    private CaptchaService captchaService;

    @Autowired
    private BlacklistService blacklistService;

    @Autowired
    private AccountSecurityValidator accountSecurityValidator;

    @Autowired
    private RedisService redisService;

    @BeforeEach
    void setUp() {
        // 清理测试数据
        clearTestData();
    }

    @Test
    @DisplayName("暴力破解防护测试")
    void testBruteForceProtection() {
        String testAccount = "<EMAIL>";
        String loginWay = "email";

        // 执行暴力破解攻击模拟
        SecurityTestUtils.BruteForceResult result = 
            securityTestUtils.simulateBruteForceAttack(testAccount, loginWay, 10);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isAccountLocked(), "账号应该被锁定");
        assertTrue(result.getLockTriggeredAt() <= 5, "应该在5次尝试内触发锁定");
        assertEquals(0, result.getSuccessfulAttempts(), "不应该有成功的登录");

        // 验证锁定状态
        assertTrue(loginSecurityService.isAccountLocked(testAccount, loginWay), 
                  "账号应该处于锁定状态");

        // 验证失败次数记录
        int failureCount = loginSecurityService.getLoginFailureCount(testAccount, loginWay);
        assertTrue(failureCount >= 5, "失败次数应该大于等于5");

        System.out.println("=== 暴力破解防护测试结果 ===");
        System.out.println("总尝试次数: " + result.getTotalAttempts());
        System.out.println("锁定触发位置: " + result.getLockTriggeredAt());
        System.out.println("账号锁定状态: " + result.isAccountLocked());
        System.out.println("测试耗时: " + (result.getEndTime() - result.getStartTime()) + "ms");
    }

    @Test
    @DisplayName("并发登录攻击防护测试")
    void testConcurrentAttackProtection() {
        String testAccount = "<EMAIL>";
        String loginWay = "email";

        // 执行并发攻击模拟
        SecurityTestUtils.ConcurrentAttackResult result = 
            securityTestUtils.simulateConcurrentAttack(testAccount, loginWay, 5, 20);

        // 验证结果
        assertNotNull(result);
        assertEquals(20, result.getTotalRequests(), "总请求数应该正确");
        assertTrue(result.getFailureCount() > 0, "应该有失败的请求");
        assertEquals(0, result.getSuccessCount(), "不应该有成功的登录");

        // 验证账号最终被锁定
        assertTrue(loginSecurityService.isAccountLocked(testAccount, loginWay), 
                  "账号应该被锁定");

        System.out.println("=== 并发登录攻击防护测试结果 ===");
        System.out.println("并发数: " + result.getConcurrentCount());
        System.out.println("总请求: " + result.getTotalRequests());
        System.out.println("成功: " + result.getSuccessCount());
        System.out.println("失败: " + result.getFailureCount());
        System.out.println("错误: " + result.getErrorCount());
        System.out.println("测试耗时: " + (result.getEndTime() - result.getStartTime()) + "ms");
    }

    @Test
    @DisplayName("验证码安全性测试")
    void testCaptchaSecurity() {
        String testAccount = "<EMAIL>";
        String purpose = "login";

        // 执行验证码安全测试
        SecurityTestUtils.CaptchaSecurityResult result = 
            securityTestUtils.testCaptchaSecurity(testAccount, purpose);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isReplayVulnerable(), "不应该存在重放攻击漏洞");
        assertTrue(result.isRateLimitEffective(), "频率限制应该有效");
        assertTrue(result.isExpirationWorking(), "过期机制应该正常工作");

        System.out.println("=== 验证码安全性测试结果 ===");
        System.out.println("暴力破解尝试次数: " + result.getBruteForceAttempts());
        System.out.println("重放攻击漏洞: " + (result.isReplayVulnerable() ? "存在" : "不存在"));
        System.out.println("频率限制有效性: " + (result.isRateLimitEffective() ? "有效" : "无效"));
        System.out.println("过期机制工作: " + (result.isExpirationWorking() ? "正常" : "异常"));
        
        for (String testResult : result.getTestResults()) {
            System.out.println(testResult);
        }
    }

    @Test
    @DisplayName("账号格式验证安全测试")
    void testAccountFormatValidation() {
        System.out.println("=== 账号格式验证安全测试 ===");

        // 测试邮箱格式验证
        String[] invalidEmails = {
            "invalid_email",
            "@example.com",
            "test@",
            "<EMAIL>",
            "test@.com",
            "<script>alert('xss')</script>@example.com",
            "<EMAIL>'; DROP TABLE user; --",
            "<EMAIL>' OR '1'='1"
        };

        for (String email : invalidEmails) {
            boolean isValid = accountSecurityValidator.isValidEmail(email);
            assertFalse(isValid, "无效邮箱应该被拒绝: " + email);
            System.out.println("邮箱验证 [" + email + "]: " + (isValid ? "通过" : "拒绝"));
        }

        // 测试手机号格式验证
        String[] invalidPhones = {
            "invalid_phone",
            "**********",
            "***********",
            "1380013800a",
            "***********",
            "************",
            "'; DROP TABLE user; --",
            "' OR '1'='1"
        };

        for (String phone : invalidPhones) {
            boolean isValid = accountSecurityValidator.isValidPhone(phone);
            assertFalse(isValid, "无效手机号应该被拒绝: " + phone);
            System.out.println("手机号验证 [" + phone + "]: " + (isValid ? "通过" : "拒绝"));
        }
    }

    @Test
    @DisplayName("验证码格式验证安全测试")
    void testCaptchaFormatValidation() {
        System.out.println("=== 验证码格式验证安全测试 ===");

        String[] invalidCaptchas = {
            "",
            "   ",
            "12345",      // 太短
            "1234567",    // 太长
            "12 456",     // 包含空格
            "abcdef",     // 包含字母
            "123abc",     // 混合字符
            "<script>",   // XSS尝试
            "'; DROP",    // SQL注入尝试
            "' OR '1'='1" // SQL注入尝试
        };

        for (String captcha : invalidCaptchas) {
            boolean isValid = accountSecurityValidator.isValidCaptcha(captcha);
            assertFalse(isValid, "无效验证码应该被拒绝: " + captcha);
            System.out.println("验证码验证 [" + captcha + "]: " + (isValid ? "通过" : "拒绝"));
        }
    }

    @Test
    @DisplayName("IP黑名单防护测试")
    void testIPBlacklistProtection() {
        System.out.println("=== IP黑名单防护测试 ===");

        String testIP = "*************";
        String reason = "安全测试";

        // 测试添加到黑名单
        blacklistService.addToBlacklist(testIP, 5, reason);
        assertTrue(blacklistService.isBlacklisted(testIP), "IP应该在黑名单中");
        System.out.println("IP黑名单添加测试: 通过");

        // 测试自动封禁机制
        String autoBlockIP = "*************";
        boolean blocked = blacklistService.checkAndAutoBlock(autoBlockIP, 10, 1, 5);
        assertTrue(blocked, "应该触发自动封禁");
        assertTrue(blacklistService.isBlacklisted(autoBlockIP), "IP应该被自动封禁");
        System.out.println("IP自动封禁测试: 通过");

        // 测试移除黑名单
        blacklistService.removeFromBlacklist(testIP);
        assertFalse(blacklistService.isBlacklisted(testIP), "IP应该不在黑名单中");
        System.out.println("IP黑名单移除测试: 通过");
    }

    @Test
    @DisplayName("登录安全信息获取测试")
    void testLoginSecurityInfo() {
        System.out.println("=== 登录安全信息获取测试 ===");

        String testAccount = "<EMAIL>";
        String loginWay = "email";

        // 初始状态
        String initialInfo = loginSecurityService.getSecurityInfo(testAccount, loginWay);
        assertEquals("账号状态正常", initialInfo);
        System.out.println("初始状态: " + initialInfo);

        // 记录几次失败
        for (int i = 0; i < 3; i++) {
            loginSecurityService.recordLoginFailure(testAccount, loginWay, "127.0.0.1");
        }

        String failureInfo = loginSecurityService.getSecurityInfo(testAccount, loginWay);
        assertTrue(failureInfo.contains("登录失败3次"), "应该显示失败次数");
        System.out.println("失败状态: " + failureInfo);

        // 触发锁定
        for (int i = 0; i < 3; i++) {
            loginSecurityService.recordLoginFailure(testAccount, loginWay, "127.0.0.1");
        }

        String lockedInfo = loginSecurityService.getSecurityInfo(testAccount, loginWay);
        assertTrue(lockedInfo.contains("账号已被锁定"), "应该显示锁定状态");
        System.out.println("锁定状态: " + lockedInfo);
    }

    @Test
    @DisplayName("安全机制性能测试")
    void testSecurityPerformance() {
        System.out.println("=== 安全机制性能测试 ===");

        String testAccount = "<EMAIL>";
        String loginWay = "email";

        // 测试安全检查性能
        long startTime = System.currentTimeMillis();
        for (int i = 0; i < 1000; i++) {
            boolean canAttempt = loginSecurityService.canAttemptLogin(testAccount + i, loginWay);
            assertTrue(canAttempt); // 初始状态应该都可以登录
        }
        long endTime = System.currentTimeMillis();

        long duration = endTime - startTime;
        System.out.println("1000次安全检查耗时: " + duration + "ms");
        System.out.println("平均每次检查耗时: " + (duration / 1000.0) + "ms");

        // 性能应该在合理范围内
        assertTrue(duration < 5000, "1000次安全检查应该在5秒内完成");
        assertTrue((duration / 1000.0) < 5, "平均每次检查应该在5ms内完成");
    }

    /**
     * 清理测试数据
     */
    private void clearTestData() {
        try {
            // 清理登录失败记录
            redisService.del("login_failure:*");
            redisService.del("login_lock:*");
            
            // 清理验证码缓存
            redisService.del("email_captcha:*");
            redisService.del("email_captcha_frequency:*");
            
            // 清理黑名单
            redisService.del("blacklist:*");
            
        } catch (Exception e) {
            System.out.println("清理测试数据时出现异常: " + e.getMessage());
        }
    }
}
