package com.vida.xinye.x2.security;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.vida.xinye.x2.dto.param.EmailLoginDto;
import com.vida.xinye.x2.dto.param.PhonePasswordLoginDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * 渗透测试模拟器
 * 模拟各种安全攻击场景
 *
 * <AUTHOR>
 * @date 2025/08/12
 */
@Slf4j
@Component
public class PenetrationTestSimulator {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private ObjectMapper objectMapper;

    private MockMvc mockMvc;

    public void initMockMvc() {
        if (mockMvc == null) {
            mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        }
    }

    /**
     * SQL注入攻击测试
     */
    public SQLInjectionTestResult testSQLInjection() {
        log.info("开始SQL注入攻击测试");
        
        SQLInjectionTestResult result = new SQLInjectionTestResult();
        result.setStartTime(System.currentTimeMillis());
        
        List<String> payloads = getSQLInjectionPayloads();
        List<String> testResults = new ArrayList<>();
        int vulnerableCount = 0;
        
        for (String payload : payloads) {
            try {
                boolean vulnerable = testSQLInjectionPayload(payload);
                String resultMsg = "Payload: " + payload + " - " + (vulnerable ? "VULNERABLE" : "SAFE");
                testResults.add(resultMsg);
                
                if (vulnerable) {
                    vulnerableCount++;
                    log.warn("发现SQL注入漏洞: {}", payload);
                }
                
            } catch (Exception e) {
                testResults.add("Payload: " + payload + " - ERROR: " + e.getMessage());
            }
        }
        
        result.setEndTime(System.currentTimeMillis());
        result.setTotalPayloads(payloads.size());
        result.setVulnerableCount(vulnerableCount);
        result.setTestResults(testResults);
        result.setVulnerable(vulnerableCount > 0);
        
        log.info("SQL注入攻击测试完成 - 总载荷: {}, 漏洞数: {}", payloads.size(), vulnerableCount);
        return result;
    }

    /**
     * XSS攻击测试
     */
    public XSSTestResult testXSSAttacks() {
        log.info("开始XSS攻击测试");
        
        XSSTestResult result = new XSSTestResult();
        result.setStartTime(System.currentTimeMillis());
        
        List<String> payloads = getXSSPayloads();
        List<String> testResults = new ArrayList<>();
        int vulnerableCount = 0;
        
        for (String payload : payloads) {
            try {
                boolean vulnerable = testXSSPayload(payload);
                String resultMsg = "Payload: " + payload + " - " + (vulnerable ? "VULNERABLE" : "SAFE");
                testResults.add(resultMsg);
                
                if (vulnerable) {
                    vulnerableCount++;
                    log.warn("发现XSS漏洞: {}", payload);
                }
                
            } catch (Exception e) {
                testResults.add("Payload: " + payload + " - ERROR: " + e.getMessage());
            }
        }
        
        result.setEndTime(System.currentTimeMillis());
        result.setTotalPayloads(payloads.size());
        result.setVulnerableCount(vulnerableCount);
        result.setTestResults(testResults);
        result.setVulnerable(vulnerableCount > 0);
        
        log.info("XSS攻击测试完成 - 总载荷: {}, 漏洞数: {}", payloads.size(), vulnerableCount);
        return result;
    }

    /**
     * 命令注入攻击测试
     */
    public CommandInjectionTestResult testCommandInjection() {
        log.info("开始命令注入攻击测试");
        
        CommandInjectionTestResult result = new CommandInjectionTestResult();
        result.setStartTime(System.currentTimeMillis());
        
        List<String> payloads = getCommandInjectionPayloads();
        List<String> testResults = new ArrayList<>();
        int vulnerableCount = 0;
        
        for (String payload : payloads) {
            try {
                boolean vulnerable = testCommandInjectionPayload(payload);
                String resultMsg = "Payload: " + payload + " - " + (vulnerable ? "VULNERABLE" : "SAFE");
                testResults.add(resultMsg);
                
                if (vulnerable) {
                    vulnerableCount++;
                    log.warn("发现命令注入漏洞: {}", payload);
                }
                
            } catch (Exception e) {
                testResults.add("Payload: " + payload + " - ERROR: " + e.getMessage());
            }
        }
        
        result.setEndTime(System.currentTimeMillis());
        result.setTotalPayloads(payloads.size());
        result.setVulnerableCount(vulnerableCount);
        result.setTestResults(testResults);
        result.setVulnerable(vulnerableCount > 0);
        
        log.info("命令注入攻击测试完成 - 总载荷: {}, 漏洞数: {}", payloads.size(), vulnerableCount);
        return result;
    }

    /**
     * 路径遍历攻击测试
     */
    public PathTraversalTestResult testPathTraversal() {
        log.info("开始路径遍历攻击测试");
        
        PathTraversalTestResult result = new PathTraversalTestResult();
        result.setStartTime(System.currentTimeMillis());
        
        List<String> payloads = getPathTraversalPayloads();
        List<String> testResults = new ArrayList<>();
        int vulnerableCount = 0;
        
        for (String payload : payloads) {
            try {
                boolean vulnerable = testPathTraversalPayload(payload);
                String resultMsg = "Payload: " + payload + " - " + (vulnerable ? "VULNERABLE" : "SAFE");
                testResults.add(resultMsg);
                
                if (vulnerable) {
                    vulnerableCount++;
                    log.warn("发现路径遍历漏洞: {}", payload);
                }
                
            } catch (Exception e) {
                testResults.add("Payload: " + payload + " - ERROR: " + e.getMessage());
            }
        }
        
        result.setEndTime(System.currentTimeMillis());
        result.setTotalPayloads(payloads.size());
        result.setVulnerableCount(vulnerableCount);
        result.setTestResults(testResults);
        result.setVulnerable(vulnerableCount > 0);
        
        log.info("路径遍历攻击测试完成 - 总载荷: {}, 漏洞数: {}", payloads.size(), vulnerableCount);
        return result;
    }

    /**
     * 获取SQL注入载荷
     */
    private List<String> getSQLInjectionPayloads() {
        List<String> payloads = new ArrayList<>();
        payloads.add("' OR '1'='1");
        payloads.add("' OR '1'='1' --");
        payloads.add("' OR '1'='1' /*");
        payloads.add("'; DROP TABLE user; --");
        payloads.add("' UNION SELECT * FROM user --");
        payloads.add("' UNION SELECT username, password FROM user --");
        payloads.add("admin'--");
        payloads.add("admin' #");
        payloads.add("admin'/*");
        payloads.add("' OR 1=1 --");
        payloads.add("' OR 'a'='a");
        payloads.add("') OR ('1'='1");
        payloads.add("1' OR '1'='1");
        payloads.add("x' OR 1=1 OR 'x'='y");
        return payloads;
    }

    /**
     * 获取XSS载荷
     */
    private List<String> getXSSPayloads() {
        List<String> payloads = new ArrayList<>();
        payloads.add("<script>alert('XSS')</script>");
        payloads.add("<img src=x onerror=alert('XSS')>");
        payloads.add("<svg onload=alert('XSS')>");
        payloads.add("javascript:alert('XSS')");
        payloads.add("<iframe src=javascript:alert('XSS')>");
        payloads.add("<body onload=alert('XSS')>");
        payloads.add("<input onfocus=alert('XSS') autofocus>");
        payloads.add("<select onfocus=alert('XSS') autofocus>");
        payloads.add("<textarea onfocus=alert('XSS') autofocus>");
        payloads.add("<keygen onfocus=alert('XSS') autofocus>");
        payloads.add("<video><source onerror=alert('XSS')>");
        payloads.add("<audio src=x onerror=alert('XSS')>");
        return payloads;
    }

    /**
     * 获取命令注入载荷
     */
    private List<String> getCommandInjectionPayloads() {
        List<String> payloads = new ArrayList<>();
        payloads.add("; ls -la");
        payloads.add("| ls -la");
        payloads.add("& ls -la");
        payloads.add("&& ls -la");
        payloads.add("|| ls -la");
        payloads.add("; cat /etc/passwd");
        payloads.add("| cat /etc/passwd");
        payloads.add("; whoami");
        payloads.add("| whoami");
        payloads.add("; id");
        payloads.add("| id");
        payloads.add("`ls -la`");
        payloads.add("$(ls -la)");
        return payloads;
    }

    /**
     * 获取路径遍历载荷
     */
    private List<String> getPathTraversalPayloads() {
        List<String> payloads = new ArrayList<>();
        payloads.add("../../../etc/passwd");
        payloads.add("..\\..\\..\\windows\\system32\\drivers\\etc\\hosts");
        payloads.add("....//....//....//etc/passwd");
        payloads.add("..%2f..%2f..%2fetc%2fpasswd");
        payloads.add("..%252f..%252f..%252fetc%252fpasswd");
        payloads.add("..%c0%af..%c0%af..%c0%afetc%c0%afpasswd");
        payloads.add("/etc/passwd");
        payloads.add("\\windows\\system32\\drivers\\etc\\hosts");
        payloads.add("file:///etc/passwd");
        payloads.add("file://c:\\windows\\system32\\drivers\\etc\\hosts");
        return payloads;
    }

    /**
     * 测试SQL注入载荷
     */
    private boolean testSQLInjectionPayload(String payload) throws Exception {
        initMockMvc();
        
        EmailLoginDto loginDto = new EmailLoginDto();
        loginDto.setEmail(payload + "@example.com");
        loginDto.setLoginMethod("password");
        loginDto.setPassword("test123");

        MvcResult result = mockMvc.perform(post("/authen/email/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(loginDto)))
                .andReturn();

        String response = result.getResponse().getContentAsString();
        
        // 检查响应中是否包含数据库错误信息或敏感数据
        return response.contains("SQLException") || 
               response.contains("ORA-") || 
               response.contains("MySQL") ||
               response.contains("database") ||
               response.contains("table") ||
               response.contains("column");
    }

    /**
     * 测试XSS载荷
     */
    private boolean testXSSPayload(String payload) throws Exception {
        initMockMvc();
        
        EmailLoginDto loginDto = new EmailLoginDto();
        loginDto.setEmail("<EMAIL>");
        loginDto.setLoginMethod("password");
        loginDto.setPassword(payload);

        MvcResult result = mockMvc.perform(post("/authen/email/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(loginDto)))
                .andReturn();

        String response = result.getResponse().getContentAsString();
        
        // 检查响应中是否包含未转义的脚本
        return response.contains("<script>") || 
               response.contains("javascript:") ||
               response.contains("onerror=") ||
               response.contains("onload=");
    }

    /**
     * 测试命令注入载荷
     */
    private boolean testCommandInjectionPayload(String payload) throws Exception {
        initMockMvc();
        
        PhonePasswordLoginDto loginDto = new PhonePasswordLoginDto();
        loginDto.setPhone("1380013800" + payload);
        loginDto.setPassword("test123");

        MvcResult result = mockMvc.perform(post("/authen/phone/password/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(loginDto)))
                .andReturn();

        String response = result.getResponse().getContentAsString();
        
        // 检查响应中是否包含命令执行结果
        return response.contains("root:") || 
               response.contains("bin/") ||
               response.contains("uid=") ||
               response.contains("gid=");
    }

    /**
     * 测试路径遍历载荷
     */
    private boolean testPathTraversalPayload(String payload) throws Exception {
        initMockMvc();
        
        // 模拟文件上传或文件访问请求
        MvcResult result = mockMvc.perform(post("/file/upload")
                .param("filename", payload)
                .contentType(MediaType.APPLICATION_JSON))
                .andReturn();

        String response = result.getResponse().getContentAsString();
        
        // 检查响应中是否包含系统文件内容
        return response.contains("root:x:0:0") || 
               response.contains("# Copyright") ||
               response.contains("127.0.0.1") ||
               response.contains("localhost");
    }

    // 测试结果类定义
    public static class SQLInjectionTestResult {
        private long startTime;
        private long endTime;
        private int totalPayloads;
        private int vulnerableCount;
        private boolean vulnerable;
        private List<String> testResults;

        // Getters and Setters
        public long getStartTime() { return startTime; }
        public void setStartTime(long startTime) { this.startTime = startTime; }
        public long getEndTime() { return endTime; }
        public void setEndTime(long endTime) { this.endTime = endTime; }
        public int getTotalPayloads() { return totalPayloads; }
        public void setTotalPayloads(int totalPayloads) { this.totalPayloads = totalPayloads; }
        public int getVulnerableCount() { return vulnerableCount; }
        public void setVulnerableCount(int vulnerableCount) { this.vulnerableCount = vulnerableCount; }
        public boolean isVulnerable() { return vulnerable; }
        public void setVulnerable(boolean vulnerable) { this.vulnerable = vulnerable; }
        public List<String> getTestResults() { return testResults; }
        public void setTestResults(List<String> testResults) { this.testResults = testResults; }
    }

    public static class XSSTestResult {
        private long startTime;
        private long endTime;
        private int totalPayloads;
        private int vulnerableCount;
        private boolean vulnerable;
        private List<String> testResults;

        // Getters and Setters
        public long getStartTime() { return startTime; }
        public void setStartTime(long startTime) { this.startTime = startTime; }
        public long getEndTime() { return endTime; }
        public void setEndTime(long endTime) { this.endTime = endTime; }
        public int getTotalPayloads() { return totalPayloads; }
        public void setTotalPayloads(int totalPayloads) { this.totalPayloads = totalPayloads; }
        public int getVulnerableCount() { return vulnerableCount; }
        public void setVulnerableCount(int vulnerableCount) { this.vulnerableCount = vulnerableCount; }
        public boolean isVulnerable() { return vulnerable; }
        public void setVulnerable(boolean vulnerable) { this.vulnerable = vulnerable; }
        public List<String> getTestResults() { return testResults; }
        public void setTestResults(List<String> testResults) { this.testResults = testResults; }
    }

    public static class CommandInjectionTestResult {
        private long startTime;
        private long endTime;
        private int totalPayloads;
        private int vulnerableCount;
        private boolean vulnerable;
        private List<String> testResults;

        // Getters and Setters
        public long getStartTime() { return startTime; }
        public void setStartTime(long startTime) { this.startTime = startTime; }
        public long getEndTime() { return endTime; }
        public void setEndTime(long endTime) { this.endTime = endTime; }
        public int getTotalPayloads() { return totalPayloads; }
        public void setTotalPayloads(int totalPayloads) { this.totalPayloads = totalPayloads; }
        public int getVulnerableCount() { return vulnerableCount; }
        public void setVulnerableCount(int vulnerableCount) { this.vulnerableCount = vulnerableCount; }
        public boolean isVulnerable() { return vulnerable; }
        public void setVulnerable(boolean vulnerable) { this.vulnerable = vulnerable; }
        public List<String> getTestResults() { return testResults; }
        public void setTestResults(List<String> testResults) { this.testResults = testResults; }
    }

    public static class PathTraversalTestResult {
        private long startTime;
        private long endTime;
        private int totalPayloads;
        private int vulnerableCount;
        private boolean vulnerable;
        private List<String> testResults;

        // Getters and Setters
        public long getStartTime() { return startTime; }
        public void setStartTime(long startTime) { this.startTime = startTime; }
        public long getEndTime() { return endTime; }
        public void setEndTime(long endTime) { this.endTime = endTime; }
        public int getTotalPayloads() { return totalPayloads; }
        public void setTotalPayloads(int totalPayloads) { this.totalPayloads = totalPayloads; }
        public int getVulnerableCount() { return vulnerableCount; }
        public void setVulnerableCount(int vulnerableCount) { this.vulnerableCount = vulnerableCount; }
        public boolean isVulnerable() { return vulnerable; }
        public void setVulnerable(boolean vulnerable) { this.vulnerable = vulnerable; }
        public List<String> getTestResults() { return testResults; }
        public void setTestResults(List<String> testResults) { this.testResults = testResults; }
    }
}
