package com.vida.xinye.x2.security;

import com.vida.xinye.x2.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * 安全测试工具类
 * 提供各种安全测试的工具方法
 *
 * <AUTHOR>
 * @date 2025/08/12
 */
@Slf4j
@Component
public class SecurityTestUtils {

    @Autowired
    private RedisService redisService;

    /**
     * 模拟暴力破解攻击
     *
     * @param account     目标账号
     * @param loginWay    登录方式
     * @param attemptCount 尝试次数
     * @return 攻击结果
     */
    public BruteForceResult simulateBruteForceAttack(String account, String loginWay, int attemptCount) {
        log.info("开始模拟暴力破解攻击 - 账号: {}, 登录方式: {}, 尝试次数: {}", 
                maskAccount(account), loginWay, attemptCount);

        BruteForceResult result = new BruteForceResult();
        result.setAccount(account);
        result.setLoginWay(loginWay);
        result.setTotalAttempts(attemptCount);
        result.setStartTime(System.currentTimeMillis());

        List<String> attemptResults = new ArrayList<>();
        boolean accountLocked = false;
        int successfulAttempts = 0;

        for (int i = 1; i <= attemptCount; i++) {
            try {
                // 模拟登录尝试
                String attemptResult = simulateLoginAttempt(account, loginWay, i);
                attemptResults.add(attemptResult);

                if (attemptResult.contains("锁定")) {
                    accountLocked = true;
                    result.setLockTriggeredAt(i);
                    log.warn("账号在第{}次尝试后被锁定", i);
                    break;
                }

                if (attemptResult.contains("成功")) {
                    successfulAttempts++;
                }

                // 模拟攻击间隔
                Thread.sleep(100);

            } catch (Exception e) {
                log.error("模拟登录尝试失败: {}", e.getMessage());
                attemptResults.add("第" + i + "次尝试异常: " + e.getMessage());
            }
        }

        result.setEndTime(System.currentTimeMillis());
        result.setAccountLocked(accountLocked);
        result.setSuccessfulAttempts(successfulAttempts);
        result.setAttemptResults(attemptResults);

        log.info("暴力破解攻击模拟完成 - 总尝试: {}, 成功: {}, 账号锁定: {}", 
                attemptCount, successfulAttempts, accountLocked);

        return result;
    }

    /**
     * 模拟并发登录攻击
     *
     * @param account        目标账号
     * @param loginWay       登录方式
     * @param concurrentCount 并发数
     * @param totalRequests   总请求数
     * @return 并发攻击结果
     */
    public ConcurrentAttackResult simulateConcurrentAttack(String account, String loginWay, 
                                                          int concurrentCount, int totalRequests) {
        log.info("开始模拟并发登录攻击 - 账号: {}, 并发数: {}, 总请求: {}", 
                maskAccount(account), concurrentCount, totalRequests);

        ConcurrentAttackResult result = new ConcurrentAttackResult();
        result.setAccount(account);
        result.setConcurrentCount(concurrentCount);
        result.setTotalRequests(totalRequests);
        result.setStartTime(System.currentTimeMillis());

        ExecutorService executor = Executors.newFixedThreadPool(concurrentCount);
        List<CompletableFuture<String>> futures = new ArrayList<>();

        // 提交并发任务
        for (int i = 0; i < totalRequests; i++) {
            final int requestId = i + 1;
            CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> {
                try {
                    return simulateLoginAttempt(account, loginWay, requestId);
                } catch (Exception e) {
                    return "请求" + requestId + "异常: " + e.getMessage();
                }
            }, executor);
            futures.add(future);
        }

        // 等待所有任务完成
        List<String> results = new ArrayList<>();
        int successCount = 0;
        int failureCount = 0;
        int errorCount = 0;

        for (CompletableFuture<String> future : futures) {
            try {
                String attemptResult = future.get(30, TimeUnit.SECONDS);
                results.add(attemptResult);

                if (attemptResult.contains("成功")) {
                    successCount++;
                } else if (attemptResult.contains("异常")) {
                    errorCount++;
                } else {
                    failureCount++;
                }
            } catch (Exception e) {
                log.error("获取并发任务结果失败: {}", e.getMessage());
                errorCount++;
            }
        }

        executor.shutdown();
        result.setEndTime(System.currentTimeMillis());
        result.setSuccessCount(successCount);
        result.setFailureCount(failureCount);
        result.setErrorCount(errorCount);
        result.setResults(results);

        log.info("并发登录攻击模拟完成 - 成功: {}, 失败: {}, 错误: {}", 
                successCount, failureCount, errorCount);

        return result;
    }

    /**
     * 测试验证码安全性
     *
     * @param account 目标账号
     * @param purpose 验证码用途
     * @return 验证码安全测试结果
     */
    public CaptchaSecurityResult testCaptchaSecurity(String account, String purpose) {
        log.info("开始测试验证码安全性 - 账号: {}, 用途: {}", maskAccount(account), purpose);

        CaptchaSecurityResult result = new CaptchaSecurityResult();
        result.setAccount(account);
        result.setPurpose(purpose);
        result.setStartTime(System.currentTimeMillis());

        List<String> testResults = new ArrayList<>();

        // 测试1: 验证码暴力破解
        testResults.add("=== 验证码暴力破解测试 ===");
        int bruteForceAttempts = testCaptchaBruteForce(account, purpose);
        testResults.add("暴力破解尝试次数: " + bruteForceAttempts);

        // 测试2: 验证码重放攻击
        testResults.add("=== 验证码重放攻击测试 ===");
        boolean replayVulnerable = testCaptchaReplay(account, purpose);
        testResults.add("重放攻击漏洞: " + (replayVulnerable ? "存在" : "不存在"));

        // 测试3: 验证码频率限制
        testResults.add("=== 验证码频率限制测试 ===");
        boolean rateLimitEffective = testCaptchaRateLimit(account, purpose);
        testResults.add("频率限制有效性: " + (rateLimitEffective ? "有效" : "无效"));

        // 测试4: 验证码过期机制
        testResults.add("=== 验证码过期机制测试 ===");
        boolean expirationWorking = testCaptchaExpiration(account, purpose);
        testResults.add("过期机制工作: " + (expirationWorking ? "正常" : "异常"));

        result.setEndTime(System.currentTimeMillis());
        result.setTestResults(testResults);
        result.setBruteForceAttempts(bruteForceAttempts);
        result.setReplayVulnerable(replayVulnerable);
        result.setRateLimitEffective(rateLimitEffective);
        result.setExpirationWorking(expirationWorking);

        log.info("验证码安全性测试完成");
        return result;
    }

    /**
     * 模拟单次登录尝试
     */
    private String simulateLoginAttempt(String account, String loginWay, int attemptId) {
        // 检查账号是否被锁定
        String lockKey = "login_lock:" + account + ":" + loginWay;
        if (redisService.hasKey(lockKey)) {
            return "第" + attemptId + "次尝试: 账号已锁定";
        }

        // 模拟登录失败，增加失败计数
        String failureKey = "login_failure:" + account + ":" + loginWay;
        Object countObj = redisService.get(failureKey);
        int count = countObj == null ? 0 : Integer.parseInt(countObj.toString());
        count++;

        redisService.set(failureKey, String.valueOf(count), 24 * 3600);

        // 检查是否达到锁定阈值
        if (count >= 5) {
            redisService.set(lockKey, "locked", 30 * 60);
            return "第" + attemptId + "次尝试: 登录失败，账号已锁定";
        }

        return "第" + attemptId + "次尝试: 登录失败，失败次数: " + count;
    }

    /**
     * 测试验证码暴力破解
     */
    private int testCaptchaBruteForce(String account, String purpose) {
        // 模拟暴力破解6位数字验证码
        int attempts = 0;
        for (int i = 0; i < 100; i++) {
            attempts++;
            String code = String.format("%06d", i);
            
            // 模拟验证码验证
            String key = "email_captcha:" + account + ":" + purpose;
            Object storedCode = redisService.get(key);
            
            if (storedCode != null && storedCode.toString().equals(code)) {
                log.info("验证码暴力破解成功，尝试次数: {}", attempts);
                return attempts;
            }
            
            // 模拟验证间隔
            try {
                Thread.sleep(10);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        
        return attempts;
    }

    /**
     * 测试验证码重放攻击
     */
    private boolean testCaptchaReplay(String account, String purpose) {
        // 设置一个测试验证码
        String testCode = "123456";
        String key = "email_captcha:" + account + ":" + purpose;
        redisService.set(key, testCode, 300);

        // 第一次验证
        boolean firstVerify = testCode.equals(redisService.get(key));
        if (firstVerify) {
            redisService.del(key); // 模拟验证成功后删除
        }

        // 第二次验证（重放攻击）
        boolean secondVerify = testCode.equals(redisService.get(key));

        // 如果第二次验证成功，说明存在重放漏洞
        return secondVerify;
    }

    /**
     * 测试验证码频率限制
     */
    private boolean testCaptchaRateLimit(String account, String purpose) {
        String frequencyKey = "email_captcha_frequency:" + account;
        
        // 第一次发送
        redisService.set(frequencyKey, "1", 60);
        
        // 立即第二次发送
        boolean hasFrequencyLimit = redisService.hasKey(frequencyKey);
        
        return hasFrequencyLimit;
    }

    /**
     * 测试验证码过期机制
     */
    private boolean testCaptchaExpiration(String account, String purpose) {
        String key = "email_captcha:" + account + ":" + purpose;
        String testCode = "123456";
        
        // 设置短过期时间
        redisService.set(key, testCode, 1);
        
        try {
            Thread.sleep(2000); // 等待过期
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 检查是否已过期
        return !redisService.hasKey(key);
    }

    /**
     * 脱敏账号信息
     */
    private String maskAccount(String account) {
        if (account == null || account.length() <= 3) {
            return account;
        }
        return account.substring(0, 3) + "***";
    }

    // 内部类定义测试结果
    public static class BruteForceResult {
        private String account;
        private String loginWay;
        private int totalAttempts;
        private int successfulAttempts;
        private boolean accountLocked;
        private int lockTriggeredAt;
        private long startTime;
        private long endTime;
        private List<String> attemptResults;

        // Getters and Setters
        public String getAccount() { return account; }
        public void setAccount(String account) { this.account = account; }
        public String getLoginWay() { return loginWay; }
        public void setLoginWay(String loginWay) { this.loginWay = loginWay; }
        public int getTotalAttempts() { return totalAttempts; }
        public void setTotalAttempts(int totalAttempts) { this.totalAttempts = totalAttempts; }
        public int getSuccessfulAttempts() { return successfulAttempts; }
        public void setSuccessfulAttempts(int successfulAttempts) { this.successfulAttempts = successfulAttempts; }
        public boolean isAccountLocked() { return accountLocked; }
        public void setAccountLocked(boolean accountLocked) { this.accountLocked = accountLocked; }
        public int getLockTriggeredAt() { return lockTriggeredAt; }
        public void setLockTriggeredAt(int lockTriggeredAt) { this.lockTriggeredAt = lockTriggeredAt; }
        public long getStartTime() { return startTime; }
        public void setStartTime(long startTime) { this.startTime = startTime; }
        public long getEndTime() { return endTime; }
        public void setEndTime(long endTime) { this.endTime = endTime; }
        public List<String> getAttemptResults() { return attemptResults; }
        public void setAttemptResults(List<String> attemptResults) { this.attemptResults = attemptResults; }
    }

    public static class ConcurrentAttackResult {
        private String account;
        private int concurrentCount;
        private int totalRequests;
        private int successCount;
        private int failureCount;
        private int errorCount;
        private long startTime;
        private long endTime;
        private List<String> results;

        // Getters and Setters
        public String getAccount() { return account; }
        public void setAccount(String account) { this.account = account; }
        public int getConcurrentCount() { return concurrentCount; }
        public void setConcurrentCount(int concurrentCount) { this.concurrentCount = concurrentCount; }
        public int getTotalRequests() { return totalRequests; }
        public void setTotalRequests(int totalRequests) { this.totalRequests = totalRequests; }
        public int getSuccessCount() { return successCount; }
        public void setSuccessCount(int successCount) { this.successCount = successCount; }
        public int getFailureCount() { return failureCount; }
        public void setFailureCount(int failureCount) { this.failureCount = failureCount; }
        public int getErrorCount() { return errorCount; }
        public void setErrorCount(int errorCount) { this.errorCount = errorCount; }
        public long getStartTime() { return startTime; }
        public void setStartTime(long startTime) { this.startTime = startTime; }
        public long getEndTime() { return endTime; }
        public void setEndTime(long endTime) { this.endTime = endTime; }
        public List<String> getResults() { return results; }
        public void setResults(List<String> results) { this.results = results; }
    }

    public static class CaptchaSecurityResult {
        private String account;
        private String purpose;
        private long startTime;
        private long endTime;
        private List<String> testResults;
        private int bruteForceAttempts;
        private boolean replayVulnerable;
        private boolean rateLimitEffective;
        private boolean expirationWorking;

        // Getters and Setters
        public String getAccount() { return account; }
        public void setAccount(String account) { this.account = account; }
        public String getPurpose() { return purpose; }
        public void setPurpose(String purpose) { this.purpose = purpose; }
        public long getStartTime() { return startTime; }
        public void setStartTime(long startTime) { this.startTime = startTime; }
        public long getEndTime() { return endTime; }
        public void setEndTime(long endTime) { this.endTime = endTime; }
        public List<String> getTestResults() { return testResults; }
        public void setTestResults(List<String> testResults) { this.testResults = testResults; }
        public int getBruteForceAttempts() { return bruteForceAttempts; }
        public void setBruteForceAttempts(int bruteForceAttempts) { this.bruteForceAttempts = bruteForceAttempts; }
        public boolean isReplayVulnerable() { return replayVulnerable; }
        public void setReplayVulnerable(boolean replayVulnerable) { this.replayVulnerable = replayVulnerable; }
        public boolean isRateLimitEffective() { return rateLimitEffective; }
        public void setRateLimitEffective(boolean rateLimitEffective) { this.rateLimitEffective = rateLimitEffective; }
        public boolean isExpirationWorking() { return expirationWorking; }
        public void setExpirationWorking(boolean expirationWorking) { this.expirationWorking = expirationWorking; }
    }
}
