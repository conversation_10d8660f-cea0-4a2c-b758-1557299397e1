package com.vida.xinye.x2.security;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 综合安全测试
 * 执行完整的安全测试套件
 *
 * <AUTHOR>
 * @date 2025/08/12
 */
@SpringBootTest
@ActiveProfiles("test")
@DisplayName("综合安全测试")
public class ComprehensiveSecurityTest {

    @Autowired
    private PenetrationTestSimulator penetrationTestSimulator;

    @Test
    @DisplayName("SQL注入漏洞扫描")
    void testSQLInjectionVulnerabilities() {
        System.out.println("\n=== SQL注入漏洞扫描开始 ===");
        
        PenetrationTestSimulator.SQLInjectionTestResult result = 
            penetrationTestSimulator.testSQLInjection();

        // 验证测试结果
        assertNotNull(result);
        assertTrue(result.getTotalPayloads() > 0, "应该测试了多个SQL注入载荷");
        
        // 安全要求：不应该存在SQL注入漏洞
        assertFalse(result.isVulnerable(), "系统不应该存在SQL注入漏洞");
        assertEquals(0, result.getVulnerableCount(), "不应该有任何SQL注入漏洞");

        // 输出测试结果
        System.out.println("总载荷数: " + result.getTotalPayloads());
        System.out.println("漏洞数量: " + result.getVulnerableCount());
        System.out.println("测试耗时: " + (result.getEndTime() - result.getStartTime()) + "ms");
        System.out.println("安全状态: " + (result.isVulnerable() ? "存在漏洞" : "安全"));

        // 输出详细测试结果
        for (String testResult : result.getTestResults()) {
            System.out.println(testResult);
        }
        
        System.out.println("=== SQL注入漏洞扫描完成 ===\n");
    }

    @Test
    @DisplayName("XSS漏洞扫描")
    void testXSSVulnerabilities() {
        System.out.println("\n=== XSS漏洞扫描开始 ===");
        
        PenetrationTestSimulator.XSSTestResult result = 
            penetrationTestSimulator.testXSSAttacks();

        // 验证测试结果
        assertNotNull(result);
        assertTrue(result.getTotalPayloads() > 0, "应该测试了多个XSS载荷");
        
        // 安全要求：不应该存在XSS漏洞
        assertFalse(result.isVulnerable(), "系统不应该存在XSS漏洞");
        assertEquals(0, result.getVulnerableCount(), "不应该有任何XSS漏洞");

        // 输出测试结果
        System.out.println("总载荷数: " + result.getTotalPayloads());
        System.out.println("漏洞数量: " + result.getVulnerableCount());
        System.out.println("测试耗时: " + (result.getEndTime() - result.getStartTime()) + "ms");
        System.out.println("安全状态: " + (result.isVulnerable() ? "存在漏洞" : "安全"));

        // 输出详细测试结果
        for (String testResult : result.getTestResults()) {
            System.out.println(testResult);
        }
        
        System.out.println("=== XSS漏洞扫描完成 ===\n");
    }

    @Test
    @DisplayName("命令注入漏洞扫描")
    void testCommandInjectionVulnerabilities() {
        System.out.println("\n=== 命令注入漏洞扫描开始 ===");
        
        PenetrationTestSimulator.CommandInjectionTestResult result = 
            penetrationTestSimulator.testCommandInjection();

        // 验证测试结果
        assertNotNull(result);
        assertTrue(result.getTotalPayloads() > 0, "应该测试了多个命令注入载荷");
        
        // 安全要求：不应该存在命令注入漏洞
        assertFalse(result.isVulnerable(), "系统不应该存在命令注入漏洞");
        assertEquals(0, result.getVulnerableCount(), "不应该有任何命令注入漏洞");

        // 输出测试结果
        System.out.println("总载荷数: " + result.getTotalPayloads());
        System.out.println("漏洞数量: " + result.getVulnerableCount());
        System.out.println("测试耗时: " + (result.getEndTime() - result.getStartTime()) + "ms");
        System.out.println("安全状态: " + (result.isVulnerable() ? "存在漏洞" : "安全"));

        // 输出详细测试结果
        for (String testResult : result.getTestResults()) {
            System.out.println(testResult);
        }
        
        System.out.println("=== 命令注入漏洞扫描完成 ===\n");
    }

    @Test
    @DisplayName("路径遍历漏洞扫描")
    void testPathTraversalVulnerabilities() {
        System.out.println("\n=== 路径遍历漏洞扫描开始 ===");
        
        PenetrationTestSimulator.PathTraversalTestResult result = 
            penetrationTestSimulator.testPathTraversal();

        // 验证测试结果
        assertNotNull(result);
        assertTrue(result.getTotalPayloads() > 0, "应该测试了多个路径遍历载荷");
        
        // 安全要求：不应该存在路径遍历漏洞
        assertFalse(result.isVulnerable(), "系统不应该存在路径遍历漏洞");
        assertEquals(0, result.getVulnerableCount(), "不应该有任何路径遍历漏洞");

        // 输出测试结果
        System.out.println("总载荷数: " + result.getTotalPayloads());
        System.out.println("漏洞数量: " + result.getVulnerableCount());
        System.out.println("测试耗时: " + (result.getEndTime() - result.getStartTime()) + "ms");
        System.out.println("安全状态: " + (result.isVulnerable() ? "存在漏洞" : "安全"));

        // 输出详细测试结果
        for (String testResult : result.getTestResults()) {
            System.out.println(testResult);
        }
        
        System.out.println("=== 路径遍历漏洞扫描完成 ===\n");
    }

    @Test
    @DisplayName("综合安全评估")
    void testComprehensiveSecurityAssessment() {
        System.out.println("\n=== 综合安全评估开始 ===");
        
        SecurityAssessmentResult assessment = new SecurityAssessmentResult();
        assessment.setStartTime(System.currentTimeMillis());

        // 执行所有安全测试
        PenetrationTestSimulator.SQLInjectionTestResult sqlResult = 
            penetrationTestSimulator.testSQLInjection();
        
        PenetrationTestSimulator.XSSTestResult xssResult = 
            penetrationTestSimulator.testXSSAttacks();
        
        PenetrationTestSimulator.CommandInjectionTestResult cmdResult = 
            penetrationTestSimulator.testCommandInjection();
        
        PenetrationTestSimulator.PathTraversalTestResult pathResult = 
            penetrationTestSimulator.testPathTraversal();

        assessment.setEndTime(System.currentTimeMillis());

        // 汇总结果
        int totalVulnerabilities = sqlResult.getVulnerableCount() + 
                                  xssResult.getVulnerableCount() + 
                                  cmdResult.getVulnerableCount() + 
                                  pathResult.getVulnerableCount();

        int totalTests = sqlResult.getTotalPayloads() + 
                        xssResult.getTotalPayloads() + 
                        cmdResult.getTotalPayloads() + 
                        pathResult.getTotalPayloads();

        assessment.setTotalTests(totalTests);
        assessment.setTotalVulnerabilities(totalVulnerabilities);
        assessment.setSqlInjectionSafe(!sqlResult.isVulnerable());
        assessment.setXssSafe(!xssResult.isVulnerable());
        assessment.setCommandInjectionSafe(!cmdResult.isVulnerable());
        assessment.setPathTraversalSafe(!pathResult.isVulnerable());

        // 计算安全评分
        int securityScore = calculateSecurityScore(assessment);
        assessment.setSecurityScore(securityScore);

        // 输出评估结果
        System.out.println("=== 综合安全评估报告 ===");
        System.out.println("总测试数: " + assessment.getTotalTests());
        System.out.println("总漏洞数: " + assessment.getTotalVulnerabilities());
        System.out.println("SQL注入安全: " + (assessment.isSqlInjectionSafe() ? "✓" : "✗"));
        System.out.println("XSS安全: " + (assessment.isXssSafe() ? "✓" : "✗"));
        System.out.println("命令注入安全: " + (assessment.isCommandInjectionSafe() ? "✓" : "✗"));
        System.out.println("路径遍历安全: " + (assessment.isPathTraversalSafe() ? "✓" : "✗"));
        System.out.println("安全评分: " + assessment.getSecurityScore() + "/100");
        System.out.println("测试耗时: " + (assessment.getEndTime() - assessment.getStartTime()) + "ms");

        // 安全要求验证
        assertEquals(0, totalVulnerabilities, "系统不应该存在任何安全漏洞");
        assertTrue(securityScore >= 90, "安全评分应该达到90分以上");
        assertTrue(assessment.isSqlInjectionSafe(), "SQL注入防护应该有效");
        assertTrue(assessment.isXssSafe(), "XSS防护应该有效");
        assertTrue(assessment.isCommandInjectionSafe(), "命令注入防护应该有效");
        assertTrue(assessment.isPathTraversalSafe(), "路径遍历防护应该有效");

        System.out.println("=== 综合安全评估完成 ===\n");
    }

    /**
     * 计算安全评分
     */
    private int calculateSecurityScore(SecurityAssessmentResult assessment) {
        int score = 100;
        
        // 每个漏洞扣分
        score -= assessment.getTotalVulnerabilities() * 10;
        
        // 各类安全检查扣分
        if (!assessment.isSqlInjectionSafe()) score -= 25;
        if (!assessment.isXssSafe()) score -= 20;
        if (!assessment.isCommandInjectionSafe()) score -= 25;
        if (!assessment.isPathTraversalSafe()) score -= 15;
        
        return Math.max(0, score);
    }

    /**
     * 安全评估结果类
     */
    public static class SecurityAssessmentResult {
        private long startTime;
        private long endTime;
        private int totalTests;
        private int totalVulnerabilities;
        private boolean sqlInjectionSafe;
        private boolean xssSafe;
        private boolean commandInjectionSafe;
        private boolean pathTraversalSafe;
        private int securityScore;

        // Getters and Setters
        public long getStartTime() { return startTime; }
        public void setStartTime(long startTime) { this.startTime = startTime; }
        public long getEndTime() { return endTime; }
        public void setEndTime(long endTime) { this.endTime = endTime; }
        public int getTotalTests() { return totalTests; }
        public void setTotalTests(int totalTests) { this.totalTests = totalTests; }
        public int getTotalVulnerabilities() { return totalVulnerabilities; }
        public void setTotalVulnerabilities(int totalVulnerabilities) { this.totalVulnerabilities = totalVulnerabilities; }
        public boolean isSqlInjectionSafe() { return sqlInjectionSafe; }
        public void setSqlInjectionSafe(boolean sqlInjectionSafe) { this.sqlInjectionSafe = sqlInjectionSafe; }
        public boolean isXssSafe() { return xssSafe; }
        public void setXssSafe(boolean xssSafe) { this.xssSafe = xssSafe; }
        public boolean isCommandInjectionSafe() { return commandInjectionSafe; }
        public void setCommandInjectionSafe(boolean commandInjectionSafe) { this.commandInjectionSafe = commandInjectionSafe; }
        public boolean isPathTraversalSafe() { return pathTraversalSafe; }
        public void setPathTraversalSafe(boolean pathTraversalSafe) { this.pathTraversalSafe = pathTraversalSafe; }
        public int getSecurityScore() { return securityScore; }
        public void setSecurityScore(int securityScore) { this.securityScore = securityScore; }
    }
}
