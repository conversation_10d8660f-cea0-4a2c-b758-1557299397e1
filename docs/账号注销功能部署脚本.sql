-- 账号注销功能部署脚本
-- 执行前请备份数据库！
-- 适用于生产环境部署

-- ========================================
-- 1. 检查环境
-- ========================================

SELECT '=== 账号注销功能部署开始 ===' as message;
SELECT NOW() as deployment_time;
SELECT DATABASE() as target_database;

-- 检查必要的表是否存在
SELECT '检查必要表结构...' as step;
SELECT 
    TABLE_NAME,
    CASE 
        WHEN TABLE_NAME IN ('user', 'user_third_party_auth', 'cloudfile') THEN '✅ 存在'
        ELSE '❌ 缺失'
    END as status
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME IN ('user', 'user_third_party_auth', 'cloudfile');

-- ========================================
-- 2. 创建可选的审计表（如果需要详细审计）
-- ========================================

SELECT '创建删除审计表（可选）...' as step;

CREATE TABLE IF NOT EXISTS `user_deletion_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `account` varchar(100) NOT NULL COMMENT '删除的账号（邮箱或手机号）',
  `delete_reason` varchar(200) DEFAULT NULL COMMENT '删除原因',
  `operator_type` varchar(20) DEFAULT 'USER' COMMENT '操作类型：USER-用户自删，ADMIN-管理员删除',
  `operator_id` bigint(20) DEFAULT NULL COMMENT '操作人ID（管理员删除时记录管理员ID）',
  `operation_ip` varchar(50) DEFAULT NULL COMMENT '操作IP地址',
  `user_agent` text COMMENT '用户代理信息',
  `deleted_files_count` int(11) DEFAULT 0 COMMENT '删除的文件数量',
  `deletion_status` varchar(20) DEFAULT 'SUCCESS' COMMENT '删除状态：SUCCESS-成功，FAILED-失败，PARTIAL-部分成功',
  `error_message` text COMMENT '错误信息（如果删除失败）',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_account` (`account`),
  KEY `idx_operator_type` (`operator_type`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deletion_status` (`deletion_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户删除审计日志表';

-- 创建复合索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_user_deletion_user_time ON user_deletion_log(user_id, create_time DESC);
CREATE INDEX IF NOT EXISTS idx_user_deletion_account_time ON user_deletion_log(account, create_time DESC);

SELECT '审计表创建完成' as result;

-- ========================================
-- 3. 验证Redis配置（提示检查项）
-- ========================================

SELECT '=== Redis配置检查提示 ===' as step;
SELECT '请确认以下Redis配置项：' as reminder;
SELECT '1. Redis连接配置正确' as check_item_1;
SELECT '2. RedisService服务可用' as check_item_2;
SELECT '3. 缓存key前缀配置：account:delete:by:account:' as check_item_3;
SELECT '4. 缓存过期时间：7天 (604800秒)' as check_item_4;

-- ========================================
-- 4. 验证OSS配置（提示检查项）
-- ========================================

SELECT '=== OSS配置检查提示 ===' as step;
SELECT '请确认以下OSS配置项：' as reminder;
SELECT '1. OSS连接配置正确' as check_item_1;
SELECT '2. OssService服务可用' as check_item_2;
SELECT '3. 文件删除权限配置正确' as check_item_3;
SELECT '4. 异步任务线程池配置' as check_item_4;

-- ========================================
-- 5. 验证安全配置（提示检查项）
-- ========================================

SELECT '=== 安全配置检查提示 ===' as step;
SELECT '请确认以下安全配置项：' as reminder;
SELECT '1. PasswordEncoder配置正确' as check_item_1;
SELECT '2. CaptchaService验证码服务可用' as check_item_2;
SELECT '3. JWT Token验证配置' as check_item_3;
SELECT '4. 接口频率限制配置：60秒3次' as check_item_4;

-- ========================================
-- 6. 创建测试数据（仅测试环境）
-- ========================================

-- 注意：以下测试数据仅用于测试环境，生产环境请跳过

-- 检查是否为测试环境
SET @is_test_env = (SELECT IF(DATABASE() LIKE '%test%' OR DATABASE() LIKE '%dev%', 1, 0));

-- 如果是测试环境，创建测试数据
SET @sql = (SELECT IF(
    @is_test_env = 1,
    'INSERT IGNORE INTO user (id, email, phone, password, avatar, create_time, update_time) VALUES 
     (999999, "<EMAIL>", "13999999999", "$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDa", "https://oss.example.com/test.jpg", NOW(), NOW())',
    'SELECT "跳过测试数据创建（生产环境）" as message'
));

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ========================================
-- 7. 验证部署结果
-- ========================================

SELECT '=== 部署验证 ===' as step;

-- 检查审计表
SELECT '审计表检查:' as check_type;
SELECT 
    TABLE_NAME,
    TABLE_COMMENT,
    CASE 
        WHEN TABLE_NAME = 'user_deletion_log' THEN '✅ 已创建'
        ELSE '❌ 未创建'
    END as status
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'user_deletion_log';

-- 检查审计表字段
SELECT '审计表字段检查:' as check_type;
SELECT COUNT(*) as field_count
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'user_deletion_log';

-- 检查索引
SELECT '审计表索引检查:' as check_type;
SELECT COUNT(*) as index_count
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'user_deletion_log';

-- ========================================
-- 8. 部署后配置检查清单
-- ========================================

SELECT '=== 部署后检查清单 ===' as step;
SELECT '请逐项检查以下配置：' as instruction;

-- 应用配置检查清单
SELECT '应用配置检查清单:' as category;
SELECT '□ 1. 重启应用服务' as checklist_1;
SELECT '□ 2. 验证AccountDeletionService Bean注册' as checklist_2;
SELECT '□ 3. 验证UserDeletionCacheService Bean注册' as checklist_3;
SELECT '□ 4. 验证Redis连接正常' as checklist_4;
SELECT '□ 5. 验证OSS连接正常' as checklist_5;

-- 接口测试检查清单
SELECT '接口测试检查清单:' as category;
SELECT '□ 1. 测试POST /user/account/delete接口' as checklist_1;
SELECT '□ 2. 测试GET /user/account/deletion/check接口' as checklist_2;
SELECT '□ 3. 验证身份验证功能' as checklist_3;
SELECT '□ 4. 验证频率限制功能' as checklist_4;
SELECT '□ 5. 验证Redis缓存标记' as checklist_5;

-- 功能验证检查清单
SELECT '功能验证检查清单:' as category;
SELECT '□ 1. 创建测试用户并注销' as checklist_1;
SELECT '□ 2. 验证数据库记录被删除' as checklist_2;
SELECT '□ 3. 验证Redis缓存标记生效' as checklist_3;
SELECT '□ 4. 验证7天重注册限制' as checklist_4;
SELECT '□ 5. 验证OSS文件删除（如果有）' as checklist_5;

-- ========================================
-- 9. 监控和告警配置建议
-- ========================================

SELECT '=== 监控告警建议 ===' as step;

-- 监控指标建议
SELECT '建议监控的指标:' as category;
SELECT '1. 账号注销成功率' as metric_1;
SELECT '2. 账号注销失败次数' as metric_2;
SELECT '3. OSS文件删除成功率' as metric_3;
SELECT '4. Redis缓存操作成功率' as metric_4;
SELECT '5. 接口响应时间' as metric_5;

-- 告警规则建议
SELECT '建议的告警规则:' as category;
SELECT '1. 注销失败率 > 5%' as alert_rule_1;
SELECT '2. OSS文件删除失败率 > 10%' as alert_rule_2;
SELECT '3. 接口响应时间 > 5秒' as alert_rule_3;
SELECT '4. Redis连接异常' as alert_rule_4;
SELECT '5. 数据库连接异常' as alert_rule_5;

-- ========================================
-- 10. 完成部署
-- ========================================

SELECT '=== 部署完成 ===' as result;
SELECT NOW() as completion_time;
SELECT '账号注销功能部署脚本执行完成' as message;
SELECT '请按照检查清单逐项验证功能' as next_step;

-- 显示重要提醒
SELECT '⚠️  重要提醒:' as warning;
SELECT '1. 请在测试环境充分测试后再部署到生产环境' as reminder_1;
SELECT '2. 账号注销操作不可逆，请确保用户充分了解后果' as reminder_2;
SELECT '3. 建议定期检查审计日志，监控删除操作' as reminder_3;
SELECT '4. 如有问题请及时回滚并联系技术支持' as reminder_4;
