{"requestId": "6fe75f00-a853-4033-8831-a79a6ddfbd0f", "errorCode": "0", "Result": {"regions": [{"boundingBox": "99,946,1556,946,1556,1386,99,1386", "score": 0.69832, "segment": "[[[88,971],[88,1008],[92,1013],[92,1217],[96,1221],[96,1246],[104,1275],[108,1329],[113,1334],[117,1354],[133,1367],[154,1371],[167,1379],[283,1379],[288,1375],[558,1379],[563,1375],[617,1375],[621,1371],[825,1371],[829,1375],[925,1379],[929,1384],[1046,1384],[1050,1388],[1121,1388],[1125,1392],[1517,1396],[1525,1392],[1529,1375],[1534,1371],[1534,1275],[1538,1271],[1538,1221],[1542,1217],[1542,1129],[1546,1125],[1546,1104],[1554,1079],[1559,1075],[1559,1054],[1567,1029],[1567,983],[1571,979],[1554,958],[1538,958],[1534,954],[1396,954],[1392,958],[1359,958],[1354,963],[1338,963],[1321,971],[1279,975],[1275,979],[1233,979],[1229,983],[1104,983],[1029,963],[921,963],[917,958],[863,958],[858,954],[783,954],[779,950],[688,954],[683,950],[613,950],[608,946],[583,946],[579,942],[458,942],[454,938],[346,942],[342,938],[208,938],[204,933],[125,933],[121,938],[108,938],[96,946]]]"}, {"boundingBox": "132,1390,1492,1390,1492,1885,132,1885", "score": 0.903267, "segment": "[[[125,1413],[129,1434],[142,1450],[138,1463],[138,1492],[133,1496],[133,1517],[125,1538],[125,1629],[146,1709],[150,1859],[167,1875],[225,1875],[229,1871],[571,1871],[575,1875],[663,1879],[667,1884],[733,1888],[738,1892],[858,1892],[863,1896],[892,1896],[896,1892],[1004,1892],[1008,1888],[1104,1888],[1108,1884],[1196,1884],[1200,1888],[1388,1884],[1392,1888],[1421,1888],[1434,1867],[1434,1846],[1421,1829],[1367,1825],[1363,1821],[1283,1825],[1258,1809],[1242,1792],[1242,1784],[1250,1775],[1279,1754],[1325,1750],[1329,1746],[1388,1746],[1396,1742],[1409,1717],[1379,1684],[1363,1684],[1359,1679],[1325,1679],[1321,1675],[1242,1675],[1238,1671],[1146,1671],[1142,1667],[1108,1667],[1104,1663],[1079,1663],[1071,1659],[1063,1646],[1071,1625],[1083,1613],[1138,1609],[1142,1604],[1359,1604],[1363,1600],[1409,1600],[1413,1596],[1479,1596],[1488,1592],[1500,1575],[1504,1529],[1509,1525],[1509,1504],[1513,1500],[1513,1479],[1492,1463],[1450,1463],[1446,1467],[1096,1467],[1092,1463],[850,1463],[846,1459],[779,1459],[775,1454],[767,1454],[763,1459],[717,1459],[713,1463],[600,1463],[596,1459],[567,1459],[563,1454],[538,1454],[533,1450],[479,1442],[467,1434],[458,1417],[438,1400],[392,1400],[388,1396],[150,1400],[146,1404],[133,1404]],[[1575,1450],[1571,1484],[1559,1517],[1638,1592],[1650,1609],[1667,1617],[1617,1650],[1654,1654],[1659,1659],[1767,1663],[1771,1667],[1834,1663],[1838,1659],[1850,1659],[1854,1654],[1854,1642],[1846,1634],[1809,1625],[1800,1617],[1813,1604],[1829,1596],[1842,1596],[1854,1588],[1854,1542],[1859,1538],[1859,1500],[1854,1496],[1854,1479],[1859,1475],[1859,1463],[1863,1459],[1863,1446],[1854,1438],[1813,1434],[1809,1429],[1788,1429],[1763,1421],[1709,1421],[1704,1417],[1650,1413],[1625,1421],[1604,1438]],[[1759,1742],[1788,1742],[1800,1721],[1796,1717],[1763,1717],[1750,1721]]]"}, {"boundingBox": "132,1955,1917,1955,1917,2509,132,2509", "score": 0.859225, "segment": "[[[129,1984],[133,2196],[138,2200],[138,2225],[154,2300],[158,2446],[163,2450],[163,2467],[171,2475],[250,2479],[254,2484],[296,2484],[300,2488],[367,2488],[371,2492],[379,2488],[488,2492],[567,2504],[829,2504],[833,2509],[975,2504],[979,2500],[1263,2504],[1288,2496],[1288,2471],[1283,2463],[1292,2454],[1329,2446],[1921,2450],[1934,2429],[1913,2359],[1913,2309],[1909,2304],[1913,2250],[1925,2221],[1929,2142],[1888,2059],[1879,2059],[1854,2075],[1813,2063],[1763,2084],[1729,2084],[1725,2079],[1684,2075],[1667,2084],[1654,2084],[1613,2109],[1592,2113],[1542,2113],[1517,2104],[1429,2104],[1425,2100],[1409,2104],[888,2100],[838,2092],[817,2071],[808,2054],[833,2029],[858,2021],[925,2021],[929,2017],[1029,2017],[1079,2025],[1088,2017],[1100,1988],[1075,1971],[1017,1971],[1013,1975],[975,1975],[971,1971],[500,1971],[496,1967],[275,1967],[271,1963],[267,1967],[146,1971]],[[1900,2554],[1888,2525],[1871,2513],[1863,2496],[1850,2492],[1838,2500],[1838,2509],[1821,2534],[1754,2546],[1738,2554],[1734,2563],[1763,2571],[1888,2575],[1900,2563]],[[2000,2550],[2000,2592]],[[2000,2492],[2000,2496]]]"}], "qRegions": [{"boundingBox": "99,946,1556,946,1556,1386,99,1386", "answerRegions": []}, {"boundingBox": "132,1390,1492,1390,1492,1885,132,1885", "answerRegions": []}, {"boundingBox": "132,1955,1917,1955,1917,2509,132,2509", "answerRegions": []}], "figures": [], "answers": []}}