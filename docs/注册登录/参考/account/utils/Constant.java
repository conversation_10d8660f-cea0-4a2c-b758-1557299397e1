package net.snaptag.system.account.utils;
public class Constant {
    /**
     * 接口Controller映射前缀常量
     **/
    public final static String           PRE_MAPPING_URL              = "/api/account/v1";
    /**
     * 新浪微博第三方登录验证接口地址--POST
     */
    public static final String           SINA_THIRD_LOGIN_CHECK_URL   = "https://api.weibo.com/oauth2/get_token_info";
    /**
     * QQ第三方登录验证接口地址--GET, POST
     */
    public static final String           QQ_THIRD_LOGIN_CHECK_URL     = "https://openmobile.qq.com/user/get_simple_userinfo";
    /**
     * 微信第三方登录验证接口地址--GET
     */
    public static final String           WEIXIN_THIRD_LOGIN_CHECK_URL = "https://api.weixin.qq.com/sns/auth";
    /**
     * 微信第三方登录验证 获取用户信息--GET
     */
    public static final String           WEIXIN_THIRD_USER_INFO_URL   = "https://api.weixin.qq.com/cgi-bin/user/info";
    /**
     * 微信登陆
     */
    public static final String           LOGIN_BY_WEIXIN              = "byweixin";
    /**
     * 新浪登陆
     */
    public static final String           LOGIN_BY_SINA                = "bysina";
    /**
     * qq登陆
     */
    public static final String           LOGIN_BY_QQ                  = "byqq";
    /**
     * 手机登陆
     */
    public static final String           LOGIN_BY_MOBILE              = "bymobile";
    /**
     * 邮箱登陆
     */
    public static final String           LOGIN_BY_MAIL              = "bymail";
    /**
     * facebook登陆
     */
    public static final String           LOGIN_BY_FACEBOOK            = "byfacebook";
    /**
     * twitter登陆
     */
    public static final String           LOGIN_BY_TWITTER             = "bytwitter";
    /**
     * twitter登陆
     */
    public static final String           LOGIN_BY_GOOGLE             = "bygoogle";
    /**
     * appleid登陆
     */
    public static final String           LOGIN_BY_APPLEID       = "byappleid";
    /**
     * 登陆错误状态
     */
    public static final String           LOGIN_ERROR                  = "login_error";
    /**
     * 登陆正常状态
     */
    public static final String           LOGIN_NORMAL                 = "login_normal";
    /**
     * 登陆正常状态--登陆
     */
    public static final String           LOGIN_SUCCESS                = "login_success";
    public static final String CREATETIME_FIELD             = "createtime";
    public static final String CREATEUSERID_FIELD           = "createuserid";
    public static final String UPDATETIME_FIELD             = "updatetime";
    public static final String UPDATEUSERID_FIELD           = "updateuserid";
    public static final String STATUS_FIELD                 = "status";
    public static final String SOURCE_FIELD                 = "source";
    /**
     * 密码明文:pwd123
     */
    public final static String           DEFAULT_PASSWORD             = "pwd123";
    /**
     * 默认电阻值
     */
    public final static String           DEFAULT_RESISTANCE           = "123480";
    /**
     * 家庭成员上限
     */
    public final static int              USER_FAMILY_NUM              = 5;
    /**
     * 短时间内令牌桶上限个数
     */
    public final static int              ACCESS_TOKEN_MAX_NUM         = 15;

    /**
     * 单日手机验证码上限
     **/
    public final static int    DAY_SMS_MAX_TOTAL_NUM        = 50;
    /**
     * 检查验证码， 重试超过三次即弹出图片验证码窗
     */
    public static int          SMS_ACCOUNT_MAX_RETRY        = 3;
    /**
     * ip验证码次数，同一个IP一天内获取短信的次数
     */
    public static int          SMS_IP_MAX_RETRY             = 100;

}
