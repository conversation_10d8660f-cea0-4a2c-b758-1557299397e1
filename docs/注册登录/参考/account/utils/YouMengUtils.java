package net.snaptag.system.account.utils;

import com.alibaba.fastjson.JSONObject;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.http.Header;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date ：Created in 2021/6/9 17:58
 * @description：友盟的一键登录工具类
 * @modified By：
 * @version: $
 */
public class YouMengUtils {

    /***
     * 根据友盟号码一键登录产生的token，获取手机号码
     * @param umAppkey 友盟应用的appId
     * @param appKey    友盟一键登录的阿里云的appId
     * @param appSecret 友盟一键登录的阿里云的appSercet
     * @param token     手机端产生的token
     * @return 返回手机号码
     * @throws NoSuchAlgorithmException
     * @throws InvalidKeyException
     */
    public static String getMobile(String umAppkey, String appKey, String appSecret, String token) throws NoSuchAlgorithmException, InvalidKeyException {
        // 下面的url要和阿里云云市场购买的商品对应
        String url = "https://verify5.market.alicloudapi.com/api/v1/mobile/info?appkey=" + umAppkey;
        HttpPost httpPost = new HttpPost(url);
        /**
         * body
         */
        JSONObject object = new JSONObject();
        object.put("token", token);
        StringEntity stringEntity = new StringEntity(object.toJSONString(), StandardCharsets.UTF_8);
        httpPost.setEntity(stringEntity);
        /**
         * header
         */
        httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
        httpPost.setHeader("Accept", "application/json");
        httpPost.setHeader("X-Ca-Version", "1");
        httpPost.setHeader("X-Ca-Signature-Headers", "X-Ca-Version,X-Ca-Stage,X-Ca-Key,X-Ca-Timestamp");
        httpPost.setHeader("X-Ca-Stage", "RELEASE");
        httpPost.setHeader("X-Ca-Key", appKey);
        httpPost.setHeader("X-Ca-Timestamp", String.valueOf(System.currentTimeMillis()));
        httpPost.setHeader("X-Ca-Nonce", UUID.randomUUID().toString());

        httpPost.setHeader("Content-MD5", Base64.encodeBase64String(DigestUtils.md5(object.toJSONString())));
        /**
         * sign
         */
        String stringToSign = getSignString(httpPost);
        System.out.println(stringToSign);
        Mac hmacSha256 = Mac.getInstance("HmacSHA256");
        byte[] keyBytes = appSecret.getBytes(StandardCharsets.UTF_8);
        hmacSha256.init(new SecretKeySpec(keyBytes, 0, keyBytes.length, "HmacSHA256"));
        String sign = new String(Base64.encodeBase64(hmacSha256.doFinal(stringToSign.getBytes(StandardCharsets.UTF_8))));
        httpPost.setHeader("X-Ca-Signature", sign);
        /**
         * execute
         */
        CloseableHttpClient httpclient = HttpClients.createDefault();
        try {
            CloseableHttpResponse response = httpclient.execute(httpPost);
            System.out.println("----------------------------------------------");
            Header[] allHeaders = response.getAllHeaders();
            for(Header header : allHeaders){
                System.out.println(header.getName()+" : " + header.getValue());
            }
            //获取响应的内容，这里只演示简单的文本内容，实际开发中并不总是这么简单，后面详细地讲解
            String bodyAsString = EntityUtils.toString(response.getEntity(),"UTF-8");
            System.out.println("响应内容：" + bodyAsString);
            JSONObject result = JSONObject.parseObject(bodyAsString);
            if (result.getBoolean("success")){
                JSONObject data = result.getJSONObject("data");
                return data.getString("mobile");
            } else {
                throw new ServiceException(result.getString("message"));
            }
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
//        return null;
    }

    public static void main(String[] args) throws InvalidKeyException, NoSuchAlgorithmException {
        String umAppkey = "60a3737cc9aacd3bd4da3950";
        String appKey = "25356907";
        String appSecret = "6d03daa643e5c4c8028d6ad7f362df60";
        String token = "xxxx";
        // 下面的url要和阿里云云市场购买的商品对应
        String url = "https://verify5.market.alicloudapi.com/api/v1/mobile/info?appkey=" + umAppkey;
        HttpPost httpPost = new HttpPost(url);
        /**
         * body
         */
        JSONObject object = new JSONObject();
        object.put("token", token);
        StringEntity stringEntity = new StringEntity(object.toJSONString(), StandardCharsets.UTF_8);
        httpPost.setEntity(stringEntity);
        /**
         * header
         */
        httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
        httpPost.setHeader("Accept", "application/json");
        httpPost.setHeader("X-Ca-Version", "1");
        httpPost.setHeader("X-Ca-Signature-Headers", "X-Ca-Version,X-Ca-Stage,X-Ca-Key,X-Ca-Timestamp");
        httpPost.setHeader("X-Ca-Stage", "RELEASE");
        httpPost.setHeader("X-Ca-Key", appKey);
        httpPost.setHeader("X-Ca-Timestamp", String.valueOf(System.currentTimeMillis()));
        httpPost.setHeader("X-Ca-Nonce", UUID.randomUUID().toString());

        httpPost.setHeader("Content-MD5", Base64.encodeBase64String(DigestUtils.md5(object.toJSONString())));
        /**
         * sign
         */
        String stringToSign = getSignString(httpPost);
        System.out.println(stringToSign);
        Mac hmacSha256 = Mac.getInstance("HmacSHA256");
        byte[] keyBytes = appSecret.getBytes(StandardCharsets.UTF_8);
        hmacSha256.init(new SecretKeySpec(keyBytes, 0, keyBytes.length, "HmacSHA256"));
        String sign = new String(Base64.encodeBase64(hmacSha256.doFinal(stringToSign.getBytes(StandardCharsets.UTF_8))));
        httpPost.setHeader("X-Ca-Signature", sign);
        /**
         * execute
         */
        CloseableHttpClient httpclient = HttpClients.createDefault();
        try {
            CloseableHttpResponse response = httpclient.execute(httpPost);
            System.out.println("----------------------------------------------");
            Header[] allHeaders = response.getAllHeaders();
            for(Header header : allHeaders){
                System.out.println(header.getName()+" : " + header.getValue());
            }

//获取响应状态行，进而获取响应状态码
            int statusCode = response.getStatusLine().getStatusCode();
            System.out.println("状态码：" + statusCode);

//获取响应的内容，这里只演示简单的文本内容，实际开发中并不总是这么简单，后面详细地讲解
            String bodyAsString = EntityUtils.toString(response.getEntity(),"UTF-8");
            System.out.println("响应内容：" + bodyAsString);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static String getSignString(HttpPost httpPost) {
        Header[] headers = httpPost.getAllHeaders();
        Map<String, String> map = new HashMap<>();
        for (Header header : headers) {
            map.put(header.getName(), header.getValue());
        }
        return httpPost.getMethod() + "\n" +
                map.get("Accept") + "\n" +
                map.get("Content-MD5") + "\n" +
                map.get("Content-Type") + "\n\n" +
                "X-Ca-Key:" + map.get("X-Ca-Key") + "\n" +
                "X-Ca-Stage:" + map.get("X-Ca-Stage") + "\n" +
                "X-Ca-Timestamp:" + map.get("X-Ca-Timestamp") + "\n" +
                "X-Ca-Version:" + map.get("X-Ca-Version") + "\n" +
                httpPost.getURI().getPath() + "?" + httpPost.getURI().getQuery();
    }
}
