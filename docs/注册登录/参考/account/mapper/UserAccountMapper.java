package net.snaptag.system.account.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.account.entity.UserAccount;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * UserAccount Mapper接口
 */
@Mapper
public interface UserAccountMapper extends BaseMapper<UserAccount> {

    /**
     * 分页查询UserAccount列表
     */
    @Select("SELECT * FROM v1_user_account WHERE status = #{status} ORDER BY createtime DESC")
    IPage<UserAccount> findPage(Page<UserAccount> page, @Param("status") String status);

    /**
     * 查询所有UserAccount列表
     */
    @Select("SELECT * FROM v1_user_account WHERE status = #{status} ORDER BY createtime DESC")
    List<UserAccount> findAllList(@Param("status") String status);

    /**
     * 根据手机号查询用户账号
     */
    @Select("SELECT * FROM v1_user_account WHERE mobile_account = #{mobileAccount} AND status = #{status}")
    UserAccount findByMobileAccount(@Param("mobileAccount") String mobileAccount, @Param("status") String status);

    /**
     * 根据邮箱查询用户账号
     */
    @Select("SELECT * FROM v1_user_account WHERE mail_account = #{mailAccount} AND status = #{status}")
    UserAccount findByMailAccount(@Param("mailAccount") String mailAccount, @Param("status") String status);

    /**
     * 根据QQ账号查询用户账号
     */
    @Select("SELECT * FROM v1_user_account WHERE qqaccount = #{qqAccount} AND status = #{status}")
    UserAccount findByQqAccount(@Param("qqAccount") String qqAccount, @Param("status") String status);

    /**
     * 根据微信账号查询用户账号
     */
    @Select("SELECT * FROM v1_user_account WHERE weixinaccount = #{weixinAccount} AND status = #{status}")
    UserAccount findByWeixinAccount(@Param("weixinAccount") String weixinAccount, @Param("status") String status);
}
