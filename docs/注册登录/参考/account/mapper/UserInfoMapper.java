package net.snaptag.system.account.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.account.entity.UserInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * UserInfo Mapper接口
 */
@Mapper
public interface UserInfoMapper extends BaseMapper<UserInfo> {

    /**
     * 分页查询UserInfo列表
     */
    @Select("SELECT * FROM v1_user_info WHERE status = #{status} ORDER BY createtime DESC")
    IPage<UserInfo> findPage(Page<UserInfo> page, @Param("status") String status);

    /**
     * 查询所有UserInfo列表
     */
    @Select("SELECT * FROM v1_user_info WHERE status = #{status} ORDER BY createtime DESC")
    List<UserInfo> findAllList(@Param("status") String status);

    /**
     * 根据用户ID查询用户信息
     */
    @Select("SELECT * FROM v1_user_info WHERE user_id = #{userId} AND status = #{status}")
    UserInfo findByUserId(@Param("userId") String userId, @Param("status") String status);
}
