package net.snaptag.system.account.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.account.entity.UserLoginHistory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * UserLoginHistory Mapper接口
 */
@Mapper
public interface UserLoginHistoryMapper extends BaseMapper<UserLoginHistory> {

    /**
     * 分页查询UserLoginHistory列表
     */
    @Select("SELECT * FROM v1_user_login_history WHERE status = #{status} ORDER BY createtime DESC")
    IPage<UserLoginHistory> findPage(Page<UserLoginHistory> page, @Param("status") String status);

    /**
     * 查询所有UserLoginHistory列表
     */
    @Select("SELECT * FROM v1_user_login_history WHERE status = #{status} ORDER BY createtime DESC")
    List<UserLoginHistory> findAllList(@Param("status") String status);

    /**
     * 根据用户ID查询登录历史
     */
    @Select("SELECT * FROM v1_user_login_history WHERE user_id = #{userId} AND status = #{status} ORDER BY createtime DESC")
    List<UserLoginHistory> findByUserId(@Param("userId") String userId, @Param("status") String status);
}
