package net.snaptag.system.account.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.account.entity.UserTitleType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * UserTitleType Mapper接口
 */
@Mapper
public interface UserTitleTypeMapper extends BaseMapper<UserTitleType> {

    /**
     * 分页查询UserTitleType列表
     */
    @Select("SELECT * FROM v1_user_title_type WHERE status = #{status} ORDER BY createtime DESC")
    IPage<UserTitleType> findPage(Page<UserTitleType> page, @Param("status") String status);

    /**
     * 查询所有UserTitleType列表
     */
    @Select("SELECT * FROM v1_user_title_type WHERE status = #{status} ORDER BY createtime DESC")
    List<UserTitleType> findAllList(@Param("status") String status);
}
