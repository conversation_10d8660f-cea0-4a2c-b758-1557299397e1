package net.snaptag.system.account.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.account.entity.RoleTable;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * RoleTable Mapper接口
 */
@Mapper
public interface RoleTableMapper extends BaseMapper<RoleTable> {

    /**
     * 分页查询RoleTable列表
     */
    @Select("SELECT * FROM v1_role_table WHERE status = #{status} ORDER BY createtime DESC")
    IPage<RoleTable> findPage(Page<RoleTable> page, @Param("status") String status);

    /**
     * 查询所有RoleTable列表
     */
    @Select("SELECT * FROM v1_role_table WHERE status = #{status} ORDER BY createtime DESC")
    List<RoleTable> findAllList(@Param("status") String status);
}
