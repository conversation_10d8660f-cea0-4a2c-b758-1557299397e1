package net.snaptag.system.account.buservice;

import cn.hutool.core.bean.BeanUtil;
import com.vida.sls.annotation.SLSLog;
import com.vida.sls.enums.OperationType;
import net.snaptag.system.account.dao.UserAuthAccountDao;
import net.snaptag.system.account.dto.CreateAccountDto;
import net.snaptag.system.account.entity.ResourceTable;
import net.snaptag.system.account.entity.UserAuthAccount;
import net.snaptag.system.sadais.core.common.core.ToolsConst;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.core.common.utils.CryptionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.common.JsonKit;
import net.snaptag.system.sadais.web.common.WebConst;
import net.snaptag.system.sadais.web.config.CommonProperties;
import net.snaptag.system.sadais.web.dto.*;
import net.snaptag.system.sadais.web.jwt.JWTTokenUtil;
import net.snaptag.system.sadais.web.jwt.SubjectModel;
import net.snaptag.system.sadais.web.utils.Encodes;
import net.snaptag.system.sadais.web.utils.WebTools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class UserAuthBuService {

    @Autowired
    private UserAuthAccountDao userAuthAccountDao;
    @Autowired
    private CommonProperties commonProperties;
    @Autowired
    private UserRoleBuService userRoleBuService;
    @Autowired
    private RoleResourceBuService roleResourceBuService;
    @Autowired
    private RoleTableBuService roleTableBuService;
    @Autowired
    private ResourceTableBuService resourceTableBuService;

    public TokenDto loginx(String account, String passWord, String channel, String ip, String uaFlag) {
        if (!"xytAdmin".equals(account)) {
            throw new ServiceException("用户输入错误，请重输");
        }
        if (!"xytAdmin123456".equals(passWord)) {
            throw new ServiceException("密码输入错误，请重输");
        }
        SubjectModel subjectModel = new SubjectModel();
        UserInfoDto userInfoDto = new UserInfoDto();
        userInfoDto.setAccount("xytAdmin");
        userInfoDto.setName("超级管理员");
        subjectModel.setUserInfoDto(userInfoDto);
        TokenDto tokenDto = new TokenDto();
        tokenDto.setTimeOut(System.currentTimeMillis() + WebConst.JWT_PLATFORM_TTL);
        String accesstoken = JWTTokenUtil.createJWT(JsonKit.toJsonString(subjectModel), WebConst.JWT_PLATFORM_TTL);
        tokenDto.setAccesstoken(accesstoken);
        tokenDto.setSubjectModel(subjectModel);
        return tokenDto;
    }

    public TokenDto login(String account, String passWord, String appId, String ip, String ua) throws ServiceException {
        if (ToolsKit.isEmpty(account)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("用户账号不能为空");
        }
        if (ToolsKit.isEmpty(passWord)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("用户密码不能为空");
        }

//        if ("xytAdmin".equals(account)){
//            return loginx(account, passWord, appId, ip, ua);
//        }


        UserAuthAccount userAccount = userAuthAccountDao.getUserAccountByAccount(account);
        if (ToolsKit.isEmpty(userAccount)) {
            throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage("查询不到该用户信息");
        }
        if (!userAccount.getPassword().equals(WebTools.buildEntryptPassword(passWord, Encodes.decodeHex(userAccount.getSalt())))) {
            throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage("密码错误");
        }
        List<String> roleIdList = userRoleBuService.findRoleIdList(userAccount.getId(), userAccount.getProjectIds());
        List<String> resourceIdList = roleResourceBuService.findProjectIdList(roleIdList);
        Map<String, String> resourceMap = new HashMap<String, String>();
        List<ResourceDto> resource = new ArrayList<ResourceDto>();
        List<RoleDto> role = roleTableBuService.getRoleDtoList(roleIdList);
        List<ResourceTable> resourceTableList = resourceTableBuService.findResourceByIds(resourceIdList);
        if (ToolsKit.isNotEmpty(resourceTableList)) {
            for (ResourceTable resourceTable : resourceTableList) {// 一级菜单
                ResourceDto dto = new ResourceDto();
                BeanUtil.copyProperties(resourceTable, dto);
                resource.add(dto);
            }
        }
        AuthorityDto authority = new AuthorityDto();
        authority.setResource(resource);
        authority.setRole(role);
        SubjectModel subjectModel = new SubjectModel();
//        subjectModel.setAuthority(authority);
        UserInfoDto userInfoDto = new UserInfoDto();
        userInfoDto.setAccount(userAccount.getAccount());
        userInfoDto.setUserId(userAccount.getAssAttribute());
        if (ToolsKit.isEmpty(userAccount.getAssAttribute())) {
            userInfoDto.setUserId(userAccount.getId());
        }
        userInfoDto.setAppId(userAccount.getAppId());
        userInfoDto.setResource(resourceMap);
        userInfoDto.setSuperAdmin(roleTableBuService.isSuperAdmin(role));
        userInfoDto.setIp(ip);
        userInfoDto.setUa(ua);
        String json = JsonKit.toJsonString(userInfoDto);
        try {
            subjectModel.setUserInfo(CryptionUtil.bytesToHexString(CryptionUtil.Encrypt(json, commonProperties.getCryptionKey())));
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage("登录签名失败");
        }
        subjectModel.setUserInfoDto(new UserInfoDto(userAccount.getAccount(), userAccount.getName()));
        TokenDto tokenDto = new TokenDto();
        tokenDto.setTimeOut(System.currentTimeMillis() + WebConst.JWT_PLATFORM_TTL);
        String accesstoken = JWTTokenUtil.createJWT(JsonKit.toJsonString(subjectModel), WebConst.JWT_PLATFORM_TTL);
        tokenDto.setAccesstoken(accesstoken);
        tokenDto.setSubjectModel(subjectModel);
        return tokenDto;
    }

    public void saveOrUpdate(CreateAccountDto createAccountDto) {
        if (ToolsKit.isEmpty(createAccountDto.getAccount())) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("用户账号不能为空");
        }
        if (ToolsKit.isEmpty(createAccountDto.getPassword()) && ToolsKit.isEmpty(createAccountDto.getId())) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("密码不能为空");
        }

        UserAuthAccount userAccount = null;
        if (ToolsKit.isNotEmpty(createAccountDto.getId())) {
            userAccount = userAuthAccountDao.getUserAccountById(createAccountDto.getId());
            if (ToolsKit.isEmpty(userAccount)) {
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage("查询不到用户信息");
            }

            // 判断修改后的account是否有存在的
            userAccount = userAuthAccountDao.getUserAccountByAccount(createAccountDto.getAccount());
            if (ToolsKit.isNotEmpty(userAccount) && !userAccount.getId().equals(createAccountDto.getId())) {
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage("账号已存在");
            }
        } else {
            userAccount = userAuthAccountDao.getUserAccountByAccount(createAccountDto.getAccount());
            if (ToolsKit.isNotEmpty(userAccount)) {
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage("账号已存在");
            }
            userAccount = new UserAuthAccount();
            ToolsKit.setIdEntityData(userAccount, ToolsConst.SYSTEM_USER_ID);
            byte[] salt = WebTools.buildEntryptSalt();
            ToolsKit.setIdEntityData(userAccount, ToolsConst.SYSTEM_USER_ID);

            userAccount.setPassword(WebTools.buildEntryptPassword(createAccountDto.getPassword(), salt));
            userAccount.setSalt(Encodes.encodeHex(salt));
        }

        userAccount.setName(createAccountDto.getName());
        userAccount.setAccount(createAccountDto.getAccount());
        userAccount.setAssAttribute(createAccountDto.getAssAttribute());
        userAuthAccountDao.saveOrUpdate(userAccount);
    }

    public Page<UserAuthAccount> findPage(int pageNo, int pageSize) {
        pageNo = pageNo > 0 ? pageNo - 1 : pageNo;
        IPage<UserAuthAccount> page = userAuthAccountDao.findUserAccountList(null, pageNo, pageSize, null);
        Page<UserAuthAccount> result = new Page<>(pageNo + 1, pageSize);
        result.setCurrent(page.getCurrent());
        result.setSize(page.getSize());
        result.setTotal(page.getTotal());
        result.setRecords(page.getRecords());
        return result;
    }

    public Object deleteById(String id) {
        userAuthAccountDao.delUserAccount(id);
        return null;
    }

    public Object findAccountRoleById(String id) {
        return userRoleBuService.findRoleIdList(id, "");
    }

    public void disrole(String id, String roleIds) {
        // 先删除当前用户的所有权限点，
        userRoleBuService.delUserRoleByAccount(id);
        // 赋值
        String[] roleIdArray = roleIds.split(";");
        if (ToolsKit.isNotEmpty(roleIdArray)) {
            for (int i = 0; i < roleIdArray.length; i++) {
                userRoleBuService.saveUserRole(id, roleIdArray[i], "");
            }
        }
    }

    public AuthorityDto getUserAuthorityInfo(String userid) {
        AuthorityDto authority = new AuthorityDto();

        List<String> roleIdList = userRoleBuService.findRoleIdList(userid, "");
        List<String> resourceIdList = roleResourceBuService.findProjectIdList(roleIdList);
        Map<String, String> resourceMap = new HashMap<String, String>();
        List<ResourceDto> resource = new ArrayList<ResourceDto>();
        List<RoleDto> role = roleTableBuService.getRoleDtoList(roleIdList);
        List<ResourceTable> resourceTableList = resourceTableBuService.findResourceByIds(resourceIdList);
        if (ToolsKit.isNotEmpty(resourceTableList)) {
            for (ResourceTable resourceTable : resourceTableList) {// 一级菜单
                ResourceDto dto = new ResourceDto();
                BeanUtil.copyProperties(resourceTable, dto);
                resource.add(dto);
            }
        }

        authority.setResource(resource);
        authority.setRole(role);
        return authority;
    }
}
