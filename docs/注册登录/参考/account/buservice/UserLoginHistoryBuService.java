package net.snaptag.system.account.buservice;

import net.snaptag.system.account.dao.UserLoginHistoryDao;
import net.snaptag.system.account.dto.UserLoginHistoryDto;
import net.snaptag.system.account.entity.UserLoginHistory;
import net.snaptag.system.sadais.core.common.core.ToolsConst;
import net.snaptag.system.sadais.util.core.ToolsKit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2023/3/20 9:14
 * @description：
 * @modified By：
 * @version: $
 */
@Service
public class UserLoginHistoryBuService {
    @Autowired
    private UserLoginHistoryDao userLoginHistoryDao;

    /***
     * 更新用户登录新
     * @param userId    用户id
     * @param appVersion    使用的app版本
     * @param phoneType     手机和系统版本型号
     */
    public void addOrUpdate(String userId, String appVersion, String phoneType){
        UserLoginHistory userLoginHistory = userLoginHistoryDao.getById(userId);
        if (userLoginHistory==null){
            userLoginHistory = new UserLoginHistory();
            userLoginHistory.setId(userId);
            userLoginHistory.setUserId(userId);
        }
        if (ToolsKit.isNotEmpty(appVersion)){
            userLoginHistory.setAppVersion(appVersion);
        }

        if (ToolsKit.isNotEmpty(phoneType)){
            userLoginHistory.setPhoneType(phoneType);
        }

        userLoginHistory.setLatestLoginTime(new Date());
        ToolsKit.setIdEntityData(userLoginHistory, userId);
        userLoginHistoryDao.saveOrUpdate(userLoginHistory);
    }

    public void updateUsedDevice(String userId, String usedDevice) {
        if (ToolsKit.isEmpty(usedDevice) || ToolsKit.isEmpty(userId)){
            return;
        }
        UserLoginHistory userLoginHistory = userLoginHistoryDao.getById(userId);
        if (userLoginHistory==null){
            return;
        }
        if (ToolsKit.isEmpty(userLoginHistory.getUsedDevices())){
            userLoginHistory.setUsedDevices(usedDevice);
        } else if (!userLoginHistory.getUsedDevices().contains(usedDevice)){
            userLoginHistory.setUsedDevices(userLoginHistory.getUsedDevices() + "、" + usedDevice);
        } else {
            return;
        }
        userLoginHistoryDao.saveOrUpdate(userLoginHistory);
    }

    public UserLoginHistoryDto findByUserId(String userId) {
        if(ToolsKit.isEmpty(userId)){
            return null;
        }
        UserLoginHistory userLoginHistory = userLoginHistoryDao.getById(userId);
        if(userLoginHistory==null){
            return null;
        }
        UserLoginHistoryDto dto = new UserLoginHistoryDto();
        ToolsKit.Bean.copyProperties(userLoginHistory, dto);
        return dto;
    }

    public List<String> findByLoginTime(Date loginStart, Date loginEnd) {
        return userLoginHistoryDao.findByLoginTime(loginStart, loginEnd);
    }
}
