package net.snaptag.system.account.buservice;

import cn.hutool.core.date.DatePattern;
import net.snaptag.system.account.buservice.mail.MailSender;
import net.snaptag.system.account.cache.MailMessageCacheService;
import net.snaptag.system.account.cache.UserAccountCacheService;
import net.snaptag.system.account.dto.UserAccountDto;
import net.snaptag.system.account.dto.UserInfoDto;
import net.snaptag.system.account.dto.UserLoginDto;
import net.snaptag.system.account.entity.UserAccount;
import net.snaptag.system.account.entity.UserInfo;
import net.snaptag.system.account.enums.LoginStatusEnums;
import net.snaptag.system.account.utils.Constant;
import net.snaptag.system.sadais.cache.common.CacheCommonTools;
import net.snaptag.system.sadais.cache.utils.JsonKit;
import net.snaptag.system.sadais.core.common.core.ToolsConst;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.core.common.utils.CryptionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.config.CommonProperties;
import net.snaptag.system.sadais.web.core.I18nUtils;
import net.snaptag.system.sadais.web.dto.HeadInfoDto;
import net.snaptag.system.sadais.web.dto.TokenDto;
import net.snaptag.system.sadais.web.enums.SystemStatusEnums;
import net.snaptag.system.sadais.web.jwt.JWTTokenUtil;
import net.snaptag.system.sadais.web.jwt.SubjectModel;
import net.snaptag.system.sadais.web.utils.Encodes;
import net.snaptag.system.sadais.web.utils.LocalTools;
import net.snaptag.system.sadais.web.utils.WebTools;
import net.snaptag.system.smscenter.buservice.SmsMessageBuService;
import net.snaptag.system.smscenter.buservice.SystemStatusBuService;
import net.snaptag.system.smscenter.dto.SmsStatusDto;
import net.snaptag.system.storage.aliyun.utils.MD5;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.ThreadLocalRandom;

import static net.snaptag.system.sadais.web.common.WebConst.JWT_API_TTL;

/**
 * <AUTHOR>
 * @date ：Created in 2024/9/3 10:05
 * @description：因为userAccountBuService和userInfoBuService互相引用问问题，把相关互相引用部分整理起来
 * @modified By：linniantai
 * @version: $
 */
@Service
public class UserAccountAndInfoBuService {
    @Autowired
    private UserAccountBuService userAccountBuService;
    @Autowired
    private UserInfoBuService userInfoBuService;
    @Autowired
    private UserLoginHistoryBuService userLoginHistoryBuService;
    @Autowired
    private I18nUtils i18nUtils;
    @Autowired
    private CommonProperties commonProperties;
    @Autowired
    private UserAccountCacheService userAccountCacheService;
    @Autowired
    private MailSender mailSender;
    @Autowired
    private SystemStatusBuService systemStatusService;
    @Autowired
    private CacheCommonTools cacheCommonTools;
    @Autowired
    private SmsMessageBuService smsMessageBuService;

    @Autowired
    private MailMessageCacheService mailMessageCacheService;

    public UserLoginDto getLoginInfo(String userId) throws ServiceException {
        return getLoginInfo(userId, Locale.SIMPLIFIED_CHINESE);
    }

    public UserLoginDto getLoginInfo(String userId, Locale locale) throws ServiceException {
        if (ToolsKit.isEmpty(userId)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_userid_notblank, locale));
        }
        return this.getLoginInfo(userId, null, locale);
    }


    public UserLoginDto getLoginInfo(String userId, String otherId, Locale locale) throws ServiceException {
        if (ToolsKit.isEmpty(userId)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_userid_notblank, locale));
        }
        UserLoginDto userLoginDto = new UserLoginDto(LoginStatusEnums.LOGIN.getValue());
        userLoginDto.setUserInfoDto(this.getUserInfoDto(userId, otherId));
        userLoginDto.setAccountInfoDto(userAccountBuService.getAccountInfoDto(userId, locale));
        return userLoginDto;
    }

    /**
     * 获取用户登录信息
     *
     * @param codeId
     *            用户ID
     * @return
     */
    public UserLoginDto getLoginInfoByCodeId(String codeId, Locale locale) throws ServiceException {
        if (ToolsKit.isEmpty(codeId)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_user_codeid_notblank, locale));
        }
        if (codeId.length() > 11) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_param_illegal, locale));
        }
        // 不允许用手机号码当着codeId传入
        String userId = null;
        if (codeId.length() == 11) {// 手机号码
            userId = userAccountBuService.getUserAccountByAccountName(UserAccount.MOBILE_ACCOUNT_FIELD, codeId);
        } else {// 用户codeId
            userId = userInfoBuService.getUserId(Integer.parseInt(codeId));
        }
        return this.getLoginInfo(userId, null);
    }



    private UserInfoDto getUserInfoDto(String userId, String otherId) {
        try {
            UserInfo userInfo = userInfoBuService.getUserInfoByUserId(userId);
            UserAccount userAccount = userAccountBuService.getUserAccountByUserId(userId);
            if (ToolsKit.isNotEmpty(userInfo)) {
                UserInfoDto userInfoDto = userInfoBuService.getUserInfoDto(userInfo);
                if (ToolsKit.isNotEmpty(userAccount) && ToolsKit.isNotEmpty(userAccount.getUdid())) {
                    userInfoDto.setIsBandedUdid(ToolsConst.STATUS_1);
                    userInfoDto.setCreateDate(ToolsKit.Date.formatDateTime(userAccount.getCreatetime()));
                }
                userInfoDto.setUserId(userId);
                if (userInfo.getBirthday()!=null){
                    userInfoDto.setBirthDay(ToolsKit.Date.formatDate(userInfo.getBirthday()));
                    userInfoDto.setAge(ToolsKit.Date.ageOfNow(userInfo.getBirthday()));
                }

                userInfoDto.setUserLoginHistoryDto(userLoginHistoryBuService.findByUserId(userId));

                return userInfoDto;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 注册登录
     *
     * @param moblieId
     *            电话
     * @param deviceSystem
     *            描述
     * @param channel
     *            渠道
     * @param account
     *            账号
     * @param pwd
     *            密码
     * @param loginway
     *            登录途径
     * @param overseas
     *            是否海外版
     * @return RegisterReturnDto
     */
    public UserLoginDto registerAndLogin(String moblieId, String deviceSystem, String channel, String account, String pwd, String loginway, Boolean overseas, Locale locale)
            throws ServiceException {
        return registerAndLogin(null, moblieId, deviceSystem, channel, account, pwd, loginway, null, null, null, null, overseas, locale);
    }


    /**
     * 注册登录
     *
     * @param moblieId
     *            电话
     * @param deviceSystem
     *            描述
     * @param channel
     *            渠道
     * @param account
     *            账号
     * @param pwd
     *            密码
     * @param loginway
     *            登录途径
     * @param name
     *            第三方登录昵称
     * @return RegisterReturnDto
     */
    public UserLoginDto registerAndLogin(String appId, String moblieId, String deviceSystem, String channel, String account, String pwd, String loginway,
                                         String name, String userPic, String sex, String phone, Boolean overseas, Locale locale) throws ServiceException {
        try {
            moblieId = ToolsKit.isEmpty(moblieId) ? ToolsKit.getDefaultMobile() : moblieId;
            deviceSystem = ToolsKit.isEmpty(deviceSystem) ? ToolsConst.DEVICE_SYSTEM_ANDROID : deviceSystem.toUpperCase();
            userPic = ToolsKit.isEmpty(userPic) ? commonProperties.getUserDefaultPic() : userPic;
            int codeIdNum = this.getCodeId();
            if (codeIdNum <= 0) {
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_codeid_create_error, locale));
            }

            if (overseas) {
                name = ToolsKit.isEmpty(name) ? "user_" + codeIdNum : name;
            } else if (Constant.LOGIN_BY_MOBILE.equals(loginway)) {
                name = commonProperties.getDefaultName() + account.substring(7);
            } else {
                name = ToolsKit.isEmpty(name) ? commonProperties.getDefaultName() + codeIdNum : name;
            }
            // 增加默认性别
            sex = ToolsKit.isEmpty(sex)?"0":sex;



            UserAccount userAccount = userAccountBuService.saveDefaultUser(appId, moblieId, deviceSystem, account, pwd, loginway, name, phone);
            UserInfo userInfo = userInfoBuService.saveDefaultFitExtuser(appId, userAccount.getId(), codeIdNum, name, channel, ToolsKit.getSexStr(sex),
                    null, ToolsConst.DEFAULT_USER_HEIGHT,
                    ToolsConst.DEFAULT_USER_WEIGHT, userPic);
            UserLoginDto userLoginDto = new UserLoginDto(LoginStatusEnums.LOGIN.getValue());
            userLoginDto.setUserInfoDto(userInfoBuService.getUserInfoDto(userInfo));
            userLoginDto.setAccountInfoDto(userAccountBuService.getAccountInfoDto(userAccount.getId(), locale));
            return userLoginDto;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_reg_fail_try, locale));
        }
    }

    public UserLoginDto registerOnReset(String appId, String moblieId, String deviceSystem, String channel, String account, String pwd, String loginway,
                                        String name, String userPic, String sex, String phone) throws ServiceException {
        try {
            moblieId = ToolsKit.isEmpty(moblieId) ? ToolsKit.getDefaultMobile() : moblieId;
            deviceSystem = ToolsKit.isEmpty(deviceSystem) ? ToolsConst.DEVICE_SYSTEM_ANDROID : deviceSystem.toUpperCase();
            userPic = ToolsKit.isEmpty(userPic) ? commonProperties.getUserDefaultPic() : userPic;
            int codeIdNum = this.getCodeId();
            if (codeIdNum <= 0) {
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage("创建codeId错误");
            }
            name = ToolsKit.isEmpty(name) ? commonProperties.getDefaultName() + codeIdNum : name;
            UserAccount userAccount = userAccountBuService.saveDefaultUserOnReset(appId, moblieId, deviceSystem, account, pwd, loginway, name, phone);
            UserInfo userInfo = userInfoBuService.saveDefaultFitExtuser(appId, userAccount.getId(), codeIdNum, name, channel, ToolsKit.getSexStr(sex),
                    net.snaptag.system.sadais.util.core.ToolsKit.Date.parse(ToolsConst.DEFAULT_USER_BIRTHDAY, DatePattern.NORM_DATE_PATTERN), ToolsConst.DEFAULT_USER_HEIGHT,
                    ToolsConst.DEFAULT_USER_WEIGHT, userPic);
            UserLoginDto userLoginDto = new UserLoginDto(LoginStatusEnums.LOGIN.getValue());
            userLoginDto.setUserInfoDto(userInfoBuService.getUserInfoDto(userInfo));
            userLoginDto.setAccountInfoDto(userAccountBuService.getAccountInfoDto(userAccount.getId()));
            return userLoginDto;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage("由于网络的原因，注册失败，请重试!");
        }
    }


    public Page<UserInfoDto> findUserInfoPage2(String keyword, String mobile, String startDate, String endDate, String latestLoginTimeBegin, String latestLoginTimeEnd, int pageNo, int pageSize, Locale locale) {
        Date start = null;
        Date end = null;
        if (ToolsKit.isNotEmpty(startDate)) {
            start = ToolsKit.Date.parse(startDate + ToolsConst.START_TIME, DatePattern.NORM_DATETIME_PATTERN);
        }
        if (ToolsKit.isNotEmpty(endDate)) {
            end = ToolsKit.Date.parse(endDate + ToolsConst.END_TIME, DatePattern.NORM_DATETIME_PATTERN);
        }

        Date loginStart = null;
        Date loginEnd = null;
        if (ToolsKit.isNotEmpty(latestLoginTimeBegin)) {
            loginStart = ToolsKit.Date.parse(latestLoginTimeBegin + ToolsConst.START_TIME, DatePattern.NORM_DATETIME_PATTERN);
        }
        if (ToolsKit.isNotEmpty(latestLoginTimeEnd)) {
            loginEnd = ToolsKit.Date.parse(latestLoginTimeEnd + ToolsConst.END_TIME, DatePattern.NORM_DATETIME_PATTERN);
        }

        if (pageNo > 0) {
            pageNo--;
        }

        Page<UserInfoDto> result = new Page<UserInfoDto>(pageNo + 1, pageSize);

        List<String> ids = null;
        if (ToolsKit.isNotEmpty(loginStart) && ToolsKit.isNotEmpty(loginEnd)){
            ids = userLoginHistoryBuService.findByLoginTime(loginStart, loginEnd);
        }
        if (ToolsKit.isNotEmpty(mobile)) {
            if (ToolsKit.isEmpty(ids)){
                ids = userAccountBuService.findByMobile(mobile);
            } else {
                ids.addAll(userAccountBuService.findByMobile(mobile));
            }
        }

        IPage<UserInfo> page = userInfoBuService.findUserInfoList(keyword, start, end, pageNo, pageSize, ids);
        result.setCurrent(page.getCurrent());
        result.setSize(page.getSize());
        result.setTotal(page.getTotal());
        if (ToolsKit.isNotEmpty(page.getRecords())) {
            List<UserInfoDto> list = new ArrayList<UserInfoDto>();
            for (UserInfo userInfo : page.getRecords()) {
                UserInfo info = userInfoBuService.getUserInfoById(userInfo.getId(), locale);
                if (ToolsKit.isNotEmpty(info)) {
                    UserInfoDto userInfoDto = getUserInfoDto(info.getUserId(),null);

                    UserLoginDto loginDto = this.getLoginInfo(info.getUserId());
                    if (loginDto!=null){
                        userInfoDto.setAccountInfoDtoList(loginDto.getAccountInfoDto());
                    }

                    list.add(userInfoDto);
                }
            }
            result.setRecords(list);
        }
        return result;
    }

    public Page<UserInfoDto> findUserInfoPageByKeyword(String keyword,int pageNo, int pageSize) {
        if (pageNo > 0) {
            pageNo--;
        }

        Page<UserInfoDto> result = new Page<UserInfoDto>(pageNo + 1, pageSize);

        List<String> ids = null;
        if (ToolsKit.isNotEmpty(keyword)) {
            ids = new ArrayList<>();
            List<String> idsList = userAccountBuService.findByMobile(keyword);
            if (ToolsKit.isNotEmpty(idsList)){
                ids.addAll(userAccountBuService.findByMobile(keyword));
            }
        }

        IPage<UserInfo> page = null;

        if (ToolsKit.isNotEmpty(ids)){
            page  = userInfoBuService.findUserInfoList(null, null, null, pageNo, pageSize, ids);
        } else {
            page = userInfoBuService.findUserInfoList(keyword, null, null, pageNo, pageSize, null);
        }

        result.setCurrent(page.getCurrent());
        result.setSize(page.getSize());
        result.setTotal(page.getTotal());
        if (ToolsKit.isNotEmpty(page.getRecords())) {
            List<UserInfoDto> list = new ArrayList<UserInfoDto>();
            for (UserInfo userInfo : page.getRecords()) {
                UserInfo info = userInfoBuService.getUserInfoById(userInfo.getId(), new Locale("zh_CN"));
                if (ToolsKit.isNotEmpty(info)) {
                    UserInfoDto userInfoDto = getUserInfoDto(info.getUserId(),null);

                    UserLoginDto loginDto = this.getLoginInfo(info.getUserId());
                    if (loginDto!=null){
                        userInfoDto.setAccountInfoDtoList(loginDto.getAccountInfoDto());
                    }

                    list.add(userInfoDto);
                }
            }
            result.setRecords(list);
        }
        return result;
    }

    public void clearUsersCachesByUserId(String userId) {
        if (ToolsKit.isEmpty(userId)){
            return;
        }
        UserLoginDto userLoginDto = this.getLoginInfo(userId);
        if (userLoginDto!=null && ToolsKit.isNotEmpty(userLoginDto.getAccountInfoDto())){
            userLoginDto.getAccountInfoDto().forEach(item -> {
                try {
                    userAccountBuService.clearUsersCaches(item.getAccount(), item.getLoginway(), null);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
        }
    }


    /**
     * 查询用户账户信息
     *
     * @return
     */
    public UserAccountDto getUserAccountDto(String codeId, Locale locale) throws ServiceException {
        if (ToolsKit.isEmpty(codeId)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_codeid_notblank, locale));
        }
        String userId = userInfoBuService.getUserId(Integer.parseInt(codeId), locale);
        UserAccount userAccount = userAccountBuService.getUserAccountByUserId(userId);
        if (ToolsKit.isNotEmpty(userAccount)) {
            UserAccountDto userAccountDto = new UserAccountDto();
            ToolsKit.Bean.copyProperties(userAccount, userAccountDto);
            userAccountDto.setUserId(userId);
            return userAccountDto;
        }
        return null;
    }

    /**
     * 从Redis中生成获取CodeId。CodeId为自增长连续的整数
     *
     * @return
     */
    public int getCodeId() throws ServiceException {
        int codeId = 0;
        try {
            int num = 0;
            if (commonProperties.getCodeIdNum() > 0) {
                num = ThreadLocalRandom.current().nextInt(commonProperties.getCodeIdNum());
            }
            if (userAccountCacheService.existsCodeId(num)) {
                codeId = (int) userAccountCacheService.getCodeId(num);
            } else {
                codeId = userInfoBuService.getMaxCodeId();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return codeId;
    }

    public UserLoginDto checkByManagerAccount(String account, String password) {
        String accountCache = userAccountCacheService.getDefaultManagerAccount();
        String pwd = userAccountCacheService.getDefaultManagerPwd();
        String userId = userAccountCacheService.getDefaultManagerBandId();
        if (!accountCache.equals(account) || ToolsKit.isEmpty(userId)){
            return null;
        } else if (accountCache.equals(account) && !pwd.equals(password) && !MD5.MD5Encode(pwd).equals(password)) {
            throw new ServiceException("用户密码不正确");
        }
        UserAccount userAccount = userAccountBuService.getUserAccountByUserId(userId);
        UserLoginDto userLoginDto = this.getLoginInfo(userId, Locale.SIMPLIFIED_CHINESE);
        return userAccountBuService.formatUserLoginDto(userLoginDto, userAccount);
    }

    /**
     * 修改用户密码
     * @param strCodeId
     * @param oldPwd
     * @param newPwd
     * @throws ServiceException
     */
    public void modifyUserPwdById(String strCodeId, String oldPwd, String newPwd, Locale locale) throws ServiceException {
        if (ToolsKit.isEmpty(strCodeId)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_codeid_notblank, locale));
        }
        Integer codeId = 0;
        try {
            codeId = Integer.parseInt(strCodeId);
        } catch (Exception e) {

        }
        if (codeId == null || codeId <= 0) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_codeid_notblank, locale));
        }
        if (ToolsKit.isEmpty(newPwd)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_newpass_notempty, locale));
        }
        String userId = userInfoBuService.getUserId(codeId);
        UserAccount userAccount = userAccountBuService.getUserAccountByUserId(userId);
        if (ToolsKit.isNotEmpty(userAccount)) {
            // 验证旧密码是否正确
            if (ToolsKit.isNotEmpty(userAccount.getPwd())
                    && !userAccount.getPwd().equals(WebTools.buildEntryptPassword(oldPwd, Encodes.decodeHex(userAccount.getSalt())))) {
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_oldpass_invalid, locale));
            }

            try {
                // 更新新的密码
                byte[] saltByte = WebTools.buildEntryptSalt();
                String password = WebTools.buildEntryptPassword(newPwd, saltByte);
                String salt = Encodes.encodeHex(saltByte);
                userAccount.setPwd(password);
                userAccount.setSalt(salt);
                userAccountBuService.save(userAccount);
            } catch (Exception e) {
                e.printStackTrace();
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_editpass_error, locale));
            }
        }
    }

    /**
     * 获取API token令牌
     *
     * @param account
     * @param loginway
     * @param ip
     * @param ua
     * @return
     * @throws ServiceException
     */
    public TokenDto getAccesstoken(String account, String loginway, String ip, String ua, Locale locale, HeadInfoDto headInfoDto) throws ServiceException {
        if (ToolsKit.isEmpty(account)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_acc_old_notblank, locale));
        }
        if (ToolsKit.isEmpty(loginway)) { //
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_log_method_blank, locale));
        }
        Integer count = cacheCommonTools.getCacheValue("ac:token:id:", Integer.class);
        if (ToolsKit.isNotEmpty(count) && count.intValue() >= Constant.ACCESS_TOKEN_MAX_NUM) {
            throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_acc_invalid, locale));
        }
        UserAccount userAccount = userAccountBuService.getUserAccountByAccount(account, loginway, StringUtils.EMPTY);
        if (ToolsKit.isEmpty(userAccount)) {
            if (loginway.equals(ToolsConst.LOGIN_BY_APPLEID)){
                userAccount = userAccountBuService.getUserAccountByAccount(account, ToolsConst.LOGIN_BY_APPLEID2, StringUtils.EMPTY);
                if (ToolsKit.isEmpty(userAccount)){
                    throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_acc_notinquire, locale));
                }
            } else {
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_acc_notinquire, locale));
            }
        }
        cacheCommonTools.saveCacheValue("ac:token:id:" + account, ToolsKit.isEmpty(count) ? 1 : count.intValue() + 1, ToolsConst.FIFTEEN_MINUTES);
        UserInfo userInfo = userInfoBuService.getUserInfoByUserId(userAccount.getId());
        TokenDto tokenDto = new TokenDto();
        try {
            SubjectModel subjectModel = new SubjectModel();
            net.snaptag.system.sadais.web.dto.UserInfoDto userInfoDto = new net.snaptag.system.sadais.web.dto.UserInfoDto();
            userInfoDto.setUserId(userAccount.getId());
            userInfoDto.setAccount(account);
            userInfoDto.setEmail(userAccount.getMailAccount());
            userInfoDto.setMobile(userAccount.getMobileAccount());
            if (ToolsKit.isNotEmpty(userInfo)) {
                userInfoDto.setCodeId(userInfo.getCodeId());
            }
            userInfoDto.setIp(ip);
            userInfoDto.setUa(ua);
            String json = JsonKit.toJsonString(userInfoDto);
            try {
                subjectModel.setUserInfo(CryptionUtil.bytesToHexString(CryptionUtil.Encrypt(json, commonProperties.getCryptionKey())));
            } catch (Exception e) {
                e.printStackTrace();
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_log_signfail, locale));
            }
            // subjectModel.setUserInfoDto(new UserInfoDto(account, loginway));
            subjectModel.setUserInfoDto(userInfoDto);
            tokenDto.setTimeOut(System.currentTimeMillis() + JWT_API_TTL); // WebConst.JWT_API_TTL
            String accesstoken = JWTTokenUtil.createJWT(JsonKit.toJsonString(subjectModel), JWT_API_TTL);
            tokenDto.setAccesstoken(accesstoken);
            tokenDto.setSubjectModel(subjectModel);

            userAccountBuService.updateUserLoginInfo(userAccount.getId(), headInfoDto);

        } catch (Exception e) {
            e.printStackTrace();
        }
        return tokenDto;
    }

    // ----------- 邮箱注册登录 ------------

    /**
     * 检测邮箱验证码
     *
     * @param mailAddr 邮箱地址
     * @param mailCode 验证码
     * @return
     * @throws ServiceException
     */
    public void checkMailCode(String mailAddr, String mailCode, Locale locale) throws ServiceException {
        if (ToolsKit.isEmpty(mailAddr)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_email_notempty, locale));
        }
        if (ToolsKit.isEmpty(mailCode)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_vercode_notempty, locale));
        }
        try {
            SmsStatusDto smsStatusDto = new SmsStatusDto();
            boolean codeStatus = false;
            String time = mailMessageCacheService.getDayMaxTotalNum(mailAddr);
            if (ToolsKit.isEmpty(time)) {
                time = "0";
            }
            if (ToolsKit.isNotEmpty(time) && Integer.parseInt(time) > Constant.DAY_SMS_MAX_TOTAL_NUM) {
                System.out.println(i18nUtils.getKey(LocalTools.toast_vermore_aftertry, locale));
                codeStatus = false;
            }
            String valCode = mailMessageCacheService.getMsgValidatorCode(mailAddr);
            if (mailCode.equals(valCode) || mailCode.equals("9182433")) {
                codeStatus = true;
            } else {
                codeStatus = false;
            }
            smsStatusDto.setCodeStatus(codeStatus);
            systemStatusService.saveStatus(SystemStatusEnums.REGISTER_COUNT.getKey(), mailAddr, String.valueOf(ToolsConst.DAY_SECOND));
//            systemStatusService.saveAccountStatus(SystemStatusEnums.REGISTER_COUNT.getKey(), mailAddr, String.valueOf(ToolsConst.DAY_SECOND));
            if (!smsStatusDto.getCodeStatus()) {
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_vercode_incorrect, locale));
            }
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException().setCode(ExceptionEnums.NET_EXCEPTION.getCode()).setMessage(ExceptionEnums.NET_EXCEPTION.getMessage());
        }
    }

    /**
     * 检测登录状态
     *
     * @param userAccount
     * @param account
     * @param pwd
     * @param code
     * @throws ServiceException
     */
    public void checkLoginStatus(UserAccount userAccount, String account, String pwd, String code, String loginWay, Locale locale) throws ServiceException {
        if (ToolsKit.isNotEmpty(pwd) && ToolsKit.isEmpty(code)) {
            if (ToolsKit.isNotEmpty(userAccount.getPwd())
                    && (userAccount.getPwd().equals(WebTools.buildEntryptPassword(pwd, Encodes.decodeHex(userAccount.getSalt())))
                    || userAccount.getPwd().equals(WebTools.buildEntryptPassword(MD5.MD5Encode(pwd), Encodes.decodeHex(userAccount.getSalt()))))) {
            } else if (ToolsKit.isEmpty(userAccount.getPwd())){
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_user_nopassword, locale));
            } else {
                try {
//                    systemStatusService.saveAccountStatus(SystemStatusEnums.PWD_ERROR_COUNT.getKey(), account, String.valueOf(ToolsConst.DAY_SECOND));
                } catch (Exception e) {
                    e.printStackTrace();
                }
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_oldpass_invalid, locale));
            }
        } else if (ToolsKit.isEmpty(pwd) && ToolsKit.isNotEmpty(code)) {
            if (loginWay != null && loginWay.equals(Constant.LOGIN_BY_MAIL)) {
                checkMailCode(account, code, locale);
            } else {
                checkSmsCode(account, code, locale);
            }
        } else {
            throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_log_incorrect, locale));
        }
    }

    /**
     * 检测短信验证码
     *
     * @param account
     *            手机号码
     * @param code
     *            验证码
     * @return
     * @throws ServiceException
     */
    public void checkSmsCode(String account, String code, Locale locale) throws ServiceException {
        if (ToolsKit.isEmpty(account)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_mobile_notblank, locale));
        }
        if (ToolsKit.isEmpty(code)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_msgcode_notblank, locale));
        }
        try {
//            mailMessageCacheService.getMsgValidatorCode(account)
//            SmsStatusDto smsStatusDto = smsCenterService.checkCode(account, code);
            SmsStatusDto smsStatusDto = smsMessageBuService.checkCode(account, code, locale);
            systemStatusService.saveStatus(SystemStatusEnums.REGISTER_COUNT.getKey(), account, String.valueOf(ToolsConst.DAY_SECOND));
//            systemStatusService.saveAccountStatus(SystemStatusEnums.REGISTER_COUNT.getKey(), account, String.valueOf(ToolsConst.DAY_SECOND));
            if (!smsStatusDto.getCodeStatus()) {
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_vercode_incorrect, locale));
            }
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException().setCode(ExceptionEnums.NET_EXCEPTION.getCode()).setMessage(ExceptionEnums.NET_EXCEPTION.getMessage());
        }
    }

}
