package net.snaptag.system.account.buservice;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import net.snaptag.system.account.dao.UserThirdPartyAuthDao;
import net.snaptag.system.account.entity.UserThirdPartyAuth;
import net.snaptag.system.sadais.util.core.ToolsKit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Locale;

import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

/**
 * 用户第三方认证业务服务类
 * 已修正JSON字段映射问题
 */
@Service
public class UserThirdPartyAuthBuService {
    
    @Autowired
    private UserThirdPartyAuthDao userThirdPartyAuthDao;
    
    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 将JSON字符串转换为Map<String, String>
     */
    private Map<String, String> parseWechatOpenIdJson(String wechatOpenIdJson) {
        if (wechatOpenIdJson == null || wechatOpenIdJson.trim().isEmpty()) {
            return new HashMap<>();
        }
        try {
            return objectMapper.readValue(wechatOpenIdJson, new TypeReference<Map<String, String>>() {});
        } catch (Exception e) {
            // 如果解析失败，返回空Map
            return new HashMap<>();
        }
    }

    /**
     * 将Map<String, String>转换为JSON字符串
     */
    private String mapToWechatOpenIdJson(Map<String, String> wechatOpenIdMap) {
        if (wechatOpenIdMap == null || wechatOpenIdMap.isEmpty()) {
            return null;
        }
        try {
            return objectMapper.writeValueAsString(wechatOpenIdMap);
        } catch (Exception e) {
            // 如果转换失败，返回null
            return null;
        }
    }

    /**
     * 将JSON字符串转换为Map<String, String> (for qqOpenId)
     */
    private Map<String, String> parseQqOpenIdJson(String qqOpenIdJson) {
        if (qqOpenIdJson == null || qqOpenIdJson.trim().isEmpty()) {
            return new HashMap<>();
        }
        try {
            return objectMapper.readValue(qqOpenIdJson, new TypeReference<Map<String, String>>() {});
        } catch (Exception e) {
            // 如果解析失败，返回空Map
            return new HashMap<>();
        }
    }

    /**
     * 将Map<String, String>转换为JSON字符串 (for qqOpenId)
     */
    private String mapToQqOpenIdJson(Map<String, String> qqOpenIdMap) {
        if (qqOpenIdMap == null || qqOpenIdMap.isEmpty()) {
            return null;
        }
        try {
            return objectMapper.writeValueAsString(qqOpenIdMap);
        } catch (Exception e) {
            // 如果转换失败，返回null
            return null;
        }
    }

    /**
     * 根据微信UnionId获取用户ID
     */
    public String getUserIdByWeChatUnionId(String unionId, Locale locale) {
        if (ToolsKit.isEmpty(unionId)) {
            return null;
        }
        UserThirdPartyAuth auth = userThirdPartyAuthDao.getByWeChatUnionId(unionId);
        return auth != null ? auth.getUserId() : null;
    }

    /**
     * 自动关联第三方账号
     */
    public void autoRelevance(String loginway, String userId, String openId, String unionId, String account, String from, Locale locale) {
        if (ToolsKit.isEmpty(userId) || ToolsKit.isEmpty(loginway)) {
            return;
        }

        UserThirdPartyAuth auth = userThirdPartyAuthDao.getByUserId(userId);
        if (auth == null) {
            auth = new UserThirdPartyAuth();
            ToolsKit.setIdEntityData(auth, userId);
            auth.setUserId(userId);
        } else {
            ToolsKit.setIdEntityData(auth, userId);
        }

        // 根据登录方式设置相应的字段
        if ("LOGIN_BY_WEIXIN".equals(loginway)) {
            Map<String, String> wechatOpenIdMap = parseWechatOpenIdJson(auth.getWechatOpenId());
            if (ToolsKit.isNotEmpty(openId)) {
                wechatOpenIdMap.put("openid", openId);
            }
            if (ToolsKit.isNotEmpty(account)) {
                wechatOpenIdMap.put("nickname", account);
            }
            auth.setWechatOpenId(mapToWechatOpenIdJson(wechatOpenIdMap));
            auth.setWechatUnionId(unionId);
        } else if ("LOGIN_BY_QQ".equals(loginway)) {
            Map<String, String> qqOpenIdMap = parseQqOpenIdJson(auth.getQqOpenId());
            if (ToolsKit.isNotEmpty(openId)) {
                qqOpenIdMap.put("openid", openId);
            }
            if (ToolsKit.isNotEmpty(account)) {
                qqOpenIdMap.put("nickname", account);
            }
            auth.setQqOpenId(mapToQqOpenIdJson(qqOpenIdMap));
            auth.setQqUnionId(unionId);
        }

        userThirdPartyAuthDao.saveOrUpdate(auth);
    }

    /**
     * 根据用户ID获取第三方认证信息
     */
    public UserThirdPartyAuth getUserThirdPartyAuthByUserId(String userId, Locale locale) {
        if (ToolsKit.isEmpty(userId)) {
            return null;
        }
        return userThirdPartyAuthDao.getByUserId(userId);
    }

    /**
     * 解绑第三方账号
     */
    public void unbinding(String loginway, String userId, String unionId, Locale locale) {
        if (ToolsKit.isEmpty(userId) || ToolsKit.isEmpty(loginway)) {
            return;
        }

        UserThirdPartyAuth auth = userThirdPartyAuthDao.getByUserId(userId);
        if (auth == null) {
            return;
        }

        // 根据登录方式清除相应的字段
        if ("LOGIN_BY_WEIXIN".equals(loginway)) {
            auth.setWechatOpenId(null);
            auth.setWechatUnionId(null);
        } else if ("LOGIN_BY_QQ".equals(loginway)) {
            auth.setQqOpenId(null);
            auth.setQqUnionId(null);
        }

        userThirdPartyAuthDao.saveOrUpdate(auth);
    }

    /**
     * 保存第三方认证信息
     */
    public void save(UserThirdPartyAuth userThirdPartyAuth) {
        if (userThirdPartyAuth != null) {
            userThirdPartyAuthDao.saveOrUpdate(userThirdPartyAuth);
        }
    }
}
