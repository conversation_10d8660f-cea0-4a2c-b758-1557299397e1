package net.snaptag.system.account.buservice;

import net.snaptag.system.account.dao.UserRoleDao;
import net.snaptag.system.account.entity.UserRole;
import net.snaptag.system.sadais.core.common.core.ToolsConst;
import net.snaptag.system.sadais.util.core.ToolsKit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 用户角色表
 */
@Service
public class UserRoleBuService {
    @Autowired
    private UserRoleDao userRoleDao;

    /**
     * 根据用户账户和项目ID查询出角色列表
     * 
     * @param userAccountId
     *            用户账户ID
     * @param projectIds
     *            项目ID
     * @return
     */
    public List<UserRole> findUserRoleByProjectId(String userAccountId, List<String> projectIds) {
        return userRoleDao.findUserRoleByProjectId(userAccountId, projectIds);
    }

    /**
     * 根据用户账户和项目ID查询出角色ID列表
     * 
     * @param userAccountId
     *            用户账户ID
     * @param projectIds
     *            项目ID
     * @return
     */
    public List<String> findRoleIdList(String userAccountId, List<String> projectIds) {
        List<UserRole> userRoleList = this.findUserRoleByProjectId(userAccountId, projectIds);
        List<String> roleIdList = new ArrayList<String>();
        for (UserRole userRole : userRoleList) {
            if (!roleIdList.contains(userRole.getRoleId())) {
                roleIdList.add(userRole.getRoleId());
            }
        }
        return roleIdList;
    }

    /**
     * 根据用户账户和项目ID查询出角色ID列表
     * 
     * @param userAccountId
     *            用户账户ID
     * @param projectId
     *            项目ID
     * @return
     */
    public List<String> findRoleIdList(String userAccountId, String projectId) {
        List<String> projectIds = new ArrayList<String>();
        projectIds.add(projectId);
        List<UserRole> userRoleList = this.findUserRoleByProjectId(userAccountId, projectIds);
        List<String> roleIdList = new ArrayList<String>();
        for (UserRole userRole : userRoleList) {
            if (!roleIdList.contains(userRole.getRoleId())) {
                roleIdList.add(userRole.getRoleId());
            }
        }
        return roleIdList;
    }

    /**
     * 根据角色ID查询数量
     * 
     * @param roleIds
     *            角色ID
     * @return
     */
    public long getCountByRole(List<String> roleIds) {
        return userRoleDao.getCountByRole(roleIds);
    }

    /**
     * 根据ID删除用户账号角色信息
     *
     * @param accountId
     *            户账号ID
     * @throws Exception
     */
    public void delUserRoleByAccount(String accountId) {
        userRoleDao.delUserRoleByAccount(accountId);
    }

    /**
     * 保存用户账户角色信息
     * 
     * @param accountId
     *            用户账号ID
     * @param roleId
     *            角色ID
     * @param projectId
     *            项目ID
     */
    public void saveUserRole(String accountId, String roleId, String projectId) {
        UserRole userRole = new UserRole();
        ToolsKit.setIdEntityData(userRole, ToolsConst.SYSTEM_USER_ID);
        userRole.setUserAccountId(accountId);
        userRole.setRoleId(roleId);
        userRole.setProjectId(projectId);
        userRoleDao.saveOrUpdate(userRole);
    }
}
