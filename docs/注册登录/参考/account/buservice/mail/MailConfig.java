package net.snaptag.system.account.buservice.mail;

import org.springframework.context.annotation.Configuration;

import java.util.Properties;

/**
 * 邮件发送配置信息
 */
@Configuration
//@ConfigurationProperties(prefix = "mail")
public class MailConfig {

    /**
     * 发送邮件帐号
     */
    private String account;
    /**
     * 发送邮件密码
     */
    private String password;
    /**
     * 服务端口
     */
    private Integer serverPort;
    /**
     * 服务主机
     */
    private String serverHost;
    /**
     * 设置是否使用ssl安全连接 ---一般都使用
     */
    private String sslEnable;
    /**
     * 设置是否显示debug信息 true 会在控制台显示相关信息
     */
    private String debug;
    /**
     * 默认邮件主题
     */
    private String subject;

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Integer getServerPort() {
        return serverPort;
    }

    public void setServerPort(Integer serverPort) {
        this.serverPort = serverPort;
    }

    public String getServerHost() {
        return serverHost;
    }

    public void setServerHost(String serverHost) {
        this.serverHost = serverHost;
    }

    public String getSslEnable() {
        return sslEnable;
    }

    public void setSslEnable(String sslEnable) {
        this.sslEnable = sslEnable;
    }

    public String getDebug() {
        return debug;
    }

    public void setDebug(String debug) {
        this.debug = debug;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public MailConfig() {
//        this.account = "<EMAIL>";
//        this.password = "pkgkxwdmqnugbigb";
//        this.serverHost = "smtp.qq.com";
//        this.serverPort = 465;
//        this.sslEnable = "true";
//        this.debug = "true";
        this.account = "<EMAIL>";
        this.password = "Star0920";
        this.serverHost = "smtp.qiye.aliyun.com";
//        this.serverHost = "smtp.exmail.qq.com";
        this.serverPort = 465;
        this.sslEnable = "true";
        this.debug = "true";
    }

    //@Bean(name = "mailProperties")
    public Properties mailProperties() {
        Properties properties = new Properties();
        properties.put("mail.transport.protocol", "smtp");
        properties.put("mail.account", account);
        properties.put("mail.password", password);
        properties.put("mail.smtp.host", serverHost);
        properties.put("mail.smtp.port", serverPort);
        properties.put("mail.smtp.auth", "true");
        properties.put("mail.smtp.ssl.enable", sslEnable);
        properties.put("mail.debug", debug);
        return properties;
    }

}
