package net.snaptag.system.account.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.snaptag.system.common.BaseEntity;

/***
 * 用户头衔
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("v1_user_title_type")
public class UserTitleType extends BaseEntity {
    /**
     * 编号（需要唯一）
     */
    @TableField("code")
    private int     code;

    /**
     * 头衔名称
     */
    @TableField("name")
    private String  name;

    /**
     * 名称url地址
     */
    @TableField("name_url")
    private String  nameUrl;

    /**
     * 头衔url地址
     */
    @TableField("border_url")
    private String  borderUrl;

    /**
     * 0：表示官方；1：其他（用戶）这个头衔只能官方授权还是用户自己可以设置，为以后扩充用
     */
    @TableField("is_official")
    private int     isOfficial;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getIsOfficial() {
        return isOfficial;
    }

    public void setIsOfficial(int isOfficial) {
        this.isOfficial = isOfficial;
    }

    public String getNameUrl() {
        return nameUrl;
    }

    public void setNameUrl(String nameUrl) {
        this.nameUrl = nameUrl;
    }

    public String getBorderUrl() {
        return borderUrl;
    }

    public void setBorderUrl(String borderUrl) {
        this.borderUrl = borderUrl;
    }
}
