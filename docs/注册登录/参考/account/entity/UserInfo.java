package net.snaptag.system.account.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.snaptag.system.common.BaseEntity;

import java.util.Date;

/**
 * 用户信息表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("v1_user_info")
public class UserInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;
    public static final String COLL = "v1_user_info";
    public static final String USER_ID_FIELD = "user_id";
    public static final String CODE_ID_FIELD = "code_id";
    public static final String NICK_NAME_FIELD = "nick_name";
    public static final String APP_ID_FIELD = "app_id";
    public static final String SEX_FIELD = "sex";

    /**
     * 账号ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 昵称
     */
    @TableField("nick_name")
    private String nickName;

    /**
     * 用户头像
     */
    @TableField("user_pic")
    private String userPic;

    /**
     * 性别
     */
    @TableField("sex")
    private String sex;

    /**
     * 生日
     */
    @TableField("birthday")
    private Date birthday;

    /**
     * 当前体重
     */
    @TableField("current_weight")
    private double currentWeight;

    /**
     * 目标体重
     */
    @TableField("target_weight")
    private double targetWeight;

    /**
     * 身高
     */
    @TableField("height")
    private int height;

    /**
     * 渠道
     */
    @TableField("channel")
    private String channel;

    /**
     * 用户codeId
     */
    @TableField("code_id")
    private int codeId;

    /**
     * 是否编辑过信息 0否 1是
     */
    @TableField("is_edit")
    private int isEdit;

    /**
     * 是否是官方认证（3.2版本后，会弃用，由userTitleType替代）
     */
    @TableField("is_official")
    private int isOfficial;

    /**
     * 所在年级1--12对应小学一年级到高中三年级
     */
    @TableField("grade_level")
    private int gradeLevel;

    /**
     * 身份 0：其他；1：学生；2：老师；3：家长
     */
    @TableField("role")
    private int role;

    /**
     * 用户头衔 0:普通用户；1:官方认证；2:KOL认证；3:素材编辑
     */
    @TableField("user_title_type")
    private int userTitleType;

    /**
     * 是否禁用，0：否，1：禁用
     */
    @TableField("is_forbidden")
    private int isForbidden;

}
