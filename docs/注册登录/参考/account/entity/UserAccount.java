package net.snaptag.system.account.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.snaptag.system.common.BaseEntity;

/**
 * 用户账号表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("v1_user_account")
public class UserAccount extends BaseEntity {
    private static final long   serialVersionUID     = 1L;
    public static final String COLL = "v1_user_account";
    public static final String MOBILE_ACCOUNT_FIELD = "mobile_account";
    public static final String MAIL_ACCOUNT_FIELD = "mail_account";
    public static final String SINAACCOUNT_FIELD = "sinaaccount";
    public static final String QQACCOUNT_FIELD = "qqaccount";
    public static final String WEIXINACCOUNT_FIELD = "weixinaccount";
    public static final String FBACCOUNT_FIELD = "fbaccount";
    public static final String TWACCOUNT_FIELD = "twaccount";
    public static final String WAACCOUNT_FIELD = "waaccount";
    public static final String GOOGLEACCOUNT_FIELD = "googleaccount";
    public static final String APPLEACCOUNT_FIELD = "appleaccount";
    public static final String APPLEACCOUNT_FIELD2 = "appleaccount2";

    /**
     * 手机号码
     */
    @TableField("mobile_account")
    private String mobileAccount;

    /**
     * 邮箱地址
     */
    @TableField("mail_account")
    private String mailAccount;

    /**
     * 密码
     */
    @TableField("pwd")
    private String pwd;

    /**
     * 盐值
     */
    @TableField("salt")
    private String salt;

    /**
     * 新浪微博账号
     */
    @TableField("sinaaccount")
    private String sinaaccount;

    /**
     * 新浪微博密码
     */
    @TableField("sinapwd")
    private String sinapwd;

    /**
     * qq账号
     */
    @TableField("qqaccount")
    private String qqaccount;

    /**
     * qq密码
     */
    @TableField("qqpwd")
    private String qqpwd;

    /**
     * 微信账号
     */
    @TableField("weixinaccount")
    private String weixinaccount;

    /**
     * 微信密码
     */
    @TableField("weixinpwd")
    private String weixinpwd;

    /**
     * 脸书账号
     */
    @TableField("fbaccount")
    private String fbaccount;

    /**
     * 脸书密码
     */
    @TableField("fbpwd")
    private String fbpwd;
    /**
     * 推特账号
     */
    @TableField("twaccount")
    private String twaccount;

    /**
     * 推特密码
     */
    @TableField("twpwd")
    private String twpwd;

    /**
     * WhatsApp账号
     */
    @TableField("waaccount")
    private String waaccount;

    /**
     * WhatsApp密码
     */
    @TableField("wapwd")
    private String wapwd;

    /**
     * 谷歌账号
     */
    @TableField("googleaccount")
    private String googleaccount;

    /**
     * 谷歌密码
     */
    @TableField("googlepwd")
    private String googlepwd;

    /**
     * AppleID账号
     */
    @TableField("appleaccount")
    private String appleaccount;

    /**
     * AppleID账号2
     */
    @TableField("appleaccount2")
    private String appleaccount2;

    /**
     * 第三方绑定名称 - JSON格式字符串
     * 例如: {"wechat": "微信昵称", "qq": "QQ昵称"}
     */
    @TableField(value = "bind_name")
    private String bindName;

    @TableField("moblie_id")
    private String              moblieId;                               // 设备ID

    @TableField("device_system")
    private String              deviceSystem;                           // 设备系统

    @TableField(exist = false)
    private String              udid;                                   // 苹果设备唯一ID

    @TableField(exist = false)
    private String              nationCode;                             // 国际区号

    public String getUdid() {
        return udid;
    }

    public void setUdid(String udid) {
        this.udid = udid;
    }

    public String getSalt() {
        return salt;
    }

    public void setSalt(String salt) {
        this.salt = salt;
    }

    public String getDeviceSystem() {
        return deviceSystem;
    }

    public void setDeviceSystem(String deviceSystem) {
        this.deviceSystem = deviceSystem;
    }

    public String getMoblieId() {
        return moblieId;
    }

    public void setMoblieId(String moblieId) {
        this.moblieId = moblieId;
    }

    public String getBindName() {
        return bindName;
    }

    public void setBindName(String bindName) {
        this.bindName = bindName;
    }

    public String getMobileAccount() {
        return mobileAccount;
    }

    public void setMobileAccount(String mobileAccount) {
        this.mobileAccount = mobileAccount;
    }

    public String getMailAccount() {
        return mailAccount;
    }

    public void setMailAccount(String mailAccount) {
        this.mailAccount = mailAccount;
    }

    public String getPwd() {
        return pwd;
    }

    public void setPwd(String pwd) {
        this.pwd = pwd;
    }

    public String getSinaaccount() {
        return sinaaccount;
    }

    public void setSinaaccount(String sinaaccount) {
        this.sinaaccount = sinaaccount;
    }

    public String getSinapwd() {
        return sinapwd;
    }

    public void setSinapwd(String sinapwd) {
        this.sinapwd = sinapwd;
    }

    public String getQqaccount() {
        return qqaccount;
    }

    public void setQqaccount(String qqaccount) {
        this.qqaccount = qqaccount;
    }

    public String getQqpwd() {
        return qqpwd;
    }

    public void setQqpwd(String qqpwd) {
        this.qqpwd = qqpwd;
    }

    public String getWeixinaccount() {
        return weixinaccount;
    }

    public void setWeixinaccount(String weixinaccount) {
        this.weixinaccount = weixinaccount;
    }

    public String getWeixinpwd() {
        return weixinpwd;
    }

    public void setWeixinpwd(String weixinpwd) {
        this.weixinpwd = weixinpwd;
    }

    public String getFbaccount() {
        return fbaccount;
    }

    public void setFbaccount(String fbaccount) {
        this.fbaccount = fbaccount;
    }

    public String getFbpwd() {
        return fbpwd;
    }

    public void setFbpwd(String fbpwd) {
        this.fbpwd = fbpwd;
    }

    public String getTwaccount() {
        return twaccount;
    }

    public void setTwaccount(String twaccount) {
        this.twaccount = twaccount;
    }

    public String getTwpwd() {
        return twpwd;
    }

    public void setTwpwd(String twpwd) {
        this.twpwd = twpwd;
    }

    public String getAppleaccount() {
        return appleaccount;
    }

    public String getAppleaccount2() {
        return appleaccount2;
    }

    public void setAppleaccount2(String appleaccount2) {
        this.appleaccount2 = appleaccount2;
    }

    public void setAppleaccount(String appleaccount) {
        this.appleaccount = appleaccount;
    }

    public String getNationCode() {
        return nationCode;
    }

    public void setNationCode(String nationCode) {
        this.nationCode = nationCode;
    }

    public String getGoogleaccount() {
        return googleaccount;
    }

    public void setGoogleaccount(String googleaccount) {
        this.googleaccount = googleaccount;
    }

    public String getGooglepwd() {
        return googlepwd;
    }

    public void setGooglepwd(String googlepwd) {
        this.googlepwd = googlepwd;
    }

    public String getWapwd() {
        return wapwd;
    }

    public void setWapwd(String wapwd) {
        this.wapwd = wapwd;
    }

    public String getWaaccount() {
        return waaccount;
    }

    public void setWaaccount(String waaccount) {
        this.waaccount = waaccount;
    }
}
