package net.snaptag.system.account.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.snaptag.system.common.BaseEntity;

/**
 * 角色表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("v1_role_table")
public class RoleTable extends BaseEntity {
    private static final long serialVersionUID = 1L;
    public static final String COLL = "v1_role_table";
    public static final String ROLE_NAME_FIELD = "role_name";
    public static final String ROLE_CODE_FIELD = "role_code";
    public static final String APP_ID_FIELD = "app_id";

    /**
     * 项目ID
     */
    @TableField("project_id")
    private String projectId;

    /**
     * 角色名称
     */
    @TableField("role_name")
    private String roleName;

    /**
     * 角色编码
     */
    @TableField("role_code")
    private String roleCode;
}
