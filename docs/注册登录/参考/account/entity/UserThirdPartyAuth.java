package net.snaptag.system.account.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.snaptag.system.common.BaseEntity;

/**
 * 用户第三方授权信息
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("v1_user_third_party_auth")
public class UserThirdPartyAuth extends BaseEntity {
    private static final long serialVersionUID = 1L;
    public static final String COLL = "v1_user_third_party_auth";
    public static final String USER_ID_FIELD = "user_id";
    public static final String WECHAT_UNION_ID_FIELD = "wechat_union_id";

    /**
     * 用户id(唯一索引)
     */
    @TableField("user_id")
    private String userId;

    /**
     * 微信openID - JSON格式字符串
     * 例如: {"openid": "wx123456", "nickname": "用户昵称"}
     */
    @TableField(value = "wechat_open_id")
    private String wechatOpenId;
    /**
     * 微信unionID
     */
    @TableField("wechat_union_id")
    private String wechatUnionId;
    /**
     * qq openID - JSON格式字符串
     * 例如: {"openid": "qq123456", "nickname": "用户昵称"}
     */
    @TableField("qq_open_id")
    private String qqOpenId;
    /**
     * qq unionID
     */
    @TableField("qq_union_id")
    private String qqUnionId;
    /**
     * 微博 unionID
     */
    @TableField("weibo_union_id")
    private String weiboUnionId;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getWechatOpenId() {
        return wechatOpenId;
    }

    public void setWechatOpenId(String wechatOpenId) {
        this.wechatOpenId = wechatOpenId;
    }

    public String getWechatUnionId() {
        return wechatUnionId;
    }

    public void setWechatUnionId(String wechatUnionId) {
        this.wechatUnionId = wechatUnionId;
    }

    public String getQqOpenId() {
        return qqOpenId;
    }

    public void setQqOpenId(String qqOpenId) {
        this.qqOpenId = qqOpenId;
    }

    public String getQqUnionId() {
        return qqUnionId;
    }

    public void setQqUnionId(String qqUnionId) {
        this.qqUnionId = qqUnionId;
    }

    public String getWeiboUnionId() {
        return weiboUnionId;
    }

    public void setWeiboUnionId(String weiboUnionId) {
        this.weiboUnionId = weiboUnionId;
    }
}