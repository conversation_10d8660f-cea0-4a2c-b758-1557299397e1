package net.snaptag.system.account.dto;
import java.io.Serializable;

public class CreateAccountDto implements Serializable {
    /**
     * 用户账号dto
     */
    private static final long serialVersionUID = 1L;
    private String            id;                   // 记录ID
    private String            name;                 // 用户名称
    private String            account;              // 用户账号
    private String            password;             // 用户密码
    private String            projectId;            // 项目ID
    private String            assAttribute;         // 关联属性
    private String            role;                 // 角色

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getAssAttribute() {
        return assAttribute;
    }

    public void setAssAttribute(String assAttribute) {
        this.assAttribute = assAttribute;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }
}
