package net.snaptag.system.account.dto;
import java.io.Serializable;
public class AddUserFamilyDto implements Serializable {
    /**
     * 添加家庭成员信息--服务器返回
     */
    private static final long serialVersionUID = 1L;
    private String            userId;               // 用户ID
    private int               height;               // 身高
    private String            nickName;             // 昵称
    private String            birthday;             // 生日
    private double            currentWeight;        // 当前体重
    private String            sex;                  // 性别
    private String            pic;                  // 头像

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public int getHeight() {
        return height;
    }

    public void setHeight(int height) {
        this.height = height;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public double getCurrentWeight() {
        return currentWeight;
    }

    public void setCurrentWeight(double currentWeight) {
        this.currentWeight = currentWeight;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }
}
