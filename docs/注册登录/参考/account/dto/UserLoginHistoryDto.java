package net.snaptag.system.account.dto;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date ：Created in 2023/3/20 9:09
 * @description：用户登录信息
 * @modified By：
 * @version: $
 */
public class UserLoginHistoryDto implements Serializable {
    private Date latestLoginTime;
    private String appVersion;
    private String usedDevices;
    private String phoneType;

    public Date getLatestLoginTime() {
        return latestLoginTime;
    }

    public void setLatestLoginTime(Date latestLoginTime) {
        this.latestLoginTime = latestLoginTime;
    }

    public String getAppVersion() {
        return appVersion;
    }

    public void setAppVersion(String appVersion) {
        this.appVersion = appVersion;
    }

    public String getUsedDevices() {
        return usedDevices;
    }

    public void setUsedDevices(String usedDevices) {
        this.usedDevices = usedDevices;
    }

    public String getPhoneType() {
        return phoneType;
    }

    public void setPhoneType(String phoneType) {
        this.phoneType = phoneType;
    }
}
