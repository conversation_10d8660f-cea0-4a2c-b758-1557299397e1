package net.snaptag.system.account.cache;

import cn.hutool.core.date.DatePattern;
import net.snaptag.system.account.enums.MailMessageCacheEnums;
import net.snaptag.system.sadais.cache.kit.CacheKit;
import net.snaptag.system.sadais.core.common.core.ToolsConst;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 邮件验证码缓存
 */
@Service
public class MailMessageCacheService {

    /**
     * 设置验证码弹窗次数
     *
     * @param account - 邮箱地址
     */
    public void setAccountMaxRetryCount(String account) {
        String date = net.snaptag.system.sadais.util.core.ToolsKit.Date.format(new Date(), DatePattern.PURE_DATE_PATTERN);
        String key = MailMessageCacheEnums.MESSAGE_ACCOUNT_RETRY.getKey() + date + ":" + account;
        CacheKit.cache().incr(key, ToolsConst.DAY_SECOND);
    }

    /**
     * 获取验证码弹窗次数
     *
     * @param account - 邮箱地址
     * @return
     */
    public String getAccountMaxRetryCount(String account) {
        String date = net.snaptag.system.sadais.util.core.ToolsKit.Date.format(new Date(), DatePattern.PURE_DATE_PATTERN);
        String key = MailMessageCacheEnums.MESSAGE_ACCOUNT_RETRY.getKey() + date + ":" + account;
        return CacheKit.cache().get(key, String.class);
    }

    /**
     * 移除验证码弹窗次数
     *
     * @param account - 邮箱地址
     * @return
     */
    public void removeAccountMaxRetryCount(String account) {
        String date = net.snaptag.system.sadais.util.core.ToolsKit.Date.format(new Date(), DatePattern.PURE_DATE_PATTERN);
        String key = MailMessageCacheEnums.MESSAGE_ACCOUNT_RETRY.getKey() + date + ":" + account;
        CacheKit.cache().del(key);
    }

    /**
     * 设置短信验证码
     *
     * @param account - 邮箱地址
     * @return
     */
    public void setMsgValidatorCode(String account, String code) {
        String key = MailMessageCacheEnums.MESSAGE_VALIDATOR_BY_ACCOUNT.getKey() + account;
        CacheKit.cache().set(key, code, ToolsConst.FIVE_MINUTES);
    }

    /**
     * 删除短信验证码
     *
     * @param account - 邮箱地址
     * @return
     */
    public void delMsgValidatorCode(String account) {
        String key = MailMessageCacheEnums.MESSAGE_VALIDATOR_BY_ACCOUNT.getKey() + account;
        CacheKit.cache().del(key);
    }

    /**
     * 获取短信验证码
     *
     * @param account - 邮箱地址
     * @return
     */
    public String getMsgValidatorCode(String account) {
        String key = MailMessageCacheEnums.MESSAGE_VALIDATOR_BY_ACCOUNT.getKey() + account;
        return CacheKit.cache().get(key, String.class);
    }

    /**
     * 获取单日验证码上限数
     *
     * @param account - 邮箱地址
     * @return
     */
    public String getDayMaxTotalNum(String account) {
        String date = net.snaptag.system.sadais.util.core.ToolsKit.Date.format(new Date(), DatePattern.PURE_DATE_PATTERN);
        String key = MailMessageCacheEnums.MESSAGE_MAX_TOTAL_BY_DAY.getKey() + date + ":" + account;
        return CacheKit.cache().get(key, String.class);
    }

    /**
     * 设置单日验证码上限数
     *
     * @param account - 邮箱地址
     * @return
     */
    public void setDayMaxTotalNum(String account) {
        String date = net.snaptag.system.sadais.util.core.ToolsKit.Date.format(new Date(), DatePattern.PURE_DATE_PATTERN);
        String key = MailMessageCacheEnums.MESSAGE_MAX_TOTAL_BY_DAY.getKey() + date + ":" + account;
        CacheKit.cache().incr(key, ToolsConst.DAY_SECOND);
    }

    /**
     * 获取单日ip数量上限数
     *
     * @param ip
     * @return
     */
    public String getDayIpTotalNum(String ip) {
        String date = net.snaptag.system.sadais.util.core.ToolsKit.Date.format(new Date(), DatePattern.PURE_DATE_PATTERN);
        String key = MailMessageCacheEnums.MESSAGE_IP_COUNT_BY_DAY.getKey() + date + ":" + ip;
        return CacheKit.cache().get(key, String.class);
    }

    /**
     * 设置单日ip数量上限数
     *
     * @param ip
     * @return
     */
    public void setDayIpTotalNum(String ip) {
        String date = net.snaptag.system.sadais.util.core.ToolsKit.Date.format(new Date(), DatePattern.PURE_DATE_PATTERN);
        String key = MailMessageCacheEnums.MESSAGE_IP_COUNT_BY_DAY.getKey() + date + ":" + ip;
        CacheKit.cache().incr(key, ToolsConst.DAY_SECOND);
    }

    /**
     * 设置用户邮箱验证码时间间隔
     *
     * @param account - 邮箱地址
     */
    public void setMailCodeIntervalTime(String account) {
        CacheKit.cache().set(MailMessageCacheEnums.MESSAGE_INTERVAL_TIME.getKey() + account, account, 60);
    }

    /**
     * 获取邮箱验证码间隔时间
     *
     * @param account - 邮箱地址
     */
    public String getMailCodeIntervalTime(String account) {
        return CacheKit.cache().get(MailMessageCacheEnums.MESSAGE_INTERVAL_TIME.getKey() + account, String.class);
    }

    /**
     * 获取验证码剩余间隔时间
     *
     * @param account - 邮箱地址
     * @return
     */
    public int getIntervalTime(String account) {
        return CacheKit.cache().ttl(MailMessageCacheEnums.MESSAGE_INTERVAL_TIME.getKey() + account).intValue();
    }

}
