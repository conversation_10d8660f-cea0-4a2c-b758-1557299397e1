package net.snaptag.system.account.cache;

import net.snaptag.system.account.entity.UserAccount;
import net.snaptag.system.account.enums.UserAccountCacheEnums;
import net.snaptag.system.sadais.cache.kit.CacheKit;
import net.snaptag.system.sadais.core.common.core.ToolsConst;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.util.core.ToolsKit;
import org.springframework.stereotype.Service;
@Service
public class UserAccountCacheService {
    /**
     * 保存用户账号信息记录
     *
     * @param userAccount
     *            用户账号信息
     */
    public void saveUserAccount(UserAccount userAccount) {
        CacheKit.cache().set(UserAccountCacheEnums.USER_ACCOUNT_BY_USER_ID.getKey() + userAccount.getId(), userAccount, ToolsConst.DAY_SECOND * 3);
    }

    /**
     * 获取用户账号信息记录
     *
     * @param userId
     *            用户ID
     * @return 用户账号信息记录
     */
    public UserAccount getUserAccountByUserId(String userId) {
        // CacheKit.cache().del(UserAccountCacheEnums.USER_ACCOUNT_BY_USER_ID.getKey() + userId);
        return CacheKit.cache().get(UserAccountCacheEnums.USER_ACCOUNT_BY_USER_ID.getKey() + userId, UserAccount.class);
    }

    /**
     * 删除用户账号信息
     * 
     * @param userId
     *            用户ID
     */
    public void delUserAccount(String userId) {
        String key = UserAccountCacheEnums.USER_ACCOUNT_BY_USER_ID.getKey() + userId;
        CacheKit.cache().del(key);
    }

    /**
     * 获取账号ID
     *
     * @param accountName
     *            账号属性名称
     * @param account
     *            账号信息
     * @return 用户账号信息记录
     */
    public String getUserAccountByAccountName(String accountName, String account) {
        // CacheKit.cache().del(UserAccountCacheEnums.USER_ACCOUNT_ID_BY_ACCOUNT_NAME.getKey() + accountName + ":" + account);
        return CacheKit.cache().get(UserAccountCacheEnums.USER_ACCOUNT_ID_BY_ACCOUNT_NAME.getKey() + accountName + ":" + account, String.class);
    }

    /**
     * 保存账号ID
     * 
     * @param accountName
     *            账号属性名称
     * @param account
     *            账号信息
     * @param userId
     *            用户ID
     * @return 账号ID信息记录
     */
    public void setUserAccountName(String accountName, String account, String userId) {
        CacheKit.cache().set(UserAccountCacheEnums.USER_ACCOUNT_ID_BY_ACCOUNT_NAME.getKey() + accountName + ":" + account, userId, ToolsConst.DAY_SECOND * 3);
    }

    /**
     * 删除账号对应的用户ID
     * 
     * @param accountName
     *            账号属性名称
     * @param account
     *            账号信息
     */
    public void delUserAccountName(String accountName, String account) {
        CacheKit.cache().del(UserAccountCacheEnums.USER_ACCOUNT_ID_BY_ACCOUNT_NAME.getKey() + accountName + ":" + account);
    }

    /**
     * 从Redis中生成获取CodeId。CodeId为自增长连续的整数
     *
     * @return
     */
    public long getCodeId(int num) throws ServiceException {
        String key = UserAccountCacheEnums.USER_CODEID_BY_RANDOM.getKey().replace("{num}", num + "");
        return CacheKit.cache().incr(key, ToolsConst.DAY_SECOND * 180);
    }

    /**
     * 判断codeId key是否存在
     * 
     * @param num
     * @return
     * @throws ServiceException
     */
    public boolean existsCodeId(int num) throws ServiceException {
        String key = UserAccountCacheEnums.USER_CODEID_BY_RANDOM.getKey().replace("{num}", num + "");
        return CacheKit.cache().exists(key);
    }

    /***
     * 标记用户账号在7天内注销过
     * @param account
     */
    public void addUnRegisterFlag(String account){
        // 为了实现删除的用户账号7天内无法二次注册，添加缓存
        CacheKit.cache().set(UserAccountCacheEnums.USER_ACCOUNT_DELETED_BY_ACCOUNT.getKey() + account, account, ToolsConst.WEEK_SECOND);
    }

    /***
     * 判断当前账号是否在一周内有做过注销动作
     * @param account
     * @return
     */
    public boolean checkIsDeletedInWeektime(String account){
        if (CacheKit.cache().exists(UserAccountCacheEnums.USER_ACCOUNT_DELETED_BY_ACCOUNT.getKey() + account)){
            return true;
        } else {
            return false;
        }
    }

    public String getDefaultManagerAccount() {
        String account = CacheKit.cache().get(UserAccountCacheEnums.USER_MANAGER_DEFAULT_ACCOUNT.getKey(), String.class);
        if (ToolsKit.isEmpty(account)){
            return "<EMAIL>";
        } else {
            return account;
        }
    }

    public void setDefaultManagerAccount(String account) {
        CacheKit.cache().set(UserAccountCacheEnums.USER_MANAGER_DEFAULT_ACCOUNT.getKey(),account, ToolsConst.MONTH_SECOND);
    }

    public void setDefaultManagerPassword(String pwd) {
        CacheKit.cache().set(UserAccountCacheEnums.USER_MANAGER_DEFAULT_PASSWORD.getKey(),pwd, ToolsConst.MONTH_SECOND);
    }

    public void setDefaultManagerBandId(String userId) {
        CacheKit.cache().set(UserAccountCacheEnums.USER_MANAGER_DEFAULT_BANDID.getKey(),userId, ToolsConst.DAY_SECOND);
    }

    public String getDefaultManagerPwd() {
        String pwd = CacheKit.cache().get(UserAccountCacheEnums.USER_MANAGER_DEFAULT_PASSWORD.getKey(), String.class);
        if (ToolsKit.isEmpty(pwd)){
            return "123456";
        } else {
            return pwd;
        }
    }

    public String getDefaultManagerBandId() {
        return CacheKit.cache().get(UserAccountCacheEnums.USER_MANAGER_DEFAULT_BANDID.getKey(), String.class);
    }
}
