package net.snaptag.system.account.cache;

import net.snaptag.system.account.entity.UserInfo;
import net.snaptag.system.account.enums.UserInfoCacheEnums;
import net.snaptag.system.sadais.cache.kit.CacheKit;
import net.snaptag.system.sadais.core.common.core.ToolsConst;
import org.springframework.stereotype.Service;
@Service
public class UserInfoCacheService {
    /**
     * 保存用户信息记录
     *
     * @param userInfo
     *            用户信息
     */
    public void saveUserInfo(UserInfo userInfo) {
        CacheKit.cache().set(UserInfoCacheEnums.USER_INFO_BY_USERID.getKey() + userInfo.getUserId(), userInfo, ToolsConst.DAY_SECOND * 3);
        CacheKit.cache().set(UserInfoCacheEnums.USER_INFO_BY_ID.getKey() + userInfo.getId(), userInfo, ToolsConst.DAY_SECOND * 3);
        CacheKit.cache().set(UserInfoCacheEnums.USER_CODEID_TO_USERID.getKey() + userInfo.getCodeId(), userInfo.getUserId(), ToolsConst.DAY_SECOND * 3);
    }

    /**
     * 获取用户信息记录
     *
     * @param userId
     *            用户ID
     * @return 用户信息记录
     */
    public UserInfo getUserInfoByUserId(String userId) {
        return CacheKit.cache().get(UserInfoCacheEnums.USER_INFO_BY_USERID.getKey() + userId, UserInfo.class);
    }

    /**
     * 获取用户信息记录
     *
     * @param id
     *            ID
     * @return 用户信息记录
     */
    public UserInfo getUserInfoById(String id) {
        return CacheKit.cache().get(UserInfoCacheEnums.USER_INFO_BY_ID.getKey() + id, UserInfo.class);
    }

    /**
     * 获取用户ID
     *            ID
     * @return 用户信息记录
     */
    public String getUserIdByCodeId(int codeId) {
        return CacheKit.cache().get(UserInfoCacheEnums.USER_CODEID_TO_USERID.getKey() + codeId, String.class);
    }

    /**
     * 删除用户信息缓存
     * @param userInfo
     */
    public void delUserInfo(UserInfo userInfo) {
        CacheKit.cache().del(UserInfoCacheEnums.USER_INFO_BY_USERID.getKey() + userInfo.getUserId());
        CacheKit.cache().del(UserInfoCacheEnums.USER_INFO_BY_ID.getKey() + userInfo.getId());
        CacheKit.cache().del(UserInfoCacheEnums.USER_CODEID_TO_USERID.getKey() + userInfo.getCodeId());
    }

}
