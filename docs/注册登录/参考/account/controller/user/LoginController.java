package net.snaptag.system.account.controller.user;

import com.vida.sls.annotation.SLSLog;
import com.vida.sls.enums.OperationType;
import net.snaptag.system.account.buservice.*;
import net.snaptag.system.account.dto.UserLoginDto;
import net.snaptag.system.account.entity.UserAccount;
import net.snaptag.system.account.enums.MobileRegularExp;
import net.snaptag.system.account.utils.Constant;
import net.snaptag.system.account.utils.ToolUtils;
//import net.snaptag.system.business.utils.IpUtils;
import net.snaptag.system.sadais.cache.common.CacheCommonTools;
import net.snaptag.system.sadais.core.common.core.ToolsConst;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.core.BaseController;
import net.snaptag.system.sadais.web.core.I18nUtils;
import net.snaptag.system.sadais.web.dto.HeadInfoDto;
import net.snaptag.system.sadais.web.dto.ReturnDto;
import net.snaptag.system.sadais.web.utils.LocalTools;
import net.snaptag.system.sadais.web.utils.WebTools;
import net.snaptag.system.smscenter.buservice.SmsMessageBuService;
import net.snaptag.system.storage.aliyun.utils.MD5;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 用户信息模块
 */
@RestController
@RequestMapping(Constant.PRE_MAPPING_URL + "/login")
public class LoginController extends BaseController {

    @Autowired
    private ThirdLoginBuService thirdLoginBuService;
    @Autowired
    private UserAccountBuService userAccountBuService;
    @Autowired
    private UserInfoBuService userInfoBuService;
    @Autowired
    private UserThirdPartyAuthBuService userThirdPartyAuthBuService;
    @Autowired
    private UserLoginHistoryBuService userLoginHistoryBuService;
    @Autowired
    private CacheCommonTools cacheCommonTools;
    @Autowired
    private SmsMessageBuService smsMessageBuService;
    @Autowired
    private I18nUtils i18nUtils;
    @Autowired
    private UserAccountAndInfoBuService userAccountAndInfoBuService;

    /**
     * 邮箱正则表达式
     */
    public static final String EMAIL_PATTERN = "^\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$";

    /**
     * 中国大陆手机号正则表达式
     */
    private static final String CHINA_MOBILE_PATTERN = "^(\\+?0?86\\-?)?1[345789]\\d{9}$";

    private UserLoginDto formatUserLoginDto(UserLoginDto userLoginDto, UserAccount userAccount) {
//        if (userLoginDto != null && userLoginDto.getAccountInfoDto() != null && userLoginDto.getAccountInfoDto().size() > 0 && userAccount != null) {
//            userLoginDto.getAccountInfoDto().get(0).setMobile(userAccount.getMobileAccount());
//            userLoginDto.getAccountInfoDto().get(0).setEmail(userAccount.getMailAccount());
//        }
        if (userLoginDto != null && userLoginDto.getAccountInfoDto() != null && userLoginDto.getAccountInfoDto().size() > 0 && userAccount != null) {
            if (ToolsKit.isEmpty(userLoginDto.getAccountInfoDto().get(0).getMobile())) {
                userLoginDto.getAccountInfoDto().get(0).setMobile(userAccount.getMobileAccount());
            }
            userLoginDto.getAccountInfoDto().get(0).setEmail(userAccount.getMailAccount());
        }
        return userLoginDto;
    }

    /***
     * 增加一些登录的获取信息
     * @param userId
     * @param headInfoDto
     */
    private void updateUserLoginInfo(String userId, HeadInfoDto headInfoDto) {
        try {
            userLoginHistoryBuService.addOrUpdate(userId, headInfoDto.getVersion(), headInfoDto.getPhoneType());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 第三方登录注册接口
     */
    @RequestMapping(value = "/login")
    public ReturnDto login() {
        try {
            HeadInfoDto headInfoDto = this.getHeadInfoDto();
            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());

            String account = this.getValue("account");
            // 登录途径 byweixin,bysina,byqq,bymobile
            String loginway = this.getValue("loginway");
            String unionId = this.getValue("unionid");
            String openId = this.getValue("openid");
//            if (ToolsKit.isEmpty(account) || account.contains("null")) {
//                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_acc_old_notblank, locale));
//            }

            if (ToolsKit.isEmpty(loginway)) {
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_log_method_blank, locale));
            }

            if (ToolsKit.isEmpty(account) || account.contains("null")) {
                if (!ToolsConst.LOGIN_BY_YM_ONEKEY.equals(loginway)) {
                    throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_acc_old_notblank, locale));
                }
            }

            if ((Constant.LOGIN_BY_MOBILE.equals(loginway) && !ToolsKit.String.isMobilePhoneNumber(account))) {
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_mobile_invalid, locale));
            }
            if ((ToolsConst.LOGIN_BY_WEIXIN.equals(loginway) || ToolsConst.LOGIN_BY_QQ.equals(loginway)) && ToolsKit.isEmpty(openId)) {
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_accid_notempty, locale));
            } else if (!ToolsConst.LOGIN_BY_MOBILE.equals(loginway) && ToolsKit.isEmpty(this.getValue("accesstoken"))) {
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_3token_notempty, locale));
            }
            if (ToolsKit.isNotEmpty(cacheCommonTools.getCacheFlag("reg:acc:id:", account))) {
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_stime_resubmit, locale));
            }
            String mobileid = this.getValue("mobileid");
            String appId = this.getValue("appid");
            String pwd = this.getValue("pwd");
            String desc = this.getValue("desc");
            String channel = this.getValue("channel");
            String name = this.getValue("name");
            String sex = this.getValue("sex");
            String headPic = this.getValue("headpic");
            String from = this.getValue("from");
            String phone = this.getValue("phone");
            String code = this.getValue("validatorcode");

            // 如果登录方式是友盟的本机号码一键登录
            if (ToolsConst.LOGIN_BY_YM_ONEKEY.equals(loginway)) {
                account = thirdLoginBuService.getPhoneNumByYoMengToken(desc, this.getRequest(), headInfoDto); // 根据接口获取手机号码：userAccountBuService.checkThirdLoginStatusByOneKey(this.getRequest()) 过程如果失败直接抛出异常
                phone = account;
            }

            if (userAccountBuService.checkIsUnRegister(account)) {
                System.out.println("=================account==" + account);
                throw new ServiceException("当前账号已注销，7天内无法再次注册");
            }

            thirdLoginBuService.checkThirdLoginStatus(loginway, this.getRequest(), locale);// 第三方授权校验
            cacheCommonTools.saveCacheFlag("reg:acc:id:", account, 5);
            UserAccount userAccount = userAccountBuService.getUserAccountByAccount(account, loginway, unionId);
            String loginRes = userAccountBuService.checkUserAndLogin(userAccount, pwd, loginway);
            if (Constant.LOGIN_NORMAL.equals(loginRes) && !loginway.equals(ToolsConst.LOGIN_BY_MOBILE)) {// 小程序第三方授权登录
                UserLoginDto userLoginDto = userAccountAndInfoBuService.registerAndLogin(appId, mobileid, desc, channel, account, pwd, loginway, name, headPic, sex,
                        phone, headInfoDto.getOverseas(), locale);
                this.userThirdPartyAuthBuService.autoRelevance(loginway, userLoginDto.getUserInfoDto().getUserId(), openId, unionId, account, from, locale);

                // modify by linnt ********
                // 增加保存登录信息
                this.updateUserLoginInfo(userLoginDto.getUserInfoDto().getUserId(), headInfoDto);

                return returnSuccessJson(ExceptionEnums.SUCCESS.getCode(), i18nUtils.getKey(LocalTools.toast_login_success, locale), formatUserLoginDto(userLoginDto, userAccount));
            } else if (Constant.LOGIN_NORMAL.equals(loginRes) && !loginway.equals(ToolsConst.LOGIN_BY_MOBILE)) { // 账号不存在,第三方的账号
//                userAccountBuService.checkSmsCode(phone, code, locale);
                UserAccount phoneAccount = userAccountBuService.getUserAccountByAccount(phone, Constant.LOGIN_BY_MOBILE);
                UserLoginDto userLoginDto = null;
                if (ToolsKit.isEmpty(phoneAccount)) {
                    userLoginDto = userAccountAndInfoBuService.registerAndLogin(appId, mobileid, desc, channel, account, pwd, loginway, name, headPic, sex, phone, headInfoDto.getOverseas(), locale);
                } else {
                    userAccountBuService.bindingAccount(phoneAccount.getId(), account, loginway, name, openId, unionId, null, locale);
                    userLoginDto = userAccountAndInfoBuService.getLoginInfo(phoneAccount.getId(), locale);
                }
                // 自动同步第三方登录信息
                userThirdPartyAuthBuService.autoRelevance(loginway, userLoginDto.getUserInfoDto().getUserId(), openId, unionId, account, from, locale);

                // modify by linnt ********
                // 增加保存登录信息
                this.updateUserLoginInfo(userLoginDto.getUserInfoDto().getUserId(), headInfoDto);

                return this.returnSuccessJson(ExceptionEnums.SUCCESS.getCode(), i18nUtils.getKey(LocalTools.toast_login_success, locale), formatUserLoginDto(userLoginDto, userAccount));
            } else if (Constant.LOGIN_ERROR.equals(loginRes)) { // 账号存在可是密码错误
                if ((ToolsConst.LOGIN_BY_MOBILE.equals(loginway) || Constant.LOGIN_BY_MAIL.equals(loginway)) && ToolsKit.isEmpty(userAccount.getPwd())) {
                    throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_user_nopassword, locale));
                }
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_oldpass_invalid, locale));
            } else if (Constant.LOGIN_NORMAL.equals(loginRes) && loginway.equals(ToolsConst.LOGIN_BY_MOBILE)) {
                throw new ServiceException().setCode(ExceptionEnums.NO_ACCOUNT_EXIST.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_acc_notexist, locale));
            } else { // 账号存在并且密码正确,则返回同步数据
                // 更新第三方昵称
                if (ToolsKit.isEmpty(userAccount)) {
                    throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_user_notexist, locale));
                }
                userAccountBuService.updateBindName(userAccount, name, loginway);
                if (ToolsKit.isNotEmpty(this.getHeadInfoDto()) && ToolsKit.isNotEmpty(this.getHeadInfoDto().getDeviceSystem())) {
                    desc = this.getHeadInfoDto().getDeviceSystem().toUpperCase();
                }
                userAccountBuService.updateDeviceSystem(userAccount, desc);
                // userInfoBuService.updateUserInfo(userAccount.getId(), name, sex, headPic);
                // 微店登录关联微信
                userThirdPartyAuthBuService.autoRelevance(loginway, userAccount.getId(), openId, unionId, account, from, locale);// 自动同步
                UserLoginDto userLoginDto = userAccountAndInfoBuService.getLoginInfo(userAccount.getId(), locale);

                // modify by linnt ********
                // 增加保存登录信息
                this.updateUserLoginInfo(userLoginDto.getUserInfoDto().getUserId(), headInfoDto);

                return this.returnSuccessJson(ExceptionEnums.SUCCESS.getCode(), i18nUtils.getKey(LocalTools.toast_login_success, locale), formatUserLoginDto(userLoginDto, userAccount));
            }
        } catch (ServiceException e) {
            e.printStackTrace();
            return this.returnFailJson(e);
        }
    }

    /**
     * AppleID登录注册接口
     *
     * @return
     */
    @RequestMapping(value = "/appleLogin")
    public ReturnDto appleLogin() {
        try {
            HeadInfoDto headInfoDto = this.getHeadInfoDto();
            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());

            String account = this.getValue("account");
            String jwt = this.getValue("jwt");
            // 登录途径 byappleid
            String loginway = ToolsConst.LOGIN_BY_APPLEID;
            if (ToolsKit.isEmpty(account)) {
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_acc_old_notblank, locale));
            }
            if (ToolsKit.isEmpty(jwt)) {
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_3token_notempty, locale)); // 身份令牌不能为空
            }
            if (ToolsKit.isNotEmpty(cacheCommonTools.getCacheFlag("reg:acc:id:", account))) {
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_stime_resubmit, locale));
            }

            String unionId = this.getValue("unionid");
            String openId = this.getValue("openid");

            String appId = this.getValue("appid");
            String mobileid = this.getValue("mobileid");
            String pwd = this.getValue("pwd");
            String desc = this.getValue("desc");
            String channel = this.getValue("channel");
            String name = this.getValue("name");
            String sex = this.getValue("sex");
            String headPic = this.getValue("headpic");
            String from = this.getValue("from");
            String phone = this.getValue("phone");


            if (userAccountBuService.checkIsUnRegister(account)) {
                String msgTmp = i18nUtils.getKey(LocalTools.toast_account_cannot_register_seven_day, locale);
                if (ToolsKit.isEmpty(msgTmp)) {
                    throw new ServiceException("当前账号已注销，7天内无法再次注册");
                } else {
                    throw new ServiceException(msgTmp);
                }
            }
            // AppleID授权校验
            thirdLoginBuService.checkAppleIDLoginStatus(account, jwt, locale);
            cacheCommonTools.saveCacheFlag("reg:acc:id:", account, 5);
            UserAccount userAccount = userAccountBuService.getUserAccountByAccount(account, loginway, null);
            if (ToolsKit.isEmpty(userAccount) && ToolsConst.LOGIN_BY_APPLEID.equals(loginway)) {
                userAccount = userAccountBuService.getUserAccountByAccount(account, ToolsConst.LOGIN_BY_APPLEID2, null);
                if (ToolsKit.isNotEmpty(userAccount)) {
                    loginway = ToolsConst.LOGIN_BY_APPLEID2;
                }
            }
            String loginRes = userAccountBuService.checkUserAndLogin(userAccount, null, loginway);
            if (Constant.LOGIN_NORMAL.equals(loginRes) && !loginway.equals(ToolsConst.LOGIN_BY_MOBILE)) {
                // 新账号
                UserLoginDto userLoginDto = userAccountAndInfoBuService.registerAndLogin(appId, mobileid, desc, channel, account, pwd, loginway, name, headPic, sex, phone, headInfoDto.getOverseas(), locale);
                this.userThirdPartyAuthBuService.autoRelevance(loginway, userLoginDto.getUserInfoDto().getUserId(), openId, unionId, account, from, locale);

                // modify by linnt ********
                // 增加保存登录信息
                this.updateUserLoginInfo(userLoginDto.getUserInfoDto().getUserId(), headInfoDto);

                return returnSuccessJson(ExceptionEnums.SUCCESS.getCode(), i18nUtils.getKey(LocalTools.toast_login_success, locale), formatUserLoginDto(userLoginDto, userAccount));
            } else if (Constant.LOGIN_ERROR.equals(loginRes)) {
                // 账号存在可是密码错误
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_oldpass_invalid, locale));
            } else {
                // 账号存在并且密码正确,则返回同步数据
                // 更新第三方昵称
                if (ToolsKit.isEmpty(userAccount)) {
                    throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_user_notexist, locale));
                }
                userAccountBuService.updateBindName(userAccount, name, loginway);
                if (ToolsKit.isNotEmpty(this.getHeadInfoDto()) && ToolsKit.isNotEmpty(this.getHeadInfoDto().getDeviceSystem())) {
                    desc = this.getHeadInfoDto().getDeviceSystem().toUpperCase();
                }
                userAccountBuService.updateDeviceSystem(userAccount, desc);
                UserLoginDto userLoginDto = userAccountAndInfoBuService.getLoginInfo(userAccount.getId(), locale);

                // modify by linnt ********
                // 增加保存登录信息
                this.updateUserLoginInfo(userLoginDto.getUserInfoDto().getUserId(), headInfoDto);

                return this.returnSuccessJson(ExceptionEnums.SUCCESS.getCode(), i18nUtils.getKey(LocalTools.toast_login_success, locale), formatUserLoginDto(userLoginDto, userAccount));
            }
        } catch (ServiceException e) {
            e.printStackTrace();
            return this.returnFailJson(e);
        }
    }

    /**
     * 手机注册登录
     */
    @RequestMapping(value = "/register")
    public ReturnDto register() {
        try {
            HeadInfoDto headInfoDto = this.getHeadInfoDto();
            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());
            // 登录途径 byweixin,bysina,byqq,bymobile
            String loginway = this.getValue("loginway");
            String desc = this.getValue("devicesystem");
            // 短信国家区域代码
            String nationCode = this.getValue("nationcode");
            String account = this.getValue("account");
            // if (ToolsKit.isEmpty(account) || !ToolsKit.String.isMobilePhoneNumber(account)) {
//            if (ToolsKit.isEmpty(account) || !isMobileNumber(nationCode, account)) {
//                throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_mobile_invalid, locale));
//            }

            if (ToolsKit.isEmpty(account) || account.contains("null")) {
                if (!ToolsConst.LOGIN_BY_YM_ONEKEY.equals(loginway)) {
                    throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_acc_old_notblank, locale));
                } else {
                    account = thirdLoginBuService.getPhoneNumByYoMengToken(desc, this.getRequest(), headInfoDto); // 根据接口获取手机号码：userAccountBuService.checkThirdLoginStatusByOneKey(this.getRequest()) 过程如果失败直接抛出异常
                }
            }

            if ((ToolsConst.LOGIN_BY_MOBILE.equals(loginway) && !isMobileNumber(nationCode, account))) {
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_mobile_invalid, locale));
            }

//            String strMobile = account;
//            if (nationCode != null) {
//                strMobile = nationCode + account;
//            }
//            if (!strMobile.matches(CHINA_MOBILE_PATTERN)) {
//                // 除了中国大陆外，手机号前加上区号
//                account = strMobile;
//            } else {
//                if (account.length() > 11) {
//                    // 中国大陆手机去掉前面区号
//                    account = account.substring(account.length() - 11);
//                }
//            }
//            String strMobile = account;
//            if (nationCode != null) {
//                strMobile = nationCode + account;
//            }
//            if (ToolsKit.isNotEmpty(nationCode) && !"86".equals(nationCode) && !"+86".equals(nationCode)) {
//                // 除了中国大陆外，手机号前加上区号
//                account = strMobile;
//            } else {
//                if (account.length() > 11) {
//                    // 中国大陆手机去掉前面区号
//                    account = account.substring(account.length() - 11);
//                }
//            }

            String pwd = this.getValue("password");

            String channel = this.getValue("channel");
            String mobileId = this.getValue("mobileid");
            String captcha = this.getValue("captcha");
            String code = this.getValue("validatorcode");

            // 处理手机端提交一样的，替换成服务器生成
            if (ToolsKit.isEmpty(mobileId)) {
                mobileId = ToolsKit.getDefaultMobile();
            }

            if (userAccountBuService.checkIsUnRegister(account)) {
                String msgTmp = i18nUtils.getKey(LocalTools.toast_account_cannot_register_seven_day, locale);
                if (ToolsKit.isEmpty(msgTmp)) {
                    throw new ServiceException("当前账号已注销，7天内无法再次注册");
                } else {
                    throw new ServiceException(msgTmp);
                }
            }

            /** 限制认证 */
            // verifyAccount(account, Constant.LOGIN_BY_MOBILE, request);
            // RedisCommonTools.saveCount(RedisKey.LOGIN_IP_COUNT, Tools.getIp(request));
//            UserAccount userAccount = userAccountBuService.getUserAccountByAccount(account, Constant.LOGIN_BY_MOBILE);
//            String loginRes = userAccountBuService.checkUserAndLogin(userAccount, pwd, Constant.LOGIN_BY_MOBILE);
            UserAccount userAccount = userAccountBuService.getUserAccountByAccount(account, loginway);
            String loginRes = userAccountBuService.checkUserAndLogin(userAccount, pwd, loginway);
            System.out.println(userAccount);
            if (Constant.LOGIN_NORMAL.equals(loginRes)) { // 账号不存在
                if (ToolsKit.isEmpty(code) && ToolsKit.isNotEmpty(pwd)) {
                    throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_acc_notexist, locale));
                }
                userAccountBuService.checkRegisterMobile(mobileId, locale);
                UserLoginDto userLoginDto = userAccountAndInfoBuService.registerAndLogin(mobileId, desc, channel, account, pwd, Constant.LOGIN_BY_MOBILE, headInfoDto.getOverseas(), locale);

                // modify by linnt ********
                // 增加保存登录信息
                this.updateUserLoginInfo(userLoginDto.getUserInfoDto().getUserId(), headInfoDto);

                return this.returnSuccessJson(ExceptionEnums.SUCCESS.getCode(), i18nUtils.getKey(LocalTools.toast_login_success, locale), formatUserLoginDto(userLoginDto, userAccount));
            } else {
                userAccountAndInfoBuService.checkLoginStatus(userAccount, account, pwd, code, loginway, locale);
                if (ToolsKit.isNotEmpty(this.getHeadInfoDto()) && ToolsKit.isNotEmpty(this.getHeadInfoDto().getDeviceSystem())) {
                    desc = this.getHeadInfoDto().getDeviceSystem().toUpperCase();
                }
                userAccountBuService.updateDeviceSystem(userAccount, desc);
                userAccountBuService.updateNationCode(userAccount, nationCode);
                UserLoginDto userLoginDto = userAccountAndInfoBuService.getLoginInfo(userAccount.getId(), locale);

                // modify by linnt ********
                // 增加保存登录信息
                this.updateUserLoginInfo(userLoginDto.getUserInfoDto().getUserId(), headInfoDto);

                return this.returnSuccessJson(ExceptionEnums.SUCCESS.getCode(), i18nUtils.getKey(LocalTools.toast_login_success, locale), formatUserLoginDto(userLoginDto, userAccount));
            }
        } catch (ServiceException e) {
            e.printStackTrace();
            return this.returnFailJson(e);
        }
    }

    /**
     * 邮箱注册登录
     */
    @RequestMapping(value = "/registerByMail")
    @SLSLog(value = "邮箱注册登录", configKey = "business", businessType = "登录模块", operation = OperationType.LOGIN)
    public ReturnDto registerByMail() {
        try {
            HeadInfoDto headInfoDto = this.getHeadInfoDto();
            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());

            String account = this.getValue("account");
            if (ToolsKit.isEmpty(account) || !account.matches(EMAIL_PATTERN)) {
                throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_email_invalid, locale));
            }
            String pwd = this.getValue("password");
            String desc = this.getValue("devicesystem");
            String channel = this.getValue("channel");
            String mobileId = this.getValue("mobileid");
            String captcha = this.getValue("captcha");
            String code = this.getValue("validatorcode");

            // 处理手机端提交一样的，替换成服务器生成
            if (ToolsKit.isEmpty(mobileId)) {
                mobileId = ToolsKit.getDefaultMobile();
            }

            if (userAccountBuService.checkIsUnRegister(account)) {
                String msgTmp = i18nUtils.getKey(LocalTools.toast_account_cannot_register_seven_day, locale);
                if (ToolsKit.isEmpty(msgTmp)) {
                    throw new ServiceException("当前账号已注销，7天内无法再次注册");
                } else {
                    throw new ServiceException(msgTmp);
                }
            }

            UserLoginDto tmp = userAccountAndInfoBuService.checkByManagerAccount(account, pwd);
            if (tmp != null) {
                return this.returnSuccessJson(ExceptionEnums.SUCCESS.getCode(), i18nUtils.getKey(LocalTools.toast_login_success, locale), tmp);
            }

            /** 限制认证 */
            // verifyAccount(account, Constant.LOGIN_BY_MOBILE, request);
            // RedisCommonTools.saveCount(RedisKey.LOGIN_IP_COUNT, Tools.getIp(request));
            UserAccount userAccount = userAccountBuService.getUserAccountByAccount(account, Constant.LOGIN_BY_MAIL);
            String loginRes = userAccountBuService.checkUserAndLogin(userAccount, pwd, Constant.LOGIN_BY_MAIL);
            if (Constant.LOGIN_NORMAL.equals(loginRes)) {
                // 账号不存在
                if (ToolsKit.isEmpty(code) && ToolsKit.isNotEmpty(pwd)) {
                    throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_acc_notexist, locale));
                }
                userAccountBuService.checkRegisterMobile(mobileId, locale);
                userAccountBuService.checkMailCode(account, code, locale);
                UserLoginDto userLoginDto = userAccountAndInfoBuService.registerAndLogin(mobileId, desc, channel, account, pwd, Constant.LOGIN_BY_MAIL, headInfoDto.getOverseas(), locale);

                // modify by linnt ********
                // 增加保存登录信息
                this.updateUserLoginInfo(userLoginDto.getUserInfoDto().getUserId(), headInfoDto);

                return this.returnSuccessJson(ExceptionEnums.SUCCESS.getCode(), i18nUtils.getKey(LocalTools.toast_login_success, locale), formatUserLoginDto(userLoginDto, userAccount));
            } else {
                userAccountAndInfoBuService.checkLoginStatus(userAccount, account, pwd, code, Constant.LOGIN_BY_MAIL, locale);
                if (ToolsKit.isNotEmpty(this.getHeadInfoDto()) && ToolsKit.isNotEmpty(this.getHeadInfoDto().getDeviceSystem())) {
                    desc = this.getHeadInfoDto().getDeviceSystem().toUpperCase();
                }
                userAccountBuService.updateDeviceSystem(userAccount, desc);
                UserLoginDto userLoginDto = userAccountAndInfoBuService.getLoginInfo(userAccount.getId(), locale);

                // modify by linnt ********
                // 增加保存登录信息
                this.updateUserLoginInfo(userLoginDto.getUserInfoDto().getUserId(), headInfoDto);

                return this.returnSuccessJson(ExceptionEnums.SUCCESS.getCode(), i18nUtils.getKey(LocalTools.toast_login_success, locale), formatUserLoginDto(userLoginDto, userAccount));
            }
        } catch (ServiceException e) {
            e.printStackTrace();
            return this.returnFailJson(e);
        }
    }

    /**
     * 邮箱获取验证码
     *
     * @return
     */
    @RequestMapping(value = "/mailValidCode")
    @SLSLog(value = "邮箱获取验证码", configKey = "business", businessType = "登录模块", operation = OperationType.LOGIN)
    public ReturnDto mailValidCode() {
        HeadInfoDto headInfoDto = this.getHeadInfoDto();
        Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());
        try {
            String from = this.getValue("from");
            String mail = this.getValue("mail");
            String captcha = this.getValue("captcha");
            if (ToolsKit.isEmpty(mail) || !mail.matches(EMAIL_PATTERN)) {
                throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_email_invalid, locale));
            }

            String userAgent2 = this.getValue("allowhost");// H5
            boolean isPhoneRequest = true;
            if (headInfoDto.getUaFlag().indexOf("(iPhone; iOS") > -1 || headInfoDto.getUaFlag().indexOf("(iPad; iOS") > -1) {
                isPhoneRequest = true;
            } else if (headInfoDto.getUaFlag().indexOf("Android") > -1) {
                isPhoneRequest = true;
            } else if (ToolsKit.isNotEmpty(userAgent2)) {
                isPhoneRequest = true;
                if (ToolsKit.isEmpty(captcha)) {
                    throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_vercode_notexist, locale));
                }
            }
            if (isPhoneRequest) {
                String ip = ToolUtils.getIp(this.getRequest());
                userAccountBuService.message(mail, ip, captcha, headInfoDto.getOverseas(), locale);
                return this.returnSuccessJson(ExceptionEnums.SUCCESS, null);
            } else {
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_op_freq_try, locale));
            }
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        } catch (Exception e) {
            return this.returnFailJson(new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_vercode_getfail, locale)));
        }
    }

    /**
     * 重设邮箱帐号密码
     *
     * @return
     */
    @RequestMapping(value = "/resetMailPwd")
    @SLSLog(value = "重设邮箱帐号密码", configKey = "business", businessType = "登录模块", operation = OperationType.RESET)
    public ReturnDto resetMailPwd() {
        HeadInfoDto headInfoDto = this.getHeadInfoDto();
        Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());

        String account = this.getValue("account");
        String pwd = this.getValue("password");
        String code = this.getValue("validatorcode");
        try {
            userAccountBuService.checkMailCode(account, code, locale);
            userAccountBuService.resetUserMailPwd(account, pwd, locale);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, null);
        } catch (ServiceException e) {
            e.printStackTrace();
            return this.returnFailJson(e);
        }
    }

    /**
     * 重设密码
     *
     * @return
     */
    @RequestMapping(value = "/resetpwd")
    @SLSLog(value = "重设密码", configKey = "business", businessType = "登录模块", operation = OperationType.RESET)
    public ReturnDto resetpwd() {
        HeadInfoDto headInfoDto = this.getHeadInfoDto();
        Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());

        String account = this.getValue("account");
        String pwd = this.getValue("password");
        String code = this.getValue("validatorcode");
        try {
            // String count = sMSMessageService.getResetPwdCount(account);
            // if (Tools.isNotEmpty(count) && Integer.parseInt(count) >= WebConst.SMS_RESET_PWD_MAX_RETRY) {
            // throw new ServiceException().setCode(EnumCode.ERROR.getCode()).setContent("您的账号已锁定，请稍后再试");
            // }
            // sMSMessageService.saveResetPwdCount(account);

            if ("ANDROID".equals(headInfoDto.getDeviceSystem())) {
                pwd = MD5.MD5Encode(pwd);
            }

            userAccountAndInfoBuService.checkSmsCode(account, code, locale);
            userAccountBuService.resetUserPwd(account, pwd, locale);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, null);
        } catch (ServiceException e) {
            e.printStackTrace();
            return this.returnFailJson(e);
        }
    }

    /**
     * 修改用户密码
     *
     * @return
     */
    @RequestMapping(value = "/modifyPwd")
    @SLSLog(value = "修改用户密码", configKey = "business", businessType = "登录模块", operation = OperationType.UPDATE)
    public ReturnDto modifyPwd() {
        HeadInfoDto headInfoDto = this.getHeadInfoDto();
        Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());

        String codeId = this.getValue("codeid");
        String oldPwd = this.getValue("oldpwd");
        String newPwd = this.getValue("newpwd");
        try {
            userAccountAndInfoBuService.modifyUserPwdById(codeId, oldPwd, newPwd, locale);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, null);
        } catch (ServiceException e) {
            e.printStackTrace();
            return this.returnFailJson(e);
        }
    }

    /**
     * 修改用户电子邮箱
     *
     * @return
     */
    @RequestMapping(value = "/modifyMail")
    @SLSLog(value = "修改用户电子邮箱", configKey = "business", businessType = "登录模块", operation = OperationType.UPDATE)
    public ReturnDto modifyMail() {
        HeadInfoDto headInfoDto = this.getHeadInfoDto();
        Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());

        String userId = this.getValue("userid");
        String email = this.getValue("email");
        String code = this.getValue("validatorcode");
        try {
            userAccountBuService.checkMailCode(email, code, locale);
            userAccountBuService.modifyUserMail(userId, email, locale);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, null);
        } catch (ServiceException e) {
            e.printStackTrace();
            return this.returnFailJson(e);
        }
    }

    /**
     * 第三方登录后绑定手机并设置密码（用于 XColor 应用）Add by RabyGao 2019-10-22
     *
     * @return
     */
    @RequestMapping(value = "/bindMobilePass")
    @SLSLog(value = "第三方登录后绑定手机并设置密码", configKey = "business", businessType = "登录模块", operation = OperationType.UPDATE)
    public ReturnDto bindMobilePass() {
        HeadInfoDto headInfoDto = this.getHeadInfoDto();
        Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());

        String userId = this.getValue("userid");
        String account = this.getValue("account");
        String pwd = this.getValue("password");
        String code = this.getValue("validatorcode");

        String mobileId = this.getValue("mobileid");

        try {
            userAccountAndInfoBuService.checkSmsCode(account, code, locale);
            userAccountBuService.bindMobilePassForXColor(userId, account, pwd, mobileId);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, null);
        } catch (ServiceException e) {
            e.printStackTrace();
            return this.returnFailJson(e);
        }
    }

    /**
     * 绑定设备ID
     *
     * @return
     */
    @RequestMapping(value = "/bindingudid")
    @SLSLog(value = "绑定设备ID", configKey = "business", businessType = "登录模块", operation = OperationType.UPDATE)
    public ReturnDto bindingUdid() {
        HeadInfoDto headInfoDto = this.getHeadInfoDto();
        Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());

        String userId = this.getValue("userid");
        String udid = this.getValue("udid");
        try {
            userAccountBuService.bindingUdid(userId, udid, locale);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, null);
        } catch (ServiceException e) {
            e.printStackTrace();
            return this.returnFailJson(e);
        }
    }

    /**
     * 绑定账号
     *
     * @return
     */
    @RequestMapping(value = "/bindingaccount")
    @SLSLog(value = "绑定账号", configKey = "business", businessType = "登录模块", operation = OperationType.UPDATE)
    public ReturnDto bindingAccount() {
        HeadInfoDto headInfoDto = this.getHeadInfoDto();
        Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());

        String nationCode = this.getValue("nationcode");
        String userId = this.getValue("userid");
        String account = this.getValue("account");
        String loginway = this.getValue("loginway");
        String code = this.getValue("validatorcode");
        String name = this.getValue("name");
        String unionId = this.getValue("unionid");
        String openId = this.getValue("openid");
        try {
            // verifyAccount(account, loginway, request);
            if (Constant.LOGIN_BY_MOBILE.equals(loginway)) {
                account = getMobileAccount(account, nationCode, locale);
                userAccountAndInfoBuService.checkSmsCode(account, code, locale);
            } else if (Constant.LOGIN_BY_MAIL.equals(loginway)) {
                userAccountBuService.checkMailCode(account, code, locale);
            }

            if (Constant.LOGIN_BY_MAIL.equals(loginway)) {
                userAccountBuService.modifyUserMail(userId, account, locale);
            } else {
                if (userAccountBuService.checkIsUnRegister(account)) {
                    String msgTmp = i18nUtils.getKey(LocalTools.toast_account_cannot_register_seven_day, locale);
                    if (ToolsKit.isEmpty(msgTmp)) {
                        throw new ServiceException("当前账号已注销，7天内无法再次注册");
                    } else {
                        throw new ServiceException(msgTmp);
                    }
                }
                userAccountBuService.bindingAccount(userId, account, loginway, name, openId, unionId, null, locale);
            }

            UserAccount userAccount = userAccountBuService.getUserAccountByUserId(userId);
            UserLoginDto userLoginDto = userAccountAndInfoBuService.getLoginInfo(userAccount.getId(), locale);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS.getCode(), i18nUtils.getKey(LocalTools.toast_login_success, locale), formatUserLoginDto(userLoginDto, userAccount));


            // RedisCommonTools.saveCount(RedisKey.LOGIN_IP_COUNT, Tools.getIp(request));
//            return this.returnSuccessJson(ExceptionEnums.SUCCESS, null);
        } catch (ServiceException e) {
            e.printStackTrace();
            return this.returnFailJson(e);
        }
    }

    /**
     * 解绑账号--只能解绑QQ、微信、新浪
     *
     * @return
     */
    @RequestMapping(value = "/unbindingaccount")
    @SLSLog(value = "解绑账号-QQ/微信/新浪", configKey = "business", businessType = "登录模块", operation = OperationType.UPDATE)
    public ReturnDto unBindingAccount() {
        HeadInfoDto headInfoDto = this.getHeadInfoDto();
        Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());

        String userId = this.getValue("userid");
        String loginway = this.getValue("loginway");
        String unionId = this.getValue("unionid");
        String openId = this.getValue("openid");
        try {
            userAccountBuService.unbinding(userId, loginway, unionId, openId, locale);

            UserAccount userAccount = userAccountBuService.getUserAccountByUserId(userId);
            ;
            UserLoginDto userLoginDto = userAccountAndInfoBuService.getLoginInfo(userAccount.getId(), locale);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS.getCode(), i18nUtils.getKey(LocalTools.toast_login_success, locale), formatUserLoginDto(userLoginDto, userAccount));


//            return this.returnSuccessJson(ExceptionEnums.SUCCESS, null);
        } catch (ServiceException e) {
            e.printStackTrace();
            return this.returnFailJson(e);
        }
    }

    /**
     * 验证手机或邮箱账号
     *
     * @return
     */
    @RequestMapping(value = "/checkaccount")
    @SLSLog(value = "验证手机或邮箱账号", configKey = "business", businessType = "登录模块", operation = OperationType.SELECT)
    public ReturnDto checkAccount() {
        HeadInfoDto headInfoDto = this.getHeadInfoDto();
        Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());

        String nationCode = this.getValue("nationcode");
        String oldAccount = this.getValue("oldaccount");
        String code = this.getValue("validatorcode");
        String loginway = this.getValue("loginway");
        try {
            if (loginway != null && Constant.LOGIN_BY_MAIL.equals(loginway)) {
                userAccountBuService.checkMailCode(oldAccount, code, locale);
                userAccountBuService.checkMailAccount(oldAccount, locale);
            } else {
                String account = oldAccount;
                account = getMobileAccount(account, nationCode, locale);
                userAccountAndInfoBuService.checkSmsCode(account, code, locale);
                userAccountBuService.checkAccount(account, locale);
            }
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, null);
        } catch (ServiceException e) {
            e.printStackTrace();
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取授权token
     *
     * @return
     */
    @RequestMapping(value = "/getaccesstoken")
    @SLSLog(value = "获取授权token", configKey = "business", businessType = "登录模块", operation = OperationType.SELECT)
    public ReturnDto getAccesstoken() {
        try {
            HeadInfoDto headInfoDto = this.getHeadInfoDto();
            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());

            String nationCode = this.getValue("nationcode");
            String account = this.getValue("account");
            String loginway = this.getValue("loginway");

            if (loginway != null && loginway.equals(ToolsConst.LOGIN_BY_MOBILE) && isMobileNumber(nationCode, account)) {
                // 手机登录
                String strMobile = account;
                if (nationCode != null) {
                    strMobile = nationCode + account;
                }
                if (!strMobile.matches(CHINA_MOBILE_PATTERN)) {
                    // 除了中国大陆外，手机号前加上区号
                    account = strMobile;
                } else {
                    if (account.length() > 11) {
                        // 中国大陆手机去掉前面区号
                        account = account.substring(account.length() - 11);
                    }
                }
            }

            return this.returnSuccessJson(ExceptionEnums.SUCCESS,
                    userAccountAndInfoBuService.getAccesstoken(account, loginway, headInfoDto.getIp(), headInfoDto.getUaFlag(), locale, headInfoDto));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 查询用户账户信息
     *
     * @return
     */
    @RequestMapping(value = "/getuseraccountinfo")
    @SLSLog(value = "查询用户账户信息", configKey = "business", businessType = "登录模块", operation = OperationType.SELECT)
    public ReturnDto getUserAccountInfo() {
        try {
            HeadInfoDto headInfoDto = this.getHeadInfoDto();
            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());

            String account = this.getValue("account");
            String loginway = this.getValue("loginway");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, userAccountBuService.getUserAccountDto(account, loginway, StringUtils.EMPTY, locale));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 查询用户账户信息
     *
     * @return
     */
    @RequestMapping(value = "/getuseraccount")
    @SLSLog(value = "查询用户账户信息", configKey = "business", businessType = "登录模块", operation = OperationType.SELECT)
    public ReturnDto getUserAccount() {
        try {
            HeadInfoDto headInfoDto = this.getHeadInfoDto();
            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());

            String codeId = this.getValue("codeid");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, userAccountAndInfoBuService.getUserAccountDto(codeId, locale));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取手机短信国家区域代码
     *
     * @return
     */
    @RequestMapping(value = "/getSmsCountryCodes")
    @SLSLog(value = "获取手机短信国家区域代码", configKey = "business", businessType = "登录模块", operation = OperationType.SELECT)
    public ReturnDto getSmsCountryCodes() {
        try {
            HeadInfoDto headInfoDto = this.getHeadInfoDto();
            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());

            return this.returnSuccessJson(ExceptionEnums.SUCCESS, userAccountBuService.getSmsCountryCodesByLocale(locale));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    private static boolean isMobileNumber(String nationalCode, String mobileNumber) {
        boolean isMobileNumber = false;
        String natCode = "";
        if (nationalCode != null) {
            natCode = nationalCode;
        }
        for (MobileRegularExp regularExp : MobileRegularExp.values()) {
            Pattern pattern = Pattern.compile(regularExp.getRegularExp());
            Matcher matcher = pattern.matcher(new StringBuilder().append(natCode).append(mobileNumber).toString());
            if (matcher.matches()) {
                isMobileNumber = true;
                // 枚举中把最常用的国际区号拍在前面可以减少校验开销
                break;
            }
        }
        return isMobileNumber;
    }

    private String getMobileAccount(String account, String nationCode, Locale locale) {
        if (ToolsKit.isEmpty(account) || !isMobileNumber(nationCode, account)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_mobile_invalid, locale));
        }

//        String strMobile = account;
//        if (nationCode != null) {
//            strMobile = nationCode + account;
//        }
//        if (!strMobile.matches(CHINA_MOBILE_PATTERN)) {
//            // 除了中国大陆外，手机号前加上区号
//            account = strMobile;
//        } else {
//            if (account.length() > 11) {
//                // 中国大陆手机去掉前面区号
//                account = account.substring(account.length() - 11);
//            }
//        }
        String strMobile = account.replaceAll("\\+", "");
        if (nationCode != null && !account.startsWith(nationCode)) {
            strMobile = nationCode + account;
        }
        if (ToolsKit.isNotEmpty(nationCode) && !"86".equals(nationCode) && !"+86".equals(nationCode)) {
            // 除了中国大陆外，手机号前加上区号
            account = strMobile;
        } else {
            if (account.length() > 11) {
                // 中国大陆手机去掉前面区号
                account = account.substring(account.length() - 11);
            }
        }
        return account;
    }

    /**
     * 短信获取验证码
     *
     * @return
     */
    @RequestMapping(value = "/sendMobileCode")
    @SLSLog(value = "短信获取验证码", configKey = "business", businessType = "登录模块", operation = OperationType.SELECT)
    public ReturnDto sendMobileCode() {
        HeadInfoDto headInfoDto = this.getHeadInfoDto();
        Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());
        try {
            String from = this.getValue("from");
            String captcha = this.getValue("captcha");
            String nationCode = this.getValue("nationcode");
            String phone = this.getValue("phone");

            if (ToolsKit.isEmpty(phone) || !isMobileNumber(nationCode, phone)) {
                throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_mobile_invalid, locale));
            }
            phone = getMobileAccount(nationCode, phone);

            // 判断当前账号是否存在
            userAccountBuService.checkAccount(phone, locale);

            String userAgent2 = this.getValue("allowhost");// H5
            boolean isPhoneRequest = true;
            if (headInfoDto.getUaFlag().indexOf("(iPhone; iOS") > -1 || headInfoDto.getUaFlag().indexOf("(iPad; iOS") > -1) {
                isPhoneRequest = true;
            } else if (headInfoDto.getUaFlag().indexOf("Android") > -1) {
                isPhoneRequest = true;
            } else if (ToolsKit.isNotEmpty(userAgent2)) {
                isPhoneRequest = true;
                if (ToolsKit.isEmpty(captcha)) {
                    throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_vercode_notexist, locale));
                }
            }
            if (isPhoneRequest) {
                String ip = net.snaptag.system.smscenter.utils.ToolUtils.getIp(this.getRequest());
                smsMessageBuService.message(phone, from, ip, captcha, headInfoDto.getOverseas(), locale);
                return this.returnSuccessJson(ExceptionEnums.SUCCESS, null);
            } else {
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_op_freq_try, locale));
            }
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        } catch (Exception e) {
            return this.returnFailJson(new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_vercode_getfail, locale)));
        }
    }

    /**
     * 邮箱获取验证码
     *
     * @return
     */
    @RequestMapping(value = "/sendMailCode")
    @SLSLog(value = "邮箱获取验证码", configKey = "business", businessType = "登录模块", operation = OperationType.SELECT)
    public ReturnDto sendMailCode() {
        HeadInfoDto headInfoDto = this.getHeadInfoDto();
        Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());
        try {
            String mail = this.getValue("mail");
            if (ToolsKit.isEmpty(mail) || !mail.matches(EMAIL_PATTERN)) {
                throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_email_invalid, locale));
            }

            // 判断当前账号是否存在
            userAccountBuService.checkMailAccount(mail, locale);

            String userAgent2 = this.getValue("allowhost");// H5
            boolean isPhoneRequest = true;
            if (headInfoDto.getUaFlag().indexOf("(iPhone; iOS") > -1 || headInfoDto.getUaFlag().indexOf("(iPad; iOS") > -1) {
                isPhoneRequest = true;
            } else if (headInfoDto.getUaFlag().indexOf("Android") > -1) {
                isPhoneRequest = true;
            } else if (ToolsKit.isNotEmpty(userAgent2)) {
                isPhoneRequest = true;
            }
            if (isPhoneRequest) {
                String ip = ToolUtils.getIp(this.getRequest());
                userAccountBuService.message(mail, ip, null, headInfoDto.getOverseas(), locale);
                return this.returnSuccessJson(ExceptionEnums.SUCCESS, null);
            } else {
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_op_freq_try, locale));
            }
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        } catch (Exception e) {
            return this.returnFailJson(new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage(i18nUtils.getKey(LocalTools.toast_vercode_getfail, locale)));
        }
    }

    /**
     * 账号注销
     */
    @RequestMapping(value = "/unregister")
    @SLSLog(value = "账号注销", configKey = "business", businessType = "登录模块", operation = OperationType.CANCEL)
    public ReturnDto unregister() {
        try {
            HeadInfoDto headInfoDto = this.getHeadInfoDto();
            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());

            // 短信国家区域代码
            String userId = this.getValue("userid");
            UserAccount userAccount = userAccountBuService.getUserAccountByUserId(userId);

            if (userAccount == null) {
                String message = i18nUtils.getKey(LocalTools.toast_account_unregister_no_mobile, locale);
                if (ToolsKit.isEmpty(message)) {
                    throw new ServiceException("注销失败，当前账号不存在");
                } else {
                    throw new ServiceException(message);
                }
            } else {
                userAccountBuService.unRegister(userAccount.getId());
                Map<String, String> msg = new HashMap<>();
                String message = i18nUtils.getKey(LocalTools.toast_account_unregister_success, locale);
                if (ToolsKit.isEmpty(message)) {
                    msg.put("msg", "该号码七日内不可再注册");
                } else {
                    msg.put("msg", message);
                }
                return this.returnSuccessJson(ExceptionEnums.SUCCESS, msg);
            }

        } catch (ServiceException e) {
            e.printStackTrace();
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/cancelaccount")
    @SLSLog(value = "账号注销", configKey = "business", businessType = "登录模块", operation = OperationType.CANCEL)
    public ReturnDto cancelAccount() {
        try {
            HeadInfoDto headInfoDto = this.getHeadInfoDto();
            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());
            // 登录途径 byweixin,bysina,byqq,bymobile
            String loginway = this.getValue("loginway");
            // 短信国家区域代码
            String nationCode = this.getValue("nationcode");
            String account = this.getValue("account");
            String code = this.getValue("validatorcode");

            // 根据来源，判断验证码正确与否
            UserAccount userAccount = null;

            // 查看当前账号是否存在
            if (Constant.LOGIN_BY_MOBILE.equals(loginway)) {
                String strMobile = account;
                if (nationCode != null) {
                    strMobile = nationCode + account;
                }
                if (ToolsKit.isNotEmpty(nationCode) && !"86".equals(nationCode) && !"+86".equals(nationCode)) {
                    // 除了中国大陆外，手机号前加上区号
                    account = strMobile;
                } else {
                    if (account.length() > 11) {
                        // 中国大陆手机去掉前面区号
                        account = account.substring(account.length() - 11);
                    }
                }
                userAccountAndInfoBuService.checkSmsCode(account, code, locale);
            } else if (Constant.LOGIN_BY_MAIL.equals(loginway)) {
                userAccountBuService.checkMailCode(account, code, locale);
            } else {
                String message = i18nUtils.getKey(LocalTools.toast_account_unregister_no_mobile, locale);
                if (ToolsKit.isEmpty(message)) {
                    throw new ServiceException("注销失败，当前账号不存在");
                } else {
                    throw new ServiceException(message);
                }
            }

            userAccount = userAccountBuService.getUserAccountByAccount(account, loginway);

            if (userAccount == null) {
                String message = i18nUtils.getKey(LocalTools.toast_account_unregister_no_mobile, locale);
                if (ToolsKit.isEmpty(message)) {
                    throw new ServiceException("注销失败，当前账号不存在");
                } else {
                    throw new ServiceException(message);
                }
            } else {
                userAccountBuService.unRegister(userAccount.getId());
                Map<String, String> msg = new HashMap<>();
                String message = i18nUtils.getKey(LocalTools.toast_account_unregister_success, locale);
                if (ToolsKit.isEmpty(message)) {
                    msg.put("msg", "该号码七日内不可再注册");
                } else {
                    msg.put("msg", message);
                }
                return this.returnSuccessJson(ExceptionEnums.SUCCESS, msg);
            }

        } catch (ServiceException e) {
            e.printStackTrace();
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/removeRegister")
    @SLSLog(value = "账号注销", configKey = "business", businessType = "登录模块", operation = OperationType.CANCEL)
    public ReturnDto removeRegister() {
        try {
            HeadInfoDto headInfoDto = this.getHeadInfoDto();
            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());

            // 短信国家区域代码
            String userId = this.getValue("id");
            UserAccount userAccount = userAccountBuService.getUserAccountByUserId(userId);

            if (userAccount == null) {
                String message = i18nUtils.getKey(LocalTools.toast_account_unregister_no_mobile, locale);
                if (ToolsKit.isEmpty(message)) {
                    throw new ServiceException("注销失败，当前账号不存在");
                } else {
                    throw new ServiceException(message);
                }
            } else {
                userAccountBuService.unRegister(userAccount.getId());
                Map<String, String> msg = new HashMap<>();
                String message = i18nUtils.getKey(LocalTools.toast_account_unregister_success, locale);
                if (ToolsKit.isEmpty(message)) {
                    msg.put("msg", "该号码七日内不可再注册");
                } else {
                    msg.put("msg", message);
                }
                return this.returnSuccessJson(ExceptionEnums.SUCCESS, msg);
            }

        } catch (ServiceException e) {
            e.printStackTrace();
            return this.returnFailJson(e);
        }
    }

    private String getMobileAccount(String nationCode, String account) {
        String strMobile = account;
        if (nationCode != null) {
            strMobile = nationCode + account;
        }
        if (!strMobile.matches(net.snaptag.system.smscenter.utils.ToolUtils.CHINA_MOBILE_PATTERN)) {
            // 除了中国大陆外，手机号前加上区号
            account = strMobile;
        } else {
            if (account.length() > 11) {
                // 中国大陆手机去掉前面区号
                account = account.substring(account.length() - 11);
            }
        }
        return account;
    }


    @RequestMapping(value = "/clearCache")
    @SLSLog(value = "清除缓存", configKey = "business", businessType = "登录模块", operation = OperationType.DELETE)
    public ReturnDto clearCache() {
        try {
            String userId = this.getValue("userid");
            return this.returnSuccessJson(userAccountBuService.clearCache(userId));

        } catch (ServiceException e) {
            e.printStackTrace();
            return this.returnFailJson(e);
        }
    }
}
