package net.snaptag.system.account.controller.auth;

import com.vida.sls.annotation.SLSLog;
import com.vida.sls.enums.OperationType;
import net.snaptag.system.account.buservice.UserAuthBuService;
import net.snaptag.system.account.cache.UserAccountCacheService;
import net.snaptag.system.account.dto.CreateAccountDto;
import net.snaptag.system.account.utils.Constant;
import net.snaptag.system.sadais.core.common.core.ToolsConst;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.core.BaseController;
import net.snaptag.system.sadais.web.dto.HeadInfoDto;
import net.snaptag.system.sadais.web.dto.ReturnDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 用户账号模块
 */
@RestController
@RequestMapping(Constant.PRE_MAPPING_URL + "/useraccount")
public class UserAuthController extends BaseController {

    @Autowired
    private UserAuthBuService userAuthBuService;

    /**
     * 用户账户登录
     * 
     * @return
     */
    @RequestMapping(value = "/login")
    @SLSLog(value = "用户登录", configKey = "business", businessType = "用户账号模块", operation = OperationType.LOGIN)
    public ReturnDto login() {
        try {
            String account = this.getValue("account");
            String passWord = this.getValue("password");
            HeadInfoDto headInfoDto = this.getHeadInfoDto();
            if (ToolsKit.isEmpty(headInfoDto) || ToolsKit.isEmpty(headInfoDto.getChannel())) {
                throw new ServiceException().setCode(ExceptionEnums.ERROR.getCode()).setMessage("校验信息不能为空");
            }
            return this.returnSuccessJson(ExceptionEnums.SUCCESS,
                    userAuthBuService.login(account, passWord, headInfoDto.getChannel(), headInfoDto.getIp(), headInfoDto.getUaFlag()));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 新增或更新用户账号
     *
     * @return
     */
    @RequestMapping(value = "/saveorupdate")
    @SLSLog(value = "用户登录", configKey = "business", businessType = "用户账号模块", operation = OperationType.LOGIN)
    public ReturnDto saveOrUpdate() {
        try {
            CreateAccountDto createAccountDto = this.getBean(CreateAccountDto.class);
            userAuthBuService.saveOrUpdate(createAccountDto);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, null);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取用户列表
     *
     * @return
     */
    @RequestMapping(value = "/findPage")
    public ReturnDto findPage() {
        try {
            String pageNo = this.getValue("pageno");
            String pageSize = this.getValue("pagesize");
            if (ToolsKit.isEmpty(pageNo)) {
                pageNo = "1";
            }
            if (ToolsKit.isEmpty(pageSize)) {
                pageSize = String.valueOf(ToolsConst.PHONEPAGESIZE);
            }
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, userAuthBuService.findPage(Integer.parseInt(pageNo), Integer.parseInt(pageSize)));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 删除用户
     *
     * @return
     */
    @RequestMapping(value = "/delete")
    public ReturnDto delete() {
        try {
            String id = this.getValue("id");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, userAuthBuService.deleteById(id));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/findAccountRoleById")
    public ReturnDto findAccountRoleById() {
        try {
            String id = this.getValue("id");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, userAuthBuService.findAccountRoleById(id));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 分配角色
     *
     * @return
     */
    @RequestMapping(value = "/disrole")
    public ReturnDto disresource() {
        try {
            String id = this.getValue("id");
            String roleIds = this.getValue("roleIds");
            userAuthBuService.disrole(id, roleIds);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, null);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/getuserauthorityinfo")
    public ReturnDto getUserAuthorityInfo() {
        try {
            String userid = this.getValue("userid");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, userAuthBuService.getUserAuthorityInfo(userid));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    @Autowired
    private UserAccountCacheService userAccountCacheService;

    @RequestMapping(value = "/settingDefaultManagerUserInfo")
    public ReturnDto settingDefaultManagerUserInfo() {
        try {
            String defaultAccount = this.getValue("defaultAccount");
            String defaultPassword = this.getValue("defaultPassword");
            userAccountCacheService.setDefaultManagerAccount(defaultAccount);
            userAccountCacheService.setDefaultManagerPassword(defaultPassword);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, "");
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/getDefaultManagerUserInfo")
    public ReturnDto getDefaultManagerUserInfo() {
        try {
            Map<String, String> sult = new HashMap<>();
            sult.put("defaultAccount", userAccountCacheService.getDefaultManagerAccount());
            sult.put("defaultPassword", userAccountCacheService.getDefaultManagerPwd());
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, sult);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/bindingDefaultManagerUserId")
    public ReturnDto bindingDefaultManagerUserId() {
        try {
            String id = this.getValue("id");
            userAccountCacheService.setDefaultManagerBandId(id);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, "");
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }
}
