package net.snaptag.system.account.controller.auth;

import com.vida.sls.annotation.SLSLog;
import com.vida.sls.enums.OperationType;
import net.snaptag.system.account.buservice.ResourceTableBuService;
import net.snaptag.system.account.utils.Constant;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.web.core.BaseController;
import net.snaptag.system.sadais.web.dto.QueryDto;
import net.snaptag.system.sadais.web.dto.ResourceDto;
import net.snaptag.system.sadais.web.dto.ReturnDto;
import net.snaptag.system.sadais.web.dto.UserInfoDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 资源模块
 */
@RestController
@RequestMapping(Constant.PRE_MAPPING_URL + "/resource")
public class ResourceController extends BaseController {
    @Autowired
    private ResourceTableBuService resourceTableBuService;

    /**
     * 新增或更新信息
     *
     * @return
     */
    @RequestMapping(value = "/saveorupdate")
    @SLSLog(value = "新增或更新资源", configKey = "business", businessType = "资源模块", operation = OperationType.UPDATE)
    public ReturnDto saveOrUpdate() {
        try {
            ResourceDto resourceDto = this.getBean(ResourceDto.class);
            resourceTableBuService.saveOrUpdate(resourceDto);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, null);
        } catch (ServiceException e) {
            e.printStackTrace();
            return this.returnFailJson(e);
        }
    }

    /**
     * 删除
     *
     * @return
     */
    @RequestMapping(value = "/del")
    @SLSLog(value = "删除资源", configKey = "business", businessType = "资源模块", operation = OperationType.DELETE)
    public ReturnDto del() {
        try {
            String id = this.getValue("id");
            resourceTableBuService.del(id);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, null);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取列表信息
     *
     * @return
     */
    @RequestMapping(value = "/findpagelist")
    @SLSLog(value = "获取资源列表", configKey = "business", businessType = "资源模块", operation = OperationType.SELECT)
    public ReturnDto findPage() {
        try {
            QueryDto queryDto = this.getBean(QueryDto.class);
            UserInfoDto userInfoDto = this.getUserInfoDto();
            String appId = "";// userInfoDto.getAppId();
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, resourceTableBuService.findPageList(appId, queryDto));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    @RequestMapping(value = "/fetchResourcesTree")
    @SLSLog(value = "获取资源树", configKey = "business", businessType = "资源模块", operation = OperationType.SELECT)
    public ReturnDto getResourcesTree() {
        try {
            QueryDto queryDto = this.getBean(QueryDto.class);
            UserInfoDto userInfoDto = this.getUserInfoDto();
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, resourceTableBuService.getResourcesTree(queryDto));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }
}
