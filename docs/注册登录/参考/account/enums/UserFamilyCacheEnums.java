package net.snaptag.system.account.enums;

import net.snaptag.system.sadais.core.common.enums.ICacheEnums;

import java.util.LinkedHashMap;
public enum UserFamilyCacheEnums implements ICacheEnums {
    USER_FAMILY_BY_USERID("pc:account:uf:by:uid:", "家庭成员信息对象");
    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;
    static {
        map = new LinkedHashMap<String, String>();
        for (UserFamilyCacheEnums userFamilyCacheEnums : UserFamilyCacheEnums.values()) {
            map.put(userFamilyCacheEnums.getKey(), userFamilyCacheEnums.getDesc());
        }  
    }

    private UserFamilyCacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }
}
