package net.snaptag.system.account.enums;
import java.util.LinkedHashMap;
/**
 * 账户登录时状态枚举
 *
 */
public enum LoginStatusEnums {
    BINDING(1, "需要绑定"), 
    LOGIN(2, "直接登录");
    private final int                                   value;
    private final String                                desc;
    private static final LinkedHashMap<Integer, String> map;
    static {
        map = new LinkedHashMap<>();
        for (LoginStatusEnums loginStatusEnums : LoginStatusEnums.values()) {
            map.put(loginStatusEnums.getValue(), loginStatusEnums.getDesc());
        }
    }

    LoginStatusEnums(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<Integer, String> getMap() {
        return map;
    }
}
