package net.snaptag.system.account.enums;
/**
 * 微信openId类型
 */
public enum WeChatOpenIdType {
    APP("app", "APP微信登录"), 
    HEALTH("health", "健康小程序"), 
    MOBILE_SITE("mobilesite", "M站");
    public String value;
    public String desc;

    WeChatOpenIdType(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
    
    /**
     * 根据from获取微信的openId 类型
     *
     * @param from
     *            来源
     * @return 类型
     */
    public static WeChatOpenIdType getByFrom(String from) {
        if (from != null) {
            for (WeChatOpenIdType weChatOpenIdType : WeChatOpenIdType.values()) {
                if (weChatOpenIdType.value.equals(from)) {
                    return weChatOpenIdType;
                }
            }
        }
        return null;
    }
}
