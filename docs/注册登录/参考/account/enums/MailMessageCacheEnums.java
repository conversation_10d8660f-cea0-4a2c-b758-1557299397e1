package net.snaptag.system.account.enums;

import net.snaptag.system.sadais.core.common.enums.ICacheEnums;

import java.util.LinkedHashMap;

public enum MailMessageCacheEnums implements ICacheEnums {
    MESSAGE_VALIDATOR_BY_ACCOUNT("pcmail:msg:validator:", "账号验证码"),
    MESSAGE_MAX_TOTAL_BY_DAY("pcmail:msg:max:num:day:", "单日验证码数量上限"),
    MESSAGE_IP_COUNT_BY_DAY("pcmail:msg:ip:num:day:", "单日IP数量上限"),
    MESSAGE_ACCOUNT_RETRY("pcmail:msg:ac:re:day:", "图片验证弹窗限制次数"),
    MESSAGE_INTERVAL_TIME("pcmail:msg:code:in:time:", "验证码生成间隔时间");

    private final String                               key;
    private final String                               desc;
    private static final LinkedHashMap<String, String> map;

    static {
        map = new LinkedHashMap<String, String>();
        for (MailMessageCacheEnums mailMessageCacheEnums : MailMessageCacheEnums.values()) {
            map.put(mailMessageCacheEnums.getKey(), mailMessageCacheEnums.getDesc());
        }
    }

    private MailMessageCacheEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static LinkedHashMap<String, String> getMap() {
        return map;
    }

}