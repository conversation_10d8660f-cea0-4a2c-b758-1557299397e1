package net.snaptag.system.account.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.snaptag.system.account.entity.UserThirdPartyAuth;
import net.snaptag.system.account.mapper.UserThirdPartyAuthMapper;
import net.snaptag.system.common.DataConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * 第三方授权信息
 */
@Repository
public class UserThirdPartyAuthDao extends ServiceImpl<UserThirdPartyAuthMapper, UserThirdPartyAuth> {

    @Autowired
    private UserThirdPartyAuthMapper userThirdPartyAuthMapper;
    /**
     * 微信uid查询
     *
     * @param weChatUnionId
     *            用户id
     * @throws Exception
     */
    public UserThirdPartyAuth getByWeChatUnionId(String weChatUnionId) {
        QueryWrapper<UserThirdPartyAuth> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);
        queryWrapper.eq("wechat_union_id", weChatUnionId);
        queryWrapper.select("user_id");
        return this.getOne(queryWrapper);
    }

    /**
     * 用户id查询
     *
     * @param userId
     *            用户id
     * @throws Exception
     */
    public UserThirdPartyAuth getByUserId(String userId) {
        QueryWrapper<UserThirdPartyAuth> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);
        queryWrapper.eq("user_id", userId);
        return this.getOne(queryWrapper);
    }
}
