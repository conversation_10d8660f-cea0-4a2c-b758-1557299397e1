package net.snaptag.system.account.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.snaptag.system.account.entity.UserRole;
import net.snaptag.system.account.mapper.UserRoleMapper;
import net.snaptag.system.common.DataConst;
import net.snaptag.system.sadais.util.core.ToolsKit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 用户角色表
 */
@Repository
public class UserRoleDao extends ServiceImpl<UserRoleMapper, UserRole> {

    @Autowired
    private UserRoleMapper userRoleMapper;
    /**
     * 根据用户账户和项目ID查询出角色列表
     * 
     * @param userAccountId
     *            用户账户ID
     * @param projectIds
     *            项目ID
     * @return
     */
    public List<UserRole> findUserRoleByProjectId(String userAccountId, List<String> projectIds) {
        QueryWrapper<UserRole> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_account_id", userAccountId);
        if (ToolsKit.isNotEmpty(projectIds)){
            queryWrapper.in("project_id", projectIds);
        }
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);
        return this.list(queryWrapper);
    }

    /**
     * 根据角色ID查询数量
     * 
     * @param roleIds
     *            角色ID
     * @return
     */
    public long getCountByRole(List<String> roleIds) {
        QueryWrapper<UserRole> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("role_id", roleIds);
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);
        return this.count(queryWrapper);
    }

    /**
     * 根据ID删除用户账号角色信息
     *
     * @param accountId
     *            户账号ID
     * @throws Exception
     */
    public void delUserRoleByAccount(String accountId) {
        QueryWrapper<UserRole> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_account_id", accountId);
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);

        UserRole userRole = new UserRole();
        userRole.setStatus(DataConst.DATA_DELETE_STATUS);
        this.update(userRole, queryWrapper);
    }
}
