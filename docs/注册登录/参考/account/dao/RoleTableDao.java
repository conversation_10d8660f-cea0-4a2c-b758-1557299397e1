package net.snaptag.system.account.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.snaptag.system.account.entity.RoleTable;
import net.snaptag.system.account.mapper.RoleTableMapper;
import net.snaptag.system.common.DataConst;
import net.snaptag.system.sadais.util.core.ToolsKit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.regex.Pattern;

/**
 * 角色表
 */
@Repository
public class RoleTableDao extends ServiceImpl<RoleTableMapper, RoleTable> {

    @Autowired
    private RoleTableMapper roleTableMapper;
    /**
     * 根据查询出角色记录
     * 
     * @return
     */
    public IPage<RoleTable> findRoleTableList(String name, int pageNo, int pageSize, String appId) {
        QueryWrapper<RoleTable> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);

        if (ToolsKit.isNotEmpty(name)) {
            queryWrapper.like("role_name", name);
        }
        if (ToolsKit.isNotEmpty(appId)) {
            queryWrapper.eq("app_id", appId);
        }

        queryWrapper.orderByDesc("createtime");
        Page<RoleTable> page = new Page<>(pageNo + 1, pageSize);
        return this.page(page, queryWrapper);
    }

    /**
     * 根据ID查询角色信息
     *
     * @param id
     *            ID
     * @return 角色信息
     * @throws Exception
     */
    public RoleTable getRoleTableById(String id) {
        QueryWrapper<RoleTable> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id);
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);
        return this.getOne(queryWrapper);
    }

    /**
     * 根据ID删除角色信息
     *
     * @param id
     *            ID
     * @throws Exception
     */
    public void delRoleTable(String id) {
        RoleTable roleTable = new RoleTable();
        roleTable.setId(id);
        roleTable.setStatus(DataConst.DATA_DELETE_STATUS);
        this.updateById(roleTable);
    }

    /**
     * 根据角色编码查询数量
     *
     * @param roleCode
     *            角色编码
     * @throws Exception
     */
    public long getCountByCode(String roleCode) {
        QueryWrapper<RoleTable> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("role_code", roleCode);
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);
        return this.count(queryWrapper);
    }
}
