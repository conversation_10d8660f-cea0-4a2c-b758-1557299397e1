package net.snaptag.system.account.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.snaptag.system.account.entity.UserAuthAccount;
import net.snaptag.system.account.mapper.UserAuthAccountMapper;
import net.snaptag.system.common.DataConst;
import net.snaptag.system.sadais.util.core.ToolsKit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date ：Created in 2023/11/20 14:30
 * @description：用户授权账号DAO
 * @modified By：
 * @version: $
 */
@Repository
public class UserAuthAccountDao extends ServiceImpl<UserAuthAccountMapper, UserAuthAccount> {

    @Autowired
    private UserAuthAccountMapper userAuthAccountMapper;
    /**
     * 根据用户账号查询用户账号对象
     * @param account 用户账号
     * @return
     */
    public UserAuthAccount getUserAccountByAccount(String account) {
        QueryWrapper<UserAuthAccount> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("account", account);
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);
        return this.getOne(queryWrapper);
    }

    /**
     * 根据查询出用户账号信息记录
     *
     * @param name
     *            名称
     * @return
     */
    public IPage<UserAuthAccount> findUserAccountList(String name, int pageNo, int pageSize, String appId) {
        QueryWrapper<UserAuthAccount> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);
        if (ToolsKit.isNotEmpty(name)) {
            queryWrapper.like("name", name);
        }
        if (ToolsKit.isNotEmpty(appId)) {
            queryWrapper.eq("app_id", appId);
        }
        queryWrapper.orderByDesc("createtime");
        Page<UserAuthAccount> page = new Page<>(pageNo + 1, pageSize);
        return this.page(page, queryWrapper);
    }

    /**
     * 根据ID查询用户账号信息
     *
     * @param id
     *            ID
     * @return 用户账号信息
     * @throws Exception
     */
    public UserAuthAccount getUserAccountById(String id) {
        QueryWrapper<UserAuthAccount> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id);
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);
        return this.getOne(queryWrapper);
    }

    /**
     * 根据ID删除用户账号信息
     *
     * @param id
     *            ID
     * @throws Exception
     */
    public void delUserAccount(String id) {
        UpdateWrapper<UserAuthAccount> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", id);
        updateWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);
        updateWrapper.set("status", DataConst.DATA_DELETE_STATUS);
        this.update(updateWrapper);
    }
}
