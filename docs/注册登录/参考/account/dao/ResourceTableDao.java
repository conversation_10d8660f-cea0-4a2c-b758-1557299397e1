package net.snaptag.system.account.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.snaptag.system.account.entity.ResourceTable;
import net.snaptag.system.account.mapper.ResourceTableMapper;
import net.snaptag.system.common.DataConst;
import net.snaptag.system.sadais.util.core.ToolsKit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * 资源表
 */
@Repository
public class ResourceTableDao extends ServiceImpl<ResourceTableMapper, ResourceTable> {

    @Autowired
    private ResourceTableMapper resourceTableMapper;
    /**
     * 获取列表信息
     */
    public IPage<ResourceTable> findPageList(String appId, int pageNo, int pageSize, Object name, Date startDate, Date endDate) {
        QueryWrapper<ResourceTable> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);

        if (ToolsKit.isNotEmpty(name)) {
            queryWrapper.like("res_name", name.toString());
        }
        if (ToolsKit.isNotEmpty(appId)) {
            queryWrapper.eq("app_id", appId);
        }
        if (ToolsKit.isNotEmpty(startDate) && ToolsKit.isNotEmpty(endDate)) {
            queryWrapper.between("createtime", startDate, endDate);
        }

        queryWrapper.orderByDesc("createtime");
        Page<ResourceTable> page = new Page<>(pageNo + 1, pageSize);
        return this.page(page, queryWrapper);
    }

    /**
     * 根据条件查询列表
     */
    public List<ResourceTable> findListByCond(Object name) {
        QueryWrapper<ResourceTable> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);

        if (ToolsKit.isNotEmpty(name)) {
            queryWrapper.like("res_name", name.toString());
        }

        queryWrapper.orderByAsc("createtime");
        return this.list(queryWrapper);
    }

    /**
     * 根据资源ID集合获取资源列表
     */
    public List<ResourceTable> findResourceByIds(List<String> resourceIds) {
        QueryWrapper<ResourceTable> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", resourceIds);
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);
        queryWrapper.orderByDesc("sort");
        return this.list(queryWrapper);
    }
}
