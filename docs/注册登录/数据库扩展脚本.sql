-- 基于参考项目的数据库扩展脚本

-- 1. 扩展User表，添加新字段
ALTER TABLE user ADD COLUMN phone_verified TINYINT(1) DEFAULT 0 COMMENT '手机号是否已验证';
ALTER TABLE user ADD COLUMN email_verified TINYINT(1) DEFAULT 0 COMMENT '邮箱是否已验证';
ALTER TABLE user ADD COLUMN password_set TINYINT(1) DEFAULT 0 COMMENT '是否已设置密码';
ALTER TABLE user ADD COLUMN bind_phone_required TINYINT(1) DEFAULT 0 COMMENT '是否需要绑定手机号';
-- 注意：不添加salt字段，因为BCryptPasswordEncoder已经内置了盐值机制

-- 2. 创建第三方登录信息表（参考snapTag设计）
CREATE TABLE user_third_party_auth (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    
    -- 微信相关字段
    wechat_open_id TEXT COMMENT '微信openID - JSON格式字符串',
    wechat_union_id VARCHAR(255) COMMENT '微信unionID',
    
    -- QQ相关字段  
    qq_open_id TEXT COMMENT 'QQ openID - JSON格式字符串',
    qq_union_id VARCHAR(255) COMMENT 'QQ unionID',
    
    -- 新浪微博相关字段
    weibo_union_id VARCHAR(255) COMMENT '微博unionID',
    
    -- Apple ID相关字段
    apple_user_id VARCHAR(255) COMMENT 'Apple用户ID',
    
    -- 通用字段
    -- 不存储access_token等敏感信息，参考原有手机验证码做法
    -- 这些信息只在验证时使用，不需要持久化存储

    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_wechat_union_id (wechat_union_id),
    INDEX idx_qq_union_id (qq_union_id),
    INDEX idx_weibo_union_id (weibo_union_id),
    INDEX idx_apple_user_id (apple_user_id),
    
    UNIQUE KEY uk_user_id (user_id)
) COMMENT='用户第三方登录信息表';

-- 3. 创建用户登录历史表
CREATE TABLE user_login_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    login_way VARCHAR(50) NOT NULL COMMENT '登录方式：bymobile,byemail,byweixin,byqq等',
    login_ip VARCHAR(50) COMMENT '登录IP',
    login_device VARCHAR(100) COMMENT '登录设备',
    login_location VARCHAR(200) COMMENT '登录地点',
    user_agent TEXT COMMENT '用户代理信息',
    login_status VARCHAR(20) DEFAULT 'success' COMMENT '登录状态：success,failed',
    login_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '登录时间',
    
    INDEX idx_user_id (user_id),
    INDEX idx_login_time (login_time),
    INDEX idx_login_way (login_way)
) COMMENT='用户登录历史表';

-- 4. 邮箱验证码存储在Redis中，无需创建数据库表
-- Redis Key设计：
-- email_captcha:{email}:{purpose} = {code}  TTL: 300秒（5分钟）
-- email_captcha_frequency:{email}:{purpose} = 1  TTL: 60秒（防重复发送）
-- email_captcha_daily:{email} = {count}  TTL: 到当天23:59:59（每日限额）

-- 5. 为现有表添加索引优化
ALTER TABLE user ADD INDEX idx_email (email);
ALTER TABLE user ADD INDEX idx_phone (phone);
ALTER TABLE user ADD INDEX idx_register_type (registerType);

-- 6. 创建登录安全表（防暴力破解）
CREATE TABLE login_security_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    account_identifier VARCHAR(255) NOT NULL COMMENT '账号标识符（手机号、邮箱等）',
    login_way VARCHAR(50) NOT NULL COMMENT '登录方式',
    attempt_count INT DEFAULT 1 COMMENT '尝试次数',
    last_attempt_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '最后尝试时间',
    locked_until DATETIME NULL COMMENT '锁定到期时间',
    client_ip VARCHAR(50) COMMENT '客户端IP',
    
    INDEX idx_account_way (account_identifier, login_way),
    INDEX idx_locked_until (locked_until)
) COMMENT='登录安全日志表';
