# 新登录功能配置示例（参考snapTag项目）

xinye:
  login:
    # 第三方登录配置
    third-party:
      # 微信登录配置
      wechat:
        # APP登录
        app:
          app-id: ${WECHAT_APP_ID:your_wechat_app_id}
          app-secret: ${WECHAT_APP_SECRET:your_wechat_app_secret}
        # 小程序登录
        miniprogram:
          app-id: ${WECHAT_MINI_APP_ID:your_mini_app_id}
          app-secret: ${WECHAT_MINI_APP_SECRET:your_mini_app_secret}
        # 网页登录
        web:
          app-id: ${WECHAT_WEB_APP_ID:your_web_app_id}
          app-secret: ${WECHAT_WEB_APP_SECRET:your_web_app_secret}
          redirect-uri: ${WECHAT_REDIRECT_URI:http://localhost:8080/callback/wechat}
      
      # QQ登录配置
      qq:
        android:
          app-id: ${QQ_ANDROID_APP_ID:your_qq_android_app_id}
          app-key: ${QQ_ANDROID_APP_KEY:your_qq_android_app_key}
        ios:
          app-id: ${QQ_IOS_APP_ID:your_qq_ios_app_id}
          app-key: ${QQ_IOS_APP_KEY:your_qq_ios_app_key}
        redirect-uri: ${QQ_REDIRECT_URI:http://localhost:8080/callback/qq}
      
      # 新浪微博登录配置
      weibo:
        app-key: ${WEIBO_APP_KEY:your_weibo_app_key}
        app-secret: ${WEIBO_APP_SECRET:your_weibo_app_secret}
        redirect-uri: ${WEIBO_REDIRECT_URI:http://localhost:8080/callback/weibo}
      
      # Apple ID登录配置
      apple:
        client-id: ${APPLE_CLIENT_ID:your_apple_client_id}
        team-id: ${APPLE_TEAM_ID:your_apple_team_id}
        key-id: ${APPLE_KEY_ID:your_apple_key_id}
        private-key-path: ${APPLE_PRIVATE_KEY_PATH:classpath:apple-private-key.p8}
    
    # 安全配置
    security:
      # 登录失败限制
      max-login-attempts: 5
      # 账号锁定时间（分钟）
      account-lock-minutes: 30
      # 登录失败记录过期时间（小时）
      failure-record-expire-hours: 24
      
      # 验证码配置
      captcha:
        # 验证码长度
        length: 6
        # 验证码有效期（分钟）
        expire-minutes: 5
        # 单日短信验证码上限
        sms-daily-limit: 50
        # 单日邮箱验证码上限
        email-daily-limit: 20
        # 同一IP单日验证码上限
        ip-daily-limit: 100
      
      # 密码强度配置
      password:
        min-length: 6
        max-length: 20
        require-digit: true
        require-letter: true
        require-special-char: false
        # 禁止的弱密码模式
        forbidden-patterns:
          - "^\\d+$"  # 纯数字
          - "^[a-zA-Z]+$"  # 纯字母
          - "^(.)\\1{5,}$"  # 相同字符重复6次以上
    
    # 邮件服务配置
    email:
      # 发送方配置
      from: ${EMAIL_FROM:<EMAIL>}
      from-name: ${EMAIL_FROM_NAME:心叶教育}
      
      # 验证码邮件模板
      templates:
        login: "您的登录验证码是：{code}，有效期5分钟。"
        register: "您的注册验证码是：{code}，有效期5分钟。"
        reset-password: "您的密码重置验证码是：{code}，有效期5分钟。"
        bind-account: "您的账号绑定验证码是：{code}，有效期5分钟。"
    
    # 短信服务配置（复用现有配置）
    sms:
      # 验证码短信模板
      templates:
        login: "您的登录验证码是{code}，有效期5分钟。【心叶教育】"
        register: "您的注册验证码是{code}，有效期5分钟。【心叶教育】"
        reset-password: "您的密码重置验证码是{code}，有效期5分钟。【心叶教育】"
        bind-account: "您的账号绑定验证码是{code}，有效期5分钟。【心叶教育】"
    
    # 登录历史配置
    history:
      # 是否记录登录历史
      enabled: true
      # 历史记录保留天数
      retention-days: 90
      # 是否记录登录地理位置
      record-location: true
    
    # 缓存配置
    cache:
      # 第三方认证信息缓存时间（小时）
      third-party-auth-expire-hours: 24
      # 用户信息缓存时间（分钟）
      user-info-expire-minutes: 30
      # 登录失败记录缓存时间（小时）
      login-failure-expire-hours: 24

# Redis配置（如果需要单独配置登录相关缓存）
spring:
  redis:
    # 登录相关缓存配置
    login:
      database: 2  # 使用独立的数据库
      timeout: 3000ms
      lettuce:
        pool:
          max-active: 20
          max-wait: -1ms
          max-idle: 10
          min-idle: 0

# 日志配置
logging:
  level:
    com.vida.xinye.x2.service.impl.ThirdPartyLoginService: DEBUG
    com.vida.xinye.x2.service.impl.ExtendedAuthServiceImpl: DEBUG
    com.vida.xinye.x2.controller.api.UnifiedLoginController: DEBUG
