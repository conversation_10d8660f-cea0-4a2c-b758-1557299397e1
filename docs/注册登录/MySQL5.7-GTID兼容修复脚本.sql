-- MySQL 5.7 + GTID 兼容数据库修复脚本
-- 执行前请备份数据库！
-- 此脚本完全兼容GTID模式

-- ========================================
-- 修复User表字段
-- ========================================

-- 重命名字段（检查是否存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'user' AND COLUMN_NAME = 'bind_phone_required') > 0,
    'ALTER TABLE user CHANGE COLUMN bind_phone_required require_phone_binding TINYINT(1) DEFAULT 0 COMMENT "是否需要绑定手机号：0-不需要，1-需要"',
    'SELECT "bind_phone_required字段不存在，跳过重命名" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 删除password_set字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'user' AND COLUMN_NAME = 'password_set') > 0,
    'ALTER TABLE user DROP COLUMN password_set',
    'SELECT "password_set字段不存在，跳过删除" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 删除password_salt字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'user' AND COLUMN_NAME = 'password_salt') > 0,
    'ALTER TABLE user DROP COLUMN password_salt',
    'SELECT "password_salt字段不存在，跳过删除" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加last_login_time字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'user' AND COLUMN_NAME = 'last_login_time') = 0,
    'ALTER TABLE user ADD COLUMN last_login_time DATETIME DEFAULT NULL COMMENT "最后登录时间"',
    'SELECT "last_login_time字段已存在，跳过添加" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加login_count字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'user' AND COLUMN_NAME = 'login_count') = 0,
    'ALTER TABLE user ADD COLUMN login_count INT(11) DEFAULT 0 COMMENT "登录次数"',
    'SELECT "login_count字段已存在，跳过添加" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加account_status字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'user' AND COLUMN_NAME = 'account_status') = 0,
    'ALTER TABLE user ADD COLUMN account_status TINYINT(1) DEFAULT 1 COMMENT "账号状态：0-禁用，1-正常，2-锁定"',
    'SELECT "account_status字段已存在，跳过添加" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ========================================
-- 手动备份第三方登录表数据
-- ========================================

-- 提示用户手动备份
SELECT '=== 重要提示 ===' as message;
SELECT '如果您的user_third_party_auth表中有重要数据，请先手动备份：' as backup_instruction;
SELECT 'mysqldump -u root -p xinye_x2 user_third_party_auth > user_third_party_auth_backup.sql' as backup_command;
SELECT '按任意键继续，或Ctrl+C取消...' as continue_prompt;

-- 等待用户确认（可选，如果需要自动执行可以删除这部分）
-- 注意：在自动化脚本中，可以跳过这个等待

-- ========================================
-- 重建第三方登录表（避免GTID问题）
-- ========================================

-- 直接删除旧表（如果存在）
DROP TABLE IF EXISTS user_third_party_auth;

-- 创建新表（简化设计，只存储登录必要信息）
CREATE TABLE `user_third_party_auth` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `provider` varchar(20) NOT NULL COMMENT '平台：wechat, qq, weibo, apple等',
  `open_id` varchar(100) NOT NULL COMMENT '平台用户唯一标识',
  `union_id` varchar(100) DEFAULT NULL COMMENT '联合标识（如微信unionid）',
  `bind_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '绑定时间',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-正常',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_provider_openid` (`provider`, `open_id`),
  UNIQUE KEY `uk_user_provider` (`user_id`, `provider`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_union_id` (`union_id`),
  KEY `idx_provider` (`provider`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户第三方登录信息表';

-- ========================================
-- 创建其他必要表
-- ========================================

-- 创建登录历史表
CREATE TABLE IF NOT EXISTS `user_login_history` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `login_way` varchar(50) NOT NULL COMMENT '登录方式',
  `login_ip` varchar(50) COMMENT '登录IP',
  `login_device` varchar(100) COMMENT '登录设备',
  `user_agent` TEXT COMMENT '用户代理信息',
  `login_status` varchar(20) DEFAULT 'success' COMMENT '登录状态',
  `login_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '登录时间',
  
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_login_time` (`login_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户登录历史表';

-- 创建登录安全表
CREATE TABLE IF NOT EXISTS `login_security_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_identifier` varchar(255) NOT NULL COMMENT '账号标识符',
  `login_way` varchar(50) NOT NULL COMMENT '登录方式',
  `attempt_count` int(11) DEFAULT 1 COMMENT '尝试次数',
  `last_attempt_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '最后尝试时间',
  `locked_until` datetime DEFAULT NULL COMMENT '锁定到期时间',
  `client_ip` varchar(50) COMMENT '客户端IP',
  
  PRIMARY KEY (`id`),
  KEY `idx_account_way` (`account_identifier`, `login_way`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='登录安全日志表';

-- ========================================
-- 验证修复结果
-- ========================================

SELECT '=== MySQL 5.7 + GTID 兼容修复完成，检查结果 ===' as message;

-- 检查User表字段
SELECT 'USER表字段检查:' as check_type;
SELECT COLUMN_NAME, DATA_TYPE, COLUMN_COMMENT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'user' 
AND COLUMN_NAME IN ('phone_verified', 'email_verified', 'require_phone_binding', 'last_login_time', 'login_count', 'account_status')
ORDER BY COLUMN_NAME;

-- 检查表是否存在
SELECT '表存在性检查:' as check_type;
SELECT TABLE_NAME, TABLE_COMMENT 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME IN ('user_third_party_auth', 'user_login_history', 'login_security_log');

-- 检查第三方登录表结构
SELECT '第三方登录表结构检查:' as check_type;
SELECT COLUMN_NAME, DATA_TYPE, COLUMN_COMMENT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'user_third_party_auth'
ORDER BY ORDINAL_POSITION;

SELECT '=== MySQL 5.7 + GTID 兼容修复完成！===' as completion;
SELECT '注意：如果您有旧的第三方登录数据，请手动迁移到新表结构' as migration_note;

-- ========================================
-- 数据迁移示例（如果需要）
-- ========================================

-- 如果您有备份数据需要迁移，可以参考以下SQL：
/*
-- 示例：从备份表迁移微信数据
INSERT INTO user_third_party_auth (user_id, provider, open_id, union_id, bind_time, status)
SELECT 
    user_id,
    'wechat' as provider,
    JSON_UNQUOTE(JSON_EXTRACT(wechat_open_id, '$.openid')) as open_id,
    wechat_union_id as union_id,
    COALESCE(create_time, NOW()) as bind_time,
    1 as status
FROM user_third_party_auth_backup 
WHERE wechat_open_id IS NOT NULL;

-- 示例：从备份表迁移QQ数据
INSERT INTO user_third_party_auth (user_id, provider, open_id, union_id, bind_time, status)
SELECT 
    user_id,
    'qq' as provider,
    JSON_UNQUOTE(JSON_EXTRACT(qq_open_id, '$.openid')) as open_id,
    qq_union_id as union_id,
    COALESCE(create_time, NOW()) as bind_time,
    1 as status
FROM user_third_party_auth_backup 
WHERE qq_open_id IS NOT NULL;
*/
