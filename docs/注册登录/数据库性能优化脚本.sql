-- 登录功能数据库性能优化脚本
-- 执行前请备份数据库

-- =====================================================
-- 1. 用户表索引优化
-- =====================================================

-- 检查现有索引
SHOW INDEX FROM user;

-- 添加邮箱+状态复合索引（用于邮箱登录）
ALTER TABLE user ADD INDEX idx_email_status (email, status);

-- 添加手机号+状态复合索引（用于手机号登录）
ALTER TABLE user ADD INDEX idx_phone_status (phone, status);

-- 添加用户名+状态复合索引（用于用户名查询）
ALTER TABLE user ADD INDEX idx_username_status (username, status);

-- 添加最后登录时间索引（用于活跃用户统计）
ALTER TABLE user ADD INDEX idx_last_login_time (last_login_time);

-- 添加创建时间索引（用于用户注册统计）
ALTER TABLE user ADD INDEX idx_create_time (create_time);

-- =====================================================
-- 2. 第三方认证表索引优化
-- =====================================================

-- 检查现有索引
SHOW INDEX FROM user_third_party_auth;

-- 添加微信UnionID索引
ALTER TABLE user_third_party_auth ADD INDEX idx_wechat_union_id (wechat_union_id);

-- 添加QQ UnionID索引
ALTER TABLE user_third_party_auth ADD INDEX idx_qq_union_id (qq_union_id);

-- 添加微博UnionID索引
ALTER TABLE user_third_party_auth ADD INDEX idx_weibo_union_id (weibo_union_id);

-- 添加Apple用户ID索引
ALTER TABLE user_third_party_auth ADD INDEX idx_apple_user_id (apple_user_id);

-- 添加用户ID索引（用于反向查询）
ALTER TABLE user_third_party_auth ADD INDEX idx_user_id (user_id);

-- 添加创建时间索引
ALTER TABLE user_third_party_auth ADD INDEX idx_create_time (create_time);

-- 添加更新时间索引
ALTER TABLE user_third_party_auth ADD INDEX idx_update_time (update_time);

-- =====================================================
-- 3. 登录历史表索引优化
-- =====================================================

-- 检查现有索引
SHOW INDEX FROM user_login_history;

-- 添加用户ID+登录时间复合索引（用于查询用户登录历史）
ALTER TABLE user_login_history ADD INDEX idx_user_login_time (user_id, login_time);

-- 添加登录方式+登录时间复合索引（用于统计不同登录方式）
ALTER TABLE user_login_history ADD INDEX idx_login_way_time (login_way, login_time);

-- 添加登录状态+登录时间复合索引（用于统计成功/失败登录）
ALTER TABLE user_login_history ADD INDEX idx_status_time (login_status, login_time);

-- 添加登录IP索引（用于IP分析）
ALTER TABLE user_login_history ADD INDEX idx_login_ip (login_ip);

-- 添加登录时间索引（用于时间范围查询）
ALTER TABLE user_login_history ADD INDEX idx_login_time (login_time);

-- =====================================================
-- 4. 登录安全日志表索引优化（如果存在）
-- =====================================================

-- 创建登录安全日志表（如果不存在）
CREATE TABLE IF NOT EXISTS login_security_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    account_identifier VARCHAR(255) NOT NULL COMMENT '账号标识符（手机号、邮箱等）',
    login_way VARCHAR(50) NOT NULL COMMENT '登录方式',
    attempt_count INT DEFAULT 1 COMMENT '尝试次数',
    last_attempt_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '最后尝试时间',
    locked_until DATETIME NULL COMMENT '锁定到期时间',
    client_ip VARCHAR(50) COMMENT '客户端IP',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT='登录安全日志表';

-- 添加账号+登录方式+时间复合索引
ALTER TABLE login_security_log ADD INDEX idx_account_way_time (account_identifier, login_way, last_attempt_time);

-- 添加锁定到期时间索引
ALTER TABLE login_security_log ADD INDEX idx_locked_until (locked_until);

-- 添加客户端IP索引
ALTER TABLE login_security_log ADD INDEX idx_client_ip (client_ip);

-- 添加最后尝试时间索引
ALTER TABLE login_security_log ADD INDEX idx_last_attempt_time (last_attempt_time);

-- =====================================================
-- 5. 查询优化建议
-- =====================================================

-- 5.1 用户查询优化示例
-- 原始查询：SELECT * FROM user WHERE email = '<EMAIL>'
-- 优化查询：SELECT id, username, password, status FROM user WHERE email = '<EMAIL>' AND status = 1

-- 5.2 第三方认证查询优化示例
-- 原始查询：SELECT * FROM user_third_party_auth WHERE wechat_union_id = 'unionid123'
-- 优化查询：SELECT user_id FROM user_third_party_auth WHERE wechat_union_id = 'unionid123' LIMIT 1

-- 5.3 登录历史查询优化示例
-- 原始查询：SELECT * FROM user_login_history WHERE user_id = 123 ORDER BY login_time DESC
-- 优化查询：SELECT login_way, login_time, login_status FROM user_login_history WHERE user_id = 123 ORDER BY login_time DESC LIMIT 10

-- =====================================================
-- 6. 表结构优化建议
-- =====================================================

-- 6.1 用户表字段优化
-- 建议将不常用字段移到扩展表，保持主表精简

-- 6.2 第三方认证表优化
-- 考虑将JSON字段拆分为独立字段，提升查询性能
ALTER TABLE user_third_party_auth 
ADD COLUMN wechat_openid VARCHAR(100) COMMENT '微信OpenID',
ADD COLUMN qq_openid VARCHAR(100) COMMENT 'QQ OpenID';

-- 为新增字段添加索引
ALTER TABLE user_third_party_auth ADD INDEX idx_wechat_openid (wechat_openid);
ALTER TABLE user_third_party_auth ADD INDEX idx_qq_openid (qq_openid);

-- =====================================================
-- 7. 分区表优化（可选，适用于大数据量）
-- =====================================================

-- 7.1 登录历史表按月分区
-- ALTER TABLE user_login_history 
-- PARTITION BY RANGE (YEAR(login_time) * 100 + MONTH(login_time)) (
--     PARTITION p202501 VALUES LESS THAN (202502),
--     PARTITION p202502 VALUES LESS THAN (202503),
--     PARTITION p202503 VALUES LESS THAN (202504),
--     PARTITION p202504 VALUES LESS THAN (202505),
--     PARTITION p202505 VALUES LESS THAN (202506),
--     PARTITION p202506 VALUES LESS THAN (202507),
--     PARTITION p202507 VALUES LESS THAN (202508),
--     PARTITION p202508 VALUES LESS THAN (202509),
--     PARTITION p202509 VALUES LESS THAN (202510),
--     PARTITION p202510 VALUES LESS THAN (202511),
--     PARTITION p202511 VALUES LESS THAN (202512),
--     PARTITION p202512 VALUES LESS THAN (202513),
--     PARTITION p_future VALUES LESS THAN MAXVALUE
-- );

-- =====================================================
-- 8. 性能监控查询
-- =====================================================

-- 8.1 检查索引使用情况
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    CARDINALITY,
    SUB_PART,
    NULLABLE,
    INDEX_TYPE
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME IN ('user', 'user_third_party_auth', 'user_login_history', 'login_security_log')
ORDER BY TABLE_NAME, INDEX_NAME;

-- 8.2 检查表大小
SELECT 
    TABLE_NAME,
    ROUND(((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024), 2) AS 'Table Size (MB)',
    TABLE_ROWS
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME IN ('user', 'user_third_party_auth', 'user_login_history', 'login_security_log');

-- 8.3 检查慢查询（需要开启慢查询日志）
-- SHOW VARIABLES LIKE 'slow_query_log';
-- SHOW VARIABLES LIKE 'long_query_time';

-- =====================================================
-- 9. 执行计划分析示例
-- =====================================================

-- 9.1 分析邮箱登录查询
EXPLAIN SELECT id, username, password, status 
FROM user 
WHERE email = '<EMAIL>' AND status = 1;

-- 9.2 分析第三方登录查询
EXPLAIN SELECT user_id 
FROM user_third_party_auth 
WHERE wechat_union_id = 'test_union_id';

-- 9.3 分析登录历史查询
EXPLAIN SELECT login_way, login_time, login_status 
FROM user_login_history 
WHERE user_id = 123 
ORDER BY login_time DESC 
LIMIT 10;

-- =====================================================
-- 10. 维护脚本
-- =====================================================

-- 10.1 定期清理过期登录历史（保留3个月）
-- DELETE FROM user_login_history 
-- WHERE login_time < DATE_SUB(NOW(), INTERVAL 3 MONTH);

-- 10.2 定期清理过期安全日志（保留1个月）
-- DELETE FROM login_security_log 
-- WHERE last_attempt_time < DATE_SUB(NOW(), INTERVAL 1 MONTH);

-- 10.3 优化表碎片
-- OPTIMIZE TABLE user;
-- OPTIMIZE TABLE user_third_party_auth;
-- OPTIMIZE TABLE user_login_history;
-- OPTIMIZE TABLE login_security_log;

-- =====================================================
-- 执行完成后的验证
-- =====================================================

-- 验证索引是否创建成功
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME,
    SEQ_IN_INDEX
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME IN ('user', 'user_third_party_auth', 'user_login_history', 'login_security_log')
AND INDEX_NAME LIKE 'idx_%'
ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX;
