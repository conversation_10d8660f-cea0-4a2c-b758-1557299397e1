{"info": {"name": "登录功能API测试集合", "description": "登录功能相关API接口的Postman测试集合", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "https://api.example.com", "type": "string"}, {"key": "accessToken", "value": "", "type": "string"}], "item": [{"name": "登录接口", "item": [{"name": "邮箱密码登录", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"loginMethod\": \"password\",\n    \"password\": \"123456\"\n}"}, "url": {"raw": "{{baseUrl}}/authen/email/login", "host": ["{{baseUrl}}"], "path": ["authen", "email", "login"]}}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has required fields\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('code');", "    pm.expect(jsonData).to.have.property('message');", "    pm.expect(jsonData).to.have.property('data');", "});", "", "pm.test(\"Login successful\", function () {", "    var jsonData = pm.response.json();", "    if (jsonData.code === 200) {", "        pm.expect(jsonData.data).to.have.property('accessToken');", "        pm.expect(jsonData.data).to.have.property('userId');", "        // 保存Token供后续请求使用", "        pm.collectionVariables.set('accessToken', jsonData.data.accessToken);", "    }", "});"]}}]}, {"name": "邮箱验证码登录", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"loginMethod\": \"captcha\",\n    \"captcha\": \"123456\"\n}"}, "url": {"raw": "{{baseUrl}}/authen/email/login", "host": ["{{baseUrl}}"], "path": ["authen", "email", "login"]}}, "response": []}, {"name": "手机号密码登录", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"***********\",\n    \"password\": \"123456\"\n}"}, "url": {"raw": "{{baseUrl}}/authen/phone/password/login", "host": ["{{baseUrl}}"], "path": ["authen", "phone", "password", "login"]}}, "response": []}, {"name": "微信第三方登录", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"loginWay\": \"byweixin\",\n    \"openId\": \"wx_openid_123456\",\n    \"unionId\": \"wx_unionid_789012\",\n    \"nickname\": \"微信用户\",\n    \"headPic\": \"https://wx.qlogo.cn/avatar.jpg\",\n    \"sex\": \"1\",\n    \"from\": \"app\"\n}"}, "url": {"raw": "{{baseUrl}}/authen/third-party/login", "host": ["{{baseUrl}}"], "path": ["authen", "third-party", "login"]}}, "response": []}, {"name": "QQ第三方登录", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"loginWay\": \"byqq\",\n    \"openId\": \"qq_openid_123456\",\n    \"unionId\": \"qq_unionid_789012\",\n    \"nickname\": \"QQ用户\",\n    \"headPic\": \"https://q.qlogo.cn/avatar.jpg\",\n    \"sex\": \"2\",\n    \"from\": \"app\"\n}"}, "url": {"raw": "{{baseUrl}}/authen/third-party/login", "host": ["{{baseUrl}}"], "path": ["authen", "third-party", "login"]}}, "response": []}, {"name": "通用登录（兼容接口）", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"<EMAIL>\",\n    \"password\": \"123456\"\n}"}, "url": {"raw": "{{baseUrl}}/authen/login", "host": ["{{baseUrl}}"], "path": ["authen", "login"]}}, "response": []}, {"name": "手机号验证码登录（兼容接口）", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"***********\",\n    \"smsCode\": \"123456\"\n}"}, "url": {"raw": "{{baseUrl}}/authen/loginBySms", "host": ["{{baseUrl}}"], "path": ["authen", "loginBySms"]}}, "response": []}]}, {"name": "验证码接口", "item": [{"name": "发送邮箱验证码", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"purpose\": \"login\"\n}"}, "url": {"raw": "{{baseUrl}}/authen/email/captcha/send", "host": ["{{baseUrl}}"], "path": ["authen", "email", "<PERSON><PERSON>a", "send"]}}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Capt<PERSON> sent successfully\", function () {", "    var jsonData = pm.response.json();", "    if (jsonData.code === 200) {", "        pm.expect(jsonData.data).to.have.property('email');", "        pm.expect(jsonData.data).to.have.property('expiration');", "    }", "});"]}}]}, {"name": "发送短信验证码", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"***********\"\n}"}, "url": {"raw": "{{baseUrl}}/captcha/send", "host": ["{{baseUrl}}"], "path": ["<PERSON><PERSON>a", "send"]}}, "response": []}, {"name": "验证短信验证码", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"account\": \"***********\",\n    \"code\": \"123456\"\n}"}, "url": {"raw": "{{baseUrl}}/captcha/verify", "host": ["{{baseUrl}}"], "path": ["<PERSON><PERSON>a", "verify"]}}, "response": []}]}, {"name": "密码管理接口", "item": [{"name": "设置密码", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "body": {"mode": "raw", "raw": "{\n    \"oldPassword\": \"123456\",\n    \"newPassword\": \"NewPass123!\",\n    \"confirmPassword\": \"NewPass123!\"\n}"}, "url": {"raw": "{{baseUrl}}/user/password/set", "host": ["{{baseUrl}}"], "path": ["user", "password", "set"]}}, "response": []}, {"name": "重置密码", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"captcha\": \"123456\",\n    \"newPassword\": \"NewPass123!\",\n    \"confirmPassword\": \"NewPass123!\"\n}"}, "url": {"raw": "{{baseUrl}}/user/password/reset", "host": ["{{baseUrl}}"], "path": ["user", "password", "reset"]}}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// 全局前置脚本", "console.log('请求开始: ' + pm.info.requestName);"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// 全局测试脚本", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});", "", "pm.test('Response has valid JSON', function () {", "    pm.response.to.be.json;", "});"]}}]}