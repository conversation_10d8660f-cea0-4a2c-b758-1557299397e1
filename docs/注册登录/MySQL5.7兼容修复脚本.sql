-- MySQL 5.7 兼容数据库修复脚本
-- 执行前请备份数据库！

-- ========================================
-- 修复User表字段
-- ========================================

-- 重命名字段（检查是否存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'user' AND COLUMN_NAME = 'bind_phone_required') > 0,
    'ALTER TABLE user CHANGE COLUMN bind_phone_required require_phone_binding TINYINT(1) DEFAULT 0 COMMENT "是否需要绑定手机号：0-不需要，1-需要"',
    'SELECT "bind_phone_required字段不存在，跳过重命名" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 删除password_set字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'user' AND COLUMN_NAME = 'password_set') > 0,
    'ALTER TABLE user DROP COLUMN password_set',
    'SELECT "password_set字段不存在，跳过删除" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 删除password_salt字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'user' AND COLUMN_NAME = 'password_salt') > 0,
    'ALTER TABLE user DROP COLUMN password_salt',
    'SELECT "password_salt字段不存在，跳过删除" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加last_login_time字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'user' AND COLUMN_NAME = 'last_login_time') = 0,
    'ALTER TABLE user ADD COLUMN last_login_time DATETIME DEFAULT NULL COMMENT "最后登录时间"',
    'SELECT "last_login_time字段已存在，跳过添加" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加login_count字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'user' AND COLUMN_NAME = 'login_count') = 0,
    'ALTER TABLE user ADD COLUMN login_count INT(11) DEFAULT 0 COMMENT "登录次数"',
    'SELECT "login_count字段已存在，跳过添加" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加account_status字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'user' AND COLUMN_NAME = 'account_status') = 0,
    'ALTER TABLE user ADD COLUMN account_status TINYINT(1) DEFAULT 1 COMMENT "账号状态：0-禁用，1-正常，2-锁定"',
    'SELECT "account_status字段已存在，跳过添加" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ========================================
-- 重建第三方登录表
-- ========================================

-- 备份现有数据（兼容GTID模式）
-- 检查原表是否存在，如果存在则备份
SET @backup_sql = '';
SET @table_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES
                     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'user_third_party_auth');

-- 如果原表存在，先创建备份表结构（基于原表结构）
SET @sql = (SELECT IF(
    @table_exists > 0,
    'CREATE TABLE IF NOT EXISTS user_third_party_auth_backup LIKE user_third_party_auth',
    'SELECT "user_third_party_auth表不存在，跳过备份" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 如果原表存在，插入备份数据
SET @sql = (SELECT IF(
    @table_exists > 0,
    'INSERT IGNORE INTO user_third_party_auth_backup SELECT * FROM user_third_party_auth',
    'SELECT "user_third_party_auth表不存在，跳过数据备份" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 删除旧表
DROP TABLE IF EXISTS user_third_party_auth;

-- 创建新表（简化设计，只存储登录必要信息）
CREATE TABLE `user_third_party_auth` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `provider` varchar(20) NOT NULL COMMENT '平台：wechat, qq, weibo, apple等',
  `open_id` varchar(100) NOT NULL COMMENT '平台用户唯一标识',
  `union_id` varchar(100) DEFAULT NULL COMMENT '联合标识（如微信unionid）',
  `bind_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '绑定时间',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-正常',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_provider_openid` (`provider`, `open_id`),
  UNIQUE KEY `uk_user_provider` (`user_id`, `provider`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_union_id` (`union_id`),
  KEY `idx_provider` (`provider`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户第三方登录信息表';

-- ========================================
-- 创建其他必要表
-- ========================================

-- 创建登录历史表
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES 
     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'user_login_history') = 0,
    'CREATE TABLE `user_login_history` (
      `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT "主键ID",
      `user_id` bigint(20) NOT NULL COMMENT "用户ID",
      `login_way` varchar(50) NOT NULL COMMENT "登录方式",
      `login_ip` varchar(50) COMMENT "登录IP",
      `login_device` varchar(100) COMMENT "登录设备",
      `user_agent` TEXT COMMENT "用户代理信息",
      `login_status` varchar(20) DEFAULT "success" COMMENT "登录状态",
      `login_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT "登录时间",
      
      PRIMARY KEY (`id`),
      KEY `idx_user_id` (`user_id`),
      KEY `idx_login_time` (`login_time`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT="用户登录历史表"',
    'SELECT "user_login_history表已存在，跳过创建" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 创建登录安全表
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES 
     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'login_security_log') = 0,
    'CREATE TABLE `login_security_log` (
      `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT "主键ID",
      `account_identifier` varchar(255) NOT NULL COMMENT "账号标识符",
      `login_way` varchar(50) NOT NULL COMMENT "登录方式",
      `attempt_count` int(11) DEFAULT 1 COMMENT "尝试次数",
      `last_attempt_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT "最后尝试时间",
      `locked_until` datetime DEFAULT NULL COMMENT "锁定到期时间",
      `client_ip` varchar(50) COMMENT "客户端IP",
      
      PRIMARY KEY (`id`),
      KEY `idx_account_way` (`account_identifier`, `login_way`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT="登录安全日志表"',
    'SELECT "login_security_log表已存在，跳过创建" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ========================================
-- 验证修复结果
-- ========================================

SELECT '=== MySQL 5.7 兼容修复完成，检查结果 ===' as message;

-- 检查User表字段
SELECT 'USER表字段检查:' as check_type;
SELECT COLUMN_NAME, DATA_TYPE, COLUMN_COMMENT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'user' 
AND COLUMN_NAME IN ('phone_verified', 'email_verified', 'require_phone_binding', 'last_login_time', 'login_count', 'account_status')
ORDER BY COLUMN_NAME;

-- 检查表是否存在
SELECT '表存在性检查:' as check_type;
SELECT TABLE_NAME, TABLE_COMMENT 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME IN ('user_third_party_auth', 'user_login_history', 'login_security_log');

-- 检查备份数据
SELECT '备份数据检查:' as check_type;
SELECT 
  CASE 
    WHEN EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'user_third_party_auth_backup')
    THEN CONCAT('发现备份表，包含 ', (SELECT COUNT(*) FROM user_third_party_auth_backup), ' 条记录')
    ELSE '没有发现备份数据'
  END as backup_status;

SELECT '=== MySQL 5.7 兼容修复完成！===' as completion;
SELECT '如果有备份数据需要迁移，请联系技术支持' as note;
