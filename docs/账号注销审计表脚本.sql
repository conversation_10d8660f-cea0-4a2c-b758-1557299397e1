-- 账号注销审计表创建脚本（可选）
-- 如果需要详细的删除审计记录，可以执行此脚本

-- 创建用户删除审计日志表
CREATE TABLE IF NOT EXISTS `user_deletion_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `account` varchar(100) NOT NULL COMMENT '删除的账号（邮箱或手机号）',
  `delete_reason` varchar(200) DEFAULT NULL COMMENT '删除原因',
  `operator_type` varchar(20) DEFAULT 'USER' COMMENT '操作类型：USER-用户自删，ADMIN-管理员删除',
  `operator_id` bigint(20) DEFAULT NULL COMMENT '操作人ID（管理员删除时记录管理员ID）',
  `operation_ip` varchar(50) DEFAULT NULL COMMENT '操作IP地址',
  `user_agent` text COMMENT '用户代理信息',
  `deleted_files_count` int(11) DEFAULT 0 COMMENT '删除的文件数量',
  `deletion_status` varchar(20) DEFAULT 'SUCCESS' COMMENT '删除状态：SUCCESS-成功，FAILED-失败，PARTIAL-部分成功',
  `error_message` text COMMENT '错误信息（如果删除失败）',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_account` (`account`),
  KEY `idx_operator_type` (`operator_type`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deletion_status` (`deletion_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户删除审计日志表';

-- 创建索引以提高查询性能
-- 按用户ID查询删除记录
CREATE INDEX idx_user_deletion_user_id ON user_deletion_log(user_id, create_time DESC);

-- 按账号查询删除记录
CREATE INDEX idx_user_deletion_account ON user_deletion_log(account, create_time DESC);

-- 按时间范围查询删除记录
CREATE INDEX idx_user_deletion_time ON user_deletion_log(create_time DESC);

-- 验证表创建
SELECT '用户删除审计表创建完成' as result;
SELECT TABLE_NAME, TABLE_COMMENT 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'user_deletion_log';

-- 查看表结构
DESCRIBE user_deletion_log;
