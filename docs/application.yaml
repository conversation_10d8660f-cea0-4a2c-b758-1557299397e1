spring:
  servlet:
    multipart:
      enabled: true
      max-request-size: 20480MB
      max-file-size: 10240MB
  datasource:
    url: *************************************************************************************************************************
    username: xprinter_test
    password: Vida@2022
  redis:
    host: localhost
    port: 6377
    database: 1
    username: r-wz9zemom88r2yiqd8l
    password: vIdaPrinter@2022
  mail:
    protocol: smtps
    host: smtp.exmail.qq.com
    port: 465
    username: <EMAIL>
    password: m8XpVEjzMNDBAv4q
    # 自定义redis key
# 日志配置
logging:
  level:
    root: info
    com.vida.xinye: debug
    com.vida.xinye.x2.mbg.mapper.*: debug
    com.vida.xinye.x2.dao.*: debug
    com.aliyun.*: error
    com.vida.xinye.x2.mbg.mapper.UserMapper: OFF
#redis缓存配置
redis:
  database: x2-dev
  captcha:
    prefix: captcha_
    expiration: 300

aliyun:
  oss:
    endpoint: oss-cn-shenzhen.aliyuncs.com # oss对外服务的访问域名
    accessKeyId: LTAI5tLF6WShrgd5NPJ3vV6t # 访问身份验证中用到用户标识
    accessKeySecret: ****************************** # 用户用于加密签名字符串和oss用来验证签名字符串的密钥
    bucketName: xprinter-private # oss的存储空间
    stsTokenServer: http://localhost:7084
  idcard:
    app-key: 204845804
    app-secret: xVECIhu4OIK8doSfepN1vro6ml4pAvyg
    url: https://kzidcardv1.market.alicloudapi.com/api-mall/api/id_card/check
    method: POST
  sms:
    access-key-id: LTAI5tJZZh61K68hkPtRaXZj
    access-key-secret: ******************************
    sign-name: 微嗒科技
    template-code: SMS_217875529
    endpoint: dysmsapi.aliyuncs.com
  log:
    level: debug
    endpoint: cn-shenzhen.log.aliyuncs.com
    accessKeyId: LTAI5tHoxVK6fykQWRFEhhm2
    accessKeySecret: ******************************
    producer:
      project: xinye-jiayong
      logstore: logstore-business
    syslog:
      project: xinye-jiayong
      logstore: logstore-syslog

# jwt
jwt:
  secret: 123456
  expiration: 86400
file:
  download:
    path: D:/flow