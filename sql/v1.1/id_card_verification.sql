-- 身份证认证缓存表
CREATE TABLE `id_card_verification` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(50) NOT NULL COMMENT '姓名',
  `id_card_hash` varchar(64) NOT NULL COMMENT '身份证号码哈希值(SHA256)',
  `verification_result` tinyint(1) NOT NULL COMMENT '验证结果：0-一致，1-不一致',
  `birthday` varchar(20) DEFAULT NULL COMMENT '生日',
  `address` varchar(200) DEFAULT NULL COMMENT '地址',
  `sex` varchar(10) DEFAULT NULL COMMENT '性别',
  `description` varchar(100) DEFAULT NULL COMMENT '描述',
  `order_no` varchar(50) DEFAULT NULL COMMENT '第三方订单编号',
  `verification_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '认证时间',
  `expire_time` timestamp NOT NULL COMMENT '过期时间',
  `source` varchar(20) NOT NULL DEFAULT 'aliyun' COMMENT '认证来源：aliyun等',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_idcard_hash` (`id_card_hash`),
  KEY `idx_verification_time` (`verification_time`),
  KEY `idx_expire_time` (`expire_time`),
  KEY `idx_name_idcard_expire` (`name`, `id_card_hash`, `expire_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='身份证认证缓存表';
