ALTER TABLE `user`
ADD COLUMN `role_type` VARCHAR(32) DEFAULT NULL COMMENT '用户身份类型',
ADD COLUMN `grade_type` VARCHAR(32) DEFAULT NULL COMMENT '年级类型';

ALTER TABLE `user`
ADD COLUMN `parent_mode` TINYINT(1) DEFAULT 0 COMMENT '家长模式是否开启 0否 1是',
ADD COLUMN `parent_name` VARCHAR(32) DEFAULT NULL COMMENT '家长姓名',
ADD COLUMN `parent_idcard` VARCHAR(32) DEFAULT NULL COMMENT '家长身份证号',
ADD COLUMN `teenager_password` VARCHAR(128) DEFAULT NULL COMMENT '青少年密码（加密存储）';